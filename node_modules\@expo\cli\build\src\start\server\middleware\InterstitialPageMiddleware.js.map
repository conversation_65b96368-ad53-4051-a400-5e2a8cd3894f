{"version": 3, "sources": ["../../../../../src/start/server/middleware/InterstitialPageMiddleware.ts"], "sourcesContent": ["import { getConfig, getNameFromConfig } from '@expo/config';\nimport { getRuntimeVersionNullableAsync } from '@expo/config-plugins/build/utils/Updates';\nimport { readFile } from 'fs/promises';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\n\ntype ProjectVersion = {\n  type: 'sdk' | 'runtime';\n  version: string | null;\n};\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:interstitialPage'\n) as typeof console.log;\n\nexport const LoadingEndpoint = '/_expo/loading';\n\nexport class InterstitialPageMiddleware extends ExpoMiddleware {\n  constructor(\n    projectRoot: string,\n    protected options: { scheme: string | null } = { scheme: null }\n  ) {\n    super(projectRoot, [LoadingEndpoint]);\n  }\n\n  /** Get the template HTML page and inject values. */\n  async _getPageAsync({\n    appName,\n    projectVersion,\n  }: {\n    appName: string;\n    projectVersion: ProjectVersion;\n  }): Promise<string> {\n    const templatePath =\n      // Production: This will resolve when installed in the project.\n      resolveFrom.silent(this.projectRoot, 'expo/static/loading-page/index.html') ??\n      // Development: This will resolve when testing locally.\n      path.resolve(__dirname, '../../../../../static/loading-page/index.html');\n    let content = (await readFile(templatePath)).toString('utf-8');\n\n    content = content.replace(/{{\\s*AppName\\s*}}/, appName);\n    content = content.replace(/{{\\s*Path\\s*}}/, this.projectRoot);\n    content = content.replace(/{{\\s*Scheme\\s*}}/, this.options.scheme ?? 'Unknown');\n    content = content.replace(\n      /{{\\s*ProjectVersionType\\s*}}/,\n      `${projectVersion.type === 'sdk' ? 'SDK' : 'Runtime'} version`\n    );\n    content = content.replace(/{{\\s*ProjectVersion\\s*}}/, projectVersion.version ?? 'Undetected');\n\n    return content;\n  }\n\n  /** Get settings for the page from the project config. */\n  async _getProjectOptionsAsync(platform: RuntimePlatform): Promise<{\n    appName: string;\n    projectVersion: ProjectVersion;\n  }> {\n    assertRuntimePlatform(platform);\n\n    const { exp } = getConfig(this.projectRoot);\n    const { appName } = getNameFromConfig(exp);\n    const runtimeVersion = await getRuntimeVersionNullableAsync(this.projectRoot, exp, platform);\n    const sdkVersion = exp.sdkVersion ?? null;\n\n    return {\n      appName: appName ?? 'App',\n      projectVersion:\n        sdkVersion && !runtimeVersion\n          ? { type: 'sdk', version: sdkVersion }\n          : { type: 'runtime', version: runtimeVersion },\n    };\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    res = disableResponseCache(res);\n    res.setHeader('Content-Type', 'text/html');\n\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n\n    const { appName, projectVersion } = await this._getProjectOptionsAsync(platform);\n    debug(\n      `Create loading page. (platform: ${platform}, appName: ${appName}, projectVersion: ${projectVersion.version}, type: ${projectVersion.type})`\n    );\n    const content = await this._getPageAsync({ appName, projectVersion });\n    res.end(content);\n  }\n}\n"], "names": ["InterstitialPageMiddleware", "LoadingEndpoint", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "options", "scheme", "_getPageAsync", "appName", "projectVersion", "templatePath", "resolveFrom", "silent", "path", "resolve", "__dirname", "content", "readFile", "toString", "replace", "type", "version", "_getProjectOptionsAsync", "platform", "assertRuntimePlatform", "exp", "getConfig", "getNameFromConfig", "runtimeVersion", "getRuntimeVersionNullableAsync", "sdkVersion", "handleRequestAsync", "req", "res", "disableResponseCache", "<PERSON><PERSON><PERSON><PERSON>", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "end"], "mappings": ";;;;;;;;;;;IA2BaA,0BAA0B;eAA1BA;;IAFAC,eAAe;eAAfA;;;;yBAzBgC;;;;;;;yBACE;;;;;;;yBACtB;;;;;;;gEACR;;;;;;;gEACO;;;;;;gCAE6B;iCAO9C;;;;;;AAQP,MAAMC,QAAQC,QAAQ,SACpB;AAGK,MAAMF,kBAAkB;AAExB,MAAMD,mCAAmCI,8BAAc;IAC5DC,YACEC,WAAmB,EACnB,AAAUC,UAAqC;QAAEC,QAAQ;IAAK,CAAC,CAC/D;QACA,KAAK,CAACF,aAAa;YAACL;SAAgB,QAF1BM,UAAAA;IAGZ;IAEA,kDAAkD,GAClD,MAAME,cAAc,EAClBC,OAAO,EACPC,cAAc,EAIf,EAAmB;QAClB,MAAMC,eACJ,+DAA+D;QAC/DC,sBAAW,CAACC,MAAM,CAAC,IAAI,CAACR,WAAW,EAAE,0CACrC,uDAAuD;QACvDS,eAAI,CAACC,OAAO,CAACC,WAAW;QAC1B,IAAIC,UAAU,AAAC,CAAA,MAAMC,IAAAA,oBAAQ,EAACP,aAAY,EAAGQ,QAAQ,CAAC;QAEtDF,UAAUA,QAAQG,OAAO,CAAC,qBAAqBX;QAC/CQ,UAAUA,QAAQG,OAAO,CAAC,kBAAkB,IAAI,CAACf,WAAW;QAC5DY,UAAUA,QAAQG,OAAO,CAAC,oBAAoB,IAAI,CAACd,OAAO,CAACC,MAAM,IAAI;QACrEU,UAAUA,QAAQG,OAAO,CACvB,gCACA,GAAGV,eAAeW,IAAI,KAAK,QAAQ,QAAQ,UAAU,QAAQ,CAAC;QAEhEJ,UAAUA,QAAQG,OAAO,CAAC,4BAA4BV,eAAeY,OAAO,IAAI;QAEhF,OAAOL;IACT;IAEA,uDAAuD,GACvD,MAAMM,wBAAwBC,QAAyB,EAGpD;QACDC,IAAAA,sCAAqB,EAACD;QAEtB,MAAM,EAAEE,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAACtB,WAAW;QAC1C,MAAM,EAAEI,OAAO,EAAE,GAAGmB,IAAAA,2BAAiB,EAACF;QACtC,MAAMG,iBAAiB,MAAMC,IAAAA,yCAA8B,EAAC,IAAI,CAACzB,WAAW,EAAEqB,KAAKF;QACnF,MAAMO,aAAaL,IAAIK,UAAU,IAAI;QAErC,OAAO;YACLtB,SAASA,WAAW;YACpBC,gBACEqB,cAAc,CAACF,iBACX;gBAAER,MAAM;gBAAOC,SAASS;YAAW,IACnC;gBAAEV,MAAM;gBAAWC,SAASO;YAAe;QACnD;IACF;IAEA,MAAMG,mBAAmBC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/EA,MAAMC,IAAAA,oCAAoB,EAACD;QAC3BA,IAAIE,SAAS,CAAC,gBAAgB;QAE9B,MAAMZ,WAAWa,IAAAA,oCAAmB,EAACJ,QAAQK,IAAAA,mDAAkC,EAACL;QAChFM,IAAAA,6CAA4B,EAACf;QAC7BC,IAAAA,sCAAqB,EAACD;QAEtB,MAAM,EAAEf,OAAO,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACa,uBAAuB,CAACC;QACvEvB,MACE,CAAC,gCAAgC,EAAEuB,SAAS,WAAW,EAAEf,QAAQ,kBAAkB,EAAEC,eAAeY,OAAO,CAAC,QAAQ,EAAEZ,eAAeW,IAAI,CAAC,CAAC,CAAC;QAE9I,MAAMJ,UAAU,MAAM,IAAI,CAACT,aAAa,CAAC;YAAEC;YAASC;QAAe;QACnEwB,IAAIM,GAAG,CAACvB;IACV;AACF"}