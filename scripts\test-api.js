/**
 * Тестирование новых API endpoints для смарт-контрактов
 */

const axios = require('axios');

const API_BASE = 'http://localhost:4000/api';

async function testContractAPI() {
  console.log('🧪 Тестирование API смарт-контрактов...\n');

  try {
    // Тест 1: Получение информации о несуществующем контракте
    console.log('1️⃣ Тест получения несуществующего контракта...');
    try {
      const response = await axios.get(`${API_BASE}/contracts/device/NONEXISTENT`);
      console.log('❌ Ожидалась ошибка 404');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Корректно возвращена ошибка 404');
      } else {
        console.log('❌ Неожиданная ошибка:', error.message);
      }
    }

    // Тест 2: Попытка деплоя контракта без необходимых параметров
    console.log('\n2️⃣ Тест деплоя контракта с неполными данными...');
    try {
      const response = await axios.post(`${API_BASE}/contracts/deploy-device-contract`, {
        device_id: 'TEST:DEVICE:001'
        // Отсутствуют price_per_minute и streamer_wallet
      });
      console.log('❌ Ожидалась ошибка 400');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Корректно возвращена ошибка 400:', error.response.data.message);
      } else {
        console.log('❌ Неожиданная ошибка:', error.message);
      }
    }

    // Тест 3: Попытка покупки времени с неполными данными
    console.log('\n3️⃣ Тест покупки времени с неполными данными...');
    try {
      const response = await axios.post(`${API_BASE}/contracts/buy-time`, {
        device_id: 'TEST:DEVICE:001'
        // Отсутствуют duration_minutes и buyer_wallet
      });
      console.log('❌ Ожидалась ошибка 400');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Корректно возвращена ошибка 400:', error.response.data.message);
      } else {
        console.log('❌ Неожиданная ошибка:', error.message);
      }
    }

    // Тест 4: Получение статуса несуществующего устройства
    console.log('\n4️⃣ Тест получения статуса несуществующего устройства...');
    try {
      const response = await axios.get(`${API_BASE}/contracts/status/NONEXISTENT`);
      console.log('❌ Ожидалась ошибка 404');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Корректно возвращена ошибка 404');
      } else {
        console.log('❌ Неожиданная ошибка:', error.message);
      }
    }

    console.log('\n✅ Тестирование API контрактов завершено');

  } catch (error) {
    console.error('❌ Критическая ошибка тестирования API:', error.message);
  }
}

async function testTransactionAPI() {
  console.log('\n🧪 Тестирование API транзакций...\n');

  try {
    // Тест 1: Получение истории транзакций несуществующего устройства
    console.log('1️⃣ Тест получения истории несуществующего устройства...');
    const response1 = await axios.get(`${API_BASE}/transactions/device/NONEXISTENT`);
    if (response1.data.success && response1.data.data.transactions.length === 0) {
      console.log('✅ Корректно возвращен пустой список транзакций');
    } else {
      console.log('❌ Неожиданный ответ:', response1.data);
    }

    // Тест 2: Получение активных сессий несуществующего стримера
    console.log('\n2️⃣ Тест получения активных сессий несуществующего стримера...');
    const response2 = await axios.get(`${API_BASE}/transactions/active-sessions/NONEXISTENT`);
    if (response2.data.success && response2.data.data.activeSessions.length === 0) {
      console.log('✅ Корректно возвращен пустой список сессий');
    } else {
      console.log('❌ Неожиданный ответ:', response2.data);
    }

    // Тест 3: Получение статистики заработка несуществующего стримера
    console.log('\n3️⃣ Тест получения статистики несуществующего стримера...');
    const response3 = await axios.get(`${API_BASE}/transactions/earnings/NONEXISTENT`);
    if (response3.data.success) {
      console.log('✅ Корректно возвращена пустая статистика');
    } else {
      console.log('❌ Неожиданный ответ:', response3.data);
    }

    // Тест 4: Получение статистики мониторинга
    console.log('\n4️⃣ Тест получения статистики мониторинга...');
    const response4 = await axios.get(`${API_BASE}/transactions/monitoring/stats`);
    if (response4.data.success) {
      console.log('✅ Статистика мониторинга получена:', Object.keys(response4.data.data.monitoringStats).length, 'статусов');
    } else {
      console.log('❌ Ошибка получения статистики:', response4.data);
    }

    // Тест 5: Получение статистики платформы
    console.log('\n5️⃣ Тест получения статистики платформы...');
    const response5 = await axios.get(`${API_BASE}/transactions/platform/stats`);
    if (response5.data.success) {
      console.log('✅ Статистика платформы получена');
      console.log('  - Устройств:', response5.data.data.totalStats.total_devices || 0);
      console.log('  - Стримеров:', response5.data.data.totalStats.total_streamers || 0);
      console.log('  - Транзакций:', response5.data.data.totalStats.total_transactions || 0);
    } else {
      console.log('❌ Ошибка получения статистики:', response5.data);
    }

    console.log('\n✅ Тестирование API транзакций завершено');

  } catch (error) {
    console.error('❌ Критическая ошибка тестирования API:', error.message);
  }
}

async function testExistingAPI() {
  console.log('\n🧪 Тестирование существующих API endpoints...\n');

  try {
    // Тест 1: Получение устройств тестового стримера
    console.log('1️⃣ Тест получения устройств тестового стримера...');
    const response1 = await axios.get(`${API_BASE.replace('/api', '')}/devices-by-nickname/test_streamer`);
    if (response1.data.success) {
      console.log('✅ Устройства получены:', response1.data.devices.length, 'устройств');
    } else {
      console.log('❌ Ошибка получения устройств:', response1.data.message);
    }

    // Тест 2: Получение настроек режима управления
    console.log('\n2️⃣ Тест получения настроек режима управления...');
    const response2 = await axios.get(`${API_BASE.replace('/api', '')}/control-mode-settings/EQTestStreamer123`);
    if (response2.data.success) {
      console.log('✅ Настройки получены:', response2.data.settings.mode);
    } else {
      console.log('❌ Ошибка получения настроек:', response2.data.message);
    }

    // Тест 3: Получение информации о стриме
    console.log('\n3️⃣ Тест получения информации о стриме...');
    const response3 = await axios.get(`${API_BASE.replace('/api', '')}/api/stream/test_streamer`);
    if (response3.data.success) {
      console.log('✅ Информация о стриме получена:', response3.data.stream.stream_platform);
    } else {
      console.log('❌ Ошибка получения информации о стриме:', response3.data.message);
    }

    console.log('\n✅ Тестирование существующих API завершено');

  } catch (error) {
    console.error('❌ Критическая ошибка тестирования API:', error.message);
  }
}

async function runAllAPITests() {
  console.log('🚀 Запуск полного тестирования API\n');

  await testContractAPI();
  await testTransactionAPI();
  await testExistingAPI();

  console.log('\n🎉 Все API тесты завершены!');
}

// Запуск тестирования
runAllAPITests().catch(error => {
  console.error('💥 Критическая ошибка тестирования API:', error);
  process.exit(1);
});
