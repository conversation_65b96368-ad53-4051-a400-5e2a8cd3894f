/**
 * Скрипт для выполнения миграции базы данных
 * Создает новые таблицы для работы со смарт-контрактами
 */

require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Начинаем миграцию базы данных...');
    
    // Читаем SQL файл
    const sqlPath = path.join(__dirname, '..', 'sql', 'add_smart_contract_tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // Выполняем миграцию
    await client.query(sqlContent);
    
    console.log('✅ Миграция успешно выполнена!');
    
    // Проверяем созданные таблицы
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'device_contracts', 
        'control_transactions', 
        'active_control_sessions', 
        'streamer_earnings', 
        'contract_events'
      )
      ORDER BY table_name
    `);
    
    console.log('📋 Созданные таблицы:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // Проверяем обновленную таблицу control_settings
    const settingsColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'control_settings' 
      AND column_name IN (
        'contract_address', 
        'auto_deploy_contract', 
        'min_control_duration', 
        'max_control_duration', 
        'contract_fee_percentage'
      )
      ORDER BY column_name
    `);
    
    if (settingsColumns.rows.length > 0) {
      console.log('🔧 Обновленные поля в control_settings:');
      settingsColumns.rows.forEach(row => {
        console.log(`  - ${row.column_name} (${row.data_type})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Ошибка миграции:', error.message);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Запуск миграции
runMigration().catch(error => {
  console.error('❌ Критическая ошибка:', error);
  process.exit(1);
});
