const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
});

async function createTestStreamer() {
  try {
    console.log('🔍 Проверка существования стримера TestNet...');

    // Проверяем, существует ли стример
    const existing = await pool.query('SELECT * FROM streamers WHERE nickname = $1', ['TestNet']);

    if (existing.rows.length === 0) {
      // Создаем тестового стримера
      await pool.query(`
        INSERT INTO streamers (nickname, wallet_address, created_at)
        VALUES ($1, $2, NOW())
      `, ['TestNet', 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t']);

      console.log('✅ Тестовый стример TestNet создан');
    } else {
      console.log('✅ Стример TestNet уже существует');
    }

    // Проверяем результат
    const result = await pool.query('SELECT * FROM streamers WHERE nickname = $1', ['TestNet']);
    console.log('📋 Данные стримера:', result.rows[0]);

    await pool.end();
    console.log('🎉 Готово!');
  } catch (error) {
    console.error('❌ Ошибка:', error);
    process.exit(1);
  }
}

createTestStreamer();
