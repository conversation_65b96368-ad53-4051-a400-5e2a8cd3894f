{"version": 3, "sources": ["../../../../src/start/platforms/ExpoGoInstaller.ts"], "sourcesContent": ["import semver from 'semver';\n\nimport type { <PERSON><PERSON><PERSON>anager } from './DeviceManager';\nimport { getVersionsAsync } from '../../api/getVersions';\nimport * as Log from '../../log';\nimport { downloadExpoGoAsync } from '../../utils/downloadExpoGoAsync';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport { logNewSection } from '../../utils/ora';\nimport { confirmAsync } from '../../utils/prompts';\n\nconst debug = require('debug')('expo:utils:ExpoGoInstaller') as typeof console.log;\n\n/** Given a platform, appId, and sdkVersion, this module will ensure that Expo Go is up-to-date on the provided device. */\nexport class ExpoGoInstaller<IDevice> {\n  // Keep a list of [platform-deviceId] so we can prevent asking multiple times if a user wants to upgrade.\n  // This can prevent annoying interactions when they don't want to upgrade for whatever reason.\n  static cache: Record<string, boolean> = {};\n\n  constructor(\n    private platform: 'ios' | 'android',\n    // Ultimately this should be inlined since we know the platform.\n    private appId: string,\n    private sdkVersion: string\n  ) {}\n\n  /** Returns true if the installed app matching the previously provided `appId` is outdated. */\n  isInstalledClientVersionMismatched(\n    installedVersion: string | null,\n    expectedExpoGoVersion: string | null\n  ): boolean {\n    if (!installedVersion) {\n      return true;\n    }\n\n    debug(\n      `Expected Expo Go version: ${expectedExpoGoVersion}, installed version: ${installedVersion}`\n    );\n    return expectedExpoGoVersion ? !semver.eq(installedVersion, expectedExpoGoVersion) : true;\n  }\n\n  /** Returns the expected version of Expo Go given the project SDK Version. Exposed for testing. */\n  async getExpectedExpoGoClientVersionAsync(): Promise<string | null> {\n    const versions = await getVersionsAsync();\n    // Like `sdkVersions['44.0.0']['androidClientVersion'] = '1.0.0'`\n    const specificVersion =\n      versions?.sdkVersions?.[this.sdkVersion]?.[`${this.platform}ClientVersion`];\n    const latestVersion = versions[`${this.platform}Version`];\n    return specificVersion ?? latestVersion ?? null;\n  }\n\n  /** Returns a boolean indicating if Expo Go should be installed. Returns `true` if the app was uninstalled. */\n  async promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync(\n    deviceManager: DeviceManager<IDevice>,\n    { containerPath }: { containerPath?: string } = {}\n  ): Promise<boolean> {\n    const cacheId = `${this.platform}-${deviceManager.identifier}`;\n\n    if (ExpoGoInstaller.cache[cacheId]) {\n      debug('skipping subsequent upgrade check');\n      return false;\n    }\n    ExpoGoInstaller.cache[cacheId] = true;\n\n    const [installedExpoGoVersion, expectedExpoGoVersion] = await Promise.all([\n      deviceManager.getAppVersionAsync(this.appId, {\n        containerPath,\n      }),\n      this.getExpectedExpoGoClientVersionAsync(),\n    ]);\n\n    if (this.isInstalledClientVersionMismatched(installedExpoGoVersion, expectedExpoGoVersion)) {\n      if (this.sdkVersion === 'UNVERSIONED') {\n        // This should only happen in the expo/expo repo, e.g. `apps/test-suite`\n        Log.log(\n          `Skipping Expo Go upgrade check for UNVERSIONED project. Manually ensure the Expo Go app is built from source.`\n        );\n        return false;\n      }\n\n      // Only prompt once per device, per run.\n      const confirm = await confirmAsync({\n        initial: true,\n        message: `Expo Go ${expectedExpoGoVersion} is recommended for SDK ${this.sdkVersion} (${\n          deviceManager.name\n        } is using ${installedExpoGoVersion}). ${learnMore(\n          'https://docs.expo.dev/get-started/expo-go/#sdk-versions'\n        )}. Install the recommended Expo Go version?`,\n      });\n\n      if (confirm) {\n        // Don't need to uninstall to update on iOS.\n        if (this.platform !== 'ios') {\n          Log.log(`Uninstalling Expo Go from ${this.platform} device ${deviceManager.name}.`);\n          await deviceManager.uninstallAppAsync(this.appId);\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /** Check if a given device has Expo Go installed, if not then download and install it. */\n  async ensureAsync(deviceManager: DeviceManager<IDevice>): Promise<boolean> {\n    const isExpoGoInstalledAndIfSoContainerPathForIOS =\n      await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(this.appId);\n    let shouldInstall = !isExpoGoInstalledAndIfSoContainerPathForIOS;\n    if (env.EXPO_OFFLINE) {\n      if (isExpoGoInstalledAndIfSoContainerPathForIOS) {\n        Log.warn(`Skipping Expo Go version validation in offline mode`);\n        return false;\n      }\n      throw new CommandError(\n        'NO_EXPO_GO',\n        `Expo Go is not installed on device \"${deviceManager.name}\", while running in offline mode. Manually install Expo Go or run without --offline flag (or EXPO_OFFLINE environment variable).`\n      );\n    }\n\n    if (isExpoGoInstalledAndIfSoContainerPathForIOS) {\n      shouldInstall =\n        await this.promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync(\n          deviceManager,\n          {\n            // iOS optimization to prevent duplicate calls to `getContainerPathAsync`.\n            containerPath:\n              typeof isExpoGoInstalledAndIfSoContainerPathForIOS === 'string'\n                ? isExpoGoInstalledAndIfSoContainerPathForIOS\n                : undefined,\n          }\n        );\n    }\n\n    if (shouldInstall) {\n      // Download the Expo Go app from the Expo servers.\n      const binaryPath = await downloadExpoGoAsync(this.platform, { sdkVersion: this.sdkVersion });\n      // Install the app on the device.\n      const ora = logNewSection(`Installing Expo Go on ${deviceManager.name}`);\n      try {\n        await deviceManager.installAppAsync(binaryPath);\n      } finally {\n        ora.stop();\n      }\n      return true;\n    }\n    return false;\n  }\n}\n"], "names": ["ExpoGoInstaller", "debug", "require", "cache", "constructor", "platform", "appId", "sdkVersion", "isInstalledClientVersionMismatched", "installedVersion", "expectedExpoGoVersion", "semver", "eq", "getExpectedExpoGoClientVersionAsync", "versions", "getVersionsAsync", "specificVersion", "sdkVersions", "latestVersion", "promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync", "deviceManager", "containerPath", "cacheId", "identifier", "installedExpoGoVersion", "Promise", "all", "getAppVersionAsync", "Log", "log", "confirm", "<PERSON><PERSON><PERSON>", "initial", "message", "name", "learnMore", "uninstallAppAsync", "ensureAsync", "isExpoGoInstalledAndIfSoContainerPathForIOS", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "shouldInstall", "env", "EXPO_OFFLINE", "warn", "CommandError", "undefined", "binaryPath", "downloadExpoGoAsync", "ora", "logNewSection", "installAppAsync", "stop"], "mappings": ";;;;+BAeaA;;;eAAAA;;;;gEAfM;;;;;;6BAGc;6DACZ;qCACe;qBAChB;wBACS;sBACH;qBACI;yBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMF;gBACX,yGAAyG;IACzG,8FAA8F;SACvFG,QAAiC,CAAC;IAEzCC,YACE,AAAQC,QAA2B,EACnC,gEAAgE;IACxDC,KAAa,EACrB,AAAQC,UAAkB,CAC1B;aAJQF,WAAAA;aAEAC,QAAAA;aACAC,aAAAA;IACP;IAEH,4FAA4F,GAC5FC,mCACEC,gBAA+B,EAC/BC,qBAAoC,EAC3B;QACT,IAAI,CAACD,kBAAkB;YACrB,OAAO;QACT;QAEAR,MACE,CAAC,0BAA0B,EAAES,sBAAsB,qBAAqB,EAAED,kBAAkB;QAE9F,OAAOC,wBAAwB,CAACC,iBAAM,CAACC,EAAE,CAACH,kBAAkBC,yBAAyB;IACvF;IAEA,gGAAgG,GAChG,MAAMG,sCAA8D;YAIhEC,uCAAAA;QAHF,MAAMA,WAAW,MAAMC,IAAAA,6BAAgB;QACvC,iEAAiE;QACjE,MAAMC,kBACJF,6BAAAA,wBAAAA,SAAUG,WAAW,sBAArBH,wCAAAA,qBAAuB,CAAC,IAAI,CAACP,UAAU,CAAC,qBAAxCO,qCAA0C,CAAC,GAAG,IAAI,CAACT,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC7E,MAAMa,gBAAgBJ,QAAQ,CAAC,GAAG,IAAI,CAACT,QAAQ,CAAC,OAAO,CAAC,CAAC;QACzD,OAAOW,mBAAmBE,iBAAiB;IAC7C;IAEA,4GAA4G,GAC5G,MAAMC,sFACJC,aAAqC,EACrC,EAAEC,aAAa,EAA8B,GAAG,CAAC,CAAC,EAChC;QAClB,MAAMC,UAAU,GAAG,IAAI,CAACjB,QAAQ,CAAC,CAAC,EAAEe,cAAcG,UAAU,EAAE;QAE9D,IAAIvB,gBAAgBG,KAAK,CAACmB,QAAQ,EAAE;YAClCrB,MAAM;YACN,OAAO;QACT;QACAD,gBAAgBG,KAAK,CAACmB,QAAQ,GAAG;QAEjC,MAAM,CAACE,wBAAwBd,sBAAsB,GAAG,MAAMe,QAAQC,GAAG,CAAC;YACxEN,cAAcO,kBAAkB,CAAC,IAAI,CAACrB,KAAK,EAAE;gBAC3Ce;YACF;YACA,IAAI,CAACR,mCAAmC;SACzC;QAED,IAAI,IAAI,CAACL,kCAAkC,CAACgB,wBAAwBd,wBAAwB;YAC1F,IAAI,IAAI,CAACH,UAAU,KAAK,eAAe;gBACrC,wEAAwE;gBACxEqB,KAAIC,GAAG,CACL,CAAC,6GAA6G,CAAC;gBAEjH,OAAO;YACT;YAEA,wCAAwC;YACxC,MAAMC,UAAU,MAAMC,IAAAA,qBAAY,EAAC;gBACjCC,SAAS;gBACTC,SAAS,CAAC,QAAQ,EAAEvB,sBAAsB,wBAAwB,EAAE,IAAI,CAACH,UAAU,CAAC,EAAE,EACpFa,cAAcc,IAAI,CACnB,UAAU,EAAEV,uBAAuB,GAAG,EAAEW,IAAAA,eAAS,EAChD,2DACA,0CAA0C,CAAC;YAC/C;YAEA,IAAIL,SAAS;gBACX,4CAA4C;gBAC5C,IAAI,IAAI,CAACzB,QAAQ,KAAK,OAAO;oBAC3BuB,KAAIC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAACxB,QAAQ,CAAC,QAAQ,EAAEe,cAAcc,IAAI,CAAC,CAAC,CAAC;oBAClF,MAAMd,cAAcgB,iBAAiB,CAAC,IAAI,CAAC9B,KAAK;gBAClD;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,wFAAwF,GACxF,MAAM+B,YAAYjB,aAAqC,EAAoB;QACzE,MAAMkB,8CACJ,MAAMlB,cAAcmB,mDAAmD,CAAC,IAAI,CAACjC,KAAK;QACpF,IAAIkC,gBAAgB,CAACF;QACrB,IAAIG,QAAG,CAACC,YAAY,EAAE;YACpB,IAAIJ,6CAA6C;gBAC/CV,KAAIe,IAAI,CAAC,CAAC,mDAAmD,CAAC;gBAC9D,OAAO;YACT;YACA,MAAM,IAAIC,oBAAY,CACpB,cACA,CAAC,oCAAoC,EAAExB,cAAcc,IAAI,CAAC,gIAAgI,CAAC;QAE/L;QAEA,IAAII,6CAA6C;YAC/CE,gBACE,MAAM,IAAI,CAACrB,qFAAqF,CAC9FC,eACA;gBACE,0EAA0E;gBAC1EC,eACE,OAAOiB,gDAAgD,WACnDA,8CACAO;YACR;QAEN;QAEA,IAAIL,eAAe;YACjB,kDAAkD;YAClD,MAAMM,aAAa,MAAMC,IAAAA,wCAAmB,EAAC,IAAI,CAAC1C,QAAQ,EAAE;gBAAEE,YAAY,IAAI,CAACA,UAAU;YAAC;YAC1F,iCAAiC;YACjC,MAAMyC,MAAMC,IAAAA,kBAAa,EAAC,CAAC,sBAAsB,EAAE7B,cAAcc,IAAI,EAAE;YACvE,IAAI;gBACF,MAAMd,cAAc8B,eAAe,CAACJ;YACtC,SAAU;gBACRE,IAAIG,IAAI;YACV;YACA,OAAO;QACT;QACA,OAAO;IACT;AACF"}