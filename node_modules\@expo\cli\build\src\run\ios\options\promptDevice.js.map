{"version": 3, "sources": ["../../../../../src/run/ios/options/promptDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as SimControl from '../../../start/platforms/ios/simctl';\nimport prompt from '../../../utils/prompts';\nimport { ConnectedDevice } from '../appleDevice/AppleDevice';\n\ntype AnyDevice = SimControl.Device | ConnectedDevice;\n\nfunction isConnectedDevice(item: AnyDevice): item is ConnectedDevice {\n  return 'deviceType' in item;\n}\n\nfunction isSimControlDevice(item: AnyDevice): item is SimControl.Device {\n  return 'state' in item;\n}\n\n/** Format a device for the prompt list. Exposed for testing. */\nexport function formatDeviceChoice(item: AnyDevice): { title: string; value: string } {\n  const isConnected = isConnectedDevice(item) && item.deviceType === 'device';\n  const isActive = isSimControlDevice(item) && item.state === 'Booted';\n  const symbol =\n    item.osType === 'macOS'\n      ? '🖥️  '\n      : isConnected\n        ? item.connectionType === 'Network'\n          ? '🌐 '\n          : '🔌 '\n        : '';\n  const format = isActive ? chalk.bold : (text: string) => text;\n  return {\n    title: `${symbol}${format(item.name)}${\n      item.osVersion ? chalk.dim(` (${item.osVersion})`) : ''\n    }`,\n    value: item.udid,\n  };\n}\n\n/** Prompt to select a device from a searchable list of devices. */\nexport async function promptDeviceAsync(devices: AnyDevice[]): Promise<AnyDevice> {\n  // --device with no props after\n  const { value } = await prompt({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a device',\n    choices: devices.map((item) => formatDeviceChoice(item)),\n    suggest: (input: any, choices: any) => {\n      const regex = new RegExp(input, 'i');\n      return choices.filter((choice: any) => regex.test(choice.title));\n    },\n  });\n  return devices.find((device) => device.udid === value)!;\n}\n"], "names": ["formatDeviceChoice", "promptDeviceAsync", "isConnectedDevice", "item", "isSimControlDevice", "isConnected", "deviceType", "isActive", "state", "symbol", "osType", "connectionType", "format", "chalk", "bold", "text", "title", "name", "osVersion", "dim", "value", "udid", "devices", "prompt", "type", "limit", "message", "choices", "map", "suggest", "input", "regex", "RegExp", "filter", "choice", "test", "find", "device"], "mappings": ";;;;;;;;;;;IAiBgBA,kBAAkB;eAAlBA;;IAqBMC,iBAAiB;eAAjBA;;;;gEAtCJ;;;;;;gEAGC;;;;;;AAKnB,SAASC,kBAAkBC,IAAe;IACxC,OAAO,gBAAgBA;AACzB;AAEA,SAASC,mBAAmBD,IAAe;IACzC,OAAO,WAAWA;AACpB;AAGO,SAASH,mBAAmBG,IAAe;IAChD,MAAME,cAAcH,kBAAkBC,SAASA,KAAKG,UAAU,KAAK;IACnE,MAAMC,WAAWH,mBAAmBD,SAASA,KAAKK,KAAK,KAAK;IAC5D,MAAMC,SACJN,KAAKO,MAAM,KAAK,UACZ,UACAL,cACEF,KAAKQ,cAAc,KAAK,YACtB,QACA,QACF;IACR,MAAMC,SAASL,WAAWM,gBAAK,CAACC,IAAI,GAAG,CAACC,OAAiBA;IACzD,OAAO;QACLC,OAAO,GAAGP,SAASG,OAAOT,KAAKc,IAAI,IACjCd,KAAKe,SAAS,GAAGL,gBAAK,CAACM,GAAG,CAAC,CAAC,EAAE,EAAEhB,KAAKe,SAAS,CAAC,CAAC,CAAC,IAAI,IACrD;QACFE,OAAOjB,KAAKkB,IAAI;IAClB;AACF;AAGO,eAAepB,kBAAkBqB,OAAoB;IAC1D,+BAA+B;IAC/B,MAAM,EAAEF,KAAK,EAAE,GAAG,MAAMG,IAAAA,gBAAM,EAAC;QAC7BC,MAAM;QACNP,MAAM;QACNQ,OAAO;QACPC,SAAS;QACTC,SAASL,QAAQM,GAAG,CAAC,CAACzB,OAASH,mBAAmBG;QAClD0B,SAAS,CAACC,OAAYH;YACpB,MAAMI,QAAQ,IAAIC,OAAOF,OAAO;YAChC,OAAOH,QAAQM,MAAM,CAAC,CAACC,SAAgBH,MAAMI,IAAI,CAACD,OAAOlB,KAAK;QAChE;IACF;IACA,OAAOM,QAAQc,IAAI,CAAC,CAACC,SAAWA,OAAOhB,IAAI,KAAKD;AAClD"}