const { TonClient, WalletContractV4, internal, external, fromNano, toNano, Address, beginCell } = require('@ton/ton');
const { mnemonicToWalletKey } = require('@ton/crypto');
const { compile } = require('@ton/blueprint');
const fs = require('fs');
const path = require('path');

class UniversalContractService {
    constructor(config = {}) {
        this.config = {
            endpoint: config.endpoint || 'https://toncenter.com/api/v2/jsonRPC',
            apiKey: config.apiKey || null,
            workchain: config.workchain || 0,
            platformAddress: config.platformAddress || null,
            platformMnemonic: config.platformMnemonic || null,
            platformFeePercent: config.platformFeePercent || 500
        };

        this.client = null;
        this.platformWallet = null;
        this.contractCode = null;
        this.contractAddress = null; // Адрес единого контракта
    }

    /**
     * Инициализация сервиса
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация UniversalContractService...');

            // Инициализируем TON клиент
            this.client = new TonClient({
                endpoint: this.config.endpoint,
                apiKey: this.config.apiKey
            });

            // Компилируем универсальный контракт
            this.contractCode = await this.compileUniversalContract();
            console.log('✅ Универсальный контракт скомпилирован');

            // Инициализируем кошелек платформы (если есть мнемоника)
            if (this.config.platformMnemonic) {
                await this.initializePlatformWallet();
            }

            // Вычисляем адрес единого контракта
            this.contractAddress = await this.calculateUniversalContractAddress();
            console.log('📍 Адрес универсального контракта:', this.contractAddress.toString());

            console.log('✅ UniversalContractService инициализирован');
        } catch (error) {
            console.error('❌ Ошибка инициализации UniversalContractService:', error);
            throw error;
        }
    }

    /**
     * Компиляция универсального контракта
     */
    async compileUniversalContract() {
        try {
            console.log('🔨 Компиляция универсального контракта...');

            // Компилируем новый универсальный контракт
            const contractCode = await compile('universal_device_control');

            console.log('✅ Универсальный контракт успешно скомпилирован');

            // Сохраняем скомпилированный код
            const compiledPath = path.join(__dirname, '..', 'contracts', 'compiled');
            if (!fs.existsSync(compiledPath)) {
                fs.mkdirSync(compiledPath, { recursive: true });
            }

            fs.writeFileSync(
                path.join(compiledPath, 'universal_device_control.boc'),
                contractCode.toBoc()
            );

            console.log('💾 Код сохранен в contracts/compiled/universal_device_control.boc');

            return contractCode;
        } catch (error) {
            console.error('❌ Ошибка компиляции универсального контракта:', error);
            throw error;
        }
    }

    /**
     * Инициализация кошелька платформы
     */
    async initializePlatformWallet() {
        try {
            const keyPair = await mnemonicToWalletKey(this.config.platformMnemonic.split(' '));
            this.platformWallet = WalletContractV4.create({
                workchain: this.config.workchain,
                publicKey: keyPair.publicKey
            });

            console.log('💼 Кошелек платформы инициализирован:', this.platformWallet.address.toString());
        } catch (error) {
            console.error('❌ Ошибка инициализации кошелька платформы:', error);
            throw error;
        }
    }

    /**
     * Вычисление адреса универсального контракта
     */
    async calculateUniversalContractAddress() {
        try {
            // Создаем начальные данные для универсального контракта
            const initialData = this.createUniversalContractInitialData();

            // Вычисляем адрес контракта
            const stateInit = beginCell()
                .storeBit(0) // split_depth
                .storeBit(0) // special
                .storeBit(1) // code
                .storeRef(this.contractCode)
                .storeBit(1) // data
                .storeRef(initialData)
                .storeBit(0) // library
                .endCell();

            return new Address(this.config.workchain, stateInit.hash());
        } catch (error) {
            console.error('❌ Ошибка вычисления адреса контракта:', error);
            throw error;
        }
    }

    /**
     * Создание начальных данных для универсального контракта
     */
    createUniversalContractInitialData() {
        // Создаем пустые словари для начального состояния
        const emptyDict = beginCell().endCell();

        return beginCell()
            .storeAddress(Address.parse(this.config.platformAddress))  // platform_address
            .storeUint(this.config.platformFeePercent, 16)            // platform_fee_percent
            .storeCoins(0)                                            // total_platform_fees
            .storeCoins(0)                                            // total_volume
            .storeRef(emptyDict)                                      // streamer_balances
            .storeRef(emptyDict)                                      // device_settings
            .storeRef(emptyDict)                                      // active_sessions
            .endCell();
    }

    /**
     * Деплой универсального контракта (выполняется один раз)
     */
    async deployUniversalContract() {
        try {
            if (!this.platformWallet) {
                throw new Error('Кошелек платформы не инициализирован');
            }

            console.log('🚀 Деплой универсального контракта...');

            const initialData = this.createUniversalContractInitialData();

            const stateInit = beginCell()
                .storeBit(0) // split_depth
                .storeBit(0) // special
                .storeBit(1) // code
                .storeRef(this.contractCode)
                .storeBit(1) // data
                .storeRef(initialData)
                .storeBit(0) // library
                .endCell();

            const contract = this.client.open(this.platformWallet);

            // Отправляем транзакцию деплоя
            const seqno = await contract.getSeqno();

            await contract.sendTransfer({
                secretKey: this.platformWallet.secretKey,
                seqno: seqno,
                messages: [internal({
                    to: this.contractAddress,
                    value: toNano('0.1'), // Газ для деплоя
                    init: stateInit,
                    body: beginCell().endCell() // Пустое тело для деплоя
                })]
            });

            console.log('✅ Универсальный контракт задеплоен');
            console.log('📍 Адрес контракта:', this.contractAddress.toString());

            return {
                success: true,
                contractAddress: this.contractAddress.toString(),
                transactionSent: true
            };
        } catch (error) {
            console.error('❌ Ошибка деплоя универсального контракта:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Деплой контракта (если есть кошелек платформы)
     */
    async deployContract(contractData) {
        try {
            if (!this.platformWallet) {
                throw new Error('Кошелек платформы не инициализирован');
            }

            console.log('🚀 Деплой контракта...');

            const contract = this.client.open(this.platformWallet);

            // Отправляем транзакцию деплоя
            const seqno = await contract.getSeqno();

            await contract.sendTransfer({
                secretKey: this.platformWallet.secretKey,
                seqno: seqno,
                messages: [internal({
                    to: contractData.deployMessage.address,
                    value: contractData.deployMessage.amount,
                    init: contractData.deployMessage.stateInit,
                    body: contractData.deployMessage.body
                })]
            });

            console.log('✅ Транзакция деплоя отправлена');
            console.log('📍 Адрес контракта:', contractData.deployMessage.address);

            return {
                success: true,
                contractAddress: contractData.deployMessage.address,
                transactionSent: true
            };
        } catch (error) {
            console.error('❌ Ошибка деплоя контракта:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Проверка существования контракта
     */
    async checkContractExists(contractAddress) {
        try {
            const state = await this.client.getContractState(contractAddress);
            return state.state === 'active';
        } catch (error) {
            console.error('❌ Ошибка проверки контракта:', error);
            return false;
        }
    }

    /**
     * Получение информации о контракте
     */
    async getContractInfo(contractAddress) {
        try {
            const result = await this.client.runMethod(contractAddress, 'get_device_info');
            const stack = result.stack;

            return {
                success: true,
                owner: stack.readAddress().toString(),
                deviceId: stack.readBigNumber().toString(),
                pricePerMinute: fromNano(stack.readBigNumber())
            };
        } catch (error) {
            console.error('❌ Ошибка получения информации о контракте:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Создание сообщения для покупки времени
     */
    createBuyTimeMessage(contractAddress, durationMinutes) {
        const body = beginCell()
            .storeUint(0x1, 32)                    // op::buy_control_time
            .storeUint(Date.now(), 64)             // query_id
            .storeUint(durationMinutes, 16)        // duration_minutes
            .endCell();

        return {
            to: contractAddress,
            value: toNano('0.1'), // Будет пересчитано на основе цены
            body: body
        };
    }

    /**
     * Создание мемо для устройства
     */
    createDeviceMemo(streamerAddress, deviceId) {
        // Формат memo: "streamer_address:device_id"
        return `${streamerAddress}:${deviceId}`;
    }

    /**
     * Создание сообщения для покупки времени
     */
    createBuyTimeMessage(streamerAddress, deviceId, durationMinutes, amount) {
        const memo = this.createDeviceMemo(streamerAddress, deviceId);

        return {
            to: this.contractAddress.toString(),
            value: toNano(amount.toString()),
            body: beginCell()
                .storeUint(0, 32) // op = 0 для простого перевода с memo
                .storeUint(Date.now(), 64) // query_id
                .storeStringTail(memo) // memo строка
                .endCell()
        };
    }

    /**
     * Создание сообщения для вывода средств стримера
     */
    createWithdrawEarningsMessage(streamerAddress) {
        return {
            to: this.contractAddress.toString(),
            value: toNano('0.05'), // Газ для операции
            body: beginCell()
                .storeUint(2, 32) // op::withdraw_streamer_earnings
                .storeUint(Date.now(), 64) // query_id
                .endCell()
        };
    }

    /**
     * Создание сообщения для вывода комиссии платформы
     */
    createWithdrawPlatformFeeMessage() {
        return {
            to: this.contractAddress.toString(),
            value: toNano('0.05'), // Газ для операции
            body: beginCell()
                .storeUint(3, 32) // op::withdraw_platform_fee
                .storeUint(Date.now(), 64) // query_id
                .endCell()
        };
    }

    /**
     * Создание сообщения для обновления настроек устройства
     */
    createUpdateDeviceSettingsMessage(streamerAddress, deviceId, newPrice) {
        const memo = this.createDeviceMemo(streamerAddress, deviceId);

        return {
            to: this.contractAddress.toString(),
            value: toNano('0.05'), // Газ для операции
            body: beginCell()
                .storeUint(5, 32) // op::update_device_settings
                .storeUint(Date.now(), 64) // query_id
                .storeRef(beginCell().storeStringTail(memo).endCell()) // device memo
                .storeCoins(toNano(newPrice.toString())) // new price
                .endCell()
        };
    }

    /**
     * Получение баланса стримера
     */
    async getStreamerBalance(streamerAddress) {
        try {
            const result = await this.client.runMethod(
                this.contractAddress,
                'get_streamer_balance',
                [{ type: 'slice', cell: beginCell().storeAddress(Address.parse(streamerAddress)).endCell() }]
            );

            const balance = result.stack.readBigNumber();
            return fromNano(balance);
        } catch (error) {
            console.error('❌ Ошибка получения баланса стримера:', error);
            return '0';
        }
    }

    /**
     * Получение статистики платформы
     */
    async getPlatformStats() {
        try {
            const result = await this.client.runMethod(
                this.contractAddress,
                'get_platform_stats'
            );

            const stack = result.stack;
            const feePercent = stack.readNumber();
            const totalFees = stack.readBigNumber();
            const totalVolume = stack.readBigNumber();

            return {
                feePercent: feePercent / 100, // Конвертируем из базисных пунктов в проценты
                totalFees: fromNano(totalFees),
                totalVolume: fromNano(totalVolume)
            };
        } catch (error) {
            console.error('❌ Ошибка получения статистики платформы:', error);
            return {
                feePercent: 0,
                totalFees: '0',
                totalVolume: '0'
            };
        }
    }

    /**
     * Получение активной сессии устройства
     */
    async getActiveSession(streamerAddress, deviceId) {
        try {
            const memo = this.createDeviceMemo(streamerAddress, deviceId);

            const result = await this.client.runMethod(
                this.contractAddress,
                'get_active_session',
                [{ type: 'slice', cell: beginCell().storeStringTail(memo).endCell() }]
            );

            const stack = result.stack;
            const endTime = stack.readNumber();
            const controller = stack.readAddress();

            return {
                endTime,
                controller: controller ? controller.toString() : null,
                isActive: endTime > Math.floor(Date.now() / 1000)
            };
        } catch (error) {
            console.error('❌ Ошибка получения активной сессии:', error);
            return {
                endTime: 0,
                controller: null,
                isActive: false
            };
        }
    }

    /**
     * Получение цены устройства
     */
    async getDevicePrice(streamerAddress, deviceId) {
        try {
            const memo = this.createDeviceMemo(streamerAddress, deviceId);

            const result = await this.client.runMethod(
                this.contractAddress,
                'get_device_price',
                [{ type: 'slice', cell: beginCell().storeStringTail(memo).endCell() }]
            );

            const price = result.stack.readBigNumber();
            return fromNano(price);
        } catch (error) {
            console.error('❌ Ошибка получения цены устройства:', error);
            return '1.0'; // Цена по умолчанию
        }
    }

    /**
     * Проверка существования контракта
     */
    async checkContractExists() {
        try {
            const state = await this.client.getContractState(this.contractAddress);
            return state.state === 'active';
        } catch (error) {
            console.error('❌ Ошибка проверки контракта:', error);
            return false;
        }
    }

    /**
     * Получение адреса универсального контракта
     */
    getContractAddress() {
        return this.contractAddress ? this.contractAddress.toString() : null;
    }
}

module.exports = UniversalContractService;
