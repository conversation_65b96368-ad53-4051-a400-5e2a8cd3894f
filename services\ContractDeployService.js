const { TonClient, WalletContractV4, internal, external, from<PERSON>ano, toNano } = require('@ton/ton');
const { mnemonicToWalletKey } = require('@ton/crypto');
const { compileContract, createInitialData, calculateContractAddress, createDeployMessage, hashDeviceId } = require('../contracts/compile');

class ContractDeployService {
    constructor(config = {}) {
        this.config = {
            endpoint: config.endpoint || 'https://toncenter.com/api/v2/jsonRPC',
            apiKey: config.apiKey || null,
            workchain: config.workchain || 0,
            platformAddress: config.platformAddress || null,
            platformMnemonic: config.platformMnemonic || null
        };
        
        this.client = null;
        this.platformWallet = null;
        this.contractCode = null;
    }

    /**
     * Инициализация сервиса
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация ContractDeployService...');
            
            // Инициализируем TON клиент
            this.client = new TonClient({
                endpoint: this.config.endpoint,
                apiKey: this.config.apiKey
            });
            
            // Компилируем контракт
            this.contractCode = await compileContract();
            console.log('✅ Контракт скомпилирован');
            
            // Инициализируем кошелек платформы (если есть мнемоника)
            if (this.config.platformMnemonic) {
                await this.initializePlatformWallet();
            }
            
            console.log('✅ ContractDeployService инициализирован');
        } catch (error) {
            console.error('❌ Ошибка инициализации ContractDeployService:', error);
            throw error;
        }
    }

    /**
     * Инициализация кошелька платформы
     */
    async initializePlatformWallet() {
        try {
            const keyPair = await mnemonicToWalletKey(this.config.platformMnemonic.split(' '));
            this.platformWallet = WalletContractV4.create({
                workchain: this.config.workchain,
                publicKey: keyPair.publicKey
            });
            
            console.log('💼 Кошелек платформы инициализирован:', this.platformWallet.address.toString());
        } catch (error) {
            console.error('❌ Ошибка инициализации кошелька платформы:', error);
            throw error;
        }
    }

    /**
     * Создание контракта для устройства
     */
    async createDeviceContract(params) {
        try {
            const {
                deviceId,           // MAC адрес устройства
                ownerAddress,       // Адрес стримера
                pricePerMinute,     // Цена в TON за минуту
                platformFeePercent = 500 // 5% комиссия платформы
            } = params;

            console.log(`📝 Создание контракта для устройства ${deviceId}...`);

            // Хешируем device_id
            const hashedDeviceId = hashDeviceId(deviceId);

            // Создаем начальные данные контракта
            const initialData = createInitialData({
                ownerAddress,
                deviceId: hashedDeviceId,
                pricePerMinute,
                platformFeePercent,
                platformAddress: this.config.platformAddress || ownerAddress
            });

            // Вычисляем адрес контракта
            const contractAddress = calculateContractAddress(this.contractCode, initialData);

            // Создаем сообщение для деплоя
            const deployMessage = createDeployMessage(this.contractCode, initialData);

            console.log('✅ Контракт подготовлен:');
            console.log('📍 Адрес:', contractAddress.toString());
            console.log('💰 Цена за минуту:', pricePerMinute, 'TON');
            console.log('💼 Владелец:', ownerAddress);

            return {
                success: true,
                contractAddress: contractAddress.toString(),
                deployMessage,
                contractData: {
                    deviceId,
                    hashedDeviceId: hashedDeviceId.toString(),
                    ownerAddress,
                    pricePerMinute,
                    platformFeePercent
                }
            };
        } catch (error) {
            console.error('❌ Ошибка создания контракта:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Деплой контракта (если есть кошелек платформы)
     */
    async deployContract(contractData) {
        try {
            if (!this.platformWallet) {
                throw new Error('Кошелек платформы не инициализирован');
            }

            console.log('🚀 Деплой контракта...');

            const contract = this.client.open(this.platformWallet);
            
            // Отправляем транзакцию деплоя
            const seqno = await contract.getSeqno();
            
            await contract.sendTransfer({
                secretKey: this.platformWallet.secretKey,
                seqno: seqno,
                messages: [internal({
                    to: contractData.deployMessage.address,
                    value: contractData.deployMessage.amount,
                    init: contractData.deployMessage.stateInit,
                    body: contractData.deployMessage.body
                })]
            });

            console.log('✅ Транзакция деплоя отправлена');
            console.log('📍 Адрес контракта:', contractData.deployMessage.address);

            return {
                success: true,
                contractAddress: contractData.deployMessage.address,
                transactionSent: true
            };
        } catch (error) {
            console.error('❌ Ошибка деплоя контракта:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Проверка существования контракта
     */
    async checkContractExists(contractAddress) {
        try {
            const state = await this.client.getContractState(contractAddress);
            return state.state === 'active';
        } catch (error) {
            console.error('❌ Ошибка проверки контракта:', error);
            return false;
        }
    }

    /**
     * Получение информации о контракте
     */
    async getContractInfo(contractAddress) {
        try {
            const result = await this.client.runMethod(contractAddress, 'get_device_info');
            const stack = result.stack;
            
            return {
                success: true,
                owner: stack.readAddress().toString(),
                deviceId: stack.readBigNumber().toString(),
                pricePerMinute: fromNano(stack.readBigNumber())
            };
        } catch (error) {
            console.error('❌ Ошибка получения информации о контракте:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    /**
     * Создание сообщения для покупки времени
     */
    createBuyTimeMessage(contractAddress, durationMinutes) {
        const body = beginCell()
            .storeUint(0x1, 32)                    // op::buy_control_time
            .storeUint(Date.now(), 64)             // query_id
            .storeUint(durationMinutes, 16)        // duration_minutes
            .endCell();

        return {
            to: contractAddress,
            value: toNano('0.1'), // Будет пересчитано на основе цены
            body: body
        };
    }

    /**
     * Хеширование device_id (для совместимости)
     */
    hashDeviceId(deviceId) {
        return hashDeviceId(deviceId);
    }
}

module.exports = ContractDeployService;
