;; Device Control Smart Contract
;; Контракт для управления временем доступа к IoT устройствам

#include "imports/stdlib.fc";

;; Storage structure:
;; owner_address - адрес владельца устройства (стримера)
;; device_id - ID устройства (хеш от MAC адреса)
;; price_per_minute - цена за минуту управления в nanoTON
;; current_controller - адрес текущего контроллера
;; control_end_time - время окончания управления (unix timestamp)
;; platform_fee_percent - процент комиссии платформы (в базисных пунктах, 500 = 5%)
;; platform_address - адрес платформы для получения комиссии
;; accumulated_earnings - накопленные средства стримера (готовые к выводу)
;; accumulated_platform_fee - накопленная комиссия платформы
;; is_active - активен ли контракт

(slice, int, int, slice, int, int, slice, int, int, int) load_data() inline {
    slice ds = get_data().begin_parse();
    return (
        ds~load_msg_addr(),     ;; owner_address
        ds~load_uint(256),      ;; device_id
        ds~load_coins(),        ;; price_per_minute
        ds~load_msg_addr(),     ;; current_controller
        ds~load_uint(32),       ;; control_end_time
        ds~load_uint(16),       ;; platform_fee_percent
        ds~load_msg_addr(),     ;; platform_address
        ds~load_coins(),        ;; accumulated_earnings
        ds~load_coins(),        ;; accumulated_platform_fee
        ds~load_uint(1)         ;; is_active
    );
}

() save_data(slice owner_address, int device_id, int price_per_minute,
             slice current_controller, int control_end_time, int platform_fee_percent,
             slice platform_address, int accumulated_earnings, int accumulated_platform_fee, int is_active) impure inline {
    set_data(begin_cell()
        .store_slice(owner_address)
        .store_uint(device_id, 256)
        .store_coins(price_per_minute)
        .store_slice(current_controller)
        .store_uint(control_end_time, 32)
        .store_uint(platform_fee_percent, 16)
        .store_slice(platform_address)
        .store_coins(accumulated_earnings)
        .store_coins(accumulated_platform_fee)
        .store_uint(is_active, 1)
        .end_cell());
}

;; Операции
const int op::buy_control_time = 0x1;
const int op::extend_control_time = 0x2;
const int op::set_price = 0x3;
const int op::withdraw_earnings = 0x4;
const int op::withdraw_platform_fee = 0x5;
const int op::emergency_stop = 0x6;
const int op::get_control_status = 0x7;

;; Ошибки
const int error::unauthorized = 401;
const int error::insufficient_payment = 402;
const int error::device_busy = 403;
const int error::invalid_duration = 404;
const int error::contract_inactive = 405;
const int error::control_expired = 406;

() recv_internal(int my_balance, int msg_value, cell in_msg_full, slice in_msg_body) impure {
    if (in_msg_body.slice_empty?()) { ;; ignore empty messages
        return ();
    }

    slice cs = in_msg_full.begin_parse();
    int flags = cs~load_uint(4);

    if (flags & 1) { ;; ignore all bounced messages
        return ();
    }

    slice sender_address = cs~load_msg_addr();

    int op = in_msg_body~load_uint(32);
    int query_id = in_msg_body~load_uint(64);

    var (owner_address, device_id, price_per_minute, current_controller,
         control_end_time, platform_fee_percent, platform_address,
         accumulated_earnings, accumulated_platform_fee, is_active) = load_data();

    ;; Проверка активности контракта
    if (~ is_active) {
        throw(error::contract_inactive);
    }

    ;; Проверка истечения времени управления
    int current_time = now();
    if (control_end_time > 0 & control_end_time < current_time) {
        current_controller = null_addr();
        control_end_time = 0;
    }

    if (op == op::buy_control_time) {
        int duration_minutes = in_msg_body~load_uint(16);

        ;; Проверка минимальной длительности (1 минута)
        if (duration_minutes < 1) {
            throw(error::invalid_duration);
        }

        ;; Проверка максимальной длительности (60 минут)
        if (duration_minutes > 60) {
            throw(error::invalid_duration);
        }

        ;; Расчет стоимости
        int total_cost = price_per_minute * duration_minutes;

        ;; Проверка достаточности платежа (с учетом комиссий газа)
        if (msg_value < total_cost + 50000000) { ;; +0.05 TON на газ
            throw(error::insufficient_payment);
        }

        ;; Проверка, что устройство свободно или управляет тот же пользователь
        if (control_end_time > current_time) {
            if (~ equal_slices(current_controller, sender_address)) {
                throw(error::device_busy);
            }
            ;; Продление времени для того же пользователя
            control_end_time += duration_minutes * 60;
        } else {
            ;; Новая сессия управления
            current_controller = sender_address;
            control_end_time = current_time + duration_minutes * 60;
        }

        ;; Расчет комиссии платформы
        int platform_fee = (total_cost * platform_fee_percent) / 10000;
        int owner_payment = total_cost - platform_fee;

        ;; Накопление средств (не отправляем сразу)
        accumulated_earnings += owner_payment;
        accumulated_platform_fee += platform_fee;

        ;; Сохранение данных
        save_data(owner_address, device_id, price_per_minute, current_controller,
                  control_end_time, platform_fee_percent, platform_address,
                  accumulated_earnings, accumulated_platform_fee, is_active);

        ;; Отправка подтверждения покупателю
        var response = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(sender_address)
            .store_coins(0)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .store_uint(0x80000000 | op::buy_control_time, 32)
            .store_uint(query_id, 64)
            .store_uint(control_end_time, 32)
            .store_uint(duration_minutes, 16)
            .end_cell();
        send_raw_message(response, 64);

        return ();
    }

    if (op == op::set_price) {
        ;; Только владелец может изменять цену
        throw_unless(error::unauthorized, equal_slices(sender_address, owner_address));

        int new_price = in_msg_body~load_coins();
        price_per_minute = new_price;

        save_data(owner_address, device_id, price_per_minute, current_controller,
                  control_end_time, platform_fee_percent, platform_address,
                  accumulated_earnings, accumulated_platform_fee, is_active);

        ;; Отправка подтверждения
        var response = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(sender_address)
            .store_coins(0)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .store_uint(0x80000000 | op::set_price, 32)
            .store_uint(query_id, 64)
            .store_coins(new_price)
            .end_cell();
        send_raw_message(response, 64);

        return ();
    }

    if (op == op::withdraw_earnings) {
        ;; Только владелец может выводить средства
        throw_unless(error::unauthorized, equal_slices(sender_address, owner_address));

        ;; Проверяем, есть ли накопленные средства для вывода
        if (accumulated_earnings > 0) {
            ;; Отправка накопленных средств владельцу
            var msg = begin_cell()
                .store_uint(0x10, 6)
                .store_slice(owner_address)
                .store_coins(accumulated_earnings)
                .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
                .store_uint(0x80000000 | op::withdraw_earnings, 32)
                .store_uint(query_id, 64)
                .store_coins(accumulated_earnings)
                .end_cell();
            send_raw_message(msg, 1);

            ;; Обнуляем накопленные средства
            accumulated_earnings = 0;

            ;; Сохраняем обновленные данные
            save_data(owner_address, device_id, price_per_minute, current_controller,
                      control_end_time, platform_fee_percent, platform_address,
                      accumulated_earnings, accumulated_platform_fee, is_active);
        }

        return ();
    }

    if (op == op::withdraw_platform_fee) {
        ;; Только платформа может выводить комиссию
        throw_unless(error::unauthorized, equal_slices(sender_address, platform_address));

        ;; Проверяем, есть ли накопленная комиссия для вывода
        if (accumulated_platform_fee > 0) {
            ;; Отправка накопленной комиссии платформе
            var msg = begin_cell()
                .store_uint(0x10, 6)
                .store_slice(platform_address)
                .store_coins(accumulated_platform_fee)
                .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
                .store_uint(0x80000000 | op::withdraw_platform_fee, 32)
                .store_uint(query_id, 64)
                .store_coins(accumulated_platform_fee)
                .end_cell();
            send_raw_message(msg, 1);

            ;; Обнуляем накопленную комиссию
            accumulated_platform_fee = 0;

            ;; Сохраняем обновленные данные
            save_data(owner_address, device_id, price_per_minute, current_controller,
                      control_end_time, platform_fee_percent, platform_address,
                      accumulated_earnings, accumulated_platform_fee, is_active);
        }

        return ();
    }

    if (op == op::emergency_stop) {
        ;; Только владелец может остановить контракт
        throw_unless(error::unauthorized, equal_slices(sender_address, owner_address));

        is_active = 0;
        current_controller = null_addr();
        control_end_time = 0;

        save_data(owner_address, device_id, price_per_minute, current_controller,
                  control_end_time, platform_fee_percent, platform_address,
                  accumulated_earnings, accumulated_platform_fee, is_active);

        return ();
    }

    throw(0xffff); ;; неизвестная операция
}

;; Get методы

(int, slice, int, int) get_control_status() method_id {
    var (owner_address, device_id, price_per_minute, current_controller,
         control_end_time, platform_fee_percent, platform_address,
         accumulated_earnings, accumulated_platform_fee, is_active) = load_data();

    int current_time = now();
    int is_controlled = (control_end_time > current_time) ? -1 : 0;
    int time_remaining = (control_end_time > current_time) ? (control_end_time - current_time) : 0;

    return (is_controlled, current_controller, control_end_time, time_remaining);
}

(slice, int, int) get_device_info() method_id {
    var (owner_address, device_id, price_per_minute, current_controller,
         control_end_time, platform_fee_percent, platform_address,
         accumulated_earnings, accumulated_platform_fee, is_active) = load_data();

    return (owner_address, device_id, price_per_minute);
}

(int, int, int, int) get_contract_stats() method_id {
    var (owner_address, device_id, price_per_minute, current_controller,
         control_end_time, platform_fee_percent, platform_address,
         accumulated_earnings, accumulated_platform_fee, is_active) = load_data();

    return (accumulated_earnings, accumulated_platform_fee, platform_fee_percent, is_active);
}

(int, int) get_accumulated_balances() method_id {
    var (owner_address, device_id, price_per_minute, current_controller,
         control_end_time, platform_fee_percent, platform_address,
         accumulated_earnings, accumulated_platform_fee, is_active) = load_data();

    return (accumulated_earnings, accumulated_platform_fee);
}
