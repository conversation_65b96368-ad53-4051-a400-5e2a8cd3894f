{"name": "ton-donations", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "setup-contracts": "node scripts/setup-contracts.js", "compile-contracts": "node contracts/compile.js", "dev": "NODE_ENV=development node server.js", "prod": "NODE_ENV=production node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ton/blueprint": "^0.17.0", "@ton/core": "^0.56.3", "@ton/crypto": "^3.3.0", "@ton/ton": "^14.0.0", "@tonconnect/ui": "^2.0.11", "@tonconnect/ui-react": "^2.0.11", "axios": "^1.7.9", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "node-cron": "^3.0.3", "pg": "^8.13.1", "tonweb": "^0.0.66", "winston": "^3.17.0", "ws": "^8.18.0"}, "description": ""}