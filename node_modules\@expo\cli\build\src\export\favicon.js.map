{"version": 3, "sources": ["../../../src/export/favicon.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport { generateFaviconAsync, generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { getUserDefinedFile } from './publicFolder';\nimport { ExportAssetMap } from './saveAssets';\nimport { Log } from '../log';\n\nconst debug = require('debug')('expo:favicon') as typeof console.log;\n\n/** @returns the file system path for a user-defined favicon.ico file in the public folder. */\nexport function getUserDefinedFaviconFile(projectRoot: string): string | null {\n  return getUserDefinedFile(projectRoot, ['./favicon.ico']);\n}\n\nexport async function getVirtualFaviconAssetsAsync(\n  projectRoot: string,\n  {\n    baseUrl,\n    outputDir,\n    files,\n    exp,\n  }: { outputDir: string; baseUrl: string; files?: ExportAssetMap; exp?: ExpoConfig }\n): Promise<((html: string) => string) | null> {\n  const existing = getUserDefinedFaviconFile(projectRoot);\n  if (existing) {\n    debug('Using user-defined favicon.ico file.');\n    return null;\n  }\n\n  const data = await getFaviconFromExpoConfigAsync(projectRoot, {\n    exp,\n  });\n\n  if (!data) {\n    return null;\n  }\n\n  await Promise.all(\n    [data].map(async (asset) => {\n      const assetPath = path.join(outputDir, asset.path);\n      if (files) {\n        debug('Storing asset for persisting: ' + assetPath);\n        files?.set(asset.path, {\n          contents: asset.source,\n          targetDomain: 'client',\n        });\n      } else {\n        debug('Writing asset to disk: ' + assetPath);\n        await fs.promises.writeFile(assetPath, asset.source);\n      }\n    })\n  );\n\n  function injectFaviconTag(html: string): string {\n    if (!html.includes('</head>')) {\n      return html;\n    }\n    return html.replace('</head>', `<link rel=\"icon\" href=\"${baseUrl}/favicon.ico\" /></head>`);\n  }\n\n  return injectFaviconTag;\n}\n\nexport async function getFaviconFromExpoConfigAsync(\n  projectRoot: string,\n  { force = false, exp = getConfig(projectRoot).exp }: { force?: boolean; exp?: ExpoConfig } = {}\n) {\n  const src = exp.web?.favicon ?? null;\n  if (!src) {\n    return null;\n  }\n\n  const dims = [16, 32, 48];\n  const cacheType = 'favicon';\n\n  const size = dims[dims.length - 1];\n  try {\n    const { source } = await generateImageAsync(\n      { projectRoot, cacheType },\n      {\n        resizeMode: 'contain',\n        src,\n        backgroundColor: 'transparent',\n        width: size,\n        height: size,\n        name: `favicon-${size}.png`,\n      }\n    );\n\n    const faviconBuffer = await generateFaviconAsync(source, dims);\n\n    return { source: faviconBuffer, path: 'favicon.ico' };\n  } catch (error: any) {\n    // Check for ENOENT\n    if (!force && error.code === 'ENOENT') {\n      Log.warn(`Favicon source file in Expo config (web.favicon) does not exist: ${src}`);\n      return null;\n    }\n    throw error;\n  }\n}\n"], "names": ["getFaviconFromExpoConfigAsync", "getUserDefinedFaviconFile", "getVirtualFaviconAssetsAsync", "debug", "require", "projectRoot", "getUserDefinedFile", "baseUrl", "outputDir", "files", "exp", "existing", "data", "Promise", "all", "map", "asset", "assetPath", "path", "join", "set", "contents", "source", "targetDomain", "fs", "promises", "writeFile", "injectFaviconTag", "html", "includes", "replace", "force", "getConfig", "src", "web", "favicon", "dims", "cacheType", "size", "length", "generateImageAsync", "resizeMode", "backgroundColor", "width", "height", "name", "favi<PERSON><PERSON><PERSON><PERSON>", "generateFaviconAsync", "error", "code", "Log", "warn"], "mappings": ";;;;;;;;;;;IAiEsBA,6BAA6B;eAA7BA;;IArDNC,yBAAyB;eAAzBA;;IAIMC,4BAA4B;eAA5BA;;;;yBAhBgB;;;;;;;yBACmB;;;;;;;gEAC1C;;;;;;;gEACE;;;;;;8BAEkB;qBAEf;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASH,0BAA0BI,WAAmB;IAC3D,OAAOC,IAAAA,gCAAkB,EAACD,aAAa;QAAC;KAAgB;AAC1D;AAEO,eAAeH,6BACpBG,WAAmB,EACnB,EACEE,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,GAAG,EAC8E;IAEnF,MAAMC,WAAWV,0BAA0BI;IAC3C,IAAIM,UAAU;QACZR,MAAM;QACN,OAAO;IACT;IAEA,MAAMS,OAAO,MAAMZ,8BAA8BK,aAAa;QAC5DK;IACF;IAEA,IAAI,CAACE,MAAM;QACT,OAAO;IACT;IAEA,MAAMC,QAAQC,GAAG,CACf;QAACF;KAAK,CAACG,GAAG,CAAC,OAAOC;QAChB,MAAMC,YAAYC,eAAI,CAACC,IAAI,CAACX,WAAWQ,MAAME,IAAI;QACjD,IAAIT,OAAO;YACTN,MAAM,mCAAmCc;YACzCR,yBAAAA,MAAOW,GAAG,CAACJ,MAAME,IAAI,EAAE;gBACrBG,UAAUL,MAAMM,MAAM;gBACtBC,cAAc;YAChB;QACF,OAAO;YACLpB,MAAM,4BAA4Bc;YAClC,MAAMO,aAAE,CAACC,QAAQ,CAACC,SAAS,CAACT,WAAWD,MAAMM,MAAM;QACrD;IACF;IAGF,SAASK,iBAAiBC,IAAY;QACpC,IAAI,CAACA,KAAKC,QAAQ,CAAC,YAAY;YAC7B,OAAOD;QACT;QACA,OAAOA,KAAKE,OAAO,CAAC,WAAW,CAAC,uBAAuB,EAAEvB,QAAQ,uBAAuB,CAAC;IAC3F;IAEA,OAAOoB;AACT;AAEO,eAAe3B,8BACpBK,WAAmB,EACnB,EAAE0B,QAAQ,KAAK,EAAErB,MAAMsB,IAAAA,mBAAS,EAAC3B,aAAaK,GAAG,EAAyC,GAAG,CAAC,CAAC;QAEnFA;IAAZ,MAAMuB,MAAMvB,EAAAA,WAAAA,IAAIwB,GAAG,qBAAPxB,SAASyB,OAAO,KAAI;IAChC,IAAI,CAACF,KAAK;QACR,OAAO;IACT;IAEA,MAAMG,OAAO;QAAC;QAAI;QAAI;KAAG;IACzB,MAAMC,YAAY;IAElB,MAAMC,OAAOF,IAAI,CAACA,KAAKG,MAAM,GAAG,EAAE;IAClC,IAAI;QACF,MAAM,EAAEjB,MAAM,EAAE,GAAG,MAAMkB,IAAAA,gCAAkB,EACzC;YAAEnC;YAAagC;QAAU,GACzB;YACEI,YAAY;YACZR;YACAS,iBAAiB;YACjBC,OAAOL;YACPM,QAAQN;YACRO,MAAM,CAAC,QAAQ,EAAEP,KAAK,IAAI,CAAC;QAC7B;QAGF,MAAMQ,gBAAgB,MAAMC,IAAAA,kCAAoB,EAACzB,QAAQc;QAEzD,OAAO;YAAEd,QAAQwB;YAAe5B,MAAM;QAAc;IACtD,EAAE,OAAO8B,OAAY;QACnB,mBAAmB;QACnB,IAAI,CAACjB,SAASiB,MAAMC,IAAI,KAAK,UAAU;YACrCC,QAAG,CAACC,IAAI,CAAC,CAAC,iEAAiE,EAAElB,KAAK;YAClF,OAAO;QACT;QACA,MAAMe;IACR;AACF"}