{"version": 3, "sources": ["../../../../../src/start/server/metro/createJResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Fork of the jest resolver but with additional settings for Metro and pnp removed.\n * https://github.com/jestjs/jest/blob/d1a2ed7fea4bdc19836274cd810c8360e3ab62f3/packages/jest-resolve/src/defaultResolver.ts#L1\n */\nimport type { JSONObject as PackageJSON } from '@expo/json-file';\nimport assert from 'assert';\nimport { dirname, isAbsolute, resolve as pathResolve } from 'path';\nimport { sync as resolveSync, SyncOpts as UpstreamResolveOptions } from 'resolve';\nimport * as resolve from 'resolve.exports';\n\n/**\n * Allows transforming parsed `package.json` contents.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param file - Path to `package.json` file.\n * @param dir - Directory that contains the `package.json`.\n *\n * @returns Transformed `package.json` contents.\n */\ntype PackageFilter = (pkg: PackageJSON, file: string, dir: string) => PackageJSON;\n\n/**\n * Allows transforming a path within a package.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param path - Path being resolved.\n * @param relativePath - Path relative from the `package.json` location.\n *\n * @returns Relative path that will be joined from the `package.json` location.\n */\ntype PathFilter = (pkg: PackageJSON, path: string, relativePath: string) => string;\n\ntype ResolverOptions = {\n  /** Directory to begin resolving from. */\n  basedir: string;\n  /** List of export conditions. */\n  conditions?: string[];\n  /** Instance of default resolver. */\n  defaultResolver: typeof defaultResolver;\n  /** List of file extensions to search in order. */\n  extensions?: string[];\n  /**\n   * List of directory names to be looked up for modules recursively.\n   *\n   * @defaultValue\n   * The default is `['node_modules']`.\n   */\n  moduleDirectory?: string[];\n  /**\n   * List of `require.paths` to use if nothing is found in `node_modules`.\n   *\n   * @defaultValue\n   * The default is `undefined`.\n   */\n  paths?: string[];\n  /** Allows transforming parsed `package.json` contents. */\n  packageFilter?: PackageFilter;\n  /** Allows transforms a path within a package. */\n  pathFilter?: PathFilter;\n  /** Current root directory. */\n  rootDir?: string;\n\n  enablePackageExports?: boolean;\n\n  blockList: RegExp[];\n\n  getPackageForModule: import('metro-resolver').CustomResolutionContext['getPackageForModule'];\n} & Pick<\n  UpstreamResolveOptions,\n  | 'readPackageSync'\n  | 'realpathSync'\n  | 'moduleDirectory'\n  | 'extensions'\n  | 'preserveSymlinks'\n  | 'includeCoreModules'\n>;\n\ntype UpstreamResolveOptionsWithConditions = UpstreamResolveOptions &\n  ResolverOptions & {\n    pathExists: (file: string) => boolean;\n  };\n\nconst defaultResolver = (\n  path: string,\n  {\n    enablePackageExports,\n    blockList = [],\n    ...options\n  }: Omit<ResolverOptions, 'defaultResolver' | 'getPackageForModule'> & {\n    isDirectory: (file: string) => boolean;\n    isFile: (file: string) => boolean;\n    pathExists: (file: string) => boolean;\n  }\n): string => {\n  // @ts-expect-error\n  const resolveOptions: UpstreamResolveOptionsWithConditions = {\n    ...options,\n    preserveSymlinks: options.preserveSymlinks,\n    defaultResolver,\n  };\n\n  // resolveSync dereferences symlinks to ensure we don't create a separate\n  // module instance depending on how it was referenced.\n  const result = resolveSync(enablePackageExports ? getPathInModule(path, resolveOptions) : path, {\n    ...resolveOptions,\n    preserveSymlinks: !options.preserveSymlinks,\n  });\n\n  return result;\n};\n\nexport default defaultResolver;\n\n/*\n * helper functions\n */\n\nfunction getPathInModule(path: string, options: UpstreamResolveOptionsWithConditions): string {\n  if (shouldIgnoreRequestForExports(path)) {\n    return path;\n  }\n\n  const segments = path.split('/');\n\n  let moduleName = segments.shift();\n\n  if (!moduleName) {\n    return path;\n  }\n\n  if (moduleName.startsWith('@')) {\n    moduleName = `${moduleName}/${segments.shift()}`;\n  }\n\n  // self-reference\n  const closestPackageJson = findClosestPackageJson(options.basedir, options);\n  if (closestPackageJson) {\n    const pkg = options.readPackageSync!(options.readFileSync!, closestPackageJson);\n    assert(pkg, 'package.json should be read by `readPackageSync`');\n\n    // Added support for the package.json \"imports\" field (#-prefixed paths)\n    if (path.startsWith('#')) {\n      const resolved = resolve.imports(pkg, path, createResolveOptions(options.conditions));\n      if (resolved) {\n        // TODO: Should we attempt to resolve every path in the array?\n        return pathResolve(dirname(closestPackageJson), resolved[0]);\n      }\n      // NOTE: resolve.imports would have thrown by this point.\n      return path;\n    }\n\n    if (pkg.name === moduleName) {\n      const resolved = resolve.exports(\n        pkg,\n        (segments.join('/') || '.') as resolve.Exports.Entry,\n        createResolveOptions(options.conditions)\n      );\n\n      if (resolved) {\n        return pathResolve(dirname(closestPackageJson), resolved[0]);\n      }\n\n      if (pkg.exports) {\n        throw new Error(\n          \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n        );\n      }\n    }\n  }\n\n  let packageJsonPath = '';\n\n  try {\n    packageJsonPath = resolveSync(`${moduleName}/package.json`, options);\n  } catch {\n    // ignore if package.json cannot be found\n  }\n\n  if (!packageJsonPath) {\n    return path;\n  }\n\n  const pkg = options.readPackageSync!(options.readFileSync!, packageJsonPath);\n  assert(pkg, 'package.json should be read by `readPackageSync`');\n\n  const resolved = resolve.exports(\n    pkg,\n    (segments.join('/') || '.') as resolve.Exports.Entry,\n    createResolveOptions(options.conditions)\n  );\n\n  if (resolved) {\n    return pathResolve(dirname(packageJsonPath), resolved[0]);\n  }\n\n  if (pkg.exports) {\n    throw new Error(\n      \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n    );\n  }\n\n  return path;\n}\n\nfunction createResolveOptions(conditions: string[] | undefined): resolve.Options {\n  return conditions\n    ? { conditions, unsafe: true }\n    : // no conditions were passed - let's assume this is Jest internal and it should be `require`\n      { browser: false, require: true };\n}\n\n// if it's a relative import or an absolute path, imports/exports are ignored\nconst shouldIgnoreRequestForExports = (path: string) => path.startsWith('.') || isAbsolute(path);\n\n// adapted from\n// https://github.com/lukeed/escalade/blob/2477005062cdbd8407afc90d3f48f4930354252b/src/sync.js\nfunction findClosestPackageJson(\n  start: string,\n  options: UpstreamResolveOptionsWithConditions\n): string | undefined {\n  let dir = pathResolve('.', start);\n  if (!options.isDirectory!(dir)) {\n    dir = dirname(dir);\n  }\n\n  while (true) {\n    const pkgJsonFile = pathResolve(dir, './package.json');\n    const hasPackageJson = options.pathExists!(pkgJsonFile);\n\n    if (hasPackageJson) {\n      return pkgJsonFile;\n    }\n\n    const prevDir = dir;\n    dir = dirname(dir);\n\n    if (prevDir === dir) {\n      return undefined;\n    }\n  }\n}\n"], "names": ["defaultResolver", "path", "enablePackageExports", "blockList", "options", "resolveOptions", "preserveSymlinks", "result", "resolveSync", "getPathInModule", "shouldIgnoreRequestForExports", "segments", "split", "moduleName", "shift", "startsWith", "closestPackageJson", "findClosestPackageJson", "basedir", "pkg", "readPackageSync", "readFileSync", "assert", "resolved", "resolve", "imports", "createResolveOptions", "conditions", "pathResolve", "dirname", "name", "exports", "join", "Error", "packageJsonPath", "unsafe", "browser", "require", "isAbsolute", "start", "dir", "isDirectory", "pkgJsonFile", "hasPackageJson", "pathExists", "prevDir", "undefined"], "mappings": "AAAA;;;;;;;;;CASC;;;;+BA4GD;;;eAAA;;;;gEA1GmB;;;;;;;yBACyC;;;;;;;yBACY;;;;;;;iEAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EzB,MAAMA,kBAAkB,CACtBC,MACA,EACEC,oBAAoB,EACpBC,YAAY,EAAE,EACd,GAAGC,SAKJ;IAED,mBAAmB;IACnB,MAAMC,iBAAuD;QAC3D,GAAGD,OAAO;QACVE,kBAAkBF,QAAQE,gBAAgB;QAC1CN;IACF;IAEA,yEAAyE;IACzE,sDAAsD;IACtD,MAAMO,SAASC,IAAAA,eAAW,EAACN,uBAAuBO,gBAAgBR,MAAMI,kBAAkBJ,MAAM;QAC9F,GAAGI,cAAc;QACjBC,kBAAkB,CAACF,QAAQE,gBAAgB;IAC7C;IAEA,OAAOC;AACT;MAEA,WAAeP;AAEf;;CAEC,GAED,SAASS,gBAAgBR,IAAY,EAAEG,OAA6C;IAClF,IAAIM,8BAA8BT,OAAO;QACvC,OAAOA;IACT;IAEA,MAAMU,WAAWV,KAAKW,KAAK,CAAC;IAE5B,IAAIC,aAAaF,SAASG,KAAK;IAE/B,IAAI,CAACD,YAAY;QACf,OAAOZ;IACT;IAEA,IAAIY,WAAWE,UAAU,CAAC,MAAM;QAC9BF,aAAa,GAAGA,WAAW,CAAC,EAAEF,SAASG,KAAK,IAAI;IAClD;IAEA,iBAAiB;IACjB,MAAME,qBAAqBC,uBAAuBb,QAAQc,OAAO,EAAEd;IACnE,IAAIY,oBAAoB;QACtB,MAAMG,MAAMf,QAAQgB,eAAe,CAAEhB,QAAQiB,YAAY,EAAGL;QAC5DM,IAAAA,iBAAM,EAACH,KAAK;QAEZ,wEAAwE;QACxE,IAAIlB,KAAKc,UAAU,CAAC,MAAM;YACxB,MAAMQ,WAAWC,kBAAQC,OAAO,CAACN,KAAKlB,MAAMyB,qBAAqBtB,QAAQuB,UAAU;YACnF,IAAIJ,UAAU;gBACZ,8DAA8D;gBAC9D,OAAOK,IAAAA,eAAW,EAACC,IAAAA,eAAO,EAACb,qBAAqBO,QAAQ,CAAC,EAAE;YAC7D;YACA,yDAAyD;YACzD,OAAOtB;QACT;QAEA,IAAIkB,IAAIW,IAAI,KAAKjB,YAAY;YAC3B,MAAMU,WAAWC,kBAAQO,OAAO,CAC9BZ,KACCR,SAASqB,IAAI,CAAC,QAAQ,KACvBN,qBAAqBtB,QAAQuB,UAAU;YAGzC,IAAIJ,UAAU;gBACZ,OAAOK,IAAAA,eAAW,EAACC,IAAAA,eAAO,EAACb,qBAAqBO,QAAQ,CAAC,EAAE;YAC7D;YAEA,IAAIJ,IAAIY,OAAO,EAAE;gBACf,MAAM,IAAIE,MACR;YAEJ;QACF;IACF;IAEA,IAAIC,kBAAkB;IAEtB,IAAI;QACFA,kBAAkB1B,IAAAA,eAAW,EAAC,GAAGK,WAAW,aAAa,CAAC,EAAET;IAC9D,EAAE,OAAM;IACN,yCAAyC;IAC3C;IAEA,IAAI,CAAC8B,iBAAiB;QACpB,OAAOjC;IACT;IAEA,MAAMkB,MAAMf,QAAQgB,eAAe,CAAEhB,QAAQiB,YAAY,EAAGa;IAC5DZ,IAAAA,iBAAM,EAACH,KAAK;IAEZ,MAAMI,WAAWC,kBAAQO,OAAO,CAC9BZ,KACCR,SAASqB,IAAI,CAAC,QAAQ,KACvBN,qBAAqBtB,QAAQuB,UAAU;IAGzC,IAAIJ,UAAU;QACZ,OAAOK,IAAAA,eAAW,EAACC,IAAAA,eAAO,EAACK,kBAAkBX,QAAQ,CAAC,EAAE;IAC1D;IAEA,IAAIJ,IAAIY,OAAO,EAAE;QACf,MAAM,IAAIE,MACR;IAEJ;IAEA,OAAOhC;AACT;AAEA,SAASyB,qBAAqBC,UAAgC;IAC5D,OAAOA,aACH;QAAEA;QAAYQ,QAAQ;IAAK,IAE3B;QAAEC,SAAS;QAAOC,SAAS;IAAK;AACtC;AAEA,6EAA6E;AAC7E,MAAM3B,gCAAgC,CAACT,OAAiBA,KAAKc,UAAU,CAAC,QAAQuB,IAAAA,kBAAU,EAACrC;AAE3F,eAAe;AACf,+FAA+F;AAC/F,SAASgB,uBACPsB,KAAa,EACbnC,OAA6C;IAE7C,IAAIoC,MAAMZ,IAAAA,eAAW,EAAC,KAAKW;IAC3B,IAAI,CAACnC,QAAQqC,WAAW,CAAED,MAAM;QAC9BA,MAAMX,IAAAA,eAAO,EAACW;IAChB;IAEA,MAAO,KAAM;QACX,MAAME,cAAcd,IAAAA,eAAW,EAACY,KAAK;QACrC,MAAMG,iBAAiBvC,QAAQwC,UAAU,CAAEF;QAE3C,IAAIC,gBAAgB;YAClB,OAAOD;QACT;QAEA,MAAMG,UAAUL;QAChBA,MAAMX,IAAAA,eAAO,EAACW;QAEd,IAAIK,YAAYL,KAAK;YACnB,OAAOM;QACT;IACF;AACF"}