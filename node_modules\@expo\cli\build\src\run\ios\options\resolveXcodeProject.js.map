{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveXcodeProject.ts"], "sourcesContent": ["import { sync as globSync } from 'glob';\n\nimport { CommandError } from '../../../utils/errors';\nimport { ProjectInfo } from '../XcodeBuild.types';\n\nconst ignoredPaths = ['**/@(Carthage|Pods|vendor|node_modules)/**'];\n\nfunction findXcodeProjectPaths(\n  projectRoot: string,\n  extension: 'xcworkspace' | 'xcodeproj'\n): string[] {\n  return globSync(`ios/*.${extension}`, {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n}\n\n/** Return the path and type of Xcode project in the given folder. */\nexport function resolveXcodeProject(projectRoot: string): ProjectInfo {\n  let paths = findXcodeProjectPaths(projectRoot, 'xcworkspace');\n  if (paths.length) {\n    return {\n      // Use full path instead of relative project root so that warnings and errors contain full paths as well, this helps with filtering.\n      // Also helps keep things consistent in monorepos.\n      name: paths[0],\n      // name: path.relative(projectRoot, paths[0]),\n      isWorkspace: true,\n    };\n  }\n  paths = findXcodeProjectPaths(projectRoot, 'xcodeproj');\n  if (paths.length) {\n    return { name: paths[0], isWorkspace: false };\n  }\n  throw new CommandError(\n    'IOS_MALFORMED',\n    `Xcode project not found in project: ${projectRoot}. You can generate a project with \\`npx expo prebuild\\``\n  );\n}\n"], "names": ["resolveXcodeProject", "ignoredPaths", "findXcodeProjectPaths", "projectRoot", "extension", "globSync", "absolute", "cwd", "ignore", "paths", "length", "name", "isWorkspace", "CommandError"], "mappings": ";;;;+BAmBgBA;;;eAAAA;;;;yBAnBiB;;;;;;wBAEJ;AAG7B,MAAMC,eAAe;IAAC;CAA6C;AAEnE,SAASC,sBACPC,WAAmB,EACnBC,SAAsC;IAEtC,OAAOC,IAAAA,YAAQ,EAAC,CAAC,MAAM,EAAED,WAAW,EAAE;QACpCE,UAAU;QACVC,KAAKJ;QACLK,QAAQP;IACV;AACF;AAGO,SAASD,oBAAoBG,WAAmB;IACrD,IAAIM,QAAQP,sBAAsBC,aAAa;IAC/C,IAAIM,MAAMC,MAAM,EAAE;QAChB,OAAO;YACL,oIAAoI;YACpI,kDAAkD;YAClDC,MAAMF,KAAK,CAAC,EAAE;YACd,8CAA8C;YAC9CG,aAAa;QACf;IACF;IACAH,QAAQP,sBAAsBC,aAAa;IAC3C,IAAIM,MAAMC,MAAM,EAAE;QAChB,OAAO;YAAEC,MAAMF,KAAK,CAAC,EAAE;YAAEG,aAAa;QAAM;IAC9C;IACA,MAAM,IAAIC,oBAAY,CACpB,iBACA,CAAC,oCAAoC,EAAEV,YAAY,uDAAuD,CAAC;AAE/G"}