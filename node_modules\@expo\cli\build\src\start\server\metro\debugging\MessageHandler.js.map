{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/MessageHandler.ts"], "sourcesContent": ["import type {\n  Connection,\n  DebuggerMetadata,\n  <PERSON>buggerRequest,\n  DebuggerResponse,\n  DeviceMetadata,\n  DeviceRequest,\n  DeviceResponse,\n  Page,\n} from './types';\n\nexport abstract class MessageHandler {\n  protected page: Page;\n  protected device: DeviceMetadata;\n  protected debugger: DebuggerMetadata;\n\n  constructor(connection: Connection) {\n    this.page = connection.page;\n    this.device = connection.device;\n    this.debugger = connection.debugger;\n  }\n\n  /** Determine if this middleware should be enabled or disabled, based on the page capabilities */\n  isEnabled(): boolean {\n    return true;\n  }\n\n  /** Send a message directly to the device */\n  sendToDevice<T = DeviceResponse | DebuggerResponse>(message: T): true {\n    // @ts-expect-error Type `T` is json serializable, just not the same one from `@react-native/dev-middleware`\n    this.device.sendMessage(message);\n    return true;\n  }\n\n  /** Send a message directly to the debugger */\n  sendToDebugger<T = DeviceResponse | DebuggerResponse>(message: T): true {\n    // @ts-expect-error Type `T` is json serializable, just not the same one from `@react-native/dev-middleware`\n    this.debugger.sendMessage(message);\n    return true;\n  }\n\n  /**\n   * Intercept a message coming from the device, modify or respond to it through `this._sendMessageToDevice`.\n   * Return `true` if the message was handled, this will stop the message propagation.\n   */\n  handleDeviceMessage?(message: DeviceRequest | DeviceResponse): boolean;\n\n  /**\n   * Intercept a message coming from the debugger, modify or respond to it through `socket.send`.\n   * Return `true` if the message was handled, this will stop the message propagation.\n   */\n  handleDebuggerMessage?(message: DebuggerRequest | DebuggerResponse): boolean;\n}\n"], "names": ["MessageHandler", "constructor", "connection", "page", "device", "debugger", "isEnabled", "sendToDevice", "message", "sendMessage", "sendToDebugger"], "mappings": ";;;;+<PERSON><PERSON>s<PERSON>;;;eAAAA;;;AAAf,MAAeA;IAKpBC,YAAYC,UAAsB,CAAE;QAClC,IAAI,CAACC,IAAI,GAAGD,WAAWC,IAAI;QAC3B,IAAI,CAACC,MAAM,GAAGF,WAAWE,MAAM;QAC/B,IAAI,CAACC,QAAQ,GAAGH,WAAWG,QAAQ;IACrC;IAEA,+FAA+F,GAC/FC,YAAqB;QACnB,OAAO;IACT;IAEA,0CAA0C,GAC1CC,aAAoDC,OAAU,EAAQ;QACpE,4GAA4G;QAC5G,IAAI,CAACJ,MAAM,CAACK,WAAW,CAACD;QACxB,OAAO;IACT;IAEA,4CAA4C,GAC5CE,eAAsDF,OAAU,EAAQ;QACtE,4GAA4G;QAC5G,IAAI,CAACH,QAAQ,CAACI,WAAW,CAACD;QAC1B,OAAO;IACT;AAaF"}