{"version": 3, "sources": ["../../../../../src/start/server/middleware/metroOptions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport type { BundleOptions as MetroBundleOptions } from 'metro/src/shared/types';\nimport resolveFrom from 'resolve-from';\n\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nconst debug = require('debug')('expo:metro:options') as typeof console.log;\n\nexport type MetroEnvironment = 'node' | 'react-server' | 'client';\n\nexport type ExpoMetroOptions = {\n  platform: string;\n  mainModuleName: string;\n  mode: string;\n  minify?: boolean;\n  environment?: MetroEnvironment;\n  serializerOutput?: 'static';\n  serializerIncludeMaps?: boolean;\n  lazy?: boolean;\n  engine?: 'hermes';\n  preserveEnvVars?: boolean;\n  bytecode: boolean;\n  /** Enable async routes (route-based bundle splitting) in Expo Router. */\n  asyncRoutes?: boolean;\n  /** Module ID relative to the projectRoot for the Expo Router app directory. */\n  routerRoot: string;\n  /** Enable React compiler support in Babel. */\n  reactCompiler: boolean;\n  baseUrl?: string;\n  isExporting: boolean;\n  /** Is bundling a DOM Component (\"use dom\"). Requires the entry dom component file path. */\n  domRoot?: string;\n  /** Exporting MD5 filename based on file contents, for EAS Update.  */\n  useMd5Filename?: boolean;\n  inlineSourceMap?: boolean;\n  clientBoundaries?: string[];\n  splitChunks?: boolean;\n  usedExports?: boolean;\n  /** Enable optimized bundling (required for tree shaking). */\n  optimize?: boolean;\n\n  modulesOnly?: boolean;\n  runModule?: boolean;\n};\n\n// See: @expo/metro-config/src/serializer/fork/baseJSBundle.ts `ExpoSerializerOptions`\nexport type SerializerOptions = {\n  includeSourceMaps?: boolean;\n  output?: 'static';\n  splitChunks?: boolean;\n  usedExports?: boolean;\n  exporting?: boolean;\n};\n\nexport type ExpoMetroBundleOptions = MetroBundleOptions & {\n  serializerOptions?: SerializerOptions;\n};\n\nexport function isServerEnvironment(environment?: any): boolean {\n  return environment === 'node' || environment === 'react-server';\n}\n\nexport function shouldEnableAsyncImports(projectRoot: string): boolean {\n  if (env.EXPO_NO_METRO_LAZY) {\n    return false;\n  }\n\n  // `@expo/metro-runtime` includes support for the fetch + eval runtime code required\n  // to support async imports. If it's not installed, we can't support async imports.\n  // If it is installed, the user MUST import it somewhere in their project.\n  // Expo Router automatically pulls this in, so we can check for it.\n  return resolveFrom.silent(projectRoot, '@expo/metro-runtime/package.json') != null;\n}\n\nfunction withDefaults({\n  mode = 'development',\n  minify = mode === 'production',\n  preserveEnvVars = mode !== 'development' && env.EXPO_NO_CLIENT_ENV_VARS,\n  lazy,\n  environment,\n  ...props\n}: ExpoMetroOptions): ExpoMetroOptions {\n  if (props.bytecode) {\n    if (props.platform === 'web') {\n      throw new CommandError('Cannot use bytecode with the web platform');\n    }\n    if (props.engine !== 'hermes') {\n      throw new CommandError('Bytecode is only supported with the Hermes engine');\n    }\n  }\n\n  const optimize =\n    props.optimize ??\n    (environment !== 'node' && mode === 'production' && env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH);\n\n  return {\n    mode,\n    minify,\n    preserveEnvVars,\n    optimize,\n    usedExports: optimize && env.EXPO_UNSTABLE_TREE_SHAKING,\n    lazy: !props.isExporting && lazy,\n    environment: environment === 'client' ? undefined : environment,\n    ...props,\n  };\n}\n\nexport function getBaseUrlFromExpoConfig(exp: ExpoConfig) {\n  return exp.experiments?.baseUrl?.trim().replace(/\\/+$/, '') ?? '';\n}\n\nexport function getAsyncRoutesFromExpoConfig(exp: ExpoConfig, mode: string, platform: string) {\n  let asyncRoutesSetting;\n\n  if (exp.extra?.router?.asyncRoutes) {\n    const asyncRoutes = exp.extra?.router?.asyncRoutes;\n    if (['boolean', 'string'].includes(typeof asyncRoutes)) {\n      asyncRoutesSetting = asyncRoutes;\n    } else if (typeof asyncRoutes === 'object') {\n      asyncRoutesSetting = asyncRoutes[platform] ?? asyncRoutes.default;\n    }\n  }\n\n  return [mode, true].includes(asyncRoutesSetting);\n}\n\nexport function getMetroDirectBundleOptionsForExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'baseUrl' | 'reactCompiler' | 'routerRoot' | 'asyncRoutes'>\n): Partial<ExpoMetroBundleOptions> {\n  return getMetroDirectBundleOptions({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n    asyncRoutes: getAsyncRoutesFromExpoConfig(exp, options.mode, options.platform),\n  });\n}\n\nexport function getMetroDirectBundleOptions(\n  options: ExpoMetroOptions\n): Partial<ExpoMetroBundleOptions> {\n  const {\n    mainModuleName,\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    bytecode,\n    lazy,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    isExporting,\n    inlineSourceMap,\n    splitChunks,\n    usedExports,\n    reactCompiler,\n    optimize,\n    domRoot,\n    clientBoundaries,\n    runModule,\n    modulesOnly,\n    useMd5Filename,\n  } = withDefaults(options);\n\n  const dev = mode !== 'production';\n  const isHermes = engine === 'hermes';\n\n  if (isExporting) {\n    debug('Disabling lazy bundling for export build');\n    options.lazy = false;\n  }\n\n  let fakeSourceUrl: string | undefined;\n  let fakeSourceMapUrl: string | undefined;\n\n  // TODO: Upstream support to Metro for passing custom serializer options.\n  if (serializerIncludeMaps != null || serializerOutput != null) {\n    fakeSourceUrl = new URL(\n      createBundleUrlPath(options).replace(/^\\//, ''),\n      'http://localhost:8081'\n    ).toString();\n    if (serializerIncludeMaps) {\n      fakeSourceMapUrl = fakeSourceUrl.replace('.bundle?', '.map?');\n    }\n  }\n\n  const customTransformOptions: ExpoMetroBundleOptions['customTransformOptions'] = {\n    __proto__: null,\n    optimize: optimize || undefined,\n    engine,\n    clientBoundaries,\n    preserveEnvVars: preserveEnvVars || undefined,\n    // Use string to match the query param behavior.\n    asyncRoutes: asyncRoutes ? String(asyncRoutes) : undefined,\n    environment,\n    baseUrl: baseUrl || undefined,\n    routerRoot,\n    bytecode: bytecode ? '1' : undefined,\n    reactCompiler: reactCompiler ? String(reactCompiler) : undefined,\n    dom: domRoot,\n    useMd5Filename: useMd5Filename || undefined,\n  };\n\n  // Iterate and delete undefined values\n  for (const key in customTransformOptions) {\n    if (customTransformOptions[key] === undefined) {\n      delete customTransformOptions[key];\n    }\n  }\n\n  const bundleOptions: Partial<ExpoMetroBundleOptions> = {\n    platform,\n    entryFile: mainModuleName,\n    dev,\n    minify: minify ?? !dev,\n    inlineSourceMap: inlineSourceMap ?? false,\n    lazy: (!isExporting && lazy) || undefined,\n    unstable_transformProfile: isHermes ? 'hermes-stable' : 'default',\n    customTransformOptions,\n    runModule,\n    modulesOnly,\n    customResolverOptions: {\n      __proto__: null,\n      environment,\n      exporting: isExporting || undefined,\n    },\n    sourceMapUrl: fakeSourceMapUrl,\n    sourceUrl: fakeSourceUrl,\n    serializerOptions: {\n      splitChunks,\n      usedExports: usedExports || undefined,\n      output: serializerOutput,\n      includeSourceMaps: serializerIncludeMaps,\n      exporting: isExporting || undefined,\n    },\n  };\n\n  return bundleOptions;\n}\n\nexport function createBundleUrlPathFromExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'reactCompiler' | 'baseUrl' | 'routerRoot'>\n): string {\n  return createBundleUrlPath({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n  });\n}\n\nexport function createBundleUrlPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  return `/${encodeURI(options.mainModuleName.replace(/^\\/+/, ''))}.bundle?${queryParams.toString()}`;\n}\n\n/**\n * Create a bundle URL, containing all required query parameters, using a valid \"os path\".\n * On POSIX systems, this would look something like `/Users/<USER>/project/file.js?dev=false&..`.\n * On UNIX systems, this would look something like `C:\\Users\\<USER>\\project\\file.js?dev=false&..`.\n * This path can safely be used with `path.*` modifiers and resolved.\n */\nexport function createBundleOsPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  const mainModuleName = toPosixPath(options.mainModuleName);\n  return `${mainModuleName}.bundle?${queryParams.toString()}`;\n}\n\nexport function createBundleUrlSearchParams(options: ExpoMetroOptions): URLSearchParams {\n  const {\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    lazy,\n    bytecode,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    reactCompiler,\n    inlineSourceMap,\n    isExporting,\n    clientBoundaries,\n    splitChunks,\n    usedExports,\n    optimize,\n    domRoot,\n    modulesOnly,\n    runModule,\n  } = withDefaults(options);\n\n  const dev = String(mode !== 'production');\n  const queryParams = new URLSearchParams({\n    platform: encodeURIComponent(platform),\n    dev,\n    // TODO: Is this still needed?\n    hot: String(false),\n  });\n\n  // Lazy bundling must be disabled for bundle splitting to work.\n  if (!isExporting && lazy) {\n    queryParams.append('lazy', String(lazy));\n  }\n\n  if (inlineSourceMap) {\n    queryParams.append('inlineSourceMap', String(inlineSourceMap));\n  }\n\n  if (minify) {\n    queryParams.append('minify', String(minify));\n  }\n\n  // We split bytecode from the engine since you could technically use Hermes without bytecode.\n  // Hermes indicates the type of language features you want to transform out of the JS, whereas bytecode\n  // indicates whether you want to use the Hermes bytecode format.\n  if (engine) {\n    queryParams.append('transform.engine', engine);\n  }\n  if (bytecode) {\n    queryParams.append('transform.bytecode', '1');\n  }\n  if (asyncRoutes) {\n    queryParams.append('transform.asyncRoutes', String(asyncRoutes));\n  }\n  if (preserveEnvVars) {\n    queryParams.append('transform.preserveEnvVars', String(preserveEnvVars));\n  }\n  if (baseUrl) {\n    queryParams.append('transform.baseUrl', baseUrl);\n  }\n  if (clientBoundaries?.length) {\n    queryParams.append('transform.clientBoundaries', JSON.stringify(clientBoundaries));\n  }\n  if (routerRoot != null) {\n    queryParams.append('transform.routerRoot', routerRoot);\n  }\n  if (reactCompiler) {\n    queryParams.append('transform.reactCompiler', String(reactCompiler));\n  }\n  if (domRoot) {\n    queryParams.append('transform.dom', domRoot);\n  }\n\n  if (environment) {\n    queryParams.append('resolver.environment', environment);\n    queryParams.append('transform.environment', environment);\n  }\n\n  if (isExporting) {\n    queryParams.append('resolver.exporting', String(isExporting));\n  }\n\n  if (splitChunks) {\n    queryParams.append('serializer.splitChunks', String(splitChunks));\n  }\n  if (usedExports) {\n    queryParams.append('serializer.usedExports', String(usedExports));\n  }\n  if (optimize) {\n    queryParams.append('transform.optimize', String(optimize));\n  }\n  if (serializerOutput) {\n    queryParams.append('serializer.output', serializerOutput);\n  }\n  if (serializerIncludeMaps) {\n    queryParams.append('serializer.map', String(serializerIncludeMaps));\n  }\n  if (engine === 'hermes') {\n    queryParams.append('unstable_transformProfile', 'hermes-stable');\n  }\n\n  if (modulesOnly != null) {\n    queryParams.set('modulesOnly', String(modulesOnly));\n  }\n  if (runModule != null) {\n    queryParams.set('runModule', String(runModule));\n  }\n\n  return queryParams;\n}\n\n/**\n * Convert all path separators to `/`, including on Windows.\n * Metro asumes that all module specifiers are posix paths.\n * References to directories can still be Windows-style paths in Metro.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules#importing_features_into_your_script\n * @see https://github.com/facebook/metro/pull/1286\n */\nexport function convertPathToModuleSpecifier(pathLike: string) {\n  return toPosixPath(pathLike);\n}\n\nexport function getMetroOptionsFromUrl(urlFragment: string) {\n  const url = new URL(urlFragment, 'http://localhost:0');\n  const getStringParam = (key: string) => {\n    const param = url.searchParams.get(key);\n    if (Array.isArray(param)) {\n      throw new Error(`Expected single value for ${key}`);\n    }\n    return param;\n  };\n\n  let pathname = url.pathname;\n  if (pathname.endsWith('.bundle')) {\n    pathname = pathname.slice(0, -'.bundle'.length);\n  }\n\n  const options: ExpoMetroOptions = {\n    mode: isTruthy(getStringParam('dev') ?? 'true') ? 'development' : 'production',\n    minify: isTruthy(getStringParam('minify') ?? 'false'),\n    lazy: isTruthy(getStringParam('lazy') ?? 'false'),\n    routerRoot: getStringParam('transform.routerRoot') ?? 'app',\n    isExporting: isTruthy(getStringParam('resolver.exporting') ?? 'false'),\n    environment: assertEnvironment(getStringParam('transform.environment') ?? 'node'),\n    platform: url.searchParams.get('platform') ?? 'web',\n    bytecode: isTruthy(getStringParam('transform.bytecode') ?? 'false'),\n    mainModuleName: convertPathToModuleSpecifier(pathname),\n    reactCompiler: isTruthy(getStringParam('transform.reactCompiler') ?? 'false'),\n    asyncRoutes: isTruthy(getStringParam('transform.asyncRoutes') ?? 'false'),\n    baseUrl: getStringParam('transform.baseUrl') ?? undefined,\n    // clientBoundaries: JSON.parse(getStringParam('transform.clientBoundaries') ?? '[]'),\n    engine: assertEngine(getStringParam('transform.engine')),\n    runModule: isTruthy(getStringParam('runModule') ?? 'true'),\n    modulesOnly: isTruthy(getStringParam('modulesOnly') ?? 'false'),\n  };\n\n  return options;\n}\n\nfunction isTruthy(value: string | null): boolean {\n  return value === 'true' || value === '1';\n}\n\nfunction assertEnvironment(environment: string | undefined): MetroEnvironment | undefined {\n  if (!environment) {\n    return undefined;\n  }\n  if (!['node', 'react-server', 'client'].includes(environment)) {\n    throw new Error(`Expected transform.environment to be one of: node, react-server, client`);\n  }\n  return environment as MetroEnvironment;\n}\nfunction assertEngine(engine: string | undefined | null): 'hermes' | undefined {\n  if (!engine) {\n    return undefined;\n  }\n  if (!['hermes'].includes(engine)) {\n    throw new Error(`Expected transform.engine to be one of: hermes`);\n  }\n  return engine as 'hermes';\n}\n"], "names": ["convertPathToModuleSpecifier", "createBundleOsPath", "createBundleUrlPath", "createBundleUrlPathFromExpoConfig", "createBundleUrlSearchParams", "getAsyncRoutesFromExpoConfig", "getBaseUrlFromExpoConfig", "getMetroDirectBundleOptions", "getMetroDirectBundleOptionsForExpoConfig", "getMetroOptionsFromUrl", "isServerEnvironment", "shouldEnableAsyncImports", "debug", "require", "environment", "projectRoot", "env", "EXPO_NO_METRO_LAZY", "resolveFrom", "silent", "with<PERSON><PERSON><PERSON><PERSON>", "mode", "minify", "preserveEnvVars", "EXPO_NO_CLIENT_ENV_VARS", "lazy", "props", "bytecode", "platform", "CommandError", "engine", "optimize", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "usedExports", "EXPO_UNSTABLE_TREE_SHAKING", "isExporting", "undefined", "exp", "experiments", "baseUrl", "trim", "replace", "asyncRoutesSetting", "extra", "router", "asyncRoutes", "includes", "default", "options", "reactCompiler", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "mainModuleName", "serializerOutput", "serializerIncludeMaps", "inlineSourceMap", "splitChunks", "domRoot", "clientBoundaries", "runModule", "modulesOnly", "useMd5Filename", "dev", "isHermes", "fakeSourceUrl", "fakeSourceMapUrl", "URL", "toString", "customTransformOptions", "__proto__", "String", "dom", "key", "bundleOptions", "entryFile", "unstable_transformProfile", "customResolverOptions", "exporting", "sourceMapUrl", "sourceUrl", "serializerOptions", "output", "includeSourceMaps", "queryParams", "encodeURI", "toPosixPath", "URLSearchParams", "encodeURIComponent", "hot", "append", "length", "JSON", "stringify", "set", "pathLike", "urlFragment", "url", "getStringParam", "param", "searchParams", "get", "Array", "isArray", "Error", "pathname", "endsWith", "slice", "<PERSON><PERSON><PERSON><PERSON>", "assertEnvironment", "assertEngine", "value"], "mappings": ";;;;;;;;;;;IAqZgBA,4BAA4B;eAA5BA;;IAnIAC,kBAAkB;eAAlBA;;IAXAC,mBAAmB;eAAnBA;;IAbAC,iCAAiC;eAAjCA;;IA8BAC,2BAA2B;eAA3BA;;IAtKAC,4BAA4B;eAA5BA;;IAJAC,wBAAwB;eAAxBA;;IAiCAC,2BAA2B;eAA3BA;;IAdAC,wCAAwC;eAAxCA;;IAwRAC,sBAAsB;eAAtBA;;IA5VAC,mBAAmB;eAAnBA;;IAIAC,wBAAwB;eAAxBA;;;;gEA/DQ;;;;;;qBAEJ;wBACS;0BACD;wBAC2B;;;;;;AAEvD,MAAMC,QAAQC,QAAQ,SAAS;AAoDxB,SAASH,oBAAoBI,WAAiB;IACnD,OAAOA,gBAAgB,UAAUA,gBAAgB;AACnD;AAEO,SAASH,yBAAyBI,WAAmB;IAC1D,IAAIC,QAAG,CAACC,kBAAkB,EAAE;QAC1B,OAAO;IACT;IAEA,oFAAoF;IACpF,mFAAmF;IACnF,0EAA0E;IAC1E,mEAAmE;IACnE,OAAOC,sBAAW,CAACC,MAAM,CAACJ,aAAa,uCAAuC;AAChF;AAEA,SAASK,aAAa,EACpBC,OAAO,aAAa,EACpBC,SAASD,SAAS,YAAY,EAC9BE,kBAAkBF,SAAS,iBAAiBL,QAAG,CAACQ,uBAAuB,EACvEC,IAAI,EACJX,WAAW,EACX,GAAGY,OACc;IACjB,IAAIA,MAAMC,QAAQ,EAAE;QAClB,IAAID,MAAME,QAAQ,KAAK,OAAO;YAC5B,MAAM,IAAIC,oBAAY,CAAC;QACzB;QACA,IAAIH,MAAMI,MAAM,KAAK,UAAU;YAC7B,MAAM,IAAID,oBAAY,CAAC;QACzB;IACF;IAEA,MAAME,WACJL,MAAMK,QAAQ,IACbjB,CAAAA,gBAAgB,UAAUO,SAAS,gBAAgBL,QAAG,CAACgB,kCAAkC,AAAD;IAE3F,OAAO;QACLX;QACAC;QACAC;QACAQ;QACAE,aAAaF,YAAYf,QAAG,CAACkB,0BAA0B;QACvDT,MAAM,CAACC,MAAMS,WAAW,IAAIV;QAC5BX,aAAaA,gBAAgB,WAAWsB,YAAYtB;QACpD,GAAGY,KAAK;IACV;AACF;AAEO,SAASpB,yBAAyB+B,GAAe;QAC/CA,0BAAAA;IAAP,OAAOA,EAAAA,mBAAAA,IAAIC,WAAW,sBAAfD,2BAAAA,iBAAiBE,OAAO,qBAAxBF,yBAA0BG,IAAI,GAAGC,OAAO,CAAC,QAAQ,QAAO;AACjE;AAEO,SAASpC,6BAA6BgC,GAAe,EAAEhB,IAAY,EAAEO,QAAgB;QAGtFS,mBAAAA;IAFJ,IAAIK;IAEJ,KAAIL,aAAAA,IAAIM,KAAK,sBAATN,oBAAAA,WAAWO,MAAM,qBAAjBP,kBAAmBQ,WAAW,EAAE;YACdR,oBAAAA;QAApB,MAAMQ,eAAcR,cAAAA,IAAIM,KAAK,sBAATN,qBAAAA,YAAWO,MAAM,qBAAjBP,mBAAmBQ,WAAW;QAClD,IAAI;YAAC;YAAW;SAAS,CAACC,QAAQ,CAAC,OAAOD,cAAc;YACtDH,qBAAqBG;QACvB,OAAO,IAAI,OAAOA,gBAAgB,UAAU;YAC1CH,qBAAqBG,WAAW,CAACjB,SAAS,IAAIiB,YAAYE,OAAO;QACnE;IACF;IAEA,OAAO;QAAC1B;QAAM;KAAK,CAACyB,QAAQ,CAACJ;AAC/B;AAEO,SAASlC,yCACdO,WAAmB,EACnBsB,GAAe,EACfW,OAA2F;QAIxEX;IAFnB,OAAO9B,4BAA4B;QACjC,GAAGyC,OAAO;QACVC,eAAe,CAAC,GAACZ,mBAAAA,IAAIC,WAAW,qBAAfD,iBAAiBY,aAAa;QAC/CV,SAASjC,yBAAyB+B;QAClCa,YAAYC,IAAAA,8CAAsC,EAACpC,aAAasB;QAChEQ,aAAaxC,6BAA6BgC,KAAKW,QAAQ3B,IAAI,EAAE2B,QAAQpB,QAAQ;IAC/E;AACF;AAEO,SAASrB,4BACdyC,OAAyB;IAEzB,MAAM,EACJI,cAAc,EACdxB,QAAQ,EACRP,IAAI,EACJC,MAAM,EACNR,WAAW,EACXuC,gBAAgB,EAChBC,qBAAqB,EACrB3B,QAAQ,EACRF,IAAI,EACJK,MAAM,EACNP,eAAe,EACfsB,WAAW,EACXN,OAAO,EACPW,UAAU,EACVf,WAAW,EACXoB,eAAe,EACfC,WAAW,EACXvB,WAAW,EACXgB,aAAa,EACblB,QAAQ,EACR0B,OAAO,EACPC,gBAAgB,EAChBC,SAAS,EACTC,WAAW,EACXC,cAAc,EACf,GAAGzC,aAAa4B;IAEjB,MAAMc,MAAMzC,SAAS;IACrB,MAAM0C,WAAWjC,WAAW;IAE5B,IAAIK,aAAa;QACfvB,MAAM;QACNoC,QAAQvB,IAAI,GAAG;IACjB;IAEA,IAAIuC;IACJ,IAAIC;IAEJ,yEAAyE;IACzE,IAAIX,yBAAyB,QAAQD,oBAAoB,MAAM;QAC7DW,gBAAgB,IAAIE,IAClBhE,oBAAoB8C,SAASP,OAAO,CAAC,OAAO,KAC5C,yBACA0B,QAAQ;QACV,IAAIb,uBAAuB;YACzBW,mBAAmBD,cAAcvB,OAAO,CAAC,YAAY;QACvD;IACF;IAEA,MAAM2B,yBAA2E;QAC/EC,WAAW;QACXtC,UAAUA,YAAYK;QACtBN;QACA4B;QACAnC,iBAAiBA,mBAAmBa;QACpC,gDAAgD;QAChDS,aAAaA,cAAcyB,OAAOzB,eAAeT;QACjDtB;QACAyB,SAASA,WAAWH;QACpBc;QACAvB,UAAUA,WAAW,MAAMS;QAC3Ba,eAAeA,gBAAgBqB,OAAOrB,iBAAiBb;QACvDmC,KAAKd;QACLI,gBAAgBA,kBAAkBzB;IACpC;IAEA,sCAAsC;IACtC,IAAK,MAAMoC,OAAOJ,uBAAwB;QACxC,IAAIA,sBAAsB,CAACI,IAAI,KAAKpC,WAAW;YAC7C,OAAOgC,sBAAsB,CAACI,IAAI;QACpC;IACF;IAEA,MAAMC,gBAAiD;QACrD7C;QACA8C,WAAWtB;QACXU;QACAxC,QAAQA,UAAU,CAACwC;QACnBP,iBAAiBA,mBAAmB;QACpC9B,MAAM,AAAC,CAACU,eAAeV,QAASW;QAChCuC,2BAA2BZ,WAAW,kBAAkB;QACxDK;QACAT;QACAC;QACAgB,uBAAuB;YACrBP,WAAW;YACXvD;YACA+D,WAAW1C,eAAeC;QAC5B;QACA0C,cAAcb;QACdc,WAAWf;QACXgB,mBAAmB;YACjBxB;YACAvB,aAAaA,eAAeG;YAC5B6C,QAAQ5B;YACR6B,mBAAmB5B;YACnBuB,WAAW1C,eAAeC;QAC5B;IACF;IAEA,OAAOqC;AACT;AAEO,SAAStE,kCACdY,WAAmB,EACnBsB,GAAe,EACfW,OAA2E;QAIxDX;IAFnB,OAAOnC,oBAAoB;QACzB,GAAG8C,OAAO;QACVC,eAAe,CAAC,GAACZ,mBAAAA,IAAIC,WAAW,qBAAfD,iBAAiBY,aAAa;QAC/CV,SAASjC,yBAAyB+B;QAClCa,YAAYC,IAAAA,8CAAsC,EAACpC,aAAasB;IAClE;AACF;AAEO,SAASnC,oBAAoB8C,OAAyB;IAC3D,MAAMmC,cAAc/E,4BAA4B4C;IAChD,OAAO,CAAC,CAAC,EAAEoC,UAAUpC,QAAQI,cAAc,CAACX,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE0C,YAAYhB,QAAQ,IAAI;AACrG;AAQO,SAASlE,mBAAmB+C,OAAyB;IAC1D,MAAMmC,cAAc/E,4BAA4B4C;IAChD,MAAMI,iBAAiBiC,IAAAA,qBAAW,EAACrC,QAAQI,cAAc;IACzD,OAAO,GAAGA,eAAe,QAAQ,EAAE+B,YAAYhB,QAAQ,IAAI;AAC7D;AAEO,SAAS/D,4BAA4B4C,OAAyB;IACnE,MAAM,EACJpB,QAAQ,EACRP,IAAI,EACJC,MAAM,EACNR,WAAW,EACXuC,gBAAgB,EAChBC,qBAAqB,EACrB7B,IAAI,EACJE,QAAQ,EACRG,MAAM,EACNP,eAAe,EACfsB,WAAW,EACXN,OAAO,EACPW,UAAU,EACVD,aAAa,EACbM,eAAe,EACfpB,WAAW,EACXuB,gBAAgB,EAChBF,WAAW,EACXvB,WAAW,EACXF,QAAQ,EACR0B,OAAO,EACPG,WAAW,EACXD,SAAS,EACV,GAAGvC,aAAa4B;IAEjB,MAAMc,MAAMQ,OAAOjD,SAAS;IAC5B,MAAM8D,cAAc,IAAIG,gBAAgB;QACtC1D,UAAU2D,mBAAmB3D;QAC7BkC;QACA,8BAA8B;QAC9B0B,KAAKlB,OAAO;IACd;IAEA,+DAA+D;IAC/D,IAAI,CAACnC,eAAeV,MAAM;QACxB0D,YAAYM,MAAM,CAAC,QAAQnB,OAAO7C;IACpC;IAEA,IAAI8B,iBAAiB;QACnB4B,YAAYM,MAAM,CAAC,mBAAmBnB,OAAOf;IAC/C;IAEA,IAAIjC,QAAQ;QACV6D,YAAYM,MAAM,CAAC,UAAUnB,OAAOhD;IACtC;IAEA,6FAA6F;IAC7F,uGAAuG;IACvG,gEAAgE;IAChE,IAAIQ,QAAQ;QACVqD,YAAYM,MAAM,CAAC,oBAAoB3D;IACzC;IACA,IAAIH,UAAU;QACZwD,YAAYM,MAAM,CAAC,sBAAsB;IAC3C;IACA,IAAI5C,aAAa;QACfsC,YAAYM,MAAM,CAAC,yBAAyBnB,OAAOzB;IACrD;IACA,IAAItB,iBAAiB;QACnB4D,YAAYM,MAAM,CAAC,6BAA6BnB,OAAO/C;IACzD;IACA,IAAIgB,SAAS;QACX4C,YAAYM,MAAM,CAAC,qBAAqBlD;IAC1C;IACA,IAAImB,oCAAAA,iBAAkBgC,MAAM,EAAE;QAC5BP,YAAYM,MAAM,CAAC,8BAA8BE,KAAKC,SAAS,CAAClC;IAClE;IACA,IAAIR,cAAc,MAAM;QACtBiC,YAAYM,MAAM,CAAC,wBAAwBvC;IAC7C;IACA,IAAID,eAAe;QACjBkC,YAAYM,MAAM,CAAC,2BAA2BnB,OAAOrB;IACvD;IACA,IAAIQ,SAAS;QACX0B,YAAYM,MAAM,CAAC,iBAAiBhC;IACtC;IAEA,IAAI3C,aAAa;QACfqE,YAAYM,MAAM,CAAC,wBAAwB3E;QAC3CqE,YAAYM,MAAM,CAAC,yBAAyB3E;IAC9C;IAEA,IAAIqB,aAAa;QACfgD,YAAYM,MAAM,CAAC,sBAAsBnB,OAAOnC;IAClD;IAEA,IAAIqB,aAAa;QACf2B,YAAYM,MAAM,CAAC,0BAA0BnB,OAAOd;IACtD;IACA,IAAIvB,aAAa;QACfkD,YAAYM,MAAM,CAAC,0BAA0BnB,OAAOrC;IACtD;IACA,IAAIF,UAAU;QACZoD,YAAYM,MAAM,CAAC,sBAAsBnB,OAAOvC;IAClD;IACA,IAAIsB,kBAAkB;QACpB8B,YAAYM,MAAM,CAAC,qBAAqBpC;IAC1C;IACA,IAAIC,uBAAuB;QACzB6B,YAAYM,MAAM,CAAC,kBAAkBnB,OAAOhB;IAC9C;IACA,IAAIxB,WAAW,UAAU;QACvBqD,YAAYM,MAAM,CAAC,6BAA6B;IAClD;IAEA,IAAI7B,eAAe,MAAM;QACvBuB,YAAYU,GAAG,CAAC,eAAevB,OAAOV;IACxC;IACA,IAAID,aAAa,MAAM;QACrBwB,YAAYU,GAAG,CAAC,aAAavB,OAAOX;IACtC;IAEA,OAAOwB;AACT;AAUO,SAASnF,6BAA6B8F,QAAgB;IAC3D,OAAOT,IAAAA,qBAAW,EAACS;AACrB;AAEO,SAASrF,uBAAuBsF,WAAmB;IACxD,MAAMC,MAAM,IAAI9B,IAAI6B,aAAa;IACjC,MAAME,iBAAiB,CAACzB;QACtB,MAAM0B,QAAQF,IAAIG,YAAY,CAACC,GAAG,CAAC5B;QACnC,IAAI6B,MAAMC,OAAO,CAACJ,QAAQ;YACxB,MAAM,IAAIK,MAAM,CAAC,0BAA0B,EAAE/B,KAAK;QACpD;QACA,OAAO0B;IACT;IAEA,IAAIM,WAAWR,IAAIQ,QAAQ;IAC3B,IAAIA,SAASC,QAAQ,CAAC,YAAY;QAChCD,WAAWA,SAASE,KAAK,CAAC,GAAG,CAAC,UAAUhB,MAAM;IAChD;IAEA,MAAM1C,UAA4B;QAChC3B,MAAMsF,SAASV,eAAe,UAAU,UAAU,gBAAgB;QAClE3E,QAAQqF,SAASV,eAAe,aAAa;QAC7CxE,MAAMkF,SAASV,eAAe,WAAW;QACzC/C,YAAY+C,eAAe,2BAA2B;QACtD9D,aAAawE,SAASV,eAAe,yBAAyB;QAC9DnF,aAAa8F,kBAAkBX,eAAe,4BAA4B;QAC1ErE,UAAUoE,IAAIG,YAAY,CAACC,GAAG,CAAC,eAAe;QAC9CzE,UAAUgF,SAASV,eAAe,yBAAyB;QAC3D7C,gBAAgBpD,6BAA6BwG;QAC7CvD,eAAe0D,SAASV,eAAe,8BAA8B;QACrEpD,aAAa8D,SAASV,eAAe,4BAA4B;QACjE1D,SAAS0D,eAAe,wBAAwB7D;QAChD,sFAAsF;QACtFN,QAAQ+E,aAAaZ,eAAe;QACpCtC,WAAWgD,SAASV,eAAe,gBAAgB;QACnDrC,aAAa+C,SAASV,eAAe,kBAAkB;IACzD;IAEA,OAAOjD;AACT;AAEA,SAAS2D,SAASG,KAAoB;IACpC,OAAOA,UAAU,UAAUA,UAAU;AACvC;AAEA,SAASF,kBAAkB9F,WAA+B;IACxD,IAAI,CAACA,aAAa;QAChB,OAAOsB;IACT;IACA,IAAI,CAAC;QAAC;QAAQ;QAAgB;KAAS,CAACU,QAAQ,CAAChC,cAAc;QAC7D,MAAM,IAAIyF,MAAM,CAAC,uEAAuE,CAAC;IAC3F;IACA,OAAOzF;AACT;AACA,SAAS+F,aAAa/E,MAAiC;IACrD,IAAI,CAACA,QAAQ;QACX,OAAOM;IACT;IACA,IAAI,CAAC;QAAC;KAAS,CAACU,QAAQ,CAAChB,SAAS;QAChC,MAAM,IAAIyE,MAAM,CAAC,8CAA8C,CAAC;IAClE;IACA,OAAOzE;AACT"}