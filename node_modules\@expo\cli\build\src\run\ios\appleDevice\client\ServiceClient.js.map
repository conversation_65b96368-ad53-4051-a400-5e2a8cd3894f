{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/ServiceClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { Socket } from 'net';\n\nimport { CommandError } from '../../../../utils/errors';\nimport { ProtocolClient } from '../protocol/AbstractProtocol';\n\nexport abstract class ServiceClient<T extends ProtocolClient> {\n  constructor(\n    public socket: Socket,\n    protected protocolClient: T\n  ) {}\n}\n\nexport class ResponseError extends CommandError {\n  constructor(\n    msg: string,\n    public response: any\n  ) {\n    super(msg);\n  }\n}\n"], "names": ["ResponseError", "ServiceClient", "constructor", "socket", "protocolClient", "CommandError", "msg", "response"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IAaYA,aAAa;eAAbA;;IAPSC,aAAa;eAAbA;;;wBAHO;AAGtB,MAAeA;IACpBC,YACE,AAAOC,MAAc,EACrB,AAAUC,cAAiB,CAC3B;aAFOD,SAAAA;aACGC,iBAAAA;IACT;AACL;AAEO,MAAMJ,sBAAsBK,oBAAY;IAC7CH,YACEI,GAAW,EACX,AAAOC,QAAa,CACpB;QACA,KAAK,CAACD,WAFCC,WAAAA;IAGT;AACF"}