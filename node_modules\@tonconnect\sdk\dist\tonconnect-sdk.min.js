var TonConnectSDK;(()=>{var e={4043:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tonConnectSdkVersion=void 0,t.tonConnectSdkVersion="3.0.6"},614:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WrongAddressError=void 0;var r=n(3748);Object.defineProperty(t,"WrongAddressError",{enumerable:!0,get:function(){return r.WrongAddressError}})},3748:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WrongAddressError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Passed address is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.WrongAddressError=o},9499:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ParseHexError=void 0;var r=n(3020);Object.defineProperty(t,"ParseHexError",{enumerable:!0,get:function(){return r.ParseHexError}})},3020:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ParseHexError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Passed hex is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.ParseHexError=o},1068:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DappMetadataError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Passed DappMetadata is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.DappMetadataError=o},2764:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.UnknownError=t.TonConnectError=void 0,o(n(2205),t),o(n(8474),t),o(n(3536),t),o(n(3847),t),o(n(614),t),o(n(9499),t);var i=n(6473);Object.defineProperty(t,"TonConnectError",{enumerable:!0,get:function(){return i.TonConnectError}});var s=n(3861);Object.defineProperty(t,"UnknownError",{enumerable:!0,get:function(){return s.UnknownError}})},8674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UserRejectsError=void 0;var r=n(657);Object.defineProperty(t,"UserRejectsError",{enumerable:!0,get:function(){return r.UserRejectsError}})},8351:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestContentErrorError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Passed `tonconnect-manifest.json` contains errors. Check format of your manifest. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest"}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.ManifestContentErrorError=o},3008:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ManifestNotFoundError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Manifest not found. Make sure you added `tonconnect-manifest.json` to the root of your app or passed correct manifestUrl. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest"}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.ManifestNotFoundError=o},657:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UserRejectsError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"User rejects the action in the wallet."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.UserRejectsError=o},3283:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(8674),t)},2205:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(3283),t),o(n(354),t)},3031:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BadRequestError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Request to the wallet contains errors."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.BadRequestError=o},354:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnknownAppError=t.BadRequestError=void 0;var r=n(3031);Object.defineProperty(t,"BadRequestError",{enumerable:!0,get:function(){return r.BadRequestError}});var o=n(801);Object.defineProperty(t,"UnknownAppError",{enumerable:!0,get:function(){return o.UnknownAppError}})},801:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnknownAppError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"App tries to send rpc request to the injected wallet while not connected."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.UnknownAppError=o},3536:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LocalstorageNotFoundError=void 0;var r=n(7701);Object.defineProperty(t,"LocalstorageNotFoundError",{enumerable:!0,get:function(){return r.LocalstorageNotFoundError}})},7701:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LocalstorageNotFoundError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Storage was not specified in the `DappMetadata` and default `localStorage` was not detected in the environment."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.LocalstorageNotFoundError=o},6473:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TonConnectError=void 0;class n extends Error{constructor(e,t){super(e,t),this.message=`${n.prefix} ${this.constructor.name}${this.info?": "+this.info:""}${e?"\n"+e:""}`,Object.setPrototypeOf(this,n.prototype)}get info(){return""}}t.TonConnectError=n,n.prefix="[TON_CONNECT_SDK_ERROR]"},3861:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnknownError=void 0;const r=n(6473);class o extends r.TonConnectError{constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.UnknownError=o},8474:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletNotInjectedError=t.WalletNotConnectedError=t.WalletAlreadyConnectedError=void 0;var r=n(4512);Object.defineProperty(t,"WalletAlreadyConnectedError",{enumerable:!0,get:function(){return r.WalletAlreadyConnectedError}});var o=n(5268);Object.defineProperty(t,"WalletNotConnectedError",{enumerable:!0,get:function(){return o.WalletNotConnectedError}});var i=n(6067);Object.defineProperty(t,"WalletNotInjectedError",{enumerable:!0,get:function(){return i.WalletNotInjectedError}})},4512:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletAlreadyConnectedError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Wallet connection called but wallet already connected. To avoid the error, disconnect the wallet before doing a new connection."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.WalletAlreadyConnectedError=o},5268:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletNotConnectedError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Send transaction or other protocol methods called while wallet is not connected."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.WalletNotConnectedError=o},6067:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletNotInjectedError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"There is an attempt to connect to the injected wallet while it is not exists in the webpage."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.WalletNotInjectedError=o},4610:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WalletNotSupportFeatureError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"Wallet doesn't support requested feature method."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.WalletNotSupportFeatureError=o},8814:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FetchWalletsError=void 0;const r=n(6473);class o extends r.TonConnectError{get info(){return"An error occurred while fetching the wallets list."}constructor(...e){super(...e),Object.setPrototypeOf(this,o.prototype)}}t.FetchWalletsError=o},3847:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FetchWalletsError=void 0;var r=n(8814);Object.defineProperty(t,"FetchWalletsError",{enumerable:!0,get:function(){return r.FetchWalletsError}})},1920:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.encodeTelegramUrlParameters=t.isTelegramUrl=t.toUserFriendlyAddress=t.SEND_TRANSACTION_ERROR_CODES=t.CONNECT_EVENT_ERROR_CODES=t.CONNECT_ITEM_ERROR_CODES=t.CHAIN=t.BrowserEventDispatcher=t.createVersionInfo=t.createResponseVersionEvent=t.createRequestVersionEvent=t.createTransactionSignedEvent=t.createTransactionSigningFailedEvent=t.createTransactionSentForSignatureEvent=t.createDisconnectionEvent=t.createConnectionRestoringCompletedEvent=t.createConnectionRestoringErrorEvent=t.createConnectionRestoringStartedEvent=t.createConnectionCompletedEvent=t.createConnectionErrorEvent=t.createConnectionStartedEvent=t.WalletsListManager=t.default=void 0,o(n(8884),t),o(n(5622),t),o(n(2764),t);var i=n(8884);Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.TonConnect}});var s=n(7419);Object.defineProperty(t,"WalletsListManager",{enumerable:!0,get:function(){return s.WalletsListManager}});var a=n(16);Object.defineProperty(t,"createConnectionStartedEvent",{enumerable:!0,get:function(){return a.createConnectionStartedEvent}}),Object.defineProperty(t,"createConnectionErrorEvent",{enumerable:!0,get:function(){return a.createConnectionErrorEvent}}),Object.defineProperty(t,"createConnectionCompletedEvent",{enumerable:!0,get:function(){return a.createConnectionCompletedEvent}}),Object.defineProperty(t,"createConnectionRestoringStartedEvent",{enumerable:!0,get:function(){return a.createConnectionRestoringStartedEvent}}),Object.defineProperty(t,"createConnectionRestoringErrorEvent",{enumerable:!0,get:function(){return a.createConnectionRestoringErrorEvent}}),Object.defineProperty(t,"createConnectionRestoringCompletedEvent",{enumerable:!0,get:function(){return a.createConnectionRestoringCompletedEvent}}),Object.defineProperty(t,"createDisconnectionEvent",{enumerable:!0,get:function(){return a.createDisconnectionEvent}}),Object.defineProperty(t,"createTransactionSentForSignatureEvent",{enumerable:!0,get:function(){return a.createTransactionSentForSignatureEvent}}),Object.defineProperty(t,"createTransactionSigningFailedEvent",{enumerable:!0,get:function(){return a.createTransactionSigningFailedEvent}}),Object.defineProperty(t,"createTransactionSignedEvent",{enumerable:!0,get:function(){return a.createTransactionSignedEvent}}),Object.defineProperty(t,"createRequestVersionEvent",{enumerable:!0,get:function(){return a.createRequestVersionEvent}}),Object.defineProperty(t,"createResponseVersionEvent",{enumerable:!0,get:function(){return a.createResponseVersionEvent}}),Object.defineProperty(t,"createVersionInfo",{enumerable:!0,get:function(){return a.createVersionInfo}});var c=n(9467);Object.defineProperty(t,"BrowserEventDispatcher",{enumerable:!0,get:function(){return c.BrowserEventDispatcher}});var l=n(9461);Object.defineProperty(t,"CHAIN",{enumerable:!0,get:function(){return l.CHAIN}}),Object.defineProperty(t,"CONNECT_ITEM_ERROR_CODES",{enumerable:!0,get:function(){return l.CONNECT_ITEM_ERROR_CODES}}),Object.defineProperty(t,"CONNECT_EVENT_ERROR_CODES",{enumerable:!0,get:function(){return l.CONNECT_EVENT_ERROR_CODES}}),Object.defineProperty(t,"SEND_TRANSACTION_ERROR_CODES",{enumerable:!0,get:function(){return l.SEND_TRANSACTION_ERROR_CODES}});var u=n(6754);Object.defineProperty(t,"toUserFriendlyAddress",{enumerable:!0,get:function(){return u.toUserFriendlyAddress}});var d=n(5370);Object.defineProperty(t,"isTelegramUrl",{enumerable:!0,get:function(){return d.isTelegramUrl}}),Object.defineProperty(t,"encodeTelegramUrlParameters",{enumerable:!0,get:function(){return d.encodeTelegramUrlParameters}})},5622:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(75),t),o(n(2913),t)},4354:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},2913:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(4354),t),o(n(3989),t)},3989:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},75:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isWalletInfoInjected=t.isWalletInfoRemote=t.isWalletInfoInjectable=t.isWalletInfoCurrentlyEmbedded=t.isWalletInfoCurrentlyInjected=void 0;var r=n(9546);Object.defineProperty(t,"isWalletInfoCurrentlyInjected",{enumerable:!0,get:function(){return r.isWalletInfoCurrentlyInjected}}),Object.defineProperty(t,"isWalletInfoCurrentlyEmbedded",{enumerable:!0,get:function(){return r.isWalletInfoCurrentlyEmbedded}}),Object.defineProperty(t,"isWalletInfoInjectable",{enumerable:!0,get:function(){return r.isWalletInfoInjectable}}),Object.defineProperty(t,"isWalletInfoRemote",{enumerable:!0,get:function(){return r.isWalletInfoRemote}}),Object.defineProperty(t,"isWalletInfoInjected",{enumerable:!0,get:function(){return r.isWalletInfoInjected}})},1431:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isWalletConnectionSourceJS=void 0,t.isWalletConnectionSourceJS=function(e){return"jsBridgeKey"in e}},9546:(e,t)=>{"use strict";function n(e){return r(e)&&e.injected}function r(e){return"jsBridgeKey"in e}Object.defineProperty(t,"__esModule",{value:!0}),t.isWalletInfoInjected=t.isWalletInfoRemote=t.isWalletInfoInjectable=t.isWalletInfoCurrentlyEmbedded=t.isWalletInfoCurrentlyInjected=void 0,t.isWalletInfoCurrentlyInjected=n,t.isWalletInfoCurrentlyEmbedded=function(e){return n(e)&&e.embedded},t.isWalletInfoInjectable=r,t.isWalletInfoRemote=function(e){return"bridgeUrl"in e},t.isWalletInfoInjected=function(e){return"jsBridgeKey"in e}},4527:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.connectErrorsParser=void 0;const r=n(2764),o=n(8351),i=n(3008),s=n(3861),a=n(9461),c={[a.CONNECT_EVENT_ERROR_CODES.UNKNOWN_ERROR]:s.UnknownError,[a.CONNECT_EVENT_ERROR_CODES.USER_REJECTS_ERROR]:r.UserRejectsError,[a.CONNECT_EVENT_ERROR_CODES.BAD_REQUEST_ERROR]:r.BadRequestError,[a.CONNECT_EVENT_ERROR_CODES.UNKNOWN_APP_ERROR]:r.UnknownAppError,[a.CONNECT_EVENT_ERROR_CODES.MANIFEST_NOT_FOUND_ERROR]:i.ManifestNotFoundError,[a.CONNECT_EVENT_ERROR_CODES.MANIFEST_CONTENT_ERROR]:o.ManifestContentErrorError};t.connectErrorsParser=new class{parseError(e){let t=s.UnknownError;return e.code in c&&(t=c[e.code]||s.UnknownError),new t(e.message)}}},2437:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RpcParser=void 0,t.RpcParser=class{isError(e){return"error"in e}}},7791:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendTransactionParser=void 0;const r=n(9461),o=n(2764),i=n(3861),s=n(2437),a={[r.SEND_TRANSACTION_ERROR_CODES.UNKNOWN_ERROR]:i.UnknownError,[r.SEND_TRANSACTION_ERROR_CODES.USER_REJECTS_ERROR]:o.UserRejectsError,[r.SEND_TRANSACTION_ERROR_CODES.BAD_REQUEST_ERROR]:o.BadRequestError,[r.SEND_TRANSACTION_ERROR_CODES.UNKNOWN_APP_ERROR]:o.UnknownAppError};class c extends s.RpcParser{convertToRpcRequest(e){return{method:"sendTransaction",params:[JSON.stringify(e)]}}parseAndThrowError(e){let t=i.UnknownError;throw e.error.code in a&&(t=a[e.error.code]||i.UnknownError),new t(e.error.message)}convertFromRpcResponse(e){return{boc:e.result}}}t.sendTransactionParser=new c},9478:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BridgeGateway=void 0;const o=n(9461),i=n(6473),s=n(9493),a=n(5370);n(618),n(3783);const c=n(6011),l=n(1583),u=n(1484),d=n(6764),h=n(9252);t.BridgeGateway=class{constructor(e,t,n,o,c){this.bridgeUrl=t,this.sessionId=n,this.listener=o,this.errorsListener=c,this.ssePath="events",this.postPath="message",this.heartbeatMessage="heartbeat",this.defaultTtl=300,this.defaultReconnectDelay=2e3,this.defaultResendDelay=5e3,this.eventSource=(0,u.createResource)(((e,t)=>r(this,void 0,void 0,(function*(){const n={bridgeUrl:this.bridgeUrl,ssePath:this.ssePath,sessionId:this.sessionId,bridgeGatewayStorage:this.bridgeGatewayStorage,errorHandler:this.errorsHandler.bind(this),messageHandler:this.messagesHandler.bind(this),signal:e,openingDeadlineMS:t};return yield function(e){return r(this,void 0,void 0,(function*(){return yield(0,d.timeout)(((t,n,o)=>r(this,void 0,void 0,(function*(){var s;const c=(0,h.createAbortController)(o.signal).signal;if(c.aborted)return void n(new i.TonConnectError("Bridge connection aborted"));const l=new URL((0,a.addPathToUrl)(e.bridgeUrl,e.ssePath));l.searchParams.append("client_id",e.sessionId);const u=yield e.bridgeGatewayStorage.getLastEventId();if(u&&l.searchParams.append("last_event_id",u),c.aborted)return void n(new i.TonConnectError("Bridge connection aborted"));const d=new EventSource(l.toString());d.onerror=o=>r(this,void 0,void 0,(function*(){if(c.aborted)return d.close(),void n(new i.TonConnectError("Bridge connection aborted"));try{const n=yield e.errorHandler(d,o);n!==d&&d.close(),n&&n!==d&&t(n)}catch(e){d.close(),n(e)}})),d.onopen=()=>{if(c.aborted)return d.close(),void n(new i.TonConnectError("Bridge connection aborted"));t(d)},d.onmessage=t=>{if(c.aborted)return d.close(),void n(new i.TonConnectError("Bridge connection aborted"));e.messageHandler(t)},null===(s=e.signal)||void 0===s||s.addEventListener("abort",(()=>{d.close(),n(new i.TonConnectError("Bridge connection aborted"))}))}))),{timeout:e.openingDeadlineMS,signal:e.signal})}))}(n)}))),(e=>r(this,void 0,void 0,(function*(){e.close()})))),this.bridgeGatewayStorage=new s.HttpBridgeGatewayStorage(e,t)}get isReady(){const e=this.eventSource.current();return(null==e?void 0:e.readyState)===EventSource.OPEN}get isClosed(){const e=this.eventSource.current();return(null==e?void 0:e.readyState)!==EventSource.OPEN}get isConnecting(){const e=this.eventSource.current();return(null==e?void 0:e.readyState)===EventSource.CONNECTING}registerSession(e){return r(this,void 0,void 0,(function*(){yield this.eventSource.create(null==e?void 0:e.signal,null==e?void 0:e.openingDeadlineMS)}))}send(e,t,n,s){var l;return r(this,void 0,void 0,(function*(){const u={};"number"==typeof s?u.ttl=s:(u.ttl=null==s?void 0:s.ttl,u.signal=null==s?void 0:s.signal,u.attempts=null==s?void 0:s.attempts);const d=new URL((0,a.addPathToUrl)(this.bridgeUrl,this.postPath));d.searchParams.append("client_id",this.sessionId),d.searchParams.append("to",t),d.searchParams.append("ttl",((null==u?void 0:u.ttl)||this.defaultTtl).toString()),d.searchParams.append("topic",n);const h=o.Base64.encode(e);yield(0,c.callForSuccess)((e=>r(this,void 0,void 0,(function*(){const t=yield this.post(d,h,e.signal);if(!t.ok)throw new i.TonConnectError(`Bridge send failed, status ${t.status}`)}))),{attempts:null!==(l=null==u?void 0:u.attempts)&&void 0!==l?l:Number.MAX_SAFE_INTEGER,delayMs:this.defaultResendDelay,signal:null==u?void 0:u.signal})}))}pause(){this.eventSource.dispose().catch((e=>(0,l.logError)(`Bridge pause failed, ${e}`)))}unPause(){return r(this,void 0,void 0,(function*(){yield this.eventSource.recreate(0)}))}close(){return r(this,void 0,void 0,(function*(){yield this.eventSource.dispose().catch((e=>(0,l.logError)(`Bridge close failed, ${e}`)))}))}setListener(e){this.listener=e}setErrorsListener(e){this.errorsListener=e}post(e,t,n){return r(this,void 0,void 0,(function*(){const r=yield fetch(e,{method:"post",body:t,signal:n});if(!r.ok)throw new i.TonConnectError(`Bridge send failed, status ${r.status}`);return r}))}errorsHandler(e,t){return r(this,void 0,void 0,(function*(){if(this.isConnecting)throw e.close(),new i.TonConnectError("Bridge error, failed to connect");if(!this.isReady){if(this.isClosed)return e.close(),(0,l.logDebug)(`Bridge reconnecting, ${this.defaultReconnectDelay}ms delay`),yield this.eventSource.recreate(this.defaultReconnectDelay);throw new i.TonConnectError("Bridge error, unknown state")}try{this.errorsListener(t)}catch(e){}}))}messagesHandler(e){return r(this,void 0,void 0,(function*(){if(e.data===this.heartbeatMessage)return;if(yield this.bridgeGatewayStorage.storeLastEventId(e.lastEventId),this.isClosed)return;let t;try{t=JSON.parse(e.data)}catch(e){throw new i.TonConnectError(`Bridge message parse failed, message ${e.data}`)}this.listener(t)}))}}},8977:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BridgeProvider=void 0;const o=n(9461),i=n(6473),s=n(9478),a=n(9465),c=n(6180),l=n(3160),u=n(1583),d=n(5370),h=n(6011),f=n(9252);class p{constructor(e,t){this.storage=e,this.walletConnectionSource=t,this.type="http",this.standardUniversalLink="tc://",this.pendingRequests=new Map,this.session=null,this.gateway=null,this.pendingGateways=[],this.listeners=[],this.defaultOpeningDeadlineMS=12e3,this.defaultRetryTimeoutMS=2e3,this.connectionStorage=new c.BridgeConnectionStorage(e)}static fromStorage(e){return r(this,void 0,void 0,(function*(){const t=new c.BridgeConnectionStorage(e),n=yield t.getHttpConnection();return(0,a.isPendingConnectionHttp)(n)?new p(e,n.connectionSource):new p(e,{bridgeUrl:n.session.bridgeUrl})}))}connect(e,t){var n;const i=(0,f.createAbortController)(null==t?void 0:t.signal);null===(n=this.abortController)||void 0===n||n.abort(),this.abortController=i,this.closeGateways();const s=new o.SessionCrypto;this.session={sessionCrypto:s,bridgeUrl:"bridgeUrl"in this.walletConnectionSource?this.walletConnectionSource.bridgeUrl:""},this.connectionStorage.storeConnection({type:"http",connectionSource:this.walletConnectionSource,sessionCrypto:s}).then((()=>r(this,void 0,void 0,(function*(){i.signal.aborted||(yield(0,h.callForSuccess)((e=>{var n;return this.openGateways(s,{openingDeadlineMS:null!==(n=null==t?void 0:t.openingDeadlineMS)&&void 0!==n?n:this.defaultOpeningDeadlineMS,signal:null==e?void 0:e.signal})}),{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:i.signal}))}))));const a="universalLink"in this.walletConnectionSource&&this.walletConnectionSource.universalLink?this.walletConnectionSource.universalLink:this.standardUniversalLink;return this.generateUniversalLink(a,e)}restoreConnection(e){var t,n;return r(this,void 0,void 0,(function*(){const r=(0,f.createAbortController)(null==e?void 0:e.signal);if(null===(t=this.abortController)||void 0===t||t.abort(),this.abortController=r,r.signal.aborted)return;this.closeGateways();const o=yield this.connectionStorage.getHttpConnection();if(!o)return;if(r.signal.aborted)return;const c=null!==(n=null==e?void 0:e.openingDeadlineMS)&&void 0!==n?n:this.defaultOpeningDeadlineMS;if((0,a.isPendingConnectionHttp)(o))return this.session={sessionCrypto:o.sessionCrypto,bridgeUrl:"bridgeUrl"in this.walletConnectionSource?this.walletConnectionSource.bridgeUrl:""},yield this.openGateways(o.sessionCrypto,{openingDeadlineMS:c,signal:null==r?void 0:r.signal});if(Array.isArray(this.walletConnectionSource))throw new i.TonConnectError("Internal error. Connection source is array while WalletConnectionSourceHTTP was expected.");if(this.session=o.session,this.gateway&&((0,u.logDebug)("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.gateway=new s.BridgeGateway(this.storage,this.walletConnectionSource.bridgeUrl,o.session.sessionCrypto.sessionId,this.gatewayListener.bind(this),this.gatewayErrorsListener.bind(this)),!r.signal.aborted){this.listeners.forEach((e=>e(o.connectEvent)));try{yield(0,h.callForSuccess)((e=>this.gateway.registerSession({openingDeadlineMS:c,signal:e.signal})),{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:r.signal})}catch(e){return void(yield this.disconnect({signal:r.signal}))}}}))}sendRequest(e,t){const n={};return"function"==typeof t?n.onRequestSent=t:(n.onRequestSent=null==t?void 0:t.onRequestSent,n.signal=null==t?void 0:t.signal,n.attempts=null==t?void 0:t.attempts),new Promise(((t,s)=>r(this,void 0,void 0,(function*(){var r;if(!this.gateway||!this.session||!("walletPublicKey"in this.session))throw new i.TonConnectError("Trying to send bridge request without session");const a=(yield this.connectionStorage.getNextRpcRequestId()).toString();yield this.connectionStorage.increaseNextRpcRequestId(),(0,u.logDebug)("Send http-bridge request:",Object.assign(Object.assign({},e),{id:a}));const c=this.session.sessionCrypto.encrypt(JSON.stringify(Object.assign(Object.assign({},e),{id:a})),(0,o.hexToByteArray)(this.session.walletPublicKey));try{yield this.gateway.send(c,this.session.walletPublicKey,e.method,{attempts:null==n?void 0:n.attempts,signal:null==n?void 0:n.signal}),null===(r=null==n?void 0:n.onRequestSent)||void 0===r||r.call(n),this.pendingRequests.set(a.toString(),t)}catch(e){s(e)}}))))}closeConnection(){this.closeGateways(),this.listeners=[],this.session=null,this.gateway=null}disconnect(e){return r(this,void 0,void 0,(function*(){return new Promise((t=>r(this,void 0,void 0,(function*(){let n=!1,r=null;const o=()=>{n||(n=!0,this.removeBridgeAndSession().then(t))};try{this.closeGateways();const t=(0,f.createAbortController)(null==e?void 0:e.signal);r=setTimeout((()=>{t.abort()}),this.defaultOpeningDeadlineMS),yield this.sendRequest({method:"disconnect",params:[]},{onRequestSent:o,signal:t.signal,attempts:1})}catch(e){(0,u.logDebug)("Disconnect error:",e),n||this.removeBridgeAndSession().then(t)}finally{r&&clearTimeout(r),o()}}))))}))}listen(e){return this.listeners.push(e),()=>this.listeners=this.listeners.filter((t=>t!==e))}pause(){var e;null===(e=this.gateway)||void 0===e||e.pause(),this.pendingGateways.forEach((e=>e.pause()))}unPause(){return r(this,void 0,void 0,(function*(){const e=this.pendingGateways.map((e=>e.unPause()));this.gateway&&e.push(this.gateway.unPause()),yield Promise.all(e)}))}pendingGatewaysListener(e,t,n){return r(this,void 0,void 0,(function*(){if(this.pendingGateways.includes(e))return this.closeGateways({except:e}),this.gateway&&((0,u.logDebug)("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.session.bridgeUrl=t,this.gateway=e,this.gateway.setErrorsListener(this.gatewayErrorsListener.bind(this)),this.gateway.setListener(this.gatewayListener.bind(this)),this.gatewayListener(n);yield e.close()}))}gatewayListener(e){return r(this,void 0,void 0,(function*(){const t=JSON.parse(this.session.sessionCrypto.decrypt(o.Base64.decode(e.message).toUint8Array(),(0,o.hexToByteArray)(e.from)));if((0,u.logDebug)("Wallet message received:",t),!("event"in t)){const e=t.id.toString(),n=this.pendingRequests.get(e);return n?(n(t),void this.pendingRequests.delete(e)):void(0,u.logDebug)(`Response id ${e} doesn't match any request's id`)}if(void 0!==t.id){const e=yield this.connectionStorage.getLastWalletEventId();if(void 0!==e&&t.id<=e)return void(0,u.logError)(`Received event id (=${t.id}) must be greater than stored last wallet event id (=${e}) `);"connect"!==t.event&&(yield this.connectionStorage.storeLastWalletEventId(t.id))}const n=this.listeners;"connect"===t.event&&(yield this.updateSession(t,e.from)),"disconnect"===t.event&&((0,u.logDebug)("Removing bridge and session: received disconnect event"),yield this.removeBridgeAndSession()),n.forEach((e=>e(t)))}))}gatewayErrorsListener(e){return r(this,void 0,void 0,(function*(){throw new i.TonConnectError(`Bridge error ${JSON.stringify(e)}`)}))}updateSession(e,t){return r(this,void 0,void 0,(function*(){this.session=Object.assign(Object.assign({},this.session),{walletPublicKey:t});const n=e.payload.items.find((e=>"ton_addr"===e.name)),r=Object.assign(Object.assign({},e),{payload:Object.assign(Object.assign({},e.payload),{items:[n]})});yield this.connectionStorage.storeConnection({type:"http",session:this.session,lastWalletEventId:e.id,connectEvent:r,nextRpcRequestId:0})}))}removeBridgeAndSession(){return r(this,void 0,void 0,(function*(){this.closeConnection(),yield this.connectionStorage.removeConnection()}))}generateUniversalLink(e,t){return(0,d.isTelegramUrl)(e)?this.generateTGUniversalLink(e,t):this.generateRegularUniversalLink(e,t)}generateRegularUniversalLink(e,t){const n=new URL(e);return n.searchParams.append("v",l.PROTOCOL_VERSION.toString()),n.searchParams.append("id",this.session.sessionCrypto.sessionId),n.searchParams.append("r",JSON.stringify(t)),n.toString()}generateTGUniversalLink(e,t){const n=this.generateRegularUniversalLink("about:blank",t).split("?")[1],r="tonconnect-"+(0,d.encodeTelegramUrlParameters)(n),o=this.convertToDirectLink(e),i=new URL(o);return i.searchParams.append("startapp",r),i.toString()}convertToDirectLink(e){const t=new URL(e);return t.searchParams.has("attach")&&(t.searchParams.delete("attach"),t.pathname+="/start"),t.toString()}openGateways(e,t){return r(this,void 0,void 0,(function*(){return Array.isArray(this.walletConnectionSource)?(this.pendingGateways.map((e=>e.close().catch())),this.pendingGateways=this.walletConnectionSource.map((t=>{const n=new s.BridgeGateway(this.storage,t.bridgeUrl,e.sessionId,(()=>{}),(()=>{}));return n.setListener((e=>this.pendingGatewaysListener(n,t.bridgeUrl,e))),n})),void(yield Promise.allSettled(this.pendingGateways.map((e=>(0,h.callForSuccess)((n=>{var r;return this.pendingGateways.some((t=>t===e))?e.registerSession({openingDeadlineMS:null!==(r=null==t?void 0:t.openingDeadlineMS)&&void 0!==r?r:this.defaultOpeningDeadlineMS,signal:n.signal}):e.close()}),{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:null==t?void 0:t.signal})))))):(this.gateway&&((0,u.logDebug)("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.gateway=new s.BridgeGateway(this.storage,this.walletConnectionSource.bridgeUrl,e.sessionId,this.gatewayListener.bind(this),this.gatewayErrorsListener.bind(this)),yield this.gateway.registerSession({openingDeadlineMS:null==t?void 0:t.openingDeadlineMS,signal:null==t?void 0:t.signal}))}))}closeGateways(e){var t;null===(t=this.gateway)||void 0===t||t.close(),this.pendingGateways.filter((t=>t!==(null==e?void 0:e.except))).forEach((e=>e.close())),this.pendingGateways=[]}}t.BridgeProvider=p},9465:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isPendingConnectionHttp=void 0,t.isPendingConnectionHttp=function(e){return!("connectEvent"in e)}},1328:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.InjectedProvider=void 0;const o=n(6067),i=n(5283),s=n(6180),a=n(9590),c=n(3160),l=n(1583);class u{constructor(e,t){this.injectedWalletKey=t,this.type="injected",this.unsubscribeCallback=null,this.listenSubscriptions=!1,this.listeners=[];const n=u.window;if(!u.isWindowContainsWallet(n,t))throw new o.WalletNotInjectedError;this.connectionStorage=new s.BridgeConnectionStorage(e),this.injectedWallet=n[t].tonconnect}static fromStorage(e){return r(this,void 0,void 0,(function*(){const t=new s.BridgeConnectionStorage(e),n=yield t.getInjectedConnection();return new u(e,n.jsBridgeKey)}))}static isWalletInjected(e){return u.isWindowContainsWallet(this.window,e)}static isInsideWalletBrowser(e){return!!u.isWindowContainsWallet(this.window,e)&&this.window[e].tonconnect.isWalletBrowser}static getCurrentlyInjectedWallets(){return this.window?(0,a.tryGetWindowKeys)().filter((([e,t])=>(0,i.isJSBridgeWithMetadata)(t))).map((([e,t])=>({name:t.tonconnect.walletInfo.name,appName:t.tonconnect.walletInfo.app_name,aboutUrl:t.tonconnect.walletInfo.about_url,imageUrl:t.tonconnect.walletInfo.image,tondns:t.tonconnect.walletInfo.tondns,jsBridgeKey:e,injected:!0,embedded:t.tonconnect.isWalletBrowser,platforms:t.tonconnect.walletInfo.platforms}))):[]}static isWindowContainsWallet(e,t){return!!e&&t in e&&"object"==typeof e[t]&&"tonconnect"in e[t]}connect(e){this._connect(c.PROTOCOL_VERSION,e)}restoreConnection(){return r(this,void 0,void 0,(function*(){try{(0,l.logDebug)("Injected Provider restoring connection...");const e=yield this.injectedWallet.restoreConnection();(0,l.logDebug)("Injected Provider restoring connection response",e),"connect"===e.event?(this.makeSubscriptions(),this.listeners.forEach((t=>t(e)))):yield this.connectionStorage.removeConnection()}catch(e){yield this.connectionStorage.removeConnection(),console.error(e)}}))}closeConnection(){this.listenSubscriptions&&this.injectedWallet.disconnect(),this.closeAllListeners()}disconnect(){return r(this,void 0,void 0,(function*(){return new Promise((e=>{const t=()=>{this.closeAllListeners(),this.connectionStorage.removeConnection().then(e)};try{this.injectedWallet.disconnect(),t()}catch(e){(0,l.logDebug)(e),this.sendRequest({method:"disconnect",params:[]},t)}}))}))}closeAllListeners(){var e;this.listenSubscriptions=!1,this.listeners=[],null===(e=this.unsubscribeCallback)||void 0===e||e.call(this)}listen(e){return this.listeners.push(e),()=>this.listeners=this.listeners.filter((t=>t!==e))}sendRequest(e,t){var n;return r(this,void 0,void 0,(function*(){const r={};"function"==typeof t?r.onRequestSent=t:(r.onRequestSent=null==t?void 0:t.onRequestSent,r.signal=null==t?void 0:t.signal);const o=(yield this.connectionStorage.getNextRpcRequestId()).toString();yield this.connectionStorage.increaseNextRpcRequestId(),(0,l.logDebug)("Send injected-bridge request:",Object.assign(Object.assign({},e),{id:o}));const i=this.injectedWallet.send(Object.assign(Object.assign({},e),{id:o}));return i.then((e=>(0,l.logDebug)("Wallet message received:",e))),null===(n=null==r?void 0:r.onRequestSent)||void 0===n||n.call(r),i}))}_connect(e,t){return r(this,void 0,void 0,(function*(){try{(0,l.logDebug)(`Injected Provider connect request: protocolVersion: ${e}, message:`,t);const n=yield this.injectedWallet.connect(e,t);(0,l.logDebug)("Injected Provider connect response:",n),"connect"===n.event&&(yield this.updateSession(),this.makeSubscriptions()),this.listeners.forEach((e=>e(n)))}catch(e){(0,l.logDebug)("Injected Provider connect error:",e);const t={event:"connect_error",payload:{code:0,message:null==e?void 0:e.toString()}};this.listeners.forEach((e=>e(t)))}}))}makeSubscriptions(){this.listenSubscriptions=!0,this.unsubscribeCallback=this.injectedWallet.listen((e=>{(0,l.logDebug)("Wallet message received:",e),this.listenSubscriptions&&this.listeners.forEach((t=>t(e))),"disconnect"===e.event&&this.disconnect()}))}updateSession(){return this.connectionStorage.storeConnection({type:"injected",jsBridgeKey:this.injectedWalletKey,nextRpcRequestId:0})}}t.InjectedProvider=u,u.window=(0,a.getWindow)()},5283:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isJSBridgeWithMetadata=void 0;const r=n(6415);t.isJSBridgeWithMetadata=function(e){try{return!(!(0,r.hasProperty)(e,"tonconnect")||!(0,r.hasProperty)(e.tonconnect,"walletInfo"))&&(0,r.hasProperties)(e.tonconnect.walletInfo,["name","app_name","image","about_url","platforms"])}catch(e){return!1}}},3041:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FALLBACK_WALLETS_LIST=void 0,t.FALLBACK_WALLETS_LIST=[{app_name:"telegram-wallet",name:"Wallet",image:"https://wallet.tg/images/logo-288.png",about_url:"https://wallet.tg/",universal_url:"https://t.me/wallet?attach=wallet",bridge:[{type:"sse",url:"https://walletbot.me/tonconnect-bridge/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"tonkeeper",name:"Tonkeeper",image:"https://tonkeeper.com/assets/tonconnect-icon.png",tondns:"tonkeeper.ton",about_url:"https://tonkeeper.com",universal_url:"https://app.tonkeeper.com/ton-connect",deepLink:"tonkeeper-tc://",bridge:[{type:"sse",url:"https://bridge.tonapi.io/bridge"},{type:"js",key:"tonkeeper"}],platforms:["ios","android","chrome","firefox","macos"]},{app_name:"mytonwallet",name:"MyTonWallet",image:"https://static.mytonwallet.io/icon-256.png",about_url:"https://mytonwallet.io",universal_url:"https://connect.mytonwallet.org",bridge:[{type:"js",key:"mytonwallet"},{type:"sse",url:"https://tonconnectbridge.mytonwallet.org/bridge/"}],platforms:["chrome","windows","macos","linux","ios","android","firefox"]},{app_name:"tonhub",name:"Tonhub",image:"https://tonhub.com/tonconnect_logo.png",about_url:"https://tonhub.com",universal_url:"https://tonhub.com/ton-connect",bridge:[{type:"js",key:"tonhub"},{type:"sse",url:"https://connect.tonhubapi.com/tonconnect"}],platforms:["ios","android"]},{app_name:"bitgetTonWallet",name:"Bitget Wallet",image:"https://raw.githubusercontent.com/bitgetwallet/download/refs/heads/main/logo/png/bitget_wallet_logo_288_mini.png",about_url:"https://web3.bitget.com",deepLink:"bitkeep://",bridge:[{type:"js",key:"bitgetTonWallet"},{type:"sse",url:"https://ton-connect-bridge.bgwapi.io/bridge"}],platforms:["ios","android","chrome"],universal_url:"https://bkcode.vip/ton-connect"},{app_name:"okxMiniWallet",name:"OKX Mini Wallet",image:"https://static.okx.com/cdn/assets/imgs/2411/8BE1A4A434D8F58A.png",about_url:"https://www.okx.com/web3",universal_url:"https://t.me/OKX_WALLET_BOT?attach=wallet",bridge:[{type:"sse",url:"https://www.okx.com/tonbridge/discover/rpc/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"binanceWeb3TonWallet",name:"Binance Web3 Wallet",image:"https://public.bnbstatic.com/static/binance-w3w/ton-provider/binancew3w.png",about_url:"https://www.binance.com/en/web3wallet",deepLink:"bnc://app.binance.com/cedefi/ton-connect",bridge:[{type:"js",key:"binancew3w"},{type:"sse",url:"https://wallet.binance.com/tonbridge/bridge"}],platforms:["ios","android","macos","windows","linux"],universal_url:"https://app.binance.com/cedefi/ton-connect"},{app_name:"fintopio-tg",name:"Fintopio",image:"https://fintopio.com/tonconnect-icon.png",about_url:"https://fintopio.com",universal_url:"https://t.me/fintopio?attach=wallet",bridge:[{type:"sse",url:"https://wallet-bridge.fintopio.com/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"okxTonWallet",name:"OKX Wallet",image:"https://static.okx.com/cdn/assets/imgs/247/58E63FEA47A2B7D7.png",about_url:"https://www.okx.com/web3",universal_url:"https://www.okx.com/download?appendQuery=true&deeplink=okx://web3/wallet/tonconnect",bridge:[{type:"js",key:"okxTonWallet"},{type:"sse",url:"https://www.okx.com/tonbridge/discover/rpc/bridge"}],platforms:["chrome","safari","firefox","ios","android"]},{app_name:"hot",name:"HOT",image:"https://raw.githubusercontent.com/hot-dao/media/main/logo.png",about_url:"https://hot-labs.org/",universal_url:"https://t.me/herewalletbot?attach=wallet",bridge:[{type:"sse",url:"https://sse-bridge.hot-labs.org"},{type:"js",key:"hotWallet"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"bybitTonWallet",name:"Bybit Wallet",image:"https://raw.githubusercontent.com/bybit-web3/bybit-web3.github.io/main/docs/images/bybit-logo.png",about_url:"https://www.bybit.com/web3",universal_url:"https://app.bybit.com/ton-connect",deepLink:"bybitapp://",bridge:[{type:"js",key:"bybitTonWallet"},{type:"sse",url:"https://api-node.bybit.com/spot/api/web3/bridge/ton/bridge"}],platforms:["ios","android","chrome"]},{app_name:"dewallet",name:"DeWallet",image:"https://raw.githubusercontent.com/delab-team/manifests-images/main/WalletAvatar.png",about_url:"https://delabwallet.com",universal_url:"https://t.me/dewallet?attach=wallet",bridge:[{type:"sse",url:"https://bridge.dewallet.pro/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"safepalwallet",name:"SafePal",image:"https://s.pvcliping.com/web/public_image/SafePal_x288.png",tondns:"",about_url:"https://www.safepal.com",universal_url:"https://link.safepal.io/ton-connect",deepLink:"safepal-tc://",bridge:[{type:"sse",url:"https://ton-bridge.safepal.com/tonbridge/v1/bridge"},{type:"js",key:"safepalwallet"}],platforms:["ios","android","chrome","firefox"]},{app_name:"GateWallet",name:"GateWallet",image:"https://img.gatedataimg.com/prd-ordinal-imgs/036f07bb8730716e/gateio-0925.png",about_url:"https://www.gate.io/",bridge:[{type:"js",key:"gatetonwallet"},{type:"sse",url:"https://dapp.gateio.services/tonbridge_api/bridge/v1"}],platforms:["ios","android"],universal_url:"https://gateio.go.link/gateio/web3?adj_t=1ff8khdw_1fu4ccc7"},{app_name:"openmask",name:"OpenMask",image:"https://raw.githubusercontent.com/OpenProduct/openmask-extension/main/public/openmask-logo-288.png",about_url:"https://www.openmask.app/",bridge:[{type:"js",key:"openmask"}],platforms:["chrome"]},{app_name:"BitgetWeb3",name:"BitgetWeb3",image:"https://img.bitgetimg.com/image/third/1731638059795.png",about_url:"​https://www.bitget.com",universal_url:"https://t.me/BitgetOfficialBot?attach=wallet",bridge:[{type:"sse",url:"https://ton-connect-bridge.bgwapi.io/bridge"}],platforms:["ios","android","windows","macos","linux"]},{app_name:"tobi",name:"Tobi",image:"https://app.tobiwallet.app/icons/logo-288.png",about_url:"https://tobi.fun",universal_url:"https://t.me/TobiCopilotBot?attach=wallet",bridge:[{type:"sse",url:"https://ton-bridge.tobiwallet.app/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"xtonwallet",name:"XTONWallet",image:"https://xtonwallet.com/assets/img/icon-256-back.png",about_url:"https://xtonwallet.com",bridge:[{type:"js",key:"xtonwallet"}],platforms:["chrome","firefox"]},{app_name:"tonwallet",name:"TON Wallet",image:"https://wallet.ton.org/assets/ui/qr-logo.png",about_url:"https://chrome.google.com/webstore/detail/ton-wallet/nphplpgoakhhjchkkhmiggakijnkhfnd",bridge:[{type:"js",key:"tonwallet"}],platforms:["chrome"]}]},3160:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PROTOCOL_VERSION=void 0,t.PROTOCOL_VERSION=2},6180:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BridgeConnectionStorage=void 0;const o=n(9461),i=n(2764),s=n(9465);t.BridgeConnectionStorage=class{constructor(e){this.storage=e,this.storeKey="ton-connect-storage_bridge-connection"}storeConnection(e){return r(this,void 0,void 0,(function*(){if("injected"===e.type)return this.storage.setItem(this.storeKey,JSON.stringify(e));if(!(0,s.isPendingConnectionHttp)(e)){const t={sessionKeyPair:e.session.sessionCrypto.stringifyKeypair(),walletPublicKey:e.session.walletPublicKey,bridgeUrl:e.session.bridgeUrl},n={type:"http",connectEvent:e.connectEvent,session:t,lastWalletEventId:e.lastWalletEventId,nextRpcRequestId:e.nextRpcRequestId};return this.storage.setItem(this.storeKey,JSON.stringify(n))}const t={type:"http",connectionSource:e.connectionSource,sessionCrypto:e.sessionCrypto.stringifyKeypair()};return this.storage.setItem(this.storeKey,JSON.stringify(t))}))}removeConnection(){return r(this,void 0,void 0,(function*(){return this.storage.removeItem(this.storeKey)}))}getConnection(){return r(this,void 0,void 0,(function*(){const e=yield this.storage.getItem(this.storeKey);if(!e)return null;const t=JSON.parse(e);if("injected"===t.type)return t;if("connectEvent"in t){const e=new o.SessionCrypto(t.session.sessionKeyPair);return{type:"http",connectEvent:t.connectEvent,lastWalletEventId:t.lastWalletEventId,nextRpcRequestId:t.nextRpcRequestId,session:{sessionCrypto:e,bridgeUrl:t.session.bridgeUrl,walletPublicKey:t.session.walletPublicKey}}}return{type:"http",sessionCrypto:new o.SessionCrypto(t.sessionCrypto),connectionSource:t.connectionSource}}))}getHttpConnection(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();if(!e)throw new i.TonConnectError("Trying to read HTTP connection source while nothing is stored");if("injected"===e.type)throw new i.TonConnectError("Trying to read HTTP connection source while injected connection is stored");return e}))}getHttpPendingConnection(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();if(!e)throw new i.TonConnectError("Trying to read HTTP connection source while nothing is stored");if("injected"===e.type)throw new i.TonConnectError("Trying to read HTTP connection source while injected connection is stored");if(!(0,s.isPendingConnectionHttp)(e))throw new i.TonConnectError("Trying to read HTTP-pending connection while http connection is stored");return e}))}getInjectedConnection(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();if(!e)throw new i.TonConnectError("Trying to read Injected bridge connection source while nothing is stored");if("http"===(null==e?void 0:e.type))throw new i.TonConnectError("Trying to read Injected bridge connection source while HTTP connection is stored");return e}))}storedConnectionType(){return r(this,void 0,void 0,(function*(){const e=yield this.storage.getItem(this.storeKey);return e?JSON.parse(e).type:null}))}storeLastWalletEventId(e){return r(this,void 0,void 0,(function*(){const t=yield this.getConnection();if(t&&"http"===t.type&&!(0,s.isPendingConnectionHttp)(t))return t.lastWalletEventId=e,this.storeConnection(t)}))}getLastWalletEventId(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();if(e&&"lastWalletEventId"in e)return e.lastWalletEventId}))}increaseNextRpcRequestId(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();if(e&&"nextRpcRequestId"in e){const t=e.nextRpcRequestId||0;return e.nextRpcRequestId=t+1,this.storeConnection(e)}}))}getNextRpcRequestId(){return r(this,void 0,void 0,(function*(){const e=yield this.getConnection();return e&&"nextRpcRequestId"in e&&e.nextRpcRequestId||0}))}}},2245:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultStorage=void 0;const o=n(9590);t.DefaultStorage=class{constructor(){this.localStorage=(0,o.tryGetLocalStorage)()}getItem(e){return r(this,void 0,void 0,(function*(){return this.localStorage.getItem(e)}))}removeItem(e){return r(this,void 0,void 0,(function*(){this.localStorage.removeItem(e)}))}setItem(e,t){return r(this,void 0,void 0,(function*(){this.localStorage.setItem(e,t)}))}}},9493:function(e,t){"use strict";var n=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.HttpBridgeGatewayStorage=void 0,t.HttpBridgeGatewayStorage=class{constructor(e,t){this.storage=e,this.storeKey="ton-connect-storage_http-bridge-gateway::"+t}storeLastEventId(e){return n(this,void 0,void 0,(function*(){return this.storage.setItem(this.storeKey,e)}))}removeLastEventId(){return n(this,void 0,void 0,(function*(){return this.storage.removeItem(this.storeKey)}))}getLastEventId(){return n(this,void 0,void 0,(function*(){return(yield this.storage.getItem(this.storeKey))||null}))}}},9934:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InMemoryStorage=void 0;class n{constructor(){this.storage={}}static getInstance(){return n.instance||(n.instance=new n),n.instance}get length(){return Object.keys(this.storage).length}clear(){this.storage={}}getItem(e){var t;return null!==(t=this.storage[e])&&void 0!==t?t:null}key(e){var t;const n=Object.keys(this.storage);return e<0||e>=n.length?null:null!==(t=n[e])&&void 0!==t?t:null}removeItem(e){delete this.storage[e]}setItem(e,t){this.storage[e]=t}}t.InMemoryStorage=n},8884:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))},o=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};Object.defineProperty(t,"__esModule",{value:!0}),t.TonConnect=void 0;const i=n(1068),s=n(8351),a=n(3008),c=n(6473),l=n(4512),u=n(5268),d=n(1431),h=n(4527),f=n(7791),p=n(8977),g=n(1328),v=n(6180),y=n(2245),b=n(9590),w=n(7419),E=n(1002),m=n(6011),_=n(1583),C=n(9252),O=n(9489),S=n(4043);class R{constructor(e){if(this.walletsList=new w.WalletsListManager,this._wallet=null,this.provider=null,this.statusChangeSubscriptions=[],this.statusChangeErrorSubscriptions=[],this.dappSettings={manifestUrl:(null==e?void 0:e.manifestUrl)||(0,b.getWebPageManifest)(),storage:(null==e?void 0:e.storage)||new y.DefaultStorage},this.walletsList=new w.WalletsListManager({walletsListSource:null==e?void 0:e.walletsListSource,cacheTTLMs:null==e?void 0:e.walletsListCacheTTLMs}),this.tracker=new O.TonConnectTracker({eventDispatcher:null==e?void 0:e.eventDispatcher,tonConnectSdkVersion:S.tonConnectSdkVersion}),!this.dappSettings.manifestUrl)throw new i.DappMetadataError("Dapp tonconnect-manifest.json must be specified if window.location.origin is undefined. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest");this.bridgeConnectionStorage=new v.BridgeConnectionStorage(this.dappSettings.storage),(null==e?void 0:e.disableAutoPauseConnection)||this.addWindowFocusAndBlurSubscriptions()}static getWallets(){return this.walletsList.getWallets()}get connected(){return null!==this._wallet}get account(){var e;return(null===(e=this._wallet)||void 0===e?void 0:e.account)||null}get wallet(){return this._wallet}set wallet(e){this._wallet=e,this.statusChangeSubscriptions.forEach((e=>e(this._wallet)))}getWallets(){return this.walletsList.getWallets()}onStatusChange(e,t){return this.statusChangeSubscriptions.push(e),t&&this.statusChangeErrorSubscriptions.push(t),()=>{this.statusChangeSubscriptions=this.statusChangeSubscriptions.filter((t=>t!==e)),t&&(this.statusChangeErrorSubscriptions=this.statusChangeErrorSubscriptions.filter((e=>e!==t)))}}connect(e,t){var n,r;const o={};if("object"==typeof t&&"tonProof"in t&&(o.request=t),"object"==typeof t&&("openingDeadlineMS"in t||"signal"in t||"request"in t)&&(o.request=null==t?void 0:t.request,o.openingDeadlineMS=null==t?void 0:t.openingDeadlineMS,o.signal=null==t?void 0:t.signal),this.connected)throw new l.WalletAlreadyConnectedError;const i=(0,C.createAbortController)(null==o?void 0:o.signal);if(null===(n=this.abortController)||void 0===n||n.abort(),this.abortController=i,i.signal.aborted)throw new c.TonConnectError("Connection was aborted");return null===(r=this.provider)||void 0===r||r.closeConnection(),this.provider=this.createProvider(e),i.signal.addEventListener("abort",(()=>{var e;null===(e=this.provider)||void 0===e||e.closeConnection(),this.provider=null})),this.tracker.trackConnectionStarted(),this.provider.connect(this.createConnectRequest(null==o?void 0:o.request),{openingDeadlineMS:null==o?void 0:o.openingDeadlineMS,signal:i.signal})}restoreConnection(e){var t,n;return r(this,void 0,void 0,(function*(){this.tracker.trackConnectionRestoringStarted();const o=(0,C.createAbortController)(null==e?void 0:e.signal);if(null===(t=this.abortController)||void 0===t||t.abort(),this.abortController=o,o.signal.aborted)return void this.tracker.trackConnectionRestoringError("Connection restoring was aborted");const[i,s]=yield Promise.all([this.bridgeConnectionStorage.storedConnectionType(),this.walletsList.getEmbeddedWallet()]);if(o.signal.aborted)return void this.tracker.trackConnectionRestoringError("Connection restoring was aborted");let a=null;try{switch(i){case"http":a=yield p.BridgeProvider.fromStorage(this.dappSettings.storage);break;case"injected":a=yield g.InjectedProvider.fromStorage(this.dappSettings.storage);break;default:if(!s)return;a=this.createProvider(s)}}catch(e){return this.tracker.trackConnectionRestoringError("Provider is not restored"),yield this.bridgeConnectionStorage.removeConnection(),null==a||a.closeConnection(),void(a=null)}if(o.signal.aborted)return null==a||a.closeConnection(),void this.tracker.trackConnectionRestoringError("Connection restoring was aborted");if(!a)return(0,_.logError)("Provider is not restored"),void this.tracker.trackConnectionRestoringError("Provider is not restored");null===(n=this.provider)||void 0===n||n.closeConnection(),this.provider=a,a.listen(this.walletEventsListener.bind(this));const c=()=>{this.tracker.trackConnectionRestoringError("Connection restoring was aborted"),null==a||a.closeConnection(),a=null};o.signal.addEventListener("abort",c);const l=(0,m.callForSuccess)((t=>r(this,void 0,void 0,(function*(){yield null==a?void 0:a.restoreConnection({openingDeadlineMS:null==e?void 0:e.openingDeadlineMS,signal:t.signal}),o.signal.removeEventListener("abort",c),this.connected?this.tracker.trackConnectionRestoringCompleted(this.wallet):this.tracker.trackConnectionRestoringError("Connection restoring failed")}))),{attempts:Number.MAX_SAFE_INTEGER,delayMs:2e3,signal:null==e?void 0:e.signal}),u=new Promise((e=>setTimeout((()=>e()),12e3)));return Promise.race([l,u])}))}sendTransaction(e,t){return r(this,void 0,void 0,(function*(){const n={};"function"==typeof t?n.onRequestSent=t:(n.onRequestSent=null==t?void 0:t.onRequestSent,n.signal=null==t?void 0:t.signal);const r=(0,C.createAbortController)(null==n?void 0:n.signal);if(r.signal.aborted)throw new c.TonConnectError("Transaction sending was aborted");this.checkConnection(),(0,E.checkSendTransactionSupport)(this.wallet.device.features,{requiredMessagesNumber:e.messages.length}),this.tracker.trackTransactionSentForSignature(this.wallet,e);const{validUntil:i}=e,s=o(e,["validUntil"]),a=e.from||this.account.address,l=e.network||this.account.chain,u=yield this.provider.sendRequest(f.sendTransactionParser.convertToRpcRequest(Object.assign(Object.assign({},s),{valid_until:i,from:a,network:l})),{onRequestSent:n.onRequestSent,signal:r.signal});if(f.sendTransactionParser.isError(u))return this.tracker.trackTransactionSigningFailed(this.wallet,e,u.error.message,u.error.code),f.sendTransactionParser.parseAndThrowError(u);const d=f.sendTransactionParser.convertFromRpcResponse(u);return this.tracker.trackTransactionSigned(this.wallet,e,d),d}))}disconnect(e){var t;return r(this,void 0,void 0,(function*(){if(!this.connected)throw new u.WalletNotConnectedError;const n=(0,C.createAbortController)(null==e?void 0:e.signal),r=this.abortController;if(this.abortController=n,n.signal.aborted)throw new c.TonConnectError("Disconnect was aborted");this.onWalletDisconnected("dapp"),yield null===(t=this.provider)||void 0===t?void 0:t.disconnect({signal:n.signal}),null==r||r.abort()}))}pauseConnection(){var e;"http"===(null===(e=this.provider)||void 0===e?void 0:e.type)&&this.provider.pause()}unPauseConnection(){var e;return"http"!==(null===(e=this.provider)||void 0===e?void 0:e.type)?Promise.resolve():this.provider.unPause()}addWindowFocusAndBlurSubscriptions(){const e=(0,b.getDocument)();if(e)try{e.addEventListener("visibilitychange",(()=>{e.hidden?this.pauseConnection():this.unPauseConnection().catch()}))}catch(e){(0,_.logError)("Cannot subscribe to the document.visibilitychange: ",e)}}createProvider(e){let t;return t=!Array.isArray(e)&&(0,d.isWalletConnectionSourceJS)(e)?new g.InjectedProvider(this.dappSettings.storage,e.jsBridgeKey):new p.BridgeProvider(this.dappSettings.storage,e),t.listen(this.walletEventsListener.bind(this)),t}walletEventsListener(e){switch(e.event){case"connect":this.onWalletConnected(e.payload);break;case"connect_error":this.onWalletConnectError(e.payload);break;case"disconnect":this.onWalletDisconnected("wallet")}}onWalletConnected(e){const t=e.items.find((e=>"ton_addr"===e.name)),n=e.items.find((e=>"ton_proof"===e.name));if(!t)throw new c.TonConnectError("ton_addr connection item was not found");const r={device:e.device,provider:this.provider.type,account:{address:t.address,chain:t.network,walletStateInit:t.walletStateInit,publicKey:t.publicKey}};n&&(r.connectItems={tonProof:n}),this.wallet=r,this.tracker.trackConnectionCompleted(r)}onWalletConnectError(e){const t=h.connectErrorsParser.parseError(e);if(this.statusChangeErrorSubscriptions.forEach((e=>e(t))),(0,_.logDebug)(t),this.tracker.trackConnectionError(e.message,e.code),t instanceof a.ManifestNotFoundError||t instanceof s.ManifestContentErrorError)throw(0,_.logError)(t),t}onWalletDisconnected(e){this.tracker.trackDisconnection(this.wallet,e),this.wallet=null}checkConnection(){if(!this.connected)throw new u.WalletNotConnectedError}createConnectRequest(e){const t=[{name:"ton_addr"}];return(null==e?void 0:e.tonProof)&&t.push({name:"ton_proof",payload:e.tonProof}),{manifestUrl:this.dappSettings.manifestUrl,items:t}}}t.TonConnect=R,R.walletsList=new w.WalletsListManager,R.isWalletInjected=e=>g.InjectedProvider.isWalletInjected(e),R.isInsideWalletBrowser=e=>g.InjectedProvider.isInsideWalletBrowser(e)},9467:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BrowserEventDispatcher=void 0;const o=n(9590);t.BrowserEventDispatcher=class{constructor(){this.window=(0,o.getWindow)()}dispatchEvent(e,t){var n;return r(this,void 0,void 0,(function*(){const r=new CustomEvent(e,{detail:t});null===(n=this.window)||void 0===n||n.dispatchEvent(r)}))}addEventListener(e,t,n){var o;return r(this,void 0,void 0,(function*(){return null===(o=this.window)||void 0===o||o.addEventListener(e,t,n),()=>{var n;return null===(n=this.window)||void 0===n?void 0:n.removeEventListener(e,t)}}))}}},9489:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.TonConnectTracker=void 0;const o=n(16),i=n(9467);t.TonConnectTracker=class{constructor(e){var t;this.eventPrefix="ton-connect-",this.tonConnectUiVersion=null,this.eventDispatcher=null!==(t=null==e?void 0:e.eventDispatcher)&&void 0!==t?t:new i.BrowserEventDispatcher,this.tonConnectSdkVersion=e.tonConnectSdkVersion,this.init().catch()}get version(){return(0,o.createVersionInfo)({ton_connect_sdk_lib:this.tonConnectSdkVersion,ton_connect_ui_lib:this.tonConnectUiVersion})}init(){return r(this,void 0,void 0,(function*(){try{yield this.setRequestVersionHandler(),this.tonConnectUiVersion=yield this.requestTonConnectUiVersion()}catch(e){}}))}setRequestVersionHandler(){return r(this,void 0,void 0,(function*(){yield this.eventDispatcher.addEventListener("ton-connect-request-version",(()=>r(this,void 0,void 0,(function*(){yield this.eventDispatcher.dispatchEvent("ton-connect-response-version",(0,o.createResponseVersionEvent)(this.tonConnectSdkVersion))}))))}))}requestTonConnectUiVersion(){return r(this,void 0,void 0,(function*(){return new Promise(((e,t)=>r(this,void 0,void 0,(function*(){try{yield this.eventDispatcher.addEventListener("ton-connect-ui-response-version",(t=>{e(t.detail.version)}),{once:!0}),yield this.eventDispatcher.dispatchEvent("ton-connect-ui-request-version",(0,o.createRequestVersionEvent)())}catch(e){t(e)}}))))}))}dispatchUserActionEvent(e){try{this.eventDispatcher.dispatchEvent(`${this.eventPrefix}${e.type}`,e).catch()}catch(e){}}trackConnectionStarted(...e){try{const t=(0,o.createConnectionStartedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionCompleted(...e){try{const t=(0,o.createConnectionCompletedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionError(...e){try{const t=(0,o.createConnectionErrorEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringStarted(...e){try{const t=(0,o.createConnectionRestoringStartedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringCompleted(...e){try{const t=(0,o.createConnectionRestoringCompletedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringError(...e){try{const t=(0,o.createConnectionRestoringErrorEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackDisconnection(...e){try{const t=(0,o.createDisconnectionEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSentForSignature(...e){try{const t=(0,o.createTransactionSentForSignatureEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSigned(...e){try{const t=(0,o.createTransactionSignedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSigningFailed(...e){try{const t=(0,o.createTransactionSigningFailedEvent)(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}}},16:(e,t)=>{"use strict";function n(e){return{ton_connect_sdk_lib:e.ton_connect_sdk_lib,ton_connect_ui_lib:e.ton_connect_ui_lib}}function r(e,t){var r,o,i,s,a,c,l,u;const d=(null===(r=null==t?void 0:t.connectItems)||void 0===r?void 0:r.tonProof)&&"proof"in t.connectItems.tonProof?"ton_proof":"ton_addr";return{wallet_address:null!==(i=null===(o=null==t?void 0:t.account)||void 0===o?void 0:o.address)&&void 0!==i?i:null,wallet_type:null!==(s=null==t?void 0:t.device.appName)&&void 0!==s?s:null,wallet_version:null!==(a=null==t?void 0:t.device.appVersion)&&void 0!==a?a:null,auth_type:d,custom_data:Object.assign({chain_id:null!==(l=null===(c=null==t?void 0:t.account)||void 0===c?void 0:c.chain)&&void 0!==l?l:null,provider:null!==(u=null==t?void 0:t.provider)&&void 0!==u?u:null},n(e))}}function o(e,t){var n,r,o,i;return{valid_until:null!==(n=String(t.validUntil))&&void 0!==n?n:null,from:null!==(i=null!==(r=t.from)&&void 0!==r?r:null===(o=null==e?void 0:e.account)||void 0===o?void 0:o.address)&&void 0!==i?i:null,messages:t.messages.map((e=>{var t,n;return{address:null!==(t=e.address)&&void 0!==t?t:null,amount:null!==(n=e.amount)&&void 0!==n?n:null}}))}}Object.defineProperty(t,"__esModule",{value:!0}),t.createDisconnectionEvent=t.createTransactionSigningFailedEvent=t.createTransactionSignedEvent=t.createTransactionSentForSignatureEvent=t.createConnectionRestoringErrorEvent=t.createConnectionRestoringCompletedEvent=t.createConnectionRestoringStartedEvent=t.createConnectionErrorEvent=t.createConnectionCompletedEvent=t.createConnectionStartedEvent=t.createVersionInfo=t.createResponseVersionEvent=t.createRequestVersionEvent=void 0,t.createRequestVersionEvent=function(){return{type:"request-version"}},t.createResponseVersionEvent=function(e){return{type:"response-version",version:e}},t.createVersionInfo=n,t.createConnectionStartedEvent=function(e){return{type:"connection-started",custom_data:n(e)}},t.createConnectionCompletedEvent=function(e,t){return Object.assign({type:"connection-completed",is_success:!0},r(e,t))},t.createConnectionErrorEvent=function(e,t,r){return{type:"connection-error",is_success:!1,error_message:t,error_code:null!=r?r:null,custom_data:n(e)}},t.createConnectionRestoringStartedEvent=function(e){return{type:"connection-restoring-started",custom_data:n(e)}},t.createConnectionRestoringCompletedEvent=function(e,t){return Object.assign({type:"connection-restoring-completed",is_success:!0},r(e,t))},t.createConnectionRestoringErrorEvent=function(e,t){return{type:"connection-restoring-error",is_success:!1,error_message:t,custom_data:n(e)}},t.createTransactionSentForSignatureEvent=function(e,t,n){return Object.assign(Object.assign({type:"transaction-sent-for-signature"},r(e,t)),o(t,n))},t.createTransactionSignedEvent=function(e,t,n,i){return Object.assign(Object.assign({type:"transaction-signed",is_success:!0,signed_transaction:i.boc},r(e,t)),o(t,n))},t.createTransactionSigningFailedEvent=function(e,t,n,i,s){return Object.assign(Object.assign({type:"transaction-signing-failed",is_success:!1,error_message:i,error_code:null!=s?s:null},r(e,t)),o(t,n))},t.createDisconnectionEvent=function(e,t,n){return Object.assign({type:"disconnection",scope:n},r(e,t))}},6754:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toUserFriendlyAddress=void 0;const r=n(2764),o=n(9461);t.toUserFriendlyAddress=function(e,t=!1){const{wc:n,hex:i}=function(e){if(!e.includes(":"))throw new r.WrongAddressError(`Wrong address ${e}. Address must include ":".`);const t=e.split(":");if(2!==t.length)throw new r.WrongAddressError(`Wrong address ${e}. Address must include ":" only once.`);const n=parseInt(t[0]);if(0!==n&&-1!==n)throw new r.WrongAddressError(`Wrong address ${e}. WC must be eq 0 or -1, but ${n} received.`);const o=t[1];if(64!==(null==o?void 0:o.length))throw new r.WrongAddressError(`Wrong address ${e}. Hex part must be 64bytes length, but ${null==o?void 0:o.length} received.`);return{wc:n,hex:s(o)}}(e);let a=81;t&&(a|=128);const c=new Int8Array(34);c[0]=a,c[1]=n,c.set(i,2);const l=new Uint8Array(36);return l.set(c),l.set(function(e){let t=0;const n=new Uint8Array(e.length+2);n.set(e);for(let e of n){let n=128;for(;n>0;)t<<=1,e&n&&(t+=1),n>>=1,t>65535&&(t&=65535,t^=4129)}return new Uint8Array([Math.floor(t/256),t%256])}(c),34),o.Base64.encode(l).replace(/\+/g,"-").replace(/\//g,"_")};const i={};for(let e=0;e<=255;e++){let t=e.toString(16);t.length<2&&(t="0"+t),i[t]=e}function s(e){const t=(e=e.toLowerCase()).length;if(t%2!=0)throw new r.ParseHexError("Hex string must have length a multiple of 2: "+e);const n=t/2,o=new Uint8Array(n);for(let t=0;t<n;t++){const n=2*t,s=e.substring(n,n+2);if(!i.hasOwnProperty(s))throw new r.ParseHexError("Invalid hex character: "+s);o[t]=i[s]}return o}},6011:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.callForSuccess=void 0;const o=n(9572),i=n(2764),s=n(9252);t.callForSuccess=function(e,t){var n,a;return r(this,void 0,void 0,(function*(){const r=null!==(n=null==t?void 0:t.attempts)&&void 0!==n?n:10,c=null!==(a=null==t?void 0:t.delayMs)&&void 0!==a?a:200,l=(0,s.createAbortController)(null==t?void 0:t.signal);if("function"!=typeof e)throw new i.TonConnectError("Expected a function, got "+typeof e);let u,d=0;for(;d<r;){if(l.signal.aborted)throw new i.TonConnectError(`Aborted after attempts ${d}`);try{return yield e({signal:l.signal})}catch(e){u=e,d++,d<r&&(yield(0,o.delay)(c))}}throw u}))}},9252:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createAbortController=void 0,t.createAbortController=function(e){const t=new AbortController;return(null==e?void 0:e.aborted)?t.abort():null==e||e.addEventListener("abort",(()=>t.abort()),{once:!0}),t}},9572:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.delay=void 0;const o=n(2764);t.delay=function(e,t){return r(this,void 0,void 0,(function*(){return new Promise(((n,r)=>{var i,s;if(null===(i=null==t?void 0:t.signal)||void 0===i?void 0:i.aborted)return void r(new o.TonConnectError("Delay aborted"));const a=setTimeout((()=>n()),e);null===(s=null==t?void 0:t.signal)||void 0===s||s.addEventListener("abort",(()=>{clearTimeout(a),r(new o.TonConnectError("Delay aborted"))}))}))}))}},1002:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkSendTransactionSupport=void 0;const r=n(1583),o=n(4610);t.checkSendTransactionSupport=function(e,t){const n=e.includes("SendTransaction"),i=e.find((e=>e&&"object"==typeof e&&"SendTransaction"===e.name));if(!n&&!i)throw new o.WalletNotSupportFeatureError("Wallet doesn't support SendTransaction feature.");if(i&&void 0!==i.maxMessages){if(i.maxMessages<t.requiredMessagesNumber)throw new o.WalletNotSupportFeatureError(`Wallet is not able to handle such SendTransaction request. Max support messages number is ${i.maxMessages}, but ${t.requiredMessagesNumber} is required.`)}else(0,r.logWarning)("Connected wallet didn't provide information about max allowed messages in the SendTransaction request. Request may be rejected by the wallet.")}},1583:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.logWarning=t.logError=t.logDebug=void 0,t.logDebug=function(...e){try{console.debug("[TON_CONNECT_SDK]",...e)}catch(e){}},t.logError=function(...e){try{console.error("[TON_CONNECT_SDK]",...e)}catch(e){}},t.logWarning=function(...e){try{console.warn("[TON_CONNECT_SDK]",...e)}catch(e){}}},1484:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.createResource=void 0;const o=n(2764),i=n(9572),s=n(9252);t.createResource=function(e,t){let n=null,a=null,c=null,l=null,u=null;const d=(i,...d)=>r(this,void 0,void 0,(function*(){if(l=null!=i?i:null,null==u||u.abort(),u=(0,s.createAbortController)(i),u.signal.aborted)throw new o.TonConnectError("Resource creation was aborted");a=null!=d?d:null;const r=e(u.signal,...d);c=r;const h=yield r;if(c!==r&&h!==n)throw yield t(h),new o.TonConnectError("Resource creation was aborted by a new resource creation");return n=h,n}));return{create:d,current:()=>null!=n?n:null,dispose:()=>r(this,void 0,void 0,(function*(){try{const e=n;n=null;const r=c;c=null;try{null==u||u.abort()}catch(e){}yield Promise.allSettled([e?t(e):Promise.resolve(),r?t(yield r):Promise.resolve()])}catch(e){}})),recreate:e=>r(this,void 0,void 0,(function*(){const t=n,r=c,s=a,u=l;if(yield(0,i.delay)(e),t===n&&r===c&&s===a&&u===l)return yield d(l,...null!=s?s:[]);throw new o.TonConnectError("Resource recreation was aborted by a new resource creation")}))}}},6764:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.timeout=void 0;const o=n(2764),i=n(9252);t.timeout=function(e,t){const n=null==t?void 0:t.timeout,s=null==t?void 0:t.signal,a=(0,i.createAbortController)(s);return new Promise(((t,i)=>r(this,void 0,void 0,(function*(){if(a.signal.aborted)return void i(new o.TonConnectError("Operation aborted"));let r;void 0!==n&&(r=setTimeout((()=>{a.abort(),i(new o.TonConnectError(`Timeout after ${n}ms`))}),n)),a.signal.addEventListener("abort",(()=>{clearTimeout(r),i(new o.TonConnectError("Operation aborted"))}),{once:!0});const s={timeout:n,abort:a.signal};yield e(((...e)=>{clearTimeout(r),t(...e)}),(()=>{clearTimeout(r),i()}),s)}))))}},6415:(e,t)=>{"use strict";function n(e,t){return!(!e||"object"!=typeof e)&&t.every((t=>t in e))}Object.defineProperty(t,"__esModule",{value:!0}),t.hasProperties=t.hasProperty=void 0,t.hasProperty=function(e,t){return n(e,[t])},t.hasProperties=n},5370:(e,t)=>{"use strict";function n(e){return"/"===e.slice(-1)?e.slice(0,-1):e}Object.defineProperty(t,"__esModule",{value:!0}),t.encodeTelegramUrlParameters=t.isTelegramUrl=t.addPathToUrl=t.removeUrlLastSlash=void 0,t.removeUrlLastSlash=n,t.addPathToUrl=function(e,t){return n(e)+"/"+t},t.isTelegramUrl=function(e){if(!e)return!1;const t=new URL(e);return"tg:"===t.protocol||"t.me"===t.hostname},t.encodeTelegramUrlParameters=function(e){return e.replaceAll(".","%2E").replaceAll("-","%2D").replaceAll("_","%5F").replaceAll("&","-").replaceAll("=","__").replaceAll("%","--")}},9590:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tryGetLocalStorage=t.getWebPageManifest=t.getDocument=t.tryGetWindowKeys=t.getWindow=void 0;const r=n(9934),o=n(2764);function i(){if("undefined"!=typeof window)return window}t.getWindow=i,t.tryGetWindowKeys=function(){const e=i();if(!e)return[];try{return Object.keys(e)}catch(e){return[]}},t.getDocument=function(){if("undefined"!=typeof document)return document},t.getWebPageManifest=function(){var e;const t=null===(e=i())||void 0===e?void 0:e.location.origin;return t?t+"/tonconnect-manifest.json":""},t.tryGetLocalStorage=function(){if(function(){try{return"undefined"!=typeof localStorage}catch(e){return!1}}())return localStorage;if("undefined"!=typeof process&&null!=process.versions&&null!=process.versions.node)throw new o.TonConnectError("`localStorage` is unavailable, but it is required for TonConnect. For more details, see https://github.com/ton-connect/sdk/tree/main/packages/sdk#init-connector");return r.InMemoryStorage.getInstance()}},7419:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.WalletsListManager=void 0;const o=n(8814),i=n(9546),s=n(1328),a=n(1583),c=n(3041);t.WalletsListManager=class{constructor(e){this.walletsListCache=null,this.walletsListCacheCreationTimestamp=null,this.walletsListSource="https://raw.githubusercontent.com/ton-blockchain/wallets-list/main/wallets-v2.json",(null==e?void 0:e.walletsListSource)&&(this.walletsListSource=e.walletsListSource),(null==e?void 0:e.cacheTTLMs)&&(this.cacheTTLMs=e.cacheTTLMs)}getWallets(){return r(this,void 0,void 0,(function*(){return this.cacheTTLMs&&this.walletsListCacheCreationTimestamp&&Date.now()>this.walletsListCacheCreationTimestamp+this.cacheTTLMs&&(this.walletsListCache=null),this.walletsListCache||(this.walletsListCache=this.fetchWalletsList(),this.walletsListCache.then((()=>{this.walletsListCacheCreationTimestamp=Date.now()})).catch((()=>{this.walletsListCache=null,this.walletsListCacheCreationTimestamp=null}))),this.walletsListCache}))}getEmbeddedWallet(){return r(this,void 0,void 0,(function*(){const e=(yield this.getWallets()).filter(i.isWalletInfoCurrentlyEmbedded);return 1!==e.length?null:e[0]}))}fetchWalletsList(){return r(this,void 0,void 0,(function*(){let e=[];try{const t=yield fetch(this.walletsListSource);if(e=yield t.json(),!Array.isArray(e))throw new o.FetchWalletsError("Wrong wallets list format, wallets list must be an array.");const n=e.filter((e=>!this.isCorrectWalletConfigDTO(e)));n.length&&((0,a.logError)(`Wallet(s) ${n.map((e=>e.name)).join(", ")} config format is wrong. They were removed from the wallets list.`),e=e.filter((e=>this.isCorrectWalletConfigDTO(e))))}catch(t){(0,a.logError)(t),e=c.FALLBACK_WALLETS_LIST}let t=[];try{t=s.InjectedProvider.getCurrentlyInjectedWallets()}catch(e){(0,a.logError)(e)}return this.mergeWalletsLists(this.walletConfigDTOListToWalletConfigList(e),t)}))}walletConfigDTOListToWalletConfigList(e){return e.map((e=>{const t={name:e.name,appName:e.app_name,imageUrl:e.image,aboutUrl:e.about_url,tondns:e.tondns,platforms:e.platforms};return e.bridge.forEach((n=>{if("sse"===n.type&&(t.bridgeUrl=n.url,t.universalLink=e.universal_url,t.deepLink=e.deepLink),"js"===n.type){const e=n.key;t.jsBridgeKey=e,t.injected=s.InjectedProvider.isWalletInjected(e),t.embedded=s.InjectedProvider.isInsideWalletBrowser(e)}})),t}))}mergeWalletsLists(e,t){return[...new Set(e.concat(t).map((e=>e.name))).values()].map((n=>{const r=e.find((e=>e.name===n)),o=t.find((e=>e.name===n));return Object.assign(Object.assign({},r&&Object.assign({},r)),o&&Object.assign({},o))}))}isCorrectWalletConfigDTO(e){if(!e||"object"!=typeof e)return!1;if(!("name"in e&&"image"in e&&"about_url"in e&&"platforms"in e&&"app_name"in e))return!1;if(!e.platforms||!Array.isArray(e.platforms)||!e.platforms.length)return!1;if(!("bridge"in e)||!Array.isArray(e.bridge)||!e.bridge.length)return!1;const t=e.bridge;if(t.some((e=>!e||"object"!=typeof e||!("type"in e))))return!1;const n=t.find((e=>"sse"===e.type));if(n&&(!("url"in n)||!n.url||!e.universal_url))return!1;const r=t.find((e=>"js"===e.type));return!!(!r||"key"in r&&r.key)}}},251:function(e){!function(t,n){"use strict";e.exports?e.exports=n():(t.nacl||(t.nacl={}),t.nacl.util=n())}(this,(function(){"use strict";var e={};function t(e){if(!/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/.test(e))throw new TypeError("invalid encoding")}return e.decodeUTF8=function(e){if("string"!=typeof e)throw new TypeError("expected string");var t,n=unescape(encodeURIComponent(e)),r=new Uint8Array(n.length);for(t=0;t<n.length;t++)r[t]=n.charCodeAt(t);return r},e.encodeUTF8=function(e){var t,n=[];for(t=0;t<e.length;t++)n.push(String.fromCharCode(e[t]));return decodeURIComponent(escape(n.join("")))},"undefined"==typeof atob?void 0!==Buffer.from?(e.encodeBase64=function(e){return Buffer.from(e).toString("base64")},e.decodeBase64=function(e){return t(e),new Uint8Array(Array.prototype.slice.call(Buffer.from(e,"base64"),0))}):(e.encodeBase64=function(e){return new Buffer(e).toString("base64")},e.decodeBase64=function(e){return t(e),new Uint8Array(Array.prototype.slice.call(new Buffer(e,"base64"),0))}):(e.encodeBase64=function(e){var t,n=[],r=e.length;for(t=0;t<r;t++)n.push(String.fromCharCode(e[t]));return btoa(n.join(""))},e.decodeBase64=function(e){t(e);var n,r=atob(e),o=new Uint8Array(r.length);for(n=0;n<r.length;n++)o[n]=r.charCodeAt(n);return o}),e}))},717:(e,t,n)=>{!function(e){"use strict";var t=function(e){var t,n=new Float64Array(16);if(e)for(t=0;t<e.length;t++)n[t]=e[t];return n},r=function(){throw new Error("no PRNG")},o=new Uint8Array(16),i=new Uint8Array(32);i[0]=9;var s=t(),a=t([1]),c=t([56129,1]),l=t([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),u=t([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),d=t([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),h=t([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),f=t([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function p(e,t,n,r){e[t]=n>>24&255,e[t+1]=n>>16&255,e[t+2]=n>>8&255,e[t+3]=255&n,e[t+4]=r>>24&255,e[t+5]=r>>16&255,e[t+6]=r>>8&255,e[t+7]=255&r}function g(e,t,n,r,o){var i,s=0;for(i=0;i<o;i++)s|=e[t+i]^n[r+i];return(1&s-1>>>8)-1}function v(e,t,n,r){return g(e,t,n,r,16)}function y(e,t,n,r){return g(e,t,n,r,32)}function b(e,t,n,r){!function(e,t,n,r){for(var o,i=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,c=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,l=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,d=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,f=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,p=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,v=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,y=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,b=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,w=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,E=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,m=i,_=s,C=a,O=c,S=l,R=u,T=d,P=h,j=f,N=p,A=g,U=v,M=y,I=b,W=w,k=E,D=0;D<20;D+=2)m^=(o=(M^=(o=(j^=(o=(S^=(o=m+M|0)<<7|o>>>25)+m|0)<<9|o>>>23)+S|0)<<13|o>>>19)+j|0)<<18|o>>>14,R^=(o=(_^=(o=(I^=(o=(N^=(o=R+_|0)<<7|o>>>25)+R|0)<<9|o>>>23)+N|0)<<13|o>>>19)+I|0)<<18|o>>>14,A^=(o=(T^=(o=(C^=(o=(W^=(o=A+T|0)<<7|o>>>25)+A|0)<<9|o>>>23)+W|0)<<13|o>>>19)+C|0)<<18|o>>>14,k^=(o=(U^=(o=(P^=(o=(O^=(o=k+U|0)<<7|o>>>25)+k|0)<<9|o>>>23)+O|0)<<13|o>>>19)+P|0)<<18|o>>>14,m^=(o=(O^=(o=(C^=(o=(_^=(o=m+O|0)<<7|o>>>25)+m|0)<<9|o>>>23)+_|0)<<13|o>>>19)+C|0)<<18|o>>>14,R^=(o=(S^=(o=(P^=(o=(T^=(o=R+S|0)<<7|o>>>25)+R|0)<<9|o>>>23)+T|0)<<13|o>>>19)+P|0)<<18|o>>>14,A^=(o=(N^=(o=(j^=(o=(U^=(o=A+N|0)<<7|o>>>25)+A|0)<<9|o>>>23)+U|0)<<13|o>>>19)+j|0)<<18|o>>>14,k^=(o=(W^=(o=(I^=(o=(M^=(o=k+W|0)<<7|o>>>25)+k|0)<<9|o>>>23)+M|0)<<13|o>>>19)+I|0)<<18|o>>>14;m=m+i|0,_=_+s|0,C=C+a|0,O=O+c|0,S=S+l|0,R=R+u|0,T=T+d|0,P=P+h|0,j=j+f|0,N=N+p|0,A=A+g|0,U=U+v|0,M=M+y|0,I=I+b|0,W=W+w|0,k=k+E|0,e[0]=m>>>0&255,e[1]=m>>>8&255,e[2]=m>>>16&255,e[3]=m>>>24&255,e[4]=_>>>0&255,e[5]=_>>>8&255,e[6]=_>>>16&255,e[7]=_>>>24&255,e[8]=C>>>0&255,e[9]=C>>>8&255,e[10]=C>>>16&255,e[11]=C>>>24&255,e[12]=O>>>0&255,e[13]=O>>>8&255,e[14]=O>>>16&255,e[15]=O>>>24&255,e[16]=S>>>0&255,e[17]=S>>>8&255,e[18]=S>>>16&255,e[19]=S>>>24&255,e[20]=R>>>0&255,e[21]=R>>>8&255,e[22]=R>>>16&255,e[23]=R>>>24&255,e[24]=T>>>0&255,e[25]=T>>>8&255,e[26]=T>>>16&255,e[27]=T>>>24&255,e[28]=P>>>0&255,e[29]=P>>>8&255,e[30]=P>>>16&255,e[31]=P>>>24&255,e[32]=j>>>0&255,e[33]=j>>>8&255,e[34]=j>>>16&255,e[35]=j>>>24&255,e[36]=N>>>0&255,e[37]=N>>>8&255,e[38]=N>>>16&255,e[39]=N>>>24&255,e[40]=A>>>0&255,e[41]=A>>>8&255,e[42]=A>>>16&255,e[43]=A>>>24&255,e[44]=U>>>0&255,e[45]=U>>>8&255,e[46]=U>>>16&255,e[47]=U>>>24&255,e[48]=M>>>0&255,e[49]=M>>>8&255,e[50]=M>>>16&255,e[51]=M>>>24&255,e[52]=I>>>0&255,e[53]=I>>>8&255,e[54]=I>>>16&255,e[55]=I>>>24&255,e[56]=W>>>0&255,e[57]=W>>>8&255,e[58]=W>>>16&255,e[59]=W>>>24&255,e[60]=k>>>0&255,e[61]=k>>>8&255,e[62]=k>>>16&255,e[63]=k>>>24&255}(e,t,n,r)}function w(e,t,n,r){!function(e,t,n,r){for(var o,i=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,c=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,l=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,d=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,f=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,p=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,v=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,y=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,b=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,w=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,E=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,m=0;m<20;m+=2)i^=(o=(y^=(o=(f^=(o=(l^=(o=i+y|0)<<7|o>>>25)+i|0)<<9|o>>>23)+l|0)<<13|o>>>19)+f|0)<<18|o>>>14,u^=(o=(s^=(o=(b^=(o=(p^=(o=u+s|0)<<7|o>>>25)+u|0)<<9|o>>>23)+p|0)<<13|o>>>19)+b|0)<<18|o>>>14,g^=(o=(d^=(o=(a^=(o=(w^=(o=g+d|0)<<7|o>>>25)+g|0)<<9|o>>>23)+w|0)<<13|o>>>19)+a|0)<<18|o>>>14,E^=(o=(v^=(o=(h^=(o=(c^=(o=E+v|0)<<7|o>>>25)+E|0)<<9|o>>>23)+c|0)<<13|o>>>19)+h|0)<<18|o>>>14,i^=(o=(c^=(o=(a^=(o=(s^=(o=i+c|0)<<7|o>>>25)+i|0)<<9|o>>>23)+s|0)<<13|o>>>19)+a|0)<<18|o>>>14,u^=(o=(l^=(o=(h^=(o=(d^=(o=u+l|0)<<7|o>>>25)+u|0)<<9|o>>>23)+d|0)<<13|o>>>19)+h|0)<<18|o>>>14,g^=(o=(p^=(o=(f^=(o=(v^=(o=g+p|0)<<7|o>>>25)+g|0)<<9|o>>>23)+v|0)<<13|o>>>19)+f|0)<<18|o>>>14,E^=(o=(w^=(o=(b^=(o=(y^=(o=E+w|0)<<7|o>>>25)+E|0)<<9|o>>>23)+y|0)<<13|o>>>19)+b|0)<<18|o>>>14;e[0]=i>>>0&255,e[1]=i>>>8&255,e[2]=i>>>16&255,e[3]=i>>>24&255,e[4]=u>>>0&255,e[5]=u>>>8&255,e[6]=u>>>16&255,e[7]=u>>>24&255,e[8]=g>>>0&255,e[9]=g>>>8&255,e[10]=g>>>16&255,e[11]=g>>>24&255,e[12]=E>>>0&255,e[13]=E>>>8&255,e[14]=E>>>16&255,e[15]=E>>>24&255,e[16]=d>>>0&255,e[17]=d>>>8&255,e[18]=d>>>16&255,e[19]=d>>>24&255,e[20]=h>>>0&255,e[21]=h>>>8&255,e[22]=h>>>16&255,e[23]=h>>>24&255,e[24]=f>>>0&255,e[25]=f>>>8&255,e[26]=f>>>16&255,e[27]=f>>>24&255,e[28]=p>>>0&255,e[29]=p>>>8&255,e[30]=p>>>16&255,e[31]=p>>>24&255}(e,t,n,r)}var E=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function m(e,t,n,r,o,i,s){var a,c,l=new Uint8Array(16),u=new Uint8Array(64);for(c=0;c<16;c++)l[c]=0;for(c=0;c<8;c++)l[c]=i[c];for(;o>=64;){for(b(u,l,s,E),c=0;c<64;c++)e[t+c]=n[r+c]^u[c];for(a=1,c=8;c<16;c++)a=a+(255&l[c])|0,l[c]=255&a,a>>>=8;o-=64,t+=64,r+=64}if(o>0)for(b(u,l,s,E),c=0;c<o;c++)e[t+c]=n[r+c]^u[c];return 0}function _(e,t,n,r,o){var i,s,a=new Uint8Array(16),c=new Uint8Array(64);for(s=0;s<16;s++)a[s]=0;for(s=0;s<8;s++)a[s]=r[s];for(;n>=64;){for(b(c,a,o,E),s=0;s<64;s++)e[t+s]=c[s];for(i=1,s=8;s<16;s++)i=i+(255&a[s])|0,a[s]=255&i,i>>>=8;n-=64,t+=64}if(n>0)for(b(c,a,o,E),s=0;s<n;s++)e[t+s]=c[s];return 0}function C(e,t,n,r,o){var i=new Uint8Array(32);w(i,r,o,E);for(var s=new Uint8Array(8),a=0;a<8;a++)s[a]=r[a+16];return _(e,t,n,s,i)}function O(e,t,n,r,o,i,s){var a=new Uint8Array(32);w(a,i,s,E);for(var c=new Uint8Array(8),l=0;l<8;l++)c[l]=i[l+16];return m(e,t,n,r,o,c,a)}var S=function(e){var t,n,r,o,i,s,a,c;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,t=255&e[0]|(255&e[1])<<8,this.r[0]=8191&t,n=255&e[2]|(255&e[3])<<8,this.r[1]=8191&(t>>>13|n<<3),r=255&e[4]|(255&e[5])<<8,this.r[2]=7939&(n>>>10|r<<6),o=255&e[6]|(255&e[7])<<8,this.r[3]=8191&(r>>>7|o<<9),i=255&e[8]|(255&e[9])<<8,this.r[4]=255&(o>>>4|i<<12),this.r[5]=i>>>1&8190,s=255&e[10]|(255&e[11])<<8,this.r[6]=8191&(i>>>14|s<<2),a=255&e[12]|(255&e[13])<<8,this.r[7]=8065&(s>>>11|a<<5),c=255&e[14]|(255&e[15])<<8,this.r[8]=8191&(a>>>8|c<<8),this.r[9]=c>>>5&127,this.pad[0]=255&e[16]|(255&e[17])<<8,this.pad[1]=255&e[18]|(255&e[19])<<8,this.pad[2]=255&e[20]|(255&e[21])<<8,this.pad[3]=255&e[22]|(255&e[23])<<8,this.pad[4]=255&e[24]|(255&e[25])<<8,this.pad[5]=255&e[26]|(255&e[27])<<8,this.pad[6]=255&e[28]|(255&e[29])<<8,this.pad[7]=255&e[30]|(255&e[31])<<8};function R(e,t,n,r,o,i){var s=new S(i);return s.update(n,r,o),s.finish(e,t),0}function T(e,t,n,r,o,i){var s=new Uint8Array(16);return R(s,0,n,r,o,i),v(e,t,s,0)}function P(e,t,n,r,o){var i;if(n<32)return-1;for(O(e,0,t,0,n,r,o),R(e,16,e,32,n-32,e),i=0;i<16;i++)e[i]=0;return 0}function j(e,t,n,r,o){var i,s=new Uint8Array(32);if(n<32)return-1;if(C(s,0,32,r,o),0!==T(t,16,t,32,n-32,s))return-1;for(O(e,0,t,0,n,r,o),i=0;i<32;i++)e[i]=0;return 0}function N(e,t){var n;for(n=0;n<16;n++)e[n]=0|t[n]}function A(e){var t,n,r=1;for(t=0;t<16;t++)n=e[t]+r+65535,r=Math.floor(n/65536),e[t]=n-65536*r;e[0]+=r-1+37*(r-1)}function U(e,t,n){for(var r,o=~(n-1),i=0;i<16;i++)r=o&(e[i]^t[i]),e[i]^=r,t[i]^=r}function M(e,n){var r,o,i,s=t(),a=t();for(r=0;r<16;r++)a[r]=n[r];for(A(a),A(a),A(a),o=0;o<2;o++){for(s[0]=a[0]-65517,r=1;r<15;r++)s[r]=a[r]-65535-(s[r-1]>>16&1),s[r-1]&=65535;s[15]=a[15]-32767-(s[14]>>16&1),i=s[15]>>16&1,s[14]&=65535,U(a,s,1-i)}for(r=0;r<16;r++)e[2*r]=255&a[r],e[2*r+1]=a[r]>>8}function I(e,t){var n=new Uint8Array(32),r=new Uint8Array(32);return M(n,e),M(r,t),y(n,0,r,0)}function W(e){var t=new Uint8Array(32);return M(t,e),1&t[0]}function k(e,t){var n;for(n=0;n<16;n++)e[n]=t[2*n]+(t[2*n+1]<<8);e[15]&=32767}function D(e,t,n){for(var r=0;r<16;r++)e[r]=t[r]+n[r]}function x(e,t,n){for(var r=0;r<16;r++)e[r]=t[r]-n[r]}function L(e,t,n){var r,o,i=0,s=0,a=0,c=0,l=0,u=0,d=0,h=0,f=0,p=0,g=0,v=0,y=0,b=0,w=0,E=0,m=0,_=0,C=0,O=0,S=0,R=0,T=0,P=0,j=0,N=0,A=0,U=0,M=0,I=0,W=0,k=n[0],D=n[1],x=n[2],L=n[3],B=n[4],K=n[5],q=n[6],F=n[7],G=n[8],H=n[9],V=n[10],$=n[11],J=n[12],Y=n[13],z=n[14],Q=n[15];i+=(r=t[0])*k,s+=r*D,a+=r*x,c+=r*L,l+=r*B,u+=r*K,d+=r*q,h+=r*F,f+=r*G,p+=r*H,g+=r*V,v+=r*$,y+=r*J,b+=r*Y,w+=r*z,E+=r*Q,s+=(r=t[1])*k,a+=r*D,c+=r*x,l+=r*L,u+=r*B,d+=r*K,h+=r*q,f+=r*F,p+=r*G,g+=r*H,v+=r*V,y+=r*$,b+=r*J,w+=r*Y,E+=r*z,m+=r*Q,a+=(r=t[2])*k,c+=r*D,l+=r*x,u+=r*L,d+=r*B,h+=r*K,f+=r*q,p+=r*F,g+=r*G,v+=r*H,y+=r*V,b+=r*$,w+=r*J,E+=r*Y,m+=r*z,_+=r*Q,c+=(r=t[3])*k,l+=r*D,u+=r*x,d+=r*L,h+=r*B,f+=r*K,p+=r*q,g+=r*F,v+=r*G,y+=r*H,b+=r*V,w+=r*$,E+=r*J,m+=r*Y,_+=r*z,C+=r*Q,l+=(r=t[4])*k,u+=r*D,d+=r*x,h+=r*L,f+=r*B,p+=r*K,g+=r*q,v+=r*F,y+=r*G,b+=r*H,w+=r*V,E+=r*$,m+=r*J,_+=r*Y,C+=r*z,O+=r*Q,u+=(r=t[5])*k,d+=r*D,h+=r*x,f+=r*L,p+=r*B,g+=r*K,v+=r*q,y+=r*F,b+=r*G,w+=r*H,E+=r*V,m+=r*$,_+=r*J,C+=r*Y,O+=r*z,S+=r*Q,d+=(r=t[6])*k,h+=r*D,f+=r*x,p+=r*L,g+=r*B,v+=r*K,y+=r*q,b+=r*F,w+=r*G,E+=r*H,m+=r*V,_+=r*$,C+=r*J,O+=r*Y,S+=r*z,R+=r*Q,h+=(r=t[7])*k,f+=r*D,p+=r*x,g+=r*L,v+=r*B,y+=r*K,b+=r*q,w+=r*F,E+=r*G,m+=r*H,_+=r*V,C+=r*$,O+=r*J,S+=r*Y,R+=r*z,T+=r*Q,f+=(r=t[8])*k,p+=r*D,g+=r*x,v+=r*L,y+=r*B,b+=r*K,w+=r*q,E+=r*F,m+=r*G,_+=r*H,C+=r*V,O+=r*$,S+=r*J,R+=r*Y,T+=r*z,P+=r*Q,p+=(r=t[9])*k,g+=r*D,v+=r*x,y+=r*L,b+=r*B,w+=r*K,E+=r*q,m+=r*F,_+=r*G,C+=r*H,O+=r*V,S+=r*$,R+=r*J,T+=r*Y,P+=r*z,j+=r*Q,g+=(r=t[10])*k,v+=r*D,y+=r*x,b+=r*L,w+=r*B,E+=r*K,m+=r*q,_+=r*F,C+=r*G,O+=r*H,S+=r*V,R+=r*$,T+=r*J,P+=r*Y,j+=r*z,N+=r*Q,v+=(r=t[11])*k,y+=r*D,b+=r*x,w+=r*L,E+=r*B,m+=r*K,_+=r*q,C+=r*F,O+=r*G,S+=r*H,R+=r*V,T+=r*$,P+=r*J,j+=r*Y,N+=r*z,A+=r*Q,y+=(r=t[12])*k,b+=r*D,w+=r*x,E+=r*L,m+=r*B,_+=r*K,C+=r*q,O+=r*F,S+=r*G,R+=r*H,T+=r*V,P+=r*$,j+=r*J,N+=r*Y,A+=r*z,U+=r*Q,b+=(r=t[13])*k,w+=r*D,E+=r*x,m+=r*L,_+=r*B,C+=r*K,O+=r*q,S+=r*F,R+=r*G,T+=r*H,P+=r*V,j+=r*$,N+=r*J,A+=r*Y,U+=r*z,M+=r*Q,w+=(r=t[14])*k,E+=r*D,m+=r*x,_+=r*L,C+=r*B,O+=r*K,S+=r*q,R+=r*F,T+=r*G,P+=r*H,j+=r*V,N+=r*$,A+=r*J,U+=r*Y,M+=r*z,I+=r*Q,E+=(r=t[15])*k,s+=38*(_+=r*x),a+=38*(C+=r*L),c+=38*(O+=r*B),l+=38*(S+=r*K),u+=38*(R+=r*q),d+=38*(T+=r*F),h+=38*(P+=r*G),f+=38*(j+=r*H),p+=38*(N+=r*V),g+=38*(A+=r*$),v+=38*(U+=r*J),y+=38*(M+=r*Y),b+=38*(I+=r*z),w+=38*(W+=r*Q),i=(r=(i+=38*(m+=r*D))+(o=1)+65535)-65536*(o=Math.floor(r/65536)),s=(r=s+o+65535)-65536*(o=Math.floor(r/65536)),a=(r=a+o+65535)-65536*(o=Math.floor(r/65536)),c=(r=c+o+65535)-65536*(o=Math.floor(r/65536)),l=(r=l+o+65535)-65536*(o=Math.floor(r/65536)),u=(r=u+o+65535)-65536*(o=Math.floor(r/65536)),d=(r=d+o+65535)-65536*(o=Math.floor(r/65536)),h=(r=h+o+65535)-65536*(o=Math.floor(r/65536)),f=(r=f+o+65535)-65536*(o=Math.floor(r/65536)),p=(r=p+o+65535)-65536*(o=Math.floor(r/65536)),g=(r=g+o+65535)-65536*(o=Math.floor(r/65536)),v=(r=v+o+65535)-65536*(o=Math.floor(r/65536)),y=(r=y+o+65535)-65536*(o=Math.floor(r/65536)),b=(r=b+o+65535)-65536*(o=Math.floor(r/65536)),w=(r=w+o+65535)-65536*(o=Math.floor(r/65536)),E=(r=E+o+65535)-65536*(o=Math.floor(r/65536)),i=(r=(i+=o-1+37*(o-1))+(o=1)+65535)-65536*(o=Math.floor(r/65536)),s=(r=s+o+65535)-65536*(o=Math.floor(r/65536)),a=(r=a+o+65535)-65536*(o=Math.floor(r/65536)),c=(r=c+o+65535)-65536*(o=Math.floor(r/65536)),l=(r=l+o+65535)-65536*(o=Math.floor(r/65536)),u=(r=u+o+65535)-65536*(o=Math.floor(r/65536)),d=(r=d+o+65535)-65536*(o=Math.floor(r/65536)),h=(r=h+o+65535)-65536*(o=Math.floor(r/65536)),f=(r=f+o+65535)-65536*(o=Math.floor(r/65536)),p=(r=p+o+65535)-65536*(o=Math.floor(r/65536)),g=(r=g+o+65535)-65536*(o=Math.floor(r/65536)),v=(r=v+o+65535)-65536*(o=Math.floor(r/65536)),y=(r=y+o+65535)-65536*(o=Math.floor(r/65536)),b=(r=b+o+65535)-65536*(o=Math.floor(r/65536)),w=(r=w+o+65535)-65536*(o=Math.floor(r/65536)),E=(r=E+o+65535)-65536*(o=Math.floor(r/65536)),i+=o-1+37*(o-1),e[0]=i,e[1]=s,e[2]=a,e[3]=c,e[4]=l,e[5]=u,e[6]=d,e[7]=h,e[8]=f,e[9]=p,e[10]=g,e[11]=v,e[12]=y,e[13]=b,e[14]=w,e[15]=E}function B(e,t){L(e,t,t)}function K(e,n){var r,o=t();for(r=0;r<16;r++)o[r]=n[r];for(r=253;r>=0;r--)B(o,o),2!==r&&4!==r&&L(o,o,n);for(r=0;r<16;r++)e[r]=o[r]}function q(e,n){var r,o=t();for(r=0;r<16;r++)o[r]=n[r];for(r=250;r>=0;r--)B(o,o),1!==r&&L(o,o,n);for(r=0;r<16;r++)e[r]=o[r]}function F(e,n,r){var o,i,s=new Uint8Array(32),a=new Float64Array(80),l=t(),u=t(),d=t(),h=t(),f=t(),p=t();for(i=0;i<31;i++)s[i]=n[i];for(s[31]=127&n[31]|64,s[0]&=248,k(a,r),i=0;i<16;i++)u[i]=a[i],h[i]=l[i]=d[i]=0;for(l[0]=h[0]=1,i=254;i>=0;--i)U(l,u,o=s[i>>>3]>>>(7&i)&1),U(d,h,o),D(f,l,d),x(l,l,d),D(d,u,h),x(u,u,h),B(h,f),B(p,l),L(l,d,l),L(d,u,f),D(f,l,d),x(l,l,d),B(u,l),x(d,h,p),L(l,d,c),D(l,l,h),L(d,d,l),L(l,h,p),L(h,u,a),B(u,f),U(l,u,o),U(d,h,o);for(i=0;i<16;i++)a[i+16]=l[i],a[i+32]=d[i],a[i+48]=u[i],a[i+64]=h[i];var g=a.subarray(32),v=a.subarray(16);return K(g,g),L(v,v,g),M(e,v),0}function G(e,t){return F(e,t,i)}function H(e,t){return r(t,32),G(e,t)}function V(e,t,n){var r=new Uint8Array(32);return F(r,n,t),w(e,o,r,E)}S.prototype.blocks=function(e,t,n){for(var r,o,i,s,a,c,l,u,d,h,f,p,g,v,y,b,w,E,m,_=this.fin?0:2048,C=this.h[0],O=this.h[1],S=this.h[2],R=this.h[3],T=this.h[4],P=this.h[5],j=this.h[6],N=this.h[7],A=this.h[8],U=this.h[9],M=this.r[0],I=this.r[1],W=this.r[2],k=this.r[3],D=this.r[4],x=this.r[5],L=this.r[6],B=this.r[7],K=this.r[8],q=this.r[9];n>=16;)h=d=0,h+=(C+=8191&(r=255&e[t+0]|(255&e[t+1])<<8))*M,h+=(O+=8191&(r>>>13|(o=255&e[t+2]|(255&e[t+3])<<8)<<3))*(5*q),h+=(S+=8191&(o>>>10|(i=255&e[t+4]|(255&e[t+5])<<8)<<6))*(5*K),h+=(R+=8191&(i>>>7|(s=255&e[t+6]|(255&e[t+7])<<8)<<9))*(5*B),d=(h+=(T+=8191&(s>>>4|(a=255&e[t+8]|(255&e[t+9])<<8)<<12))*(5*L))>>>13,h&=8191,h+=(P+=a>>>1&8191)*(5*x),h+=(j+=8191&(a>>>14|(c=255&e[t+10]|(255&e[t+11])<<8)<<2))*(5*D),h+=(N+=8191&(c>>>11|(l=255&e[t+12]|(255&e[t+13])<<8)<<5))*(5*k),h+=(A+=8191&(l>>>8|(u=255&e[t+14]|(255&e[t+15])<<8)<<8))*(5*W),f=d+=(h+=(U+=u>>>5|_)*(5*I))>>>13,f+=C*I,f+=O*M,f+=S*(5*q),f+=R*(5*K),d=(f+=T*(5*B))>>>13,f&=8191,f+=P*(5*L),f+=j*(5*x),f+=N*(5*D),f+=A*(5*k),d+=(f+=U*(5*W))>>>13,f&=8191,p=d,p+=C*W,p+=O*I,p+=S*M,p+=R*(5*q),d=(p+=T*(5*K))>>>13,p&=8191,p+=P*(5*B),p+=j*(5*L),p+=N*(5*x),p+=A*(5*D),g=d+=(p+=U*(5*k))>>>13,g+=C*k,g+=O*W,g+=S*I,g+=R*M,d=(g+=T*(5*q))>>>13,g&=8191,g+=P*(5*K),g+=j*(5*B),g+=N*(5*L),g+=A*(5*x),v=d+=(g+=U*(5*D))>>>13,v+=C*D,v+=O*k,v+=S*W,v+=R*I,d=(v+=T*M)>>>13,v&=8191,v+=P*(5*q),v+=j*(5*K),v+=N*(5*B),v+=A*(5*L),y=d+=(v+=U*(5*x))>>>13,y+=C*x,y+=O*D,y+=S*k,y+=R*W,d=(y+=T*I)>>>13,y&=8191,y+=P*M,y+=j*(5*q),y+=N*(5*K),y+=A*(5*B),b=d+=(y+=U*(5*L))>>>13,b+=C*L,b+=O*x,b+=S*D,b+=R*k,d=(b+=T*W)>>>13,b&=8191,b+=P*I,b+=j*M,b+=N*(5*q),b+=A*(5*K),w=d+=(b+=U*(5*B))>>>13,w+=C*B,w+=O*L,w+=S*x,w+=R*D,d=(w+=T*k)>>>13,w&=8191,w+=P*W,w+=j*I,w+=N*M,w+=A*(5*q),E=d+=(w+=U*(5*K))>>>13,E+=C*K,E+=O*B,E+=S*L,E+=R*x,d=(E+=T*D)>>>13,E&=8191,E+=P*k,E+=j*W,E+=N*I,E+=A*M,m=d+=(E+=U*(5*q))>>>13,m+=C*q,m+=O*K,m+=S*B,m+=R*L,d=(m+=T*x)>>>13,m&=8191,m+=P*D,m+=j*k,m+=N*W,m+=A*I,C=h=8191&(d=(d=((d+=(m+=U*M)>>>13)<<2)+d|0)+(h&=8191)|0),O=f+=d>>>=13,S=p&=8191,R=g&=8191,T=v&=8191,P=y&=8191,j=b&=8191,N=w&=8191,A=E&=8191,U=m&=8191,t+=16,n-=16;this.h[0]=C,this.h[1]=O,this.h[2]=S,this.h[3]=R,this.h[4]=T,this.h[5]=P,this.h[6]=j,this.h[7]=N,this.h[8]=A,this.h[9]=U},S.prototype.finish=function(e,t){var n,r,o,i,s=new Uint16Array(10);if(this.leftover){for(i=this.leftover,this.buffer[i++]=1;i<16;i++)this.buffer[i]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(n=this.h[1]>>>13,this.h[1]&=8191,i=2;i<10;i++)this.h[i]+=n,n=this.h[i]>>>13,this.h[i]&=8191;for(this.h[0]+=5*n,n=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=n,n=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=n,s[0]=this.h[0]+5,n=s[0]>>>13,s[0]&=8191,i=1;i<10;i++)s[i]=this.h[i]+n,n=s[i]>>>13,s[i]&=8191;for(s[9]-=8192,r=(1^n)-1,i=0;i<10;i++)s[i]&=r;for(r=~r,i=0;i<10;i++)this.h[i]=this.h[i]&r|s[i];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),o=this.h[0]+this.pad[0],this.h[0]=65535&o,i=1;i<8;i++)o=(this.h[i]+this.pad[i]|0)+(o>>>16)|0,this.h[i]=65535&o;e[t+0]=this.h[0]>>>0&255,e[t+1]=this.h[0]>>>8&255,e[t+2]=this.h[1]>>>0&255,e[t+3]=this.h[1]>>>8&255,e[t+4]=this.h[2]>>>0&255,e[t+5]=this.h[2]>>>8&255,e[t+6]=this.h[3]>>>0&255,e[t+7]=this.h[3]>>>8&255,e[t+8]=this.h[4]>>>0&255,e[t+9]=this.h[4]>>>8&255,e[t+10]=this.h[5]>>>0&255,e[t+11]=this.h[5]>>>8&255,e[t+12]=this.h[6]>>>0&255,e[t+13]=this.h[6]>>>8&255,e[t+14]=this.h[7]>>>0&255,e[t+15]=this.h[7]>>>8&255},S.prototype.update=function(e,t,n){var r,o;if(this.leftover){for((o=16-this.leftover)>n&&(o=n),r=0;r<o;r++)this.buffer[this.leftover+r]=e[t+r];if(n-=o,t+=o,this.leftover+=o,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(n>=16&&(o=n-n%16,this.blocks(e,t,o),t+=o,n-=o),n){for(r=0;r<n;r++)this.buffer[this.leftover+r]=e[t+r];this.leftover+=n}};var $=P,J=j,Y=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function z(e,t,n,r){for(var o,i,s,a,c,l,u,d,h,f,p,g,v,y,b,w,E,m,_,C,O,S,R,T,P,j,N=new Int32Array(16),A=new Int32Array(16),U=e[0],M=e[1],I=e[2],W=e[3],k=e[4],D=e[5],x=e[6],L=e[7],B=t[0],K=t[1],q=t[2],F=t[3],G=t[4],H=t[5],V=t[6],$=t[7],J=0;r>=128;){for(_=0;_<16;_++)C=8*_+J,N[_]=n[C+0]<<24|n[C+1]<<16|n[C+2]<<8|n[C+3],A[_]=n[C+4]<<24|n[C+5]<<16|n[C+6]<<8|n[C+7];for(_=0;_<80;_++)if(o=U,i=M,s=I,a=W,c=k,l=D,u=x,h=B,f=K,p=q,g=F,v=G,y=H,b=V,R=65535&(S=$),T=S>>>16,P=65535&(O=L),j=O>>>16,R+=65535&(S=(G>>>14|k<<18)^(G>>>18|k<<14)^(k>>>9|G<<23)),T+=S>>>16,P+=65535&(O=(k>>>14|G<<18)^(k>>>18|G<<14)^(G>>>9|k<<23)),j+=O>>>16,R+=65535&(S=G&H^~G&V),T+=S>>>16,P+=65535&(O=k&D^~k&x),j+=O>>>16,O=Y[2*_],R+=65535&(S=Y[2*_+1]),T+=S>>>16,P+=65535&O,j+=O>>>16,O=N[_%16],T+=(S=A[_%16])>>>16,P+=65535&O,j+=O>>>16,P+=(T+=(R+=65535&S)>>>16)>>>16,R=65535&(S=m=65535&R|T<<16),T=S>>>16,P=65535&(O=E=65535&P|(j+=P>>>16)<<16),j=O>>>16,R+=65535&(S=(B>>>28|U<<4)^(U>>>2|B<<30)^(U>>>7|B<<25)),T+=S>>>16,P+=65535&(O=(U>>>28|B<<4)^(B>>>2|U<<30)^(B>>>7|U<<25)),j+=O>>>16,T+=(S=B&K^B&q^K&q)>>>16,P+=65535&(O=U&M^U&I^M&I),j+=O>>>16,d=65535&(P+=(T+=(R+=65535&S)>>>16)>>>16)|(j+=P>>>16)<<16,w=65535&R|T<<16,R=65535&(S=g),T=S>>>16,P=65535&(O=a),j=O>>>16,T+=(S=m)>>>16,P+=65535&(O=E),j+=O>>>16,M=o,I=i,W=s,k=a=65535&(P+=(T+=(R+=65535&S)>>>16)>>>16)|(j+=P>>>16)<<16,D=c,x=l,L=u,U=d,K=h,q=f,F=p,G=g=65535&R|T<<16,H=v,V=y,$=b,B=w,_%16==15)for(C=0;C<16;C++)O=N[C],R=65535&(S=A[C]),T=S>>>16,P=65535&O,j=O>>>16,O=N[(C+9)%16],R+=65535&(S=A[(C+9)%16]),T+=S>>>16,P+=65535&O,j+=O>>>16,E=N[(C+1)%16],R+=65535&(S=((m=A[(C+1)%16])>>>1|E<<31)^(m>>>8|E<<24)^(m>>>7|E<<25)),T+=S>>>16,P+=65535&(O=(E>>>1|m<<31)^(E>>>8|m<<24)^E>>>7),j+=O>>>16,E=N[(C+14)%16],T+=(S=((m=A[(C+14)%16])>>>19|E<<13)^(E>>>29|m<<3)^(m>>>6|E<<26))>>>16,P+=65535&(O=(E>>>19|m<<13)^(m>>>29|E<<3)^E>>>6),j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,N[C]=65535&P|j<<16,A[C]=65535&R|T<<16;R=65535&(S=B),T=S>>>16,P=65535&(O=U),j=O>>>16,O=e[0],T+=(S=t[0])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[0]=U=65535&P|j<<16,t[0]=B=65535&R|T<<16,R=65535&(S=K),T=S>>>16,P=65535&(O=M),j=O>>>16,O=e[1],T+=(S=t[1])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[1]=M=65535&P|j<<16,t[1]=K=65535&R|T<<16,R=65535&(S=q),T=S>>>16,P=65535&(O=I),j=O>>>16,O=e[2],T+=(S=t[2])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[2]=I=65535&P|j<<16,t[2]=q=65535&R|T<<16,R=65535&(S=F),T=S>>>16,P=65535&(O=W),j=O>>>16,O=e[3],T+=(S=t[3])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[3]=W=65535&P|j<<16,t[3]=F=65535&R|T<<16,R=65535&(S=G),T=S>>>16,P=65535&(O=k),j=O>>>16,O=e[4],T+=(S=t[4])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[4]=k=65535&P|j<<16,t[4]=G=65535&R|T<<16,R=65535&(S=H),T=S>>>16,P=65535&(O=D),j=O>>>16,O=e[5],T+=(S=t[5])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[5]=D=65535&P|j<<16,t[5]=H=65535&R|T<<16,R=65535&(S=V),T=S>>>16,P=65535&(O=x),j=O>>>16,O=e[6],T+=(S=t[6])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[6]=x=65535&P|j<<16,t[6]=V=65535&R|T<<16,R=65535&(S=$),T=S>>>16,P=65535&(O=L),j=O>>>16,O=e[7],T+=(S=t[7])>>>16,P+=65535&O,j+=O>>>16,j+=(P+=(T+=(R+=65535&S)>>>16)>>>16)>>>16,e[7]=L=65535&P|j<<16,t[7]=$=65535&R|T<<16,J+=128,r-=128}return r}function Q(e,t,n){var r,o=new Int32Array(8),i=new Int32Array(8),s=new Uint8Array(256),a=n;for(o[0]=1779033703,o[1]=3144134277,o[2]=1013904242,o[3]=2773480762,o[4]=1359893119,o[5]=2600822924,o[6]=528734635,o[7]=1541459225,i[0]=4089235720,i[1]=2227873595,i[2]=4271175723,i[3]=1595750129,i[4]=2917565137,i[5]=725511199,i[6]=4215389547,i[7]=327033209,z(o,i,t,n),n%=128,r=0;r<n;r++)s[r]=t[a-n+r];for(s[n]=128,s[(n=256-128*(n<112?1:0))-9]=0,p(s,n-8,a/536870912|0,a<<3),z(o,i,s,n),r=0;r<8;r++)p(e,8*r,o[r],i[r]);return 0}function X(e,n){var r=t(),o=t(),i=t(),s=t(),a=t(),c=t(),l=t(),d=t(),h=t();x(r,e[1],e[0]),x(h,n[1],n[0]),L(r,r,h),D(o,e[0],e[1]),D(h,n[0],n[1]),L(o,o,h),L(i,e[3],n[3]),L(i,i,u),L(s,e[2],n[2]),D(s,s,s),x(a,o,r),x(c,s,i),D(l,s,i),D(d,o,r),L(e[0],a,c),L(e[1],d,l),L(e[2],l,c),L(e[3],a,d)}function Z(e,t,n){var r;for(r=0;r<4;r++)U(e[r],t[r],n)}function ee(e,n){var r=t(),o=t(),i=t();K(i,n[2]),L(r,n[0],i),L(o,n[1],i),M(e,o),e[31]^=W(r)<<7}function te(e,t,n){var r,o;for(N(e[0],s),N(e[1],a),N(e[2],a),N(e[3],s),o=255;o>=0;--o)Z(e,t,r=n[o/8|0]>>(7&o)&1),X(t,e),X(e,e),Z(e,t,r)}function ne(e,n){var r=[t(),t(),t(),t()];N(r[0],d),N(r[1],h),N(r[2],a),L(r[3],d,h),te(e,r,n)}function re(e,n,o){var i,s=new Uint8Array(64),a=[t(),t(),t(),t()];for(o||r(n,32),Q(s,n,32),s[0]&=248,s[31]&=127,s[31]|=64,ne(a,s),ee(e,a),i=0;i<32;i++)n[i+32]=e[i];return 0}var oe=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function ie(e,t){var n,r,o,i;for(r=63;r>=32;--r){for(n=0,o=r-32,i=r-12;o<i;++o)t[o]+=n-16*t[r]*oe[o-(r-32)],n=Math.floor((t[o]+128)/256),t[o]-=256*n;t[o]+=n,t[r]=0}for(n=0,o=0;o<32;o++)t[o]+=n-(t[31]>>4)*oe[o],n=t[o]>>8,t[o]&=255;for(o=0;o<32;o++)t[o]-=n*oe[o];for(r=0;r<32;r++)t[r+1]+=t[r]>>8,e[r]=255&t[r]}function se(e){var t,n=new Float64Array(64);for(t=0;t<64;t++)n[t]=e[t];for(t=0;t<64;t++)e[t]=0;ie(e,n)}function ae(e,n,r,o){var i,s,a=new Uint8Array(64),c=new Uint8Array(64),l=new Uint8Array(64),u=new Float64Array(64),d=[t(),t(),t(),t()];Q(a,o,32),a[0]&=248,a[31]&=127,a[31]|=64;var h=r+64;for(i=0;i<r;i++)e[64+i]=n[i];for(i=0;i<32;i++)e[32+i]=a[32+i];for(Q(l,e.subarray(32),r+32),se(l),ne(d,l),ee(e,d),i=32;i<64;i++)e[i]=o[i];for(Q(c,e,r+64),se(c),i=0;i<64;i++)u[i]=0;for(i=0;i<32;i++)u[i]=l[i];for(i=0;i<32;i++)for(s=0;s<32;s++)u[i+s]+=c[i]*a[s];return ie(e.subarray(32),u),h}function ce(e,n,r,o){var i,c=new Uint8Array(32),u=new Uint8Array(64),d=[t(),t(),t(),t()],h=[t(),t(),t(),t()];if(r<64)return-1;if(function(e,n){var r=t(),o=t(),i=t(),c=t(),u=t(),d=t(),h=t();return N(e[2],a),k(e[1],n),B(i,e[1]),L(c,i,l),x(i,i,e[2]),D(c,e[2],c),B(u,c),B(d,u),L(h,d,u),L(r,h,i),L(r,r,c),q(r,r),L(r,r,i),L(r,r,c),L(r,r,c),L(e[0],r,c),B(o,e[0]),L(o,o,c),I(o,i)&&L(e[0],e[0],f),B(o,e[0]),L(o,o,c),I(o,i)?-1:(W(e[0])===n[31]>>7&&x(e[0],s,e[0]),L(e[3],e[0],e[1]),0)}(h,o))return-1;for(i=0;i<r;i++)e[i]=n[i];for(i=0;i<32;i++)e[i+32]=o[i];if(Q(u,e,r),se(u),te(d,h,u),ne(h,n.subarray(32)),X(d,h),ee(c,d),r-=64,y(n,0,c,0)){for(i=0;i<r;i++)e[i]=0;return-1}for(i=0;i<r;i++)e[i]=n[i+64];return r}var le,ue=32,de=24,he=de,fe=64,pe=32,ge=64;function ve(e,t){if(e.length!==ue)throw new Error("bad key size");if(t.length!==de)throw new Error("bad nonce size")}function ye(){for(var e=0;e<arguments.length;e++)if(!(arguments[e]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function be(e){for(var t=0;t<e.length;t++)e[t]=0}e.lowlevel={crypto_core_hsalsa20:w,crypto_stream_xor:O,crypto_stream:C,crypto_stream_salsa20_xor:m,crypto_stream_salsa20:_,crypto_onetimeauth:R,crypto_onetimeauth_verify:T,crypto_verify_16:v,crypto_verify_32:y,crypto_secretbox:P,crypto_secretbox_open:j,crypto_scalarmult:F,crypto_scalarmult_base:G,crypto_box_beforenm:V,crypto_box_afternm:$,crypto_box:function(e,t,n,r,o,i){var s=new Uint8Array(32);return V(s,o,i),$(e,t,n,r,s)},crypto_box_open:function(e,t,n,r,o,i){var s=new Uint8Array(32);return V(s,o,i),J(e,t,n,r,s)},crypto_box_keypair:H,crypto_hash:Q,crypto_sign:ae,crypto_sign_keypair:re,crypto_sign_open:ce,crypto_secretbox_KEYBYTES:ue,crypto_secretbox_NONCEBYTES:de,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:he,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:fe,crypto_sign_PUBLICKEYBYTES:pe,crypto_sign_SECRETKEYBYTES:ge,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:t,D:l,L:oe,pack25519:M,unpack25519:k,M:L,A:D,S:B,Z:x,pow2523:q,add:X,set25519:N,modL:ie,scalarmult:te,scalarbase:ne},e.randomBytes=function(e){var t=new Uint8Array(e);return r(t,e),t},e.secretbox=function(e,t,n){ye(e,t,n),ve(n,t);for(var r=new Uint8Array(32+e.length),o=new Uint8Array(r.length),i=0;i<e.length;i++)r[i+32]=e[i];return P(o,r,r.length,t,n),o.subarray(16)},e.secretbox.open=function(e,t,n){ye(e,t,n),ve(n,t);for(var r=new Uint8Array(16+e.length),o=new Uint8Array(r.length),i=0;i<e.length;i++)r[i+16]=e[i];return r.length<32||0!==j(o,r,r.length,t,n)?null:o.subarray(32)},e.secretbox.keyLength=ue,e.secretbox.nonceLength=de,e.secretbox.overheadLength=16,e.scalarMult=function(e,t){if(ye(e,t),32!==e.length)throw new Error("bad n size");if(32!==t.length)throw new Error("bad p size");var n=new Uint8Array(32);return F(n,e,t),n},e.scalarMult.base=function(e){if(ye(e),32!==e.length)throw new Error("bad n size");var t=new Uint8Array(32);return G(t,e),t},e.scalarMult.scalarLength=32,e.scalarMult.groupElementLength=32,e.box=function(t,n,r,o){var i=e.box.before(r,o);return e.secretbox(t,n,i)},e.box.before=function(e,t){ye(e,t),function(e,t){if(32!==e.length)throw new Error("bad public key size");if(32!==t.length)throw new Error("bad secret key size")}(e,t);var n=new Uint8Array(32);return V(n,e,t),n},e.box.after=e.secretbox,e.box.open=function(t,n,r,o){var i=e.box.before(r,o);return e.secretbox.open(t,n,i)},e.box.open.after=e.secretbox.open,e.box.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(32);return H(e,t),{publicKey:e,secretKey:t}},e.box.keyPair.fromSecretKey=function(e){if(ye(e),32!==e.length)throw new Error("bad secret key size");var t=new Uint8Array(32);return G(t,e),{publicKey:t,secretKey:new Uint8Array(e)}},e.box.publicKeyLength=32,e.box.secretKeyLength=32,e.box.sharedKeyLength=32,e.box.nonceLength=he,e.box.overheadLength=e.secretbox.overheadLength,e.sign=function(e,t){if(ye(e,t),t.length!==ge)throw new Error("bad secret key size");var n=new Uint8Array(fe+e.length);return ae(n,e,e.length,t),n},e.sign.open=function(e,t){if(ye(e,t),t.length!==pe)throw new Error("bad public key size");var n=new Uint8Array(e.length),r=ce(n,e,e.length,t);if(r<0)return null;for(var o=new Uint8Array(r),i=0;i<o.length;i++)o[i]=n[i];return o},e.sign.detached=function(t,n){for(var r=e.sign(t,n),o=new Uint8Array(fe),i=0;i<o.length;i++)o[i]=r[i];return o},e.sign.detached.verify=function(e,t,n){if(ye(e,t,n),t.length!==fe)throw new Error("bad signature size");if(n.length!==pe)throw new Error("bad public key size");var r,o=new Uint8Array(fe+e.length),i=new Uint8Array(fe+e.length);for(r=0;r<fe;r++)o[r]=t[r];for(r=0;r<e.length;r++)o[r+fe]=e[r];return ce(i,o,o.length,n)>=0},e.sign.keyPair=function(){var e=new Uint8Array(pe),t=new Uint8Array(ge);return re(e,t),{publicKey:e,secretKey:t}},e.sign.keyPair.fromSecretKey=function(e){if(ye(e),e.length!==ge)throw new Error("bad secret key size");for(var t=new Uint8Array(pe),n=0;n<t.length;n++)t[n]=e[32+n];return{publicKey:t,secretKey:new Uint8Array(e)}},e.sign.keyPair.fromSeed=function(e){if(ye(e),32!==e.length)throw new Error("bad seed size");for(var t=new Uint8Array(pe),n=new Uint8Array(ge),r=0;r<32;r++)n[r]=e[r];return re(t,n,!0),{publicKey:t,secretKey:n}},e.sign.publicKeyLength=pe,e.sign.secretKeyLength=ge,e.sign.seedLength=32,e.sign.signatureLength=fe,e.hash=function(e){ye(e);var t=new Uint8Array(64);return Q(t,e,e.length),t},e.hash.hashLength=64,e.verify=function(e,t){return ye(e,t),0!==e.length&&0!==t.length&&e.length===t.length&&0===g(e,0,t,0,e.length)},e.setPRNG=function(e){r=e},(le="undefined"!=typeof self?self.crypto||self.msCrypto:null)&&le.getRandomValues?e.setPRNG((function(e,t){var n,r=new Uint8Array(t);for(n=0;n<t;n+=65536)le.getRandomValues(r.subarray(n,n+Math.min(t-n,65536)));for(n=0;n<t;n++)e[n]=r[n];be(r)})):(le=n(5338))&&le.randomBytes&&e.setPRNG((function(e,t){var n,r=le.randomBytes(t);for(n=0;n<t;n++)e[n]=r[n];be(r)}))}(e.exports?e.exports:self.nacl=self.nacl||{})},618:()=>{},3783:()=>{},5338:()=>{},9461:(e,t,n)=>{"use strict";var r,o,i,s,a,c,l=n(251),u=n(717);t.CONNECT_EVENT_ERROR_CODES=void 0,(r=t.CONNECT_EVENT_ERROR_CODES||(t.CONNECT_EVENT_ERROR_CODES={}))[r.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",r[r.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",r[r.MANIFEST_NOT_FOUND_ERROR=2]="MANIFEST_NOT_FOUND_ERROR",r[r.MANIFEST_CONTENT_ERROR=3]="MANIFEST_CONTENT_ERROR",r[r.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",r[r.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",r[r.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED",t.CONNECT_ITEM_ERROR_CODES=void 0,(o=t.CONNECT_ITEM_ERROR_CODES||(t.CONNECT_ITEM_ERROR_CODES={}))[o.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",o[o.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED",t.SEND_TRANSACTION_ERROR_CODES=void 0,(i=t.SEND_TRANSACTION_ERROR_CODES||(t.SEND_TRANSACTION_ERROR_CODES={}))[i.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",i[i.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",i[i.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",i[i.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",i[i.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED",t.SIGN_DATA_ERROR_CODES=void 0,(s=t.SIGN_DATA_ERROR_CODES||(t.SIGN_DATA_ERROR_CODES={}))[s.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",s[s.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",s[s.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",s[s.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",s[s.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED",t.DISCONNECT_ERROR_CODES=void 0,(a=t.DISCONNECT_ERROR_CODES||(t.DISCONNECT_ERROR_CODES={}))[a.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",a[a.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",a[a.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",a[a.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED",t.CHAIN=void 0,(c=t.CHAIN||(t.CHAIN={})).MAINNET="-239",c.TESTNET="-3";const d={encode:function(e,t=!1){let n;return e instanceof Uint8Array?n=e:("string"!=typeof e&&(e=JSON.stringify(e)),n=l.decodeUTF8(e)),function(e,t){const n=l.encodeBase64(e);return t?encodeURIComponent(n):n}(n,t)},decode:function(e,t=!1){const n=function(e,t){return t&&(e=decodeURIComponent(e)),l.decodeBase64(e)}(e,t);return{toString:()=>l.encodeUTF8(n),toObject(){try{return JSON.parse(l.encodeUTF8(n))}catch(e){return null}},toUint8Array:()=>n}}};function h(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}function f(e,t){if(t>=e.length)throw new Error("Index is out of buffer");return[e.slice(0,t),e.slice(t)]}function p(e){let t="";return e.forEach((e=>{t+=("0"+(255&e).toString(16)).slice(-2)})),t}function g(e){if(e.length%2!=0)throw new Error(`Cannot convert ${e} to bytesArray`);const t=new Uint8Array(e.length/2);for(let n=0;n<e.length;n+=2)t[n/2]=parseInt(e.slice(n,n+2),16);return t}t.Base64=d,t.SessionCrypto=class{constructor(e){this.nonceLength=24,this.keyPair=e?this.createKeypairFromString(e):this.createKeypair(),this.sessionId=p(this.keyPair.publicKey)}createKeypair(){return u.box.keyPair()}createKeypairFromString(e){return{publicKey:g(e.publicKey),secretKey:g(e.secretKey)}}createNonce(){return u.randomBytes(this.nonceLength)}encrypt(e,t){const n=(new TextEncoder).encode(e),r=this.createNonce();return h(r,u.box(n,r,t,this.keyPair.secretKey))}decrypt(e,t){const[n,r]=f(e,this.nonceLength),o=u.box.open(r,n,t,this.keyPair.secretKey);if(!o)throw new Error(`Decryption error: \n message: ${e.toString()} \n sender pubkey: ${t.toString()} \n keypair pubkey: ${this.keyPair.publicKey.toString()} \n keypair secretkey: ${this.keyPair.secretKey.toString()}`);return(new TextDecoder).decode(o)}stringifyKeypair(){return{publicKey:p(this.keyPair.publicKey),secretKey:p(this.keyPair.secretKey)}}},t.concatUint8Arrays=h,t.hexToByteArray=g,t.isNode=function(){return"undefined"!=typeof process&&null!=process.versions&&null!=process.versions.node},t.splitToUint8Arrays=f,t.toHexString=p}},t={},n=function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}(1920);TonConnectSDK=n})();
//# sourceMappingURL=tonconnect-sdk.min.js.map