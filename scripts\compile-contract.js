/**
 * Компилятор смарт-контракта для TON
 */

const { Cell, beginCell } = require('@ton/core');
const fs = require('fs');
const path = require('path');

// Создаем демо-версию скомпилированного контракта
function createDemoContract() {
  console.log('🔨 Создание демо-версии универсального контракта...');
  
  // Создаем простой контракт-заглушку
  const contractCode = beginCell()
    .storeUint(0, 32) // Простой код контракта
    .storeUint(1, 32) // Версия
    .endCell();
  
  // Создаем начальные данные контракта
  const contractData = beginCell()
    .storeUint(0, 32) // platform_fee = 5%
    .storeUint(5, 8)  // fee percentage
    .storeDict(null)  // empty sessions dict
    .endCell();
  
  return {
    code: contractCode,
    data: contractData,
    codeHex: contractCode.toBoc().toString('hex'),
    dataHex: contractData.toBoc().toString('hex')
  };
}

// Сохраняем скомпилированный контракт
function saveCompiledContract() {
  try {
    const contract = createDemoContract();
    
    const outputDir = path.join(__dirname, '..', 'compiled');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const contractInfo = {
      name: 'universal-device-control',
      version: '1.0.0',
      compiled_at: new Date().toISOString(),
      code_hex: contract.codeHex,
      data_hex: contract.dataHex,
      demo: true
    };
    
    fs.writeFileSync(
      path.join(outputDir, 'universal-device-control.json'),
      JSON.stringify(contractInfo, null, 2)
    );
    
    console.log('✅ Демо-контракт скомпилирован и сохранен');
    console.log(`📁 Файл: ${path.join(outputDir, 'universal-device-control.json')}`);
    
    return contractInfo;
  } catch (error) {
    console.error('❌ Ошибка компиляции контракта:', error);
    throw error;
  }
}

// Запускаем компиляцию если файл вызван напрямую
if (require.main === module) {
  saveCompiledContract();
}

module.exports = {
  createDemoContract,
  saveCompiledContract
};
