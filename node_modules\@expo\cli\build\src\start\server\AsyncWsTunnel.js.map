{"version": 3, "sources": ["../../../../src/start/server/AsyncWsTunnel.ts"], "sourcesContent": ["import * as tunnel from '@expo/ws-tunnel';\nimport chalk from 'chalk';\nimport * as fs from 'node:fs';\nimport { tmpdir, hostname } from 'node:os';\nimport * as path from 'node:path';\n\nimport * as Log from '../../log';\nimport { env, envIsWebcontainer } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\n\nconst debug = require('debug')('expo:start:server:ws-tunnel') as typeof console.log;\n\nexport class AsyncWsTunnel {\n  /** Info about the currently running instance of tunnel. */\n  private serverUrl: string | null = null;\n\n  constructor(_projectRoot: string, port: number) {\n    if (port !== 8081) {\n      throw new CommandError(\n        'WS_TUNNEL_PORT',\n        `WS-tunnel only supports tunneling over port 8081, attempted to use port ${port}`\n      );\n    }\n  }\n\n  public getActiveUrl(): string | null {\n    return this.serverUrl;\n  }\n\n  async startAsync(): Promise<void> {\n    this.serverUrl = await tunnel.startAsync({\n      ...getTunnelOptions(),\n      onStatusChange(status) {\n        if (status === 'disconnected') {\n          Log.error(\n            chalk.red(\n              'Tunnel connection has been closed. This is often related to intermittent connection problems with the ws proxy servers. Restart the dev server to try connecting again.'\n            ) + chalk.gray('\\nCheck the Expo status page for outages: https://status.expo.dev/')\n          );\n        }\n      },\n    });\n\n    debug('Tunnel URL:', this.serverUrl);\n  }\n\n  async stopAsync(): Promise<void> {\n    debug('Stopping Tunnel');\n    await tunnel.stopAsync();\n    this.serverUrl = null;\n  }\n}\n\n// Generate a base-36 string of 5 characters (from 32 bits of randomness)\nfunction randomStr() {\n  return (Math.random().toString(36) + '00000').slice(2, 7);\n}\n\nfunction getTunnelSession(): string {\n  let session = randomStr() + randomStr() + randomStr();\n  if (envIsWebcontainer()) {\n    const leaseId = Buffer.from(hostname()).toString('base64url');\n    const leaseFile = path.join(tmpdir(), `_ws_tunnel_lease_${leaseId}`);\n    try {\n      session = fs.readFileSync(leaseFile, 'utf8').trim() || session;\n    } catch {}\n    try {\n      fs.writeFileSync(leaseFile, session, 'utf8');\n    } catch {}\n  }\n  return session;\n}\n\nfunction getTunnelOptions() {\n  const userDefinedSubdomain = env.EXPO_TUNNEL_SUBDOMAIN;\n  if (userDefinedSubdomain && typeof userDefinedSubdomain === 'string') {\n    debug('Session:', userDefinedSubdomain);\n    return { session: userDefinedSubdomain };\n  } else {\n    const session = getTunnelSession();\n    return { session };\n  }\n}\n"], "names": ["AsyncWsTunnel", "debug", "require", "constructor", "_projectRoot", "port", "serverUrl", "CommandError", "getActiveUrl", "startAsync", "tunnel", "getTunnelOptions", "onStatusChange", "status", "Log", "error", "chalk", "red", "gray", "stopAsync", "randomStr", "Math", "random", "toString", "slice", "getTunnelSession", "session", "envIsWebcontainer", "leaseId", "<PERSON><PERSON><PERSON>", "from", "hostname", "leaseFile", "path", "join", "tmpdir", "fs", "readFileSync", "trim", "writeFileSync", "userDefinedSubdomain", "env", "EXPO_TUNNEL_SUBDOMAIN"], "mappings": ";;;;+BAYaA;;;eAAAA;;;;iEAZW;;;;;;;gEACN;;;;;;;iEACE;;;;;;;yBACa;;;;;;;iEACX;;;;;;6DAED;qBACkB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAExB,MAAMF;IAIXG,YAAYC,YAAoB,EAAEC,IAAY,CAAE;QAHhD,yDAAyD,QACjDC,YAA2B;QAGjC,IAAID,SAAS,MAAM;YACjB,MAAM,IAAIE,oBAAY,CACpB,kBACA,CAAC,wEAAwE,EAAEF,MAAM;QAErF;IACF;IAEOG,eAA8B;QACnC,OAAO,IAAI,CAACF,SAAS;IACvB;IAEA,MAAMG,aAA4B;QAChC,IAAI,CAACH,SAAS,GAAG,MAAMI,YAAOD,UAAU,CAAC;YACvC,GAAGE,kBAAkB;YACrBC,gBAAeC,MAAM;gBACnB,IAAIA,WAAW,gBAAgB;oBAC7BC,KAAIC,KAAK,CACPC,gBAAK,CAACC,GAAG,CACP,6KACED,gBAAK,CAACE,IAAI,CAAC;gBAEnB;YACF;QACF;QAEAjB,MAAM,eAAe,IAAI,CAACK,SAAS;IACrC;IAEA,MAAMa,YAA2B;QAC/BlB,MAAM;QACN,MAAMS,YAAOS,SAAS;QACtB,IAAI,CAACb,SAAS,GAAG;IACnB;AACF;AAEA,yEAAyE;AACzE,SAASc;IACP,OAAO,AAACC,CAAAA,KAAKC,MAAM,GAAGC,QAAQ,CAAC,MAAM,OAAM,EAAGC,KAAK,CAAC,GAAG;AACzD;AAEA,SAASC;IACP,IAAIC,UAAUN,cAAcA,cAAcA;IAC1C,IAAIO,IAAAA,sBAAiB,KAAI;QACvB,MAAMC,UAAUC,OAAOC,IAAI,CAACC,IAAAA,kBAAQ,KAAIR,QAAQ,CAAC;QACjD,MAAMS,YAAYC,YAAKC,IAAI,CAACC,IAAAA,gBAAM,KAAI,CAAC,iBAAiB,EAAEP,SAAS;QACnE,IAAI;YACFF,UAAUU,UAAGC,YAAY,CAACL,WAAW,QAAQM,IAAI,MAAMZ;QACzD,EAAE,OAAM,CAAC;QACT,IAAI;YACFU,UAAGG,aAAa,CAACP,WAAWN,SAAS;QACvC,EAAE,OAAM,CAAC;IACX;IACA,OAAOA;AACT;AAEA,SAASf;IACP,MAAM6B,uBAAuBC,QAAG,CAACC,qBAAqB;IACtD,IAAIF,wBAAwB,OAAOA,yBAAyB,UAAU;QACpEvC,MAAM,YAAYuC;QAClB,OAAO;YAAEd,SAASc;QAAqB;IACzC,OAAO;QACL,MAAMd,UAAUD;QAChB,OAAO;YAAEC;QAAQ;IACnB;AACF"}