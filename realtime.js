const WebSocket = require('ws');

// Переменная для хранения сервера
let realtimeWSS = null;

// Функция инициализации сервера
function initRealtimeServer() {
  if (realtimeWSS) {
    return realtimeWSS; // Уже инициализирован
  }

  // Создаем WebSocket-сервер на порту 4001
  realtimeWSS = new WebSocket.Server({ port: 4001 });
  console.log('Realtime WebSocket сервер запущен на порту 4001');

  return realtimeWSS;
}

// Функция настройки обработчиков событий
function setupRealtimeHandlers() {
  if (!realtimeWSS) {
    initRealtimeServer();
  }

  realtimeWSS.on('connection', (ws) => {
    // Не логируем подключение клиента
    ws.isAlive = true;

    // Обработка PONG для обновления флага активности
    ws.on('pong', () => {
      ws.isAlive = true;
    });

    ws.send(JSON.stringify({ type: 'info', message: 'Подключение установлено' }));

    ws.on('close', () => {
      // Не логируем отключение клиента
    });

    ws.on('error', (error) => {
      console.error('Realtime WebSocket: ошибка соединения', error);
    });
  });

  // Интервал пинга для realtime сервера
  const interval = setInterval(() => {
    if (realtimeWSS && realtimeWSS.clients) {
      realtimeWSS.clients.forEach((ws) => {
        if (!ws.isAlive) return ws.terminate();
        ws.isAlive = false;
        ws.ping();
      });
    }
  }, 5000);

  realtimeWSS.on('close', () => {
    clearInterval(interval);
  });
}

// Функция рассылки обновлений всем клиентам
function broadcastUpdate(data) {
  if (!realtimeWSS) {
    initRealtimeServer();
    setupRealtimeHandlers();
  }

  console.log('Realtime WebSocket: рассылка обновления', data);
  if (realtimeWSS && realtimeWSS.clients) {
    realtimeWSS.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(data));
      }
    });
  }
}

module.exports = {
  initRealtimeServer,
  setupRealtimeHandlers,
  broadcastUpdate,
  broadcastDeviceStatusUpdate: (deviceData) => {
    broadcastUpdate({
      type: 'device_status_update',
      device: deviceData
    });
  }
};
