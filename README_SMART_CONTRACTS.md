# 🚀 Быстрый старт с универсальным смарт-контрактом

## 📋 Что было добавлено

Ваш проект теперь использует **ОДИН УНИВЕРСАЛЬНЫЙ СМАРТ-КОНТРАКТ** для всех устройств с мемо-фразами для идентификации!

### ✨ Новая архитектура:

1. **🔐 Один универсальный контракт** - для всех устройств и стримеров
2. **📝 Мемо-фразы** - для идентификации стримера и устройства
3. **💰 Накопительная система** - средства накапливаются в одном контракте
4. **🏦 Безопасный вывод** - только владельцы кошельков могут выводить средства
5. **⚡ Быстрая и стабильная** - оптимизированная архитектура

## 🚀 Быстрый запуск

### 1. Установка зависимостей
```bash
npm install
```

### 2. Настройка окружения
```bash
# Скопируйте пример конфигурации
cp .env.example .env

# Отредактируйте .env файл
# Минимально необходимо:
# - TON_API_KEY (получить на https://toncenter.com)
# - PLATFORM_ADDRESS (адрес кошелька платформы)
# - PLATFORM_MNEMONIC (мнемоника кошелька платформы)
```

### 3. Автоматическая настройка
```bash
# Запустите скрипт автоматической настройки
npm run setup-contracts
```

### 4. Компиляция контрактов
```bash
# Скомпилируйте смарт-контракты
npm run compile-contracts
```

### 5. Запуск сервера
```bash
# Для разработки
npm run dev

# Для продакшена
npm run prod
```

## 🎯 Основные URL

- **Главная страница**: http://localhost:4000
- **Админ-панель**: http://localhost:4000/admin-panel
- **Настройки стримера**: http://localhost:4000/streamer-settings
- **Страница стримера**: http://localhost:4000/streamer/nickname

## 🔧 Архитектура универсального смарт-контракта

### Один контракт для всех устройств
```
Все стримеры → УНИВЕРСАЛЬНЫЙ КОНТРАКТ ← Все пользователи
├── Стример А (memo: "EQD...:device_1") → Накопленные средства
├── Стример Б (memo: "EQC...:device_2") → Накопленные средства
├── Стример В (memo: "EQB...:device_3") → Накопленные средства
└── Комиссия платформы → Накопленные комиссии
```

### Формат мемо-фразы:
```
{адрес_стримера}:{id_устройства}
Пример: "EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t:AA:BB:CC:DD:EE:FF"
```

### Процесс работы:
1. **Деплой контракта** - один раз администратором платформы
2. **Покупка времени** - пользователь отправляет TON с мемо-фразой
3. **Автоматическое распределение** - контракт распределяет средства
4. **Управление** - доступ предоставляется на оплаченное время
5. **Вывод средств** - стримеры и админ выводят накопленные средства

## 💰 Экономическая модель

### Распределение платежей:
- **Стример**: 95% (настраивается)
- **Платформа**: 5% (настраивается)

### Накопление средств:
- Средства накапливаются в смарт-контрактах
- Стримеры могут выводить в любое время
- Администраторы выводят комиссии через админ-панель

## 🛡️ Безопасность

### На уровне смарт-контрактов:
- ✅ Проверка платежей
- ✅ Контроль времени доступа
- ✅ Защита от повторных атак
- ✅ Автоматическое распределение средств

### На уровне сервера:
- ✅ Валидация всех входных данных
- ✅ Проверка прав доступа
- ✅ Логирование всех операций
- ✅ Мониторинг транзакций

## 📊 Мониторинг и аналитика

### Админ-панель предоставляет:
- 📈 Статистику платформы
- 💰 Накопленные комиссии
- 🔄 Активные устройства
- 📋 История транзакций

### Логирование:
- Все операции записываются в `service.log`
- Детальное логирование транзакций
- Мониторинг ошибок и предупреждений

## 🔌 API для интеграции

### Основные endpoints:

#### Универсальный контракт:
```javascript
// Деплой универсального контракта (один раз)
POST /api/contracts/deploy-universal-contract
{
  "admin_wallet": "EQD..."
}

// Информация о контракте
GET /api/contracts/universal-contract-info

// Покупка времени управления (с мемо)
POST /api/contracts/buy-time
{
  "device_id": "AA:BB:CC:DD:EE:FF",
  "duration_minutes": 5,
  "buyer_wallet": "EQD...",
  "streamer_nickname": "streamer123"
}

// Вывод средств стримера
POST /api/contracts/withdraw-earnings
{
  "streamer_wallet": "EQD..."
}

// Получение баланса стримера
GET /api/contracts/streamer-balance/EQD...
```

#### Администрирование:
```javascript
// Статистика платформы
GET /api/transactions/platform/stats

// Вывод комиссий платформы
POST /api/transactions/platform/withdraw-all-fees
{
  "admin_wallet": "EQD..."
}
```

## 🔧 Настройка для продакшена

### 1. Переменные окружения:
```bash
NODE_ENV=production
TON_NETWORK=mainnet
TON_API_KEY=your_mainnet_api_key
PLATFORM_ADDRESS=your_production_wallet
PLATFORM_MNEMONIC="your production mnemonic"
```

### 2. Безопасность:
- Используйте отдельный кошелек для продакшена
- Регулярно ротируйте API ключи
- Настройте мониторинг и алерты
- Используйте HTTPS для всех соединений

### 3. Масштабирование:
- Настройте пул соединений к базе данных
- Используйте кэширование для часто запрашиваемых данных
- Настройте балансировку нагрузки
- Мониторьте производительность

## 🆘 Устранение неполадок

### Частые проблемы:

1. **Ошибка компиляции контракта**
   ```bash
   npm install @ton/blueprint
   npm run compile-contracts
   ```

2. **Ошибка подключения к TON**
   - Проверьте API ключ
   - Убедитесь в правильности сети (testnet/mainnet)
   - Проверьте интернет-соединение

3. **Ошибка деплоя контракта**
   - Проверьте баланс кошелька платформы
   - Убедитесь в корректности мнемоники
   - Проверьте права доступа

### Полезные команды:
```bash
# Проверка настройки
npm run setup-contracts

# Просмотр логов
tail -f service.log

# Проверка статуса сервисов
curl http://localhost:4000/api/transactions/platform/stats
```

## 📚 Дополнительная документация

- 📖 [Подробная документация](docs/SMART_CONTRACTS_INTEGRATION.md)
- 🔧 [Конфигурация](config/contracts.js)
- 📝 [Примеры API](docs/API_EXAMPLES.md)

## 🎉 Готово!

Ваша платформа теперь полностью интегрирована со смарт-контрактами TON!

### Что дальше:
1. Настройте переменные окружения
2. Запустите автоматическую настройку
3. Протестируйте функциональность
4. Запустите в продакшен

**Удачи с вашим проектом! 🚀**
