/**
 * Конфигурация смарт-контрактов
 */

module.exports = {
  // Настройки сети TON
  network: {
    // Основная сеть (mainnet)
    mainnet: {
      endpoint: 'https://toncenter.com/api/v2/jsonRPC',
      apiKey: process.env.TON_API_KEY || null,
      workchain: 0
    },
    
    // Тестовая сеть (testnet)
    testnet: {
      endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',
      apiKey: process.env.TON_TESTNET_API_KEY || null,
      workchain: 0
    }
  },

  // Настройки платформы
  platform: {
    // Адрес кошелька платформы для получения комиссий
    address: process.env.PLATFORM_WALLET_ADDRESS || null,
    
    // Мнемоническая фраза кошелька платформы (для автоматического деплоя)
    mnemonic: process.env.PLATFORM_WALLET_MNEMONIC || null,
    
    // Комиссия платформы по умолчанию (в базисных пунктах, 500 = 5%)
    defaultFeePercent: parseInt(process.env.PLATFORM_FEE_PERCENT) || 500,
    
    // Минимальная комиссия платформы
    minFeePercent: 100, // 1%
    
    // Максимальная комиссия платформы
    maxFeePercent: 2000 // 20%
  },

  // Настройки контрактов устройств
  deviceContract: {
    // Минимальная цена за минуту (в TON)
    minPricePerMinute: '0.001',
    
    // Максимальная цена за минуту (в TON)
    maxPricePerMinute: '10.0',
    
    // Максимальное время управления за одну покупку (в минутах)
    maxControlTimeMinutes: 60,
    
    // Минимальное время управления за одну покупку (в минутах)
    minControlTimeMinutes: 1,
    
    // Газ для деплоя контракта (в TON)
    deployGas: '0.1',
    
    // Газ для операций с контрактом (в TON)
    operationGas: '0.05'
  },

  // Настройки безопасности
  security: {
    // Максимальное количество активных контрактов на один кошелек
    maxContractsPerWallet: 10,
    
    // Время жизни транзакции (в секундах)
    transactionTtl: 300, // 5 минут
    
    // Максимальное время ожидания подтверждения транзакции (в секундах)
    confirmationTimeout: 600, // 10 минут
    
    // Разрешенные адреса для административных операций
    adminAddresses: (process.env.ADMIN_ADDRESSES || '').split(',').filter(addr => addr.trim())
  },

  // Настройки мониторинга
  monitoring: {
    // Интервал синхронизации балансов контрактов (в миллисекундах)
    syncInterval: 60000, // 1 минута
    
    // Интервал проверки истекших сессий управления (в миллисекундах)
    sessionCheckInterval: 30000, // 30 секунд
    
    // Включить автоматическую синхронизацию
    autoSync: true,
    
    // Логирование операций с контрактами
    logOperations: true
  },

  // Настройки для разработки
  development: {
    // Использовать тестовую сеть
    useTestnet: process.env.NODE_ENV !== 'production',
    
    // Автоматически создавать контракты для новых устройств
    autoCreateContracts: false,
    
    // Пропускать проверки безопасности
    skipSecurityChecks: false,
    
    // Использовать моковые данные
    useMockData: false
  }
};

/**
 * Получение конфигурации для текущей среды
 */
function getConfig() {
  const config = module.exports;
  const isProduction = process.env.NODE_ENV === 'production';
  const useTestnet = config.development.useTestnet;
  
  return {
    ...config,
    network: useTestnet ? config.network.testnet : config.network.mainnet,
    isProduction,
    useTestnet
  };
}

/**
 * Валидация конфигурации
 */
function validateConfig() {
  const config = getConfig();
  const errors = [];

  // Проверка обязательных параметров для продакшена
  if (config.isProduction) {
    if (!config.platform.address) {
      errors.push('PLATFORM_WALLET_ADDRESS не установлен для продакшена');
    }
    
    if (!config.network.apiKey) {
      errors.push('TON_API_KEY не установлен для продакшена');
    }
  }

  // Проверка корректности комиссии платформы
  if (config.platform.defaultFeePercent < config.platform.minFeePercent ||
      config.platform.defaultFeePercent > config.platform.maxFeePercent) {
    errors.push(`Комиссия платформы должна быть между ${config.platform.minFeePercent} и ${config.platform.maxFeePercent} базисными пунктами`);
  }

  // Проверка корректности цен
  const minPrice = parseFloat(config.deviceContract.minPricePerMinute);
  const maxPrice = parseFloat(config.deviceContract.maxPricePerMinute);
  
  if (minPrice >= maxPrice) {
    errors.push('Минимальная цена должна быть меньше максимальной');
  }

  if (errors.length > 0) {
    throw new Error('Ошибки конфигурации:\n' + errors.join('\n'));
  }

  return config;
}

module.exports.getConfig = getConfig;
module.exports.validateConfig = validateConfig;
