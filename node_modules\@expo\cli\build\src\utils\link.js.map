{"version": 3, "sources": ["../../../src/utils/link.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport terminalLink from 'terminal-link';\n\n/**\n * Prints a link for given URL, using text if provided, otherwise text is just the URL.\n * Format links as dim (unless disabled) and with an underline.\n *\n * @example https://expo.dev\n */\nexport function link(\n  url: string,\n  { text = url, dim = true }: { text?: string; dim?: boolean } = {}\n): string {\n  let output: string;\n  // Links can be disabled via env variables https://github.com/jamestalmage/supports-hyperlinks/blob/master/index.js\n  if (terminalLink.isSupported) {\n    output = terminalLink(text, url);\n  } else {\n    output = `${text === url ? '' : text + ': '}${chalk.underline(url)}`;\n  }\n  return dim ? chalk.dim(output) : output;\n}\n\n/**\n * Provide a consistent \"Learn more\" link experience.\n * Format links as dim (unless disabled) with an underline.\n *\n * @example [Learn more](https://expo.dev)\n * @example Learn more: https://expo.dev\n */\nexport function learnMore(\n  url: string,\n  {\n    learnMoreMessage: maybeLearnMoreMessage,\n    dim = true,\n  }: { learnMoreMessage?: string; dim?: boolean } = {}\n): string {\n  return link(url, { text: maybeLearnMoreMessage ?? 'Learn more', dim });\n}\n"], "names": ["learnMore", "link", "url", "text", "dim", "output", "terminalLink", "isSupported", "chalk", "underline", "learnMoreMessage", "maybeLearnMoreMessage"], "mappings": ";;;;;;;;;;;IA8BgBA,SAAS;eAATA;;IArBAC,IAAI;eAAJA;;;;gEATE;;;;;;;gEACO;;;;;;;;;;;AAQlB,SAASA,KACdC,GAAW,EACX,EAAEC,OAAOD,GAAG,EAAEE,MAAM,IAAI,EAAoC,GAAG,CAAC,CAAC;IAEjE,IAAIC;IACJ,mHAAmH;IACnH,IAAIC,uBAAY,CAACC,WAAW,EAAE;QAC5BF,SAASC,IAAAA,uBAAY,EAACH,MAAMD;IAC9B,OAAO;QACLG,SAAS,GAAGF,SAASD,MAAM,KAAKC,OAAO,OAAOK,gBAAK,CAACC,SAAS,CAACP,MAAM;IACtE;IACA,OAAOE,MAAMI,gBAAK,CAACJ,GAAG,CAACC,UAAUA;AACnC;AASO,SAASL,UACdE,GAAW,EACX,EACEQ,kBAAkBC,qBAAqB,EACvCP,MAAM,IAAI,EACmC,GAAG,CAAC,CAAC;IAEpD,OAAOH,KAAKC,KAAK;QAAEC,MAAMQ,yBAAyB;QAAcP;IAAI;AACtE"}