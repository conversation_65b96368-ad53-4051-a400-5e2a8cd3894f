{"version": 3, "sources": ["../../../../../src/start/server/middleware/ContextModuleSourceMapsMiddleware.ts"], "sourcesContent": ["import { ServerRequest, ServerResponse } from './server.types';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:metro-context-modules'\n) as typeof console.log;\n\n/**\n * Source maps for `require.context` modules aren't supported in the Metro dev server\n * we should intercept the request and return a noop response to prevent Chrome/Metro\n * from erroring out.\n */\nexport class ContextModuleSourceMapsMiddleware {\n  getHandler() {\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n        return next();\n      }\n\n      if (req.url.match(/%3Fctx=[\\d\\w\\W]+\\.map\\?/)) {\n        debug('Skipping sourcemap request for context module %s', req.url);\n        // Return a noop response for the sourcemap\n        res.writeHead(200, {\n          'Content-Type': 'application/json',\n        });\n        res.end('{}');\n        return;\n      }\n\n      next();\n    };\n  }\n}\n"], "names": ["ContextModuleSourceMapsMiddleware", "debug", "require", "<PERSON><PERSON><PERSON><PERSON>", "req", "res", "next", "url", "method", "match", "writeHead", "end"], "mappings": ";;;;+BAWaA;;;eAAAA;;;AATb,MAAMC,QAAQC,QAAQ,SACpB;AAQK,MAAMF;IACXG,aAAa;QACX,OAAO,CAACC,KAAoBC,KAAqBC;YAC/C,IAAI,EAACF,uBAAAA,IAAKG,GAAG,KAAKH,IAAII,MAAM,KAAK,SAASJ,IAAII,MAAM,KAAK,QAAS;gBAChE,OAAOF;YACT;YAEA,IAAIF,IAAIG,GAAG,CAACE,KAAK,CAAC,4BAA4B;gBAC5CR,MAAM,oDAAoDG,IAAIG,GAAG;gBACjE,2CAA2C;gBAC3CF,IAAIK,SAAS,CAAC,KAAK;oBACjB,gBAAgB;gBAClB;gBACAL,IAAIM,GAAG,CAAC;gBACR;YACF;YAEAL;QACF;IACF;AACF"}