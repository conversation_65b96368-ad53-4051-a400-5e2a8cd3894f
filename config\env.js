// config/env.js
require("dotenv").config();

module.exports = {
  HTTP_PORT: process.env.HTTP_PORT || 4000,
  WS_PORT: process.env.WS_PORT || 9090,
  DB: {
    user: process.env.DB_USER || "postgres",
    host: process.env.DB_HOST || "***********",
    database: process.env.DB_NAME || "esp32_db",
    password: process.env.DB_PASSWORD || "Fast777",
    port: process.env.DB_PORT || 5432,
    ssl: false,
  },
};
