{"version": 3, "sources": ["../../../../src/api/user/user.ts"], "sourcesContent": ["import { gql } from '@urql/core';\nimport { promises as fs } from 'fs';\n\nimport { getAccessToken, getSession, setSessionAsync } from './UserSettings';\nimport { getSessionUsingBrowserAuthFlowAsync } from './expoSsoLauncher';\nimport { CurrentUserQuery } from '../../graphql/generated';\nimport * as Log from '../../log';\nimport { getDevelopmentCodeSigningDirectory } from '../../utils/codesigning';\nimport { env } from '../../utils/env';\nimport { getExpoWebsiteBaseUrl } from '../endpoint';\nimport { graphqlClient } from '../graphql/client';\nimport { UserQuery } from '../graphql/queries/UserQuery';\nimport { fetchAsync } from '../rest/client';\n\nexport type Actor = NonNullable<CurrentUserQuery['meActor']>;\n\nlet currentUser: Actor | undefined;\n\nexport const ANONYMOUS_USERNAME = 'anonymous';\n\n/**\n * Resolve the name of the actor, either normal user or robot user.\n * This should be used whenever the \"current user\" needs to be displayed.\n * The display name CANNOT be used as project owner.\n */\nexport function getActorDisplayName(user?: Actor): string {\n  switch (user?.__typename) {\n    case 'User':\n      return user.username;\n    case 'SSOUser':\n      return user.username;\n    case 'Robot':\n      return user.firstName ? `${user.firstName} (robot)` : 'robot';\n    default:\n      return ANONYMOUS_USERNAME;\n  }\n}\n\nexport async function getUserAsync(): Promise<Actor | undefined> {\n  const hasCredentials = getAccessToken() || getSession()?.sessionSecret;\n  if (!env.EXPO_OFFLINE && !currentUser && hasCredentials) {\n    const user = await UserQuery.currentUserAsync();\n    currentUser = user ?? undefined;\n  }\n  return currentUser;\n}\n\nexport async function loginAsync(credentials: {\n  username: string;\n  password: string;\n  otp?: string;\n}): Promise<void> {\n  const res = await fetchAsync('auth/loginAsync', {\n    method: 'POST',\n    body: JSON.stringify(credentials),\n  });\n  const json: any = await res.json();\n  const sessionSecret = json.data.sessionSecret;\n\n  const userData = await fetchUserAsync({ sessionSecret });\n\n  await setSessionAsync({\n    sessionSecret,\n    userId: userData.id,\n    username: userData.username,\n    currentConnection: 'Username-Password-Authentication',\n  });\n}\n\nexport async function ssoLoginAsync(): Promise<void> {\n  const sessionSecret = await getSessionUsingBrowserAuthFlowAsync({\n    expoWebsiteUrl: getExpoWebsiteBaseUrl(),\n  });\n  const userData = await fetchUserAsync({ sessionSecret });\n\n  await setSessionAsync({\n    sessionSecret,\n    userId: userData.id,\n    username: userData.username,\n    currentConnection: 'Browser-Flow-Authentication',\n  });\n}\n\nexport async function logoutAsync(): Promise<void> {\n  currentUser = undefined;\n  await Promise.all([\n    fs.rm(getDevelopmentCodeSigningDirectory(), { recursive: true, force: true }),\n    setSessionAsync(undefined),\n  ]);\n  Log.log('Logged out');\n}\n\nasync function fetchUserAsync({\n  sessionSecret,\n}: {\n  sessionSecret: string;\n}): Promise<{ id: string; username: string }> {\n  const result = await graphqlClient\n    .query(\n      gql`\n        query UserQuery {\n          meUserActor {\n            id\n            username\n          }\n        }\n      `,\n      {},\n      {\n        fetchOptions: {\n          headers: {\n            'expo-session': sessionSecret,\n          },\n        },\n        additionalTypenames: [] /* UserQuery has immutable fields */,\n      }\n    )\n    .toPromise();\n  const { data } = result;\n  return {\n    id: data.meUserActor.id,\n    username: data.meUserActor.username,\n  };\n}\n"], "names": ["ANONYMOUS_USERNAME", "getActorDisplayName", "getUserAsync", "loginAsync", "logoutAsync", "ssoLoginAsync", "currentUser", "user", "__typename", "username", "firstName", "getSession", "hasCredentials", "getAccessToken", "sessionSecret", "env", "EXPO_OFFLINE", "UserQuery", "currentUserAsync", "undefined", "credentials", "res", "fetchAsync", "method", "body", "JSON", "stringify", "json", "data", "userData", "fetchUserAsync", "setSessionAsync", "userId", "id", "currentConnection", "getSessionUsingBrowserAuthFlowAsync", "expoWebsiteUrl", "getExpoWebsiteBaseUrl", "Promise", "all", "fs", "rm", "getDevelopmentCodeSigningDirectory", "recursive", "force", "Log", "log", "result", "graphqlClient", "query", "gql", "fetchOptions", "headers", "additionalTypenames", "to<PERSON>romise", "meUserActor"], "mappings": ";;;;;;;;;;;IAkBaA,kBAAkB;eAAlBA;;IAOGC,mBAAmB;eAAnBA;;IAaMC,YAAY;eAAZA;;IASAC,UAAU;eAAVA;;IAoCAC,WAAW;eAAXA;;IAdAC,aAAa;eAAbA;;;;yBArEF;;;;;;;yBACW;;;;;;8BAE6B;iCACR;6DAE/B;6BAC8B;qBAC/B;0BACkB;wBACR;2BACJ;yBACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI3B,IAAIC;AAEG,MAAMN,qBAAqB;AAO3B,SAASC,oBAAoBM,IAAY;IAC9C,OAAQA,wBAAAA,KAAMC,UAAU;QACtB,KAAK;YACH,OAAOD,KAAKE,QAAQ;QACtB,KAAK;YACH,OAAOF,KAAKE,QAAQ;QACtB,KAAK;YACH,OAAOF,KAAKG,SAAS,GAAG,GAAGH,KAAKG,SAAS,CAAC,QAAQ,CAAC,GAAG;QACxD;YACE,OAAOV;IACX;AACF;AAEO,eAAeE;QACuBS;IAA3C,MAAMC,iBAAiBC,IAAAA,4BAAc,SAAMF,cAAAA,IAAAA,wBAAU,wBAAVA,YAAcG,aAAa;IACtE,IAAI,CAACC,QAAG,CAACC,YAAY,IAAI,CAACV,eAAeM,gBAAgB;QACvD,MAAML,OAAO,MAAMU,oBAAS,CAACC,gBAAgB;QAC7CZ,cAAcC,QAAQY;IACxB;IACA,OAAOb;AACT;AAEO,eAAeH,WAAWiB,WAIhC;IACC,MAAMC,MAAM,MAAMC,IAAAA,mBAAU,EAAC,mBAAmB;QAC9CC,QAAQ;QACRC,MAAMC,KAAKC,SAAS,CAACN;IACvB;IACA,MAAMO,OAAY,MAAMN,IAAIM,IAAI;IAChC,MAAMb,gBAAgBa,KAAKC,IAAI,CAACd,aAAa;IAE7C,MAAMe,WAAW,MAAMC,eAAe;QAAEhB;IAAc;IAEtD,MAAMiB,IAAAA,6BAAe,EAAC;QACpBjB;QACAkB,QAAQH,SAASI,EAAE;QACnBxB,UAAUoB,SAASpB,QAAQ;QAC3ByB,mBAAmB;IACrB;AACF;AAEO,eAAe7B;IACpB,MAAMS,gBAAgB,MAAMqB,IAAAA,oDAAmC,EAAC;QAC9DC,gBAAgBC,IAAAA,+BAAqB;IACvC;IACA,MAAMR,WAAW,MAAMC,eAAe;QAAEhB;IAAc;IAEtD,MAAMiB,IAAAA,6BAAe,EAAC;QACpBjB;QACAkB,QAAQH,SAASI,EAAE;QACnBxB,UAAUoB,SAASpB,QAAQ;QAC3ByB,mBAAmB;IACrB;AACF;AAEO,eAAe9B;IACpBE,cAAca;IACd,MAAMmB,QAAQC,GAAG,CAAC;QAChBC,cAAE,CAACC,EAAE,CAACC,IAAAA,+CAAkC,KAAI;YAAEC,WAAW;YAAMC,OAAO;QAAK;QAC3Eb,IAAAA,6BAAe,EAACZ;KACjB;IACD0B,KAAIC,GAAG,CAAC;AACV;AAEA,eAAehB,eAAe,EAC5BhB,aAAa,EAGd;IACC,MAAMiC,SAAS,MAAMC,qBAAa,CAC/BC,KAAK,CACJC,IAAAA,WAAG,CAAA,CAAC;;;;;;;MAOJ,CAAC,EACD,CAAC,GACD;QACEC,cAAc;YACZC,SAAS;gBACP,gBAAgBtC;YAClB;QACF;QACAuC,qBAAqB,EAAE;IACzB,GAEDC,SAAS;IACZ,MAAM,EAAE1B,IAAI,EAAE,GAAGmB;IACjB,OAAO;QACLd,IAAIL,KAAK2B,WAAW,CAACtB,EAAE;QACvBxB,UAAUmB,KAAK2B,WAAW,CAAC9C,QAAQ;IACrC;AACF"}