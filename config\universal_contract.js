/**
 * Конфигурация универсального смарт-контракта
 */

module.exports = {
  // Настройки сети TON
  network: {
    endpoint: process.env.TON_NETWORK === 'mainnet' 
      ? 'https://toncenter.com/api/v2/jsonRPC'
      : 'https://testnet.toncenter.com/api/v2/jsonRPC',
    apiKey: process.env.TON_API_KEY,
    workchain: 0,
    isMainnet: process.env.TON_NETWORK === 'mainnet'
  },

  // Настройки универсального контракта
  universalContract: {
    // Адрес будет вычислен автоматически при инициализации
    address: null,
    
    // Настройки платформы
    platform: {
      address: process.env.PLATFORM_ADDRESS,
      mnemonic: process.env.PLATFORM_MNEMONIC,
      feePercent: parseInt(process.env.PLATFORM_FEE_PERCENT) || 500, // 5% в базисных пунктах
    },

    // Настройки деплоя
    deploy: {
      minAmount: '0.1', // Минимальная сумма для деплоя контракта в TON
      gasAmount: '0.05', // Газ для деплоя в TON
      autoRetry: true,
      maxRetries: 3
    },

    // Настройки операций
    operations: {
      buyTimeGas: '0.05', // Газ для покупки времени
      withdrawGas: '0.05', // Газ для вывода средств
      updateSettingsGas: '0.03', // Газ для обновления настроек
      emergencyStopGas: '0.02' // Газ для экстренной остановки
    }
  },

  // Настройки мемо-фраз
  memo: {
    format: '{streamer_address}:{device_id}', // Формат мемо
    maxLength: 500, // Максимальная длина мемо
    separator: ':', // Разделитель в мемо
    encoding: 'utf8' // Кодировка мемо
  },

  // Настройки устройств
  devices: {
    defaultPricePerMinute: '1.0', // TON за минуту по умолчанию
    maxControlTime: 60, // Максимальное время управления в минутах
    minControlTime: 1,  // Минимальное время управления в минутах
    sessionTimeout: 300, // Таймаут сессии в секундах (5 минут)
    
    // Настройки цен
    pricing: {
      minPrice: '0.1', // Минимальная цена за минуту
      maxPrice: '10.0', // Максимальная цена за минуту
      defaultPrice: '1.0', // Цена по умолчанию
      priceStep: '0.1' // Шаг изменения цены
    }
  },

  // Настройки стримеров
  streamers: {
    minWithdrawalAmount: '0.1', // Минимальная сумма для вывода
    withdrawalFee: '0.0', // Комиссия за вывод (0 = без комиссии)
    autoWithdrawal: false, // Автоматический вывод средств
    autoWithdrawalThreshold: '10.0' // Порог для автоматического вывода
  },

  // Настройки безопасности
  security: {
    maxTransactionAmount: '100.0', // Максимальная сумма транзакции в TON
    requireConfirmations: 1, // Количество подтверждений для транзакции
    enableRateLimit: true, // Включить ограничение частоты запросов
    rateLimitWindow: 60000, // Окно ограничения в миллисекундах (1 минута)
    rateLimitMax: 10, // Максимальное количество запросов в окне
    
    // Защита от спама
    antiSpam: {
      enabled: true,
      minTimeBetweenTransactions: 5000, // 5 секунд между транзакциями
      maxTransactionsPerHour: 100, // Максимум транзакций в час
      blacklistDuration: 3600000 // Время блокировки в миллисекундах (1 час)
    }
  },

  // Настройки мониторинга
  monitoring: {
    enableTransactionMonitoring: true,
    monitoringInterval: 30000, // Интервал мониторинга в миллисекундах (30 секунд)
    maxRetries: 3, // Максимальное количество повторных попыток
    retryDelay: 5000, // Задержка между повторными попытками в миллисекундах
    
    // Мониторинг балансов
    balanceMonitoring: {
      enabled: true,
      interval: 60000, // Проверка балансов каждую минуту
      lowBalanceThreshold: '1.0', // Порог низкого баланса
      alertOnLowBalance: true
    },

    // Мониторинг сессий
    sessionMonitoring: {
      enabled: true,
      cleanupInterval: 300000, // Очистка истекших сессий каждые 5 минут
      maxInactiveSessions: 1000 // Максимальное количество неактивных сессий
    }
  },

  // Настройки кэширования
  cache: {
    enabled: true,
    ttl: 60000, // Время жизни кэша в миллисекундах (1 минута)
    maxSize: 1000, // Максимальный размер кэша
    
    // Кэширование балансов
    balanceCache: {
      enabled: true,
      ttl: 30000, // 30 секунд для балансов
      maxSize: 500
    },

    // Кэширование цен устройств
    priceCache: {
      enabled: true,
      ttl: 300000, // 5 минут для цен
      maxSize: 1000
    }
  },

  // Настройки логирования
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableFileLogging: true,
    logFile: 'service.log',
    enableConsoleLogging: true,
    logTransactions: true,
    logContractOperations: true,
    
    // Детальное логирование
    detailed: {
      logMemoOperations: true,
      logBalanceChanges: true,
      logSessionChanges: true,
      logPriceUpdates: true
    }
  },

  // Настройки базы данных
  database: {
    // Настройки синхронизации
    sync: {
      enabled: true,
      interval: 60000, // Синхронизация каждую минуту
      batchSize: 100, // Размер пакета для синхронизации
      maxRetries: 3
    },

    // Настройки очистки
    cleanup: {
      enabled: true,
      interval: 3600000, // Очистка каждый час
      retentionDays: 30, // Хранить данные 30 дней
      cleanupOldSessions: true,
      cleanupOldTransactions: false // Не удалять транзакции
    }
  },

  // Настройки уведомлений
  notifications: {
    enabled: false, // Отключено по умолчанию
    webhook: {
      url: process.env.WEBHOOK_URL,
      secret: process.env.WEBHOOK_SECRET,
      events: ['transaction_confirmed', 'withdrawal_completed', 'error_occurred']
    },
    
    // Email уведомления
    email: {
      enabled: false,
      smtp: {
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT,
        user: process.env.SMTP_USER,
        password: process.env.SMTP_PASSWORD
      },
      recipients: {
        admin: process.env.ADMIN_EMAIL,
        alerts: process.env.ALERTS_EMAIL
      }
    }
  },

  // Операционные коды для смарт-контракта
  opcodes: {
    BUY_TIME: 0x1,
    WITHDRAW_STREAMER_EARNINGS: 0x2,
    WITHDRAW_PLATFORM_FEE: 0x3,
    EMERGENCY_STOP: 0x4,
    UPDATE_DEVICE_SETTINGS: 0x5
  },

  // Коды ошибок смарт-контракта
  errors: {
    UNAUTHORIZED: 401,
    INSUFFICIENT_PAYMENT: 402,
    INVALID_MEMO: 403,
    INVALID_TIME: 404,
    INSUFFICIENT_BALANCE: 405
  },

  // Настройки для разработки
  development: {
    enableDebugMode: process.env.NODE_ENV === 'development',
    mockTransactions: false, // Использовать мок-транзакции для тестирования
    skipContractValidation: false, // Пропускать валидацию контракта
    enableTestEndpoints: process.env.NODE_ENV === 'development'
  }
};
