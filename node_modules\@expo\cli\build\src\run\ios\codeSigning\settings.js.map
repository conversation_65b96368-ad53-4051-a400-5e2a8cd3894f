{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/settings.ts"], "sourcesContent": ["import { getSettings } from '../../../api/user/UserSettings';\n\n/** Get the cached code signing ID from the last time a user configured code signing via the CLI. */\nexport async function getLastDeveloperCodeSigningIdAsync(): Promise<string | null> {\n  return await getSettings().getAsync('developmentCodeSigningId', null);\n}\n\n/** Cache the code signing ID that the user chose for their project, we'll recommend this value for the next project they code sign. */\nexport async function setLastDeveloperCodeSigningIdAsync(id: string): Promise<void> {\n  await getSettings()\n    .setAsync('developmentCodeSigningId', id)\n    .catch(() => {});\n}\n"], "names": ["getLastDeveloperCodeSigningIdAsync", "setLastDeveloperCodeSigningIdAsync", "getSettings", "getAsync", "id", "setAsync", "catch"], "mappings": ";;;;;;;;;;;IAGsBA,kCAAkC;eAAlCA;;IAKAC,kCAAkC;eAAlCA;;;8BARM;AAGrB,eAAeD;IACpB,OAAO,MAAME,IAAAA,yBAAW,IAAGC,QAAQ,CAAC,4BAA4B;AAClE;AAGO,eAAeF,mCAAmCG,EAAU;IACjE,MAAMF,IAAAA,yBAAW,IACdG,QAAQ,CAAC,4BAA4BD,IACrCE,KAAK,CAAC,KAAO;AAClB"}