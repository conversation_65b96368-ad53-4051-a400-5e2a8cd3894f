# Пример конфигурации для интеграции смарт-контрактов
# Скопируйте этот файл в .env и заполните необходимые значения

# ========================
# Настройки базы данных
# ========================
DB_USER=postgres
DB_HOST=***********
DB_NAME=esp32_db
DB_PASSWORD=Fast777
DB_PORT=5432

# ========================
# Настройки сервера
# ========================
HTTP_PORT=4000
WS_PORT=9090
REALTIME_PORT=4001
NODE_ENV=development

# ========================================
# НАСТРОЙКИ УНИВЕРСАЛЬНОГО СМАРТ-КОНТРАКТА
# ========================================

# Сеть TON (testnet/mainnet)
TON_NETWORK=testnet

# API ключ для TON Center (получить на https://toncenter.com)
TON_API_KEY=your_ton_api_key_here

# Endpoint для подключения к TON (автоматически выбирается на основе TON_NETWORK)
# TON_ENDPOINT=https://testnet.toncenter.com/api/v2/jsonRPC

# Мнемоника кошелька платформы для деплоя универсального контракта (24 слова через пробел)
PLATFORM_MNEMONIC="word1 word2 word3 ... word24"

# Адрес кошелька платформы для получения комиссий
PLATFORM_ADDRESS=EQD...

# Комиссия платформы в базисных пунктах (500 = 5%)
PLATFORM_FEE_PERCENT=500

# Адрес универсального контракта (заполняется автоматически после деплоя)
UNIVERSAL_CONTRACT_ADDRESS=

# ========================
# Настройки универсального контракта
# ========================

# Минимальная сумма для деплоя контракта в TON
UNIVERSAL_CONTRACT_DEPLOY_AMOUNT=0.1

# Газ для операций покупки времени в TON
BUY_TIME_GAS=0.05

# Газ для операций вывода средств в TON
WITHDRAW_GAS=0.05

# Максимальная длина мемо-фразы
MAX_MEMO_LENGTH=500

# Формат мемо-фразы (используется для генерации)
MEMO_FORMAT="{streamer_address}:{device_id}"

# Стоимость деплоя контракта в TON
CONTRACT_DEPLOY_FEE=0.1

# Таймаут для транзакций в секундах
TRANSACTION_TIMEOUT=300

# Интервал мониторинга транзакций в миллисекундах
MONITORING_INTERVAL=30000

# Максимальное количество попыток обработки транзакции
MAX_RETRY_ATTEMPTS=3

# Минимальная сумма для вывода средств в TON
MIN_WITHDRAWAL_AMOUNT=0.1

# Резерв газа для операций в nanoTON
GAS_RESERVE=50000000

# Код смарт-контракта устройства в base64 (будет заполнен после компиляции)
DEVICE_CONTRACT_CODE=

# ========================
# Настройки мониторинга
# ========================

# Включить мониторинг транзакций
MONITORING_ENABLED=true

# Включить логирование блокчейн операций
ENABLE_BLOCKCHAIN_LOGS=true

# Включить логирование транзакций
ENABLE_TRANSACTION_LOGS=true

# Уровень логирования (debug, info, warn, error)
LOG_LEVEL=info

# ========================
# Настройки для тестирования
# ========================

# Использовать мок транзакции для тестирования
MOCK_TRANSACTIONS=false

# ========================
# Дополнительные настройки безопасности
# ========================

# Максимальная длительность управления в минутах
MAX_CONTROL_DURATION=60

# Минимальная длительность управления в минутах
MIN_CONTROL_DURATION=1

# Максимальная цена за минуту в TON
MAX_PRICE_PER_MINUTE=10

# Минимальная цена за минуту в TON
MIN_PRICE_PER_MINUTE=0.001

# Максимальное количество одновременных сессий
MAX_CONCURRENT_SESSIONS=100

# Максимальное количество транзакций в час
MAX_TRANSACTIONS_PER_HOUR=1000

# ========================
# Настройки кэширования
# ========================

# TTL для кэша информации о контрактах в миллисекундах
CONTRACT_INFO_TTL=60000

# TTL для кэша балансов в миллисекундах
BALANCE_TTL=30000

# TTL для кэша транзакций в миллисекундах
TRANSACTION_TTL=300000

# ========================
# Примеры значений для разработки
# ========================

# Для тестирования в testnet можно использовать:
# TON_NETWORK=testnet
# PLATFORM_ADDRESS=kQD... (тестовый адрес)
# PLATFORM_FEE_PERCENT=100 (1% для тестирования)
# CONTRACT_DEPLOY_FEE=0.05
# MIN_WITHDRAWAL_AMOUNT=0.01

# ========================
# Инструкции по настройке
# ========================

# 1. Создайте кошелек TON для платформы
# 2. Получите API ключ на https://toncenter.com
# 3. Скомпилируйте смарт-контракт и получите его код в base64
# 4. Заполните все необходимые поля выше
# 5. Запустите миграцию базы данных: node -e "require('./sql/add_smart_contract_tables.sql')"
# 6. Запустите сервер: npm start

# ========================
# Безопасность
# ========================

# ВАЖНО: Никогда не коммитьте .env файл с реальными данными в git!
# Добавьте .env в .gitignore
# Используйте переменные окружения в продакшене
# Регулярно ротируйте API ключи и мнемоники
