{"version": 3, "file": "index.cjs", "sources": ["../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../node_modules/react/cjs/react-jsx-runtime.development.js", "../../../node_modules/react/jsx-runtime.js", "../src/utils/web.ts", "../src/components/TonConnectUIProvider.tsx", "../src/errors/ton-connect-ui-react.error.ts", "../src/errors/ton-connect-provider-not-set.error.ts", "../src/utils/errors.ts", "../src/hooks/useTonConnectUI.ts", "../src/components/TonConnectButton.tsx", "../src/hooks/useTonWallet.ts", "../src/hooks/useTonAddress.ts", "../src/hooks/useTonConnectModal.ts", "../src/hooks/useIsConnectionRestored.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n// even with the prod transform. This means that jsxDEV is purely\n// opt-in behavior for better messages but that we won't stop\n// giving you warnings if you use production apis.\n\nfunction jsxWithValidationStatic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, true);\n  }\n}\nfunction jsxWithValidationDynamic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, false);\n  }\n}\n\nvar jsx =  jsxWithValidationDynamic ; // we may want to special case jsxs internally to take advantage of static children.\n// for now we can ship identical prod functions\n\nvar jsxs =  jsxWithValidationStatic ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsx;\nexports.jsxs = jsxs;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "export function isClientSide(): boolean {\n    return typeof window !== 'undefined';\n}\n\nexport function isServerSide(): boolean {\n    return !isClientSide();\n}\n", "import { createContext, FunctionComponent, memo, ReactNode } from 'react';\nimport {\n    ActionConfiguration,\n    Locales,\n    TonConnectUI,\n    UIPreferences,\n    WalletsListConfiguration\n} from '@tonconnect/ui';\nimport type { ITonConnect } from '@tonconnect/ui';\nimport { isClientSide } from '../utils/web';\n\nexport const TonConnectUIContext = createContext<TonConnectUI | null>(null);\n\nexport type TonConnectUIProviderProps = {\n    children: ReactNode;\n} & Partial<TonConnectUIProviderPropsBase> &\n    Partial<TonConnectUIProviderPropsWithManifest | TonConnectUIProviderPropsWithConnector>;\n\nexport interface TonConnectUIProviderPropsWithManifest {\n    /**\n     * Url to the [manifest]{@link https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest} with the Dapp metadata that will be displayed in the user's wallet.\n     * If not passed, manifest from `${window.location.origin}/tonconnect-manifest.json` will be taken.\n     */\n    manifestUrl: string;\n}\n\nexport interface TonConnectUIProviderPropsWithConnector {\n    /**\n     * TonConnect instance. Can be helpful if you use custom ITonConnect implementation, or use both of @tonconnect/sdk and @tonconnect/ui in your app.\n     */\n    connector: ITonConnect;\n}\n\nexport interface TonConnectUIProviderPropsBase {\n    /**\n     * Try to restore existing session and reconnect to the corresponding wallet.\n     * @default true.\n     */\n    restoreConnection: boolean;\n\n    /**\n     * Language for the phrases it the UI elements.\n     * @default system\n     */\n    language: Locales;\n\n    /**\n     * HTML element id to attach the modal window element. If not passed, `div#tc-widget-root` in the end of the <body> will be added and used.\n     * @default `div#tc-widget-root`.\n     */\n    widgetRootId: string;\n\n    /**\n     * UI elements configuration.\n     */\n    uiPreferences?: UIPreferences;\n\n    /**\n     * Configuration for the wallets list in the connect wallet modal.\n     */\n    walletsListConfiguration?: WalletsListConfiguration;\n\n    /**\n     * Configuration for action-period (e.g. sendTransaction) UI elements: modals and notifications and wallet behaviour (return strategy).\n     */\n    actionsConfiguration?: ActionConfiguration;\n\n    /**\n     * Specifies whether the Android back button should be used to close modals and notifications on Android devices.\n     * @default true\n     */\n    enableAndroidBackHandler?: boolean;\n}\n\nlet tonConnectUI: TonConnectUI | null = null;\n\n/**\n * Add TonConnectUIProvider to the root of the app. You can specify UI options using props.\n * All TonConnect UI hooks calls and `<TonConnectButton />` component must be placed inside `<TonConnectUIProvider>`.\n * @param children JSX to insert.\n * @param [options] additional options.\n * @constructor\n */\nconst TonConnectUIProvider: FunctionComponent<TonConnectUIProviderProps> = ({\n    children,\n    ...options\n}) => {\n    if (isClientSide() && !tonConnectUI) {\n        tonConnectUI = new TonConnectUI(options);\n    }\n\n    return (\n        <TonConnectUIContext.Provider value={tonConnectUI}>{children}</TonConnectUIContext.Provider>\n    );\n};\n\nexport default memo(TonConnectUIProvider);\n", "import { TonConnectUIError } from '@tonconnect/ui';\n\n/**\n * Base class for TonConnectUIReact errors. You can check if the error was triggered by the @tonconnect/ui-react using `err instanceof TonConnectUIReactError`.\n */\nexport class TonConnectUIReactError extends TonConnectUIError {\n    constructor(...args: ConstructorParameters<typeof Error>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, TonConnectUIReactError.prototype);\n    }\n}\n", "import { TonConnectUIReactError } from './ton-connect-ui-react.error';\n\n/**\n * Thrown when either <TonConnectProvider> not added to the top of the tags tree,\n * either there is an attempt using TonConnect UI hook or <TonConnectButton> inside <TonConnectProvider>\n */\nexport class TonConnectProviderNotSetError extends TonConnectUIReactError {\n    constructor(...args: ConstructorParameters<typeof Error>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, TonConnectProviderNotSetError.prototype);\n    }\n}\n", "import { TonConnectUI } from '@tonconnect/ui';\nimport { TonConnectProviderNotSetError } from '../errors/ton-connect-provider-not-set.error';\n\nexport function checkProvider(provider: TonConnectUI | null): provider is TonConnectUI {\n    if (!provider) {\n        throw new TonConnectProviderNotSetError(\n            'You should add <TonConnectUIProvider> on the top of the app to use TonConnect'\n        );\n    }\n\n    return true;\n}\n", "import { useCallback, useContext } from 'react';\nimport { TonConnectUIContext } from '../components/TonConnectUIProvider';\nimport { TonConnectUI, TonConnectUiOptions } from '@tonconnect/ui';\nimport { checkProvider } from '../utils/errors';\nimport { isServerSide } from '../utils/web';\n\n/**\n * Use it to get access to the `TonConnectUI` instance and UI options updating function.\n */\nexport function useTonConnectUI(): [TonConnectUI, (options: TonConnectUiOptions) => void] {\n    const tonConnectUI = useContext(TonConnectUIContext);\n    const setOptions = useCallback(\n        (options: TonConnectUiOptions) => {\n            if (tonConnectUI) {\n                tonConnectUI!.uiOptions = options;\n            }\n        },\n        [tonConnectUI]\n    );\n\n    if (isServerSide()) {\n        return [null as unknown as TonConnectUI, () => {}];\n    }\n\n    checkProvider(tonConnectUI);\n    return [tonConnectUI!, setOptions];\n}\n", "import { CSSProperties, FunctionComponent, memo, useEffect } from 'react';\nimport { useTonConnectUI } from '../hooks/useTonConnectUI';\n\nconst buttonRootId = 'ton-connect-button';\n\nexport interface TonConnectButtonProps {\n    className?: string;\n\n    style?: CSSProperties;\n}\n\n/**\n * TonConnect Button is universal UI component for initializing connection. After wallet is connected it transforms to a wallet menu.\n * It is recommended to place it in the top right corner of your app.\n * @param [className] css class to add to the button container.\n * @param [style] style to add to the button container.\n * @constructor\n */\nconst TonConnectButton: FunctionComponent<TonConnectButtonProps> = ({ className, style }) => {\n    const [_, setOptions] = useTonConnectUI();\n\n    useEffect(() => {\n        setOptions({ buttonRootId });\n        return () => setOptions({ buttonRootId: null });\n    }, [setOptions]);\n\n    return (\n        <div\n            id={buttonRootId}\n            className={className}\n            style={{ width: 'fit-content', ...style }}\n        ></div>\n    );\n};\n\nexport default memo(TonConnectButton);\n", "import { useEffect, useState } from 'react';\nimport { ConnectedWallet, Wallet, WalletInfoWithOpenMethod } from '@tonconnect/ui';\nimport { useTonConnectUI } from './useTonConnectUI';\n\n/**\n * Use it to get user's current ton wallet. If wallet is not connected hook will return null.\n */\nexport function useTonWallet(): Wallet | (Wallet & WalletInfoWithOpenMethod) | null {\n    const [tonConnectUI] = useTonConnectUI();\n    const [wallet, setWallet] = useState<Wallet | (Wallet & WalletInfoWithOpenMethod) | null>(\n        tonConnectUI?.wallet || null\n    );\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            setWallet(tonConnectUI.wallet);\n            return tonConnectUI.onStatusChange((value: ConnectedWallet | null) => {\n                setWallet(value);\n            });\n        }\n    }, [tonConnectUI]);\n\n    return wallet;\n}\n", "import { CHAIN, toUserFriendlyAddress } from '@tonconnect/ui';\nimport { useTonWallet } from './useTonWallet';\nimport { useMemo } from 'react';\n\n/**\n * Use it to get user's current ton wallet address. If wallet is not connected hook will return empty string.\n * @param [userFriendly=true] allows to choose format of the address.\n */\nexport function useTonAddress(userFriendly = true): string {\n    const wallet = useTonWallet();\n    return useMemo(() => {\n        if (wallet) {\n            return userFriendly\n                ? toUserFriendlyAddress(\n                      wallet.account.address,\n                      wallet.account.chain === CHAIN.TESTNET\n                  )\n                : wallet.account.address;\n        } else {\n            return '';\n        }\n    }, [wallet, userFriendly, wallet?.account.address, wallet?.account.chain]);\n}\n", "import { WalletsModal, WalletsModalState } from '@tonconnect/ui';\nimport { useTonConnectUI } from './useTonConnectUI';\nimport { useEffect, useState } from 'react';\n\n/**\n * Use it to get access to the open/close modal functions.\n */\nexport function useTonConnectModal(): Omit<WalletsModal, 'onStateChange'> {\n    const [tonConnectUI] = useTonConnectUI();\n    const [state, setState] = useState(tonConnectUI?.modal.state || null);\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            setState(tonConnectUI.modal.state);\n            return tonConnectUI.onModalStateChange((value: WalletsModalState) => {\n                setState(value);\n            });\n        }\n    }, [tonConnectUI]);\n\n    return {\n        state: state,\n        open: () => tonConnectUI?.modal.open(),\n        close: () => tonConnectUI?.modal.close()\n    };\n}\n", "import { useEffect, useState } from 'react';\nimport { useTonConnectUI } from './useTonConnectUI';\n\n/**\n * Indicates current status of the connection restoring process.\n */\nexport function useIsConnectionRestored(): boolean {\n    const [restored, setRestored] = useState(false);\n    const [tonConnectUI] = useTonConnectUI();\n\n    useEffect(() => {\n        if (tonConnectUI) {\n            tonConnectUI.connectionRestored.then(() => setRestored(true));\n        }\n    }, [tonConnectUI]);\n\n    return restored;\n}\n"], "names": ["ReactDebugCurrentFrame", "jsx", "module", "require$$0", "require$$1", "createContext", "TonConnectUI", "memo", "TonConnectUIError", "tonConnectUI", "useContext", "useCallback", "useEffect", "useState", "useMemo", "toUser<PERSON>rien<PERSON><PERSON><PERSON><PERSON>", "CHAIN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASa,MAAI,IAAE,YAAiB,IAAE,OAAO,IAAI,eAAe,GAAE,IAAE,OAAO,IAAI,gBAAgB,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAE,EAAE,mDAAmD,mBAAkB,IAAE,EAAC,KAAI,MAAG,KAAI,MAAG,QAAO,MAAG,UAAS,KAAE;AAClP,WAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAI,GAAE,IAAE,IAAG,IAAE,MAAK,IAAE;AAAK,eAAS,MAAI,IAAE,KAAG;AAAG,eAAS,EAAE,QAAM,IAAE,KAAG,EAAE;AAAK,eAAS,EAAE,QAAM,IAAE,EAAE;AAAK,SAAI,KAAK;AAAE,QAAE,KAAK,GAAE,CAAC,KAAG,CAAC,EAAE,eAAe,CAAC,MAAI,EAAE,KAAG,EAAE;AAAI,QAAG,KAAG,EAAE;AAAa,WAAI,KAAK,IAAE,EAAE,cAAa;AAAE,mBAAS,EAAE,OAAK,EAAE,KAAG,EAAE;AAAI,WAAM,EAAC,UAAS,GAAE,MAAK,GAAE,KAAI,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,EAAE,QAAO;AAAA,EAAC;AAAC,4CAAiB;AAAE,iCAAW,MAAC;AAAE,iCAAA,OAAa;;;;;;;;;;;;;;;;;;ACE1W,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,KAAC,WAAW;AAGd,UAAI,QAAQ;AAMZ,UAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,UAAI,oBAAoB,OAAO,IAAI,cAAc;AACjD,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,UAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,2BAA2B,OAAO,IAAI,qBAAqB;AAC/D,UAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,UAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,UAAI,uBAAuB,OAAO,IAAI,iBAAiB;AACvD,UAAI,wBAAwB,OAAO;AACnC,UAAI,uBAAuB;AAC3B,eAAS,cAAc,eAAe;AACpC,YAAI,kBAAkB,QAAQ,OAAO,kBAAkB,UAAU;AAC/D,iBAAO;AAAA,QACR;AAED,YAAI,gBAAgB,yBAAyB,cAAc,0BAA0B,cAAc;AAEnG,YAAI,OAAO,kBAAkB,YAAY;AACvC,iBAAO;AAAA,QACR;AAED,eAAO;AAAA,MACR;AAED,UAAI,uBAAuB,MAAM;AAEjC,eAAS,MAAM,QAAQ;AACrB;AACE;AACE,qBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,mBAAK,QAAQ,KAAK,UAAU;AAAA,YAC7B;AAED,yBAAa,SAAS,QAAQ,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAED,eAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,cAAIA,0BAAyB,qBAAqB;AAClD,cAAI,QAAQA,wBAAuB;AAEnC,cAAI,UAAU,IAAI;AAChB,sBAAU;AACV,mBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,UAC3B;AAGD,cAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,mBAAO,OAAO,IAAI;AAAA,UACxB,CAAK;AAED,yBAAe,QAAQ,cAAc,MAAM;AAI3C,mBAAS,UAAU,MAAM,KAAK,QAAQ,QAAQ,SAAS,cAAc;AAAA,QACtE;AAAA,MACF;AAID,UAAI,iBAAiB;AACrB,UAAI,qBAAqB;AACzB,UAAI,0BAA0B;AAE9B,UAAI,qBAAqB;AAIzB,UAAI,qBAAqB;AAEzB,UAAI;AAEJ;AACE,iCAAyB,OAAO,IAAI,wBAAwB;AAAA,MAC7D;AAED,eAAS,mBAAmB,MAAM;AAChC,YAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,iBAAO;AAAA,QACR;AAGD,YAAI,SAAS,uBAAuB,SAAS,uBAAuB,sBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,sBAAuB,SAAS,wBAAwB,kBAAmB,sBAAuB,yBAA0B;AAC7T,iBAAO;AAAA,QACR;AAED,YAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,cAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAIjL,KAAK,aAAa,0BAA0B,KAAK,gBAAgB,QAAW;AAC1E,mBAAO;AAAA,UACR;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,eAAS,eAAe,WAAW,WAAW,aAAa;AACzD,YAAI,cAAc,UAAU;AAE5B,YAAI,aAAa;AACf,iBAAO;AAAA,QACR;AAED,YAAI,eAAe,UAAU,eAAe,UAAU,QAAQ;AAC9D,eAAO,iBAAiB,KAAK,cAAc,MAAM,eAAe,MAAM;AAAA,MACvE;AAGD,eAAS,eAAe,MAAM;AAC5B,eAAO,KAAK,eAAe;AAAA,MAC5B;AAGD,eAAS,yBAAyB,MAAM;AACtC,YAAI,QAAQ,MAAM;AAEhB,iBAAO;AAAA,QACR;AAED;AACE,cAAI,OAAO,KAAK,QAAQ,UAAU;AAChC,kBAAM,mHAAwH;AAAA,UAC/H;AAAA,QACF;AAED,YAAI,OAAO,SAAS,YAAY;AAC9B,iBAAO,KAAK,eAAe,KAAK,QAAQ;AAAA,QACzC;AAED,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO;AAAA,QACR;AAED,gBAAQ,MAAI;AAAA,UACV,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO;AAAA,UAET,KAAK;AACH,mBAAO;AAAA,QAEV;AAED,YAAI,OAAO,SAAS,UAAU;AAC5B,kBAAQ,KAAK,UAAQ;AAAA,YACnB,KAAK;AACH,kBAAI,UAAU;AACd,qBAAO,eAAe,OAAO,IAAI;AAAA,YAEnC,KAAK;AACH,kBAAI,WAAW;AACf,qBAAO,eAAe,SAAS,QAAQ,IAAI;AAAA,YAE7C,KAAK;AACH,qBAAO,eAAe,MAAM,KAAK,QAAQ,YAAY;AAAA,YAEvD,KAAK;AACH,kBAAI,YAAY,KAAK,eAAe;AAEpC,kBAAI,cAAc,MAAM;AACtB,uBAAO;AAAA,cACR;AAED,qBAAO,yBAAyB,KAAK,IAAI,KAAK;AAAA,YAEhD,KAAK,iBACH;AACE,kBAAI,gBAAgB;AACpB,kBAAI,UAAU,cAAc;AAC5B,kBAAI,OAAO,cAAc;AAEzB,kBAAI;AACF,uBAAO,yBAAyB,KAAK,OAAO,CAAC;AAAA,cAC9C,SAAQ,GAAP;AACA,uBAAO;AAAA,cACR;AAAA,YACF;AAAA,UAGJ;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,UAAI,SAAS,OAAO;AAMpB,UAAI,gBAAgB;AACpB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,cAAc;AAAA,MAAE;AAEzB,kBAAY,qBAAqB;AACjC,eAAS,cAAc;AACrB;AACE,cAAI,kBAAkB,GAAG;AAEvB,sBAAU,QAAQ;AAClB,uBAAW,QAAQ;AACnB,uBAAW,QAAQ;AACnB,wBAAY,QAAQ;AACpB,wBAAY,QAAQ;AACpB,iCAAqB,QAAQ;AAC7B,2BAAe,QAAQ;AAEvB,gBAAI,QAAQ;AAAA,cACV,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,YAClB;AAEM,mBAAO,iBAAiB,SAAS;AAAA,cAC/B,MAAM;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,cACP,gBAAgB;AAAA,cAChB,UAAU;AAAA,YAClB,CAAO;AAAA,UAEF;AAED;AAAA,QACD;AAAA,MACF;AACD,eAAS,eAAe;AACtB;AACE;AAEA,cAAI,kBAAkB,GAAG;AAEvB,gBAAI,QAAQ;AAAA,cACV,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,YAClB;AAEM,mBAAO,iBAAiB,SAAS;AAAA,cAC/B,KAAK,OAAO,CAAE,GAAE,OAAO;AAAA,gBACrB,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,MAAM,OAAO,CAAE,GAAE,OAAO;AAAA,gBACtB,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,MAAM,OAAO,CAAE,GAAE,OAAO;AAAA,gBACtB,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,OAAO,OAAO,CAAE,GAAE,OAAO;AAAA,gBACvB,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,OAAO,OAAO,CAAE,GAAE,OAAO;AAAA,gBACvB,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,gBAAgB,OAAO,CAAE,GAAE,OAAO;AAAA,gBAChC,OAAO;AAAA,cACjB,CAAS;AAAA,cACD,UAAU,OAAO,CAAE,GAAE,OAAO;AAAA,gBAC1B,OAAO;AAAA,cACjB,CAAS;AAAA,YACT,CAAO;AAAA,UAEF;AAED,cAAI,gBAAgB,GAAG;AACrB,kBAAM,8EAAmF;AAAA,UAC1F;AAAA,QACF;AAAA,MACF;AAED,UAAI,yBAAyB,qBAAqB;AAClD,UAAI;AACJ,eAAS,8BAA8B,MAAM,QAAQ,SAAS;AAC5D;AACE,cAAI,WAAW,QAAW;AAExB,gBAAI;AACF,oBAAM,MAAK;AAAA,YACZ,SAAQ,GAAP;AACA,kBAAI,QAAQ,EAAE,MAAM,KAAI,EAAG,MAAM,cAAc;AAC/C,uBAAS,SAAS,MAAM,MAAM;AAAA,YAC/B;AAAA,UACF;AAGD,iBAAO,OAAO,SAAS;AAAA,QACxB;AAAA,MACF;AACD,UAAI,UAAU;AACd,UAAI;AAEJ;AACE,YAAI,kBAAkB,OAAO,YAAY,aAAa,UAAU;AAChE,8BAAsB,IAAI;MAC3B;AAED,eAAS,6BAA6B,IAAI,WAAW;AAEnD,YAAK,CAAC,MAAM,SAAS;AACnB,iBAAO;AAAA,QACR;AAED;AACE,cAAI,QAAQ,oBAAoB,IAAI,EAAE;AAEtC,cAAI,UAAU,QAAW;AACvB,mBAAO;AAAA,UACR;AAAA,QACF;AAED,YAAI;AACJ,kBAAU;AACV,YAAI,4BAA4B,MAAM;AAEtC,cAAM,oBAAoB;AAC1B,YAAI;AAEJ;AACE,+BAAqB,uBAAuB;AAG5C,iCAAuB,UAAU;AACjC;QACD;AAED,YAAI;AAEF,cAAI,WAAW;AAEb,gBAAI,OAAO,WAAY;AACrB,oBAAM,MAAK;AAAA,YACnB;AAGM,mBAAO,eAAe,KAAK,WAAW,SAAS;AAAA,cAC7C,KAAK,WAAY;AAGf,sBAAM,MAAK;AAAA,cACZ;AAAA,YACT,CAAO;AAED,gBAAI,OAAO,YAAY,YAAY,QAAQ,WAAW;AAGpD,kBAAI;AACF,wBAAQ,UAAU,MAAM,CAAA,CAAE;AAAA,cAC3B,SAAQ,GAAP;AACA,0BAAU;AAAA,cACX;AAED,sBAAQ,UAAU,IAAI,CAAE,GAAE,IAAI;AAAA,YACtC,OAAa;AACL,kBAAI;AACF,qBAAK,KAAI;AAAA,cACV,SAAQ,GAAP;AACA,0BAAU;AAAA,cACX;AAED,iBAAG,KAAK,KAAK,SAAS;AAAA,YACvB;AAAA,UACP,OAAW;AACL,gBAAI;AACF,oBAAM,MAAK;AAAA,YACZ,SAAQ,GAAP;AACA,wBAAU;AAAA,YACX;AAED;UACD;AAAA,QACF,SAAQ,QAAP;AAEA,cAAI,UAAU,WAAW,OAAO,OAAO,UAAU,UAAU;AAGzD,gBAAI,cAAc,OAAO,MAAM,MAAM,IAAI;AACzC,gBAAI,eAAe,QAAQ,MAAM,MAAM,IAAI;AAC3C,gBAAI,IAAI,YAAY,SAAS;AAC7B,gBAAI,IAAI,aAAa,SAAS;AAE9B,mBAAO,KAAK,KAAK,KAAK,KAAK,YAAY,OAAO,aAAa,IAAI;AAO7D;AAAA,YACD;AAED,mBAAO,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK;AAGjC,kBAAI,YAAY,OAAO,aAAa,IAAI;AAMtC,oBAAI,MAAM,KAAK,MAAM,GAAG;AACtB,qBAAG;AACD;AACA;AAGA,wBAAI,IAAI,KAAK,YAAY,OAAO,aAAa,IAAI;AAE/C,0BAAI,SAAS,OAAO,YAAY,GAAG,QAAQ,YAAY,MAAM;AAK7D,0BAAI,GAAG,eAAe,OAAO,SAAS,aAAa,GAAG;AACpD,iCAAS,OAAO,QAAQ,eAAe,GAAG,WAAW;AAAA,sBACtD;AAED;AACE,4BAAI,OAAO,OAAO,YAAY;AAC5B,8CAAoB,IAAI,IAAI,MAAM;AAAA,wBACnC;AAAA,sBACF;AAGD,6BAAO;AAAA,oBACR;AAAA,kBACF,SAAQ,KAAK,KAAK,KAAK;AAAA,gBACzB;AAED;AAAA,cACD;AAAA,YACF;AAAA,UACF;AAAA,QACL,UAAY;AACR,oBAAU;AAEV;AACE,mCAAuB,UAAU;AACjC;UACD;AAED,gBAAM,oBAAoB;AAAA,QAC3B;AAGD,YAAI,OAAO,KAAK,GAAG,eAAe,GAAG,OAAO;AAC5C,YAAI,iBAAiB,OAAO,8BAA8B,IAAI,IAAI;AAElE;AACE,cAAI,OAAO,OAAO,YAAY;AAC5B,gCAAoB,IAAI,IAAI,cAAc;AAAA,UAC3C;AAAA,QACF;AAED,eAAO;AAAA,MACR;AACD,eAAS,+BAA+B,IAAI,QAAQ,SAAS;AAC3D;AACE,iBAAO,6BAA6B,IAAI,KAAK;AAAA,QAC9C;AAAA,MACF;AAED,eAAS,gBAAgB,WAAW;AAClC,YAAI,YAAY,UAAU;AAC1B,eAAO,CAAC,EAAE,aAAa,UAAU;AAAA,MAClC;AAED,eAAS,qCAAqC,MAAM,QAAQ,SAAS;AAEnE,YAAI,QAAQ,MAAM;AAChB,iBAAO;AAAA,QACR;AAED,YAAI,OAAO,SAAS,YAAY;AAC9B;AACE,mBAAO,6BAA6B,MAAM,gBAAgB,IAAI,CAAC;AAAA,UAChE;AAAA,QACF;AAED,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,8BAA8B,IAAI;AAAA,QAC1C;AAED,gBAAQ,MAAI;AAAA,UACV,KAAK;AACH,mBAAO,8BAA8B,UAAU;AAAA,UAEjD,KAAK;AACH,mBAAO,8BAA8B,cAAc;AAAA,QACtD;AAED,YAAI,OAAO,SAAS,UAAU;AAC5B,kBAAQ,KAAK,UAAQ;AAAA,YACnB,KAAK;AACH,qBAAO,+BAA+B,KAAK,MAAM;AAAA,YAEnD,KAAK;AAEH,qBAAO,qCAAqC,KAAK,MAAM,QAAQ,OAAO;AAAA,YAExE,KAAK,iBACH;AACE,kBAAI,gBAAgB;AACpB,kBAAI,UAAU,cAAc;AAC5B,kBAAI,OAAO,cAAc;AAEzB,kBAAI;AAEF,uBAAO,qCAAqC,KAAK,OAAO,GAAG,QAAQ,OAAO;AAAA,cACtF,SAAmB,GAAP;AAAA,cAAY;AAAA,YACf;AAAA,UACJ;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,UAAI,iBAAiB,OAAO,UAAU;AAEtC,UAAI,qBAAqB,CAAA;AACzB,UAAI,yBAAyB,qBAAqB;AAElD,eAAS,8BAA8B,SAAS;AAC9C;AACE,cAAI,SAAS;AACX,gBAAI,QAAQ,QAAQ;AACpB,gBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,mCAAuB,mBAAmB,KAAK;AAAA,UACrD,OAAW;AACL,mCAAuB,mBAAmB,IAAI;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAED,eAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,SAAS;AAC3E;AAEE,cAAI,MAAM,SAAS,KAAK,KAAK,cAAc;AAE3C,mBAAS,gBAAgB,WAAW;AAClC,gBAAI,IAAI,WAAW,YAAY,GAAG;AAChC,kBAAI,UAAU;AAId,kBAAI;AAGF,oBAAI,OAAO,UAAU,kBAAkB,YAAY;AAEjD,sBAAI,MAAM,OAAO,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FAAoG,OAAO,UAAU,gBAAgB,iGAAsG;AAC3U,sBAAI,OAAO;AACX,wBAAM;AAAA,gBACP;AAED,0BAAU,UAAU,cAAc,QAAQ,cAAc,eAAe,UAAU,MAAM,8CAA8C;AAAA,cACtI,SAAQ,IAAP;AACA,0BAAU;AAAA,cACX;AAED,kBAAI,WAAW,EAAE,mBAAmB,QAAQ;AAC1C,8CAA8B,OAAO;AAErC,sBAAM,4RAAqT,iBAAiB,eAAe,UAAU,cAAc,OAAO,OAAO;AAEjY,8CAA8B,IAAI;AAAA,cACnC;AAED,kBAAI,mBAAmB,SAAS,EAAE,QAAQ,WAAW,qBAAqB;AAGxE,mCAAmB,QAAQ,WAAW;AACtC,8CAA8B,OAAO;AAErC,sBAAM,sBAAsB,UAAU,QAAQ,OAAO;AAErD,8CAA8B,IAAI;AAAA,cACnC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,UAAI,cAAc,MAAM;AAExB,eAAS,QAAQ,GAAG;AAClB,eAAO,YAAY,CAAC;AAAA,MACrB;AAYD,eAAS,SAAS,OAAO;AACvB;AAEE,cAAI,iBAAiB,OAAO,WAAW,cAAc,OAAO;AAC5D,cAAI,OAAO,kBAAkB,MAAM,OAAO,gBAAgB,MAAM,YAAY,QAAQ;AACpF,iBAAO;AAAA,QACR;AAAA,MACF;AAGD,eAAS,kBAAkB,OAAO;AAChC;AACE,cAAI;AACF,+BAAmB,KAAK;AACxB,mBAAO;AAAA,UACR,SAAQ,GAAP;AACA,mBAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAED,eAAS,mBAAmB,OAAO;AAwBjC,eAAO,KAAK;AAAA,MACb;AACD,eAAS,uBAAuB,OAAO;AACrC;AACE,cAAI,kBAAkB,KAAK,GAAG;AAC5B,kBAAM,mHAAwH,SAAS,KAAK,CAAC;AAE7I,mBAAO,mBAAmB,KAAK;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAED,UAAI,oBAAoB,qBAAqB;AAC7C,UAAI,iBAAiB;AAAA,QACnB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AACA,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ;AACE,iCAAyB,CAAA;AAAA,MAC1B;AAED,eAAS,YAAY,QAAQ;AAC3B;AACE,cAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,gBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,gBAAI,UAAU,OAAO,gBAAgB;AACnC,qBAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAED,eAAO,OAAO,QAAQ;AAAA,MACvB;AAED,eAAS,YAAY,QAAQ;AAC3B;AACE,cAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,gBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,gBAAI,UAAU,OAAO,gBAAgB;AACnC,qBAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAED,eAAO,OAAO,QAAQ;AAAA,MACvB;AAED,eAAS,qCAAqC,QAAQ,MAAM;AAC1D;AACE,cAAI,OAAO,OAAO,QAAQ,YAAY,kBAAkB,WAAW,QAAQ,kBAAkB,QAAQ,cAAc,MAAM;AACvH,gBAAI,gBAAgB,yBAAyB,kBAAkB,QAAQ,IAAI;AAE3E,gBAAI,CAAC,uBAAuB,gBAAgB;AAC1C,oBAAM,6VAAsX,yBAAyB,kBAAkB,QAAQ,IAAI,GAAG,OAAO,GAAG;AAEhc,qCAAuB,iBAAiB;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,eAAS,2BAA2B,OAAO,aAAa;AACtD;AACE,cAAI,wBAAwB,WAAY;AACtC,gBAAI,CAAC,4BAA4B;AAC/B,2CAA6B;AAE7B,oBAAM,6OAA4P,WAAW;AAAA,YAC9Q;AAAA,UACP;AAEI,gCAAsB,iBAAiB;AACvC,iBAAO,eAAe,OAAO,OAAO;AAAA,YAClC,KAAK;AAAA,YACL,cAAc;AAAA,UACpB,CAAK;AAAA,QACF;AAAA,MACF;AAED,eAAS,2BAA2B,OAAO,aAAa;AACtD;AACE,cAAI,wBAAwB,WAAY;AACtC,gBAAI,CAAC,4BAA4B;AAC/B,2CAA6B;AAE7B,oBAAM,6OAA4P,WAAW;AAAA,YAC9Q;AAAA,UACP;AAEI,gCAAsB,iBAAiB;AACvC,iBAAO,eAAe,OAAO,OAAO;AAAA,YAClC,KAAK;AAAA,YACL,cAAc;AAAA,UACpB,CAAK;AAAA,QACF;AAAA,MACF;AAuBD,UAAI,eAAe,SAAU,MAAM,KAAK,KAAK,MAAM,QAAQ,OAAO,OAAO;AACvE,YAAI,UAAU;AAAA,UAEZ,UAAU;AAAA,UAEV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAEA,QAAQ;AAAA,QACZ;AAEE;AAKE,kBAAQ,SAAS;AAKjB,iBAAO,eAAe,QAAQ,QAAQ,aAAa;AAAA,YACjD,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,OAAO;AAAA,UACb,CAAK;AAED,iBAAO,eAAe,SAAS,SAAS;AAAA,YACtC,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,OAAO;AAAA,UACb,CAAK;AAGD,iBAAO,eAAe,SAAS,WAAW;AAAA,YACxC,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,OAAO;AAAA,UACb,CAAK;AAED,cAAI,OAAO,QAAQ;AACjB,mBAAO,OAAO,QAAQ,KAAK;AAC3B,mBAAO,OAAO,OAAO;AAAA,UACtB;AAAA,QACF;AAED,eAAO;AAAA,MACT;AAQA,eAAS,OAAO,MAAM,QAAQ,UAAU,QAAQ,MAAM;AACpD;AACE,cAAI;AAEJ,cAAI,QAAQ,CAAA;AACZ,cAAI,MAAM;AACV,cAAI,MAAM;AAOV,cAAI,aAAa,QAAW;AAC1B;AACE,qCAAuB,QAAQ;AAAA,YAChC;AAED,kBAAM,KAAK;AAAA,UACZ;AAED,cAAI,YAAY,MAAM,GAAG;AACvB;AACE,qCAAuB,OAAO,GAAG;AAAA,YAClC;AAED,kBAAM,KAAK,OAAO;AAAA,UACnB;AAED,cAAI,YAAY,MAAM,GAAG;AACvB,kBAAM,OAAO;AACb,iDAAqC,QAAQ,IAAI;AAAA,UAClD;AAGD,eAAK,YAAY,QAAQ;AACvB,gBAAI,eAAe,KAAK,QAAQ,QAAQ,KAAK,CAAC,eAAe,eAAe,QAAQ,GAAG;AACrF,oBAAM,YAAY,OAAO;AAAA,YAC1B;AAAA,UACF;AAGD,cAAI,QAAQ,KAAK,cAAc;AAC7B,gBAAI,eAAe,KAAK;AAExB,iBAAK,YAAY,cAAc;AAC7B,kBAAI,MAAM,cAAc,QAAW;AACjC,sBAAM,YAAY,aAAa;AAAA,cAChC;AAAA,YACF;AAAA,UACF;AAED,cAAI,OAAO,KAAK;AACd,gBAAI,cAAc,OAAO,SAAS,aAAa,KAAK,eAAe,KAAK,QAAQ,YAAY;AAE5F,gBAAI,KAAK;AACP,yCAA2B,OAAO,WAAW;AAAA,YAC9C;AAED,gBAAI,KAAK;AACP,yCAA2B,OAAO,WAAW;AAAA,YAC9C;AAAA,UACF;AAED,iBAAO,aAAa,MAAM,KAAK,KAAK,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AAAA,QACnF;AAAA,MACF;AAED,UAAI,sBAAsB,qBAAqB;AAC/C,UAAI,2BAA2B,qBAAqB;AAEpD,eAAS,gCAAgC,SAAS;AAChD;AACE,cAAI,SAAS;AACX,gBAAI,QAAQ,QAAQ;AACpB,gBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,qCAAyB,mBAAmB,KAAK;AAAA,UACvD,OAAW;AACL,qCAAyB,mBAAmB,IAAI;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAED,UAAI;AAEJ;AACE,wCAAgC;AAAA,MACjC;AAUD,eAAS,eAAe,QAAQ;AAC9B;AACE,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC7E;AAAA,MACF;AAED,eAAS,8BAA8B;AACrC;AACE,cAAI,oBAAoB,SAAS;AAC/B,gBAAI,OAAO,yBAAyB,oBAAoB,QAAQ,IAAI;AAEpE,gBAAI,MAAM;AACR,qBAAO,qCAAqC,OAAO;AAAA,YACpD;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAED,eAAS,2BAA2B,QAAQ;AAC1C;AACE,cAAI,WAAW,QAAW;AACxB,gBAAI,WAAW,OAAO,SAAS,QAAQ,aAAa,EAAE;AACtD,gBAAI,aAAa,OAAO;AACxB,mBAAO,4BAA4B,WAAW,MAAM,aAAa;AAAA,UAClE;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAQD,UAAI,wBAAwB,CAAA;AAE5B,eAAS,6BAA6B,YAAY;AAChD;AACE,cAAI,OAAO;AAEX,cAAI,CAAC,MAAM;AACT,gBAAI,aAAa,OAAO,eAAe,WAAW,aAAa,WAAW,eAAe,WAAW;AAEpG,gBAAI,YAAY;AACd,qBAAO,gDAAgD,aAAa;AAAA,YACrE;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAcD,eAAS,oBAAoB,SAAS,YAAY;AAChD;AACE,cAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO,aAAa,QAAQ,OAAO,MAAM;AACtE;AAAA,UACD;AAED,kBAAQ,OAAO,YAAY;AAC3B,cAAI,4BAA4B,6BAA6B,UAAU;AAEvE,cAAI,sBAAsB,4BAA4B;AACpD;AAAA,UACD;AAED,gCAAsB,6BAA6B;AAInD,cAAI,aAAa;AAEjB,cAAI,WAAW,QAAQ,UAAU,QAAQ,WAAW,oBAAoB,SAAS;AAE/E,yBAAa,iCAAiC,yBAAyB,QAAQ,OAAO,IAAI,IAAI;AAAA,UAC/F;AAED,0CAAgC,OAAO;AAEvC,gBAAM,6HAAkI,2BAA2B,UAAU;AAE7K,0CAAgC,IAAI;AAAA,QACrC;AAAA,MACF;AAYD,eAAS,kBAAkB,MAAM,YAAY;AAC3C;AACE,cAAI,OAAO,SAAS,UAAU;AAC5B;AAAA,UACD;AAED,cAAI,QAAQ,IAAI,GAAG;AACjB,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,QAAQ,KAAK;AAEjB,kBAAI,eAAe,KAAK,GAAG;AACzB,oCAAoB,OAAO,UAAU;AAAA,cACtC;AAAA,YACF;AAAA,UACP,WAAe,eAAe,IAAI,GAAG;AAE/B,gBAAI,KAAK,QAAQ;AACf,mBAAK,OAAO,YAAY;AAAA,YACzB;AAAA,UACF,WAAU,MAAM;AACf,gBAAI,aAAa,cAAc,IAAI;AAEnC,gBAAI,OAAO,eAAe,YAAY;AAGpC,kBAAI,eAAe,KAAK,SAAS;AAC/B,oBAAI,WAAW,WAAW,KAAK,IAAI;AACnC,oBAAI;AAEJ,uBAAO,EAAE,OAAO,SAAS,KAAI,GAAI,MAAM;AACrC,sBAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,wCAAoB,KAAK,OAAO,UAAU;AAAA,kBAC3C;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AASD,eAAS,kBAAkB,SAAS;AAClC;AACE,cAAI,OAAO,QAAQ;AAEnB,cAAI,SAAS,QAAQ,SAAS,UAAa,OAAO,SAAS,UAAU;AACnE;AAAA,UACD;AAED,cAAI;AAEJ,cAAI,OAAO,SAAS,YAAY;AAC9B,wBAAY,KAAK;AAAA,UAClB,WAAU,OAAO,SAAS,aAAa,KAAK,aAAa,0BAE1D,KAAK,aAAa,kBAAkB;AAClC,wBAAY,KAAK;AAAA,UACvB,OAAW;AACL;AAAA,UACD;AAED,cAAI,WAAW;AAEb,gBAAI,OAAO,yBAAyB,IAAI;AACxC,2BAAe,WAAW,QAAQ,OAAO,QAAQ,MAAM,OAAO;AAAA,UAC/D,WAAU,KAAK,cAAc,UAAa,CAAC,+BAA+B;AACzE,4CAAgC;AAEhC,gBAAI,QAAQ,yBAAyB,IAAI;AAEzC,kBAAM,uGAAuG,SAAS,SAAS;AAAA,UAChI;AAED,cAAI,OAAO,KAAK,oBAAoB,cAAc,CAAC,KAAK,gBAAgB,sBAAsB;AAC5F,kBAAM,4HAAiI;AAAA,UACxI;AAAA,QACF;AAAA,MACF;AAOD,eAAS,sBAAsB,UAAU;AACvC;AACE,cAAI,OAAO,OAAO,KAAK,SAAS,KAAK;AAErC,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,MAAM,KAAK;AAEf,gBAAI,QAAQ,cAAc,QAAQ,OAAO;AACvC,8CAAgC,QAAQ;AAExC,oBAAM,4GAAiH,GAAG;AAE1H,8CAAgC,IAAI;AACpC;AAAA,YACD;AAAA,UACF;AAED,cAAI,SAAS,QAAQ,MAAM;AACzB,4CAAgC,QAAQ;AAExC,kBAAM,uDAAuD;AAE7D,4CAAgC,IAAI;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAED,eAAS,kBAAkB,MAAM,OAAO,KAAK,kBAAkB,QAAQ,MAAM;AAC3E;AACE,cAAI,YAAY,mBAAmB,IAAI;AAGvC,cAAI,CAAC,WAAW;AACd,gBAAI,OAAO;AAEX,gBAAI,SAAS,UAAa,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AACrG,sBAAQ;AAAA,YACT;AAED,gBAAI,aAAa,2BAA2B,MAAM;AAElD,gBAAI,YAAY;AACd,sBAAQ;AAAA,YAChB,OAAa;AACL,sBAAQ,4BAA2B;AAAA,YACpC;AAED,gBAAI;AAEJ,gBAAI,SAAS,MAAM;AACjB,2BAAa;AAAA,YACrB,WAAiB,QAAQ,IAAI,GAAG;AACxB,2BAAa;AAAA,YACd,WAAU,SAAS,UAAa,KAAK,aAAa,oBAAoB;AACrE,2BAAa,OAAO,yBAAyB,KAAK,IAAI,KAAK,aAAa;AACxE,qBAAO;AAAA,YACf,OAAa;AACL,2BAAa,OAAO;AAAA,YACrB;AAED,kBAAM,2IAAqJ,YAAY,IAAI;AAAA,UAC5K;AAED,cAAI,UAAU,OAAO,MAAM,OAAO,KAAK,QAAQ,IAAI;AAGnD,cAAI,WAAW,MAAM;AACnB,mBAAO;AAAA,UACR;AAOD,cAAI,WAAW;AACb,gBAAI,WAAW,MAAM;AAErB,gBAAI,aAAa,QAAW;AAC1B,kBAAI,kBAAkB;AACpB,oBAAI,QAAQ,QAAQ,GAAG;AACrB,2BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,sCAAkB,SAAS,IAAI,IAAI;AAAA,kBACpC;AAED,sBAAI,OAAO,QAAQ;AACjB,2BAAO,OAAO,QAAQ;AAAA,kBACvB;AAAA,gBACb,OAAiB;AACL,wBAAM,sJAAgK;AAAA,gBACvK;AAAA,cACX,OAAe;AACL,kCAAkB,UAAU,IAAI;AAAA,cACjC;AAAA,YACF;AAAA,UACF;AAED,cAAI,SAAS,qBAAqB;AAChC,kCAAsB,OAAO;AAAA,UACnC,OAAW;AACL,8BAAkB,OAAO;AAAA,UAC1B;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAKD,eAAS,wBAAwB,MAAM,OAAO,KAAK;AACjD;AACE,iBAAO,kBAAkB,MAAM,OAAO,KAAK,IAAI;AAAA,QAChD;AAAA,MACF;AACD,eAAS,yBAAyB,MAAM,OAAO,KAAK;AAClD;AACE,iBAAO,kBAAkB,MAAM,OAAO,KAAK,KAAK;AAAA,QACjD;AAAA,MACF;AAED,UAAIC,OAAO;AAGX,UAAI,OAAQ;AAEI,kCAAA,WAAG;AACR,kCAAA,MAAGA;AACF,kCAAA,OAAG;AAAA,IACf;EACA;;;;AC/xCA,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,IAAAC,QAAA,UAAiBC;EACnB,OAAO;AACL,IAAAD,QAAA,UAAiBE;EACnB;;;ACNO,SAAS,eAAwB;AACpC,SAAO,OAAO,WAAW;AAC7B;AAEO,SAAS,eAAwB;AACpC,SAAO,CAAC,aAAa;AACzB;ACKa,MAAA,sBAAsBC,yBAAmC,IAAI;AA+D1E,IAAI,eAAoC;AASxC,MAAM,uBAAqE,CAAC,OAGtE;AAHsE,eACxE;AAAA;AAAA,MADwE,IAErE,oBAFqE,IAErE;AAAA,IADH;AAAA;AAGI,MAAA,aAAA,KAAkB,CAAC,cAAc;AAClB,mBAAA,IAAIC,gBAAa,OAAO;AAAA,EAC3C;AAEA,6BACK,oBAAoB,UAApB,EAA6B,OAAO,cAAe,SAAS,CAAA;AAErE;AAEA,MAAeC,yBAAAA,WAAAA,KAAK,oBAAoB;AC3FjC,MAAM,+BAA+BC,GAAAA,kBAAkB;AAAA,EAC1D,eAAe,MAA2C;AACtD,UAAM,GAAG,IAAI;AAEN,WAAA,eAAe,MAAM,uBAAuB,SAAS;AAAA,EAChE;AACJ;ACLO,MAAM,sCAAsC,uBAAuB;AAAA,EACtE,eAAe,MAA2C;AACtD,UAAM,GAAG,IAAI;AAEN,WAAA,eAAe,MAAM,8BAA8B,SAAS;AAAA,EACvE;AACJ;ACTO,SAAS,cAAc,UAAyD;AACnF,MAAI,CAAC,UAAU;AACX,UAAM,IAAI;AAAA,MACN;AAAA,IAAA;AAAA,EAER;AAEO,SAAA;AACX;ACFO,SAAS,kBAA0E;AAChF,QAAAC,gBAAeC,sBAAW,mBAAmB;AACnD,QAAM,aAAaC,WAAA;AAAA,IACf,CAAC,YAAiC;AAC9B,UAAIF,eAAc;AACd,QAAAA,cAAc,YAAY;AAAA,MAC9B;AAAA,IACJ;AAAA,IACA,CAACA,aAAY;AAAA,EAAA;AAGjB,MAAI,gBAAgB;AACT,WAAA,CAAC,MAAiC,MAAM;AAAA,IAAA,CAAE;AAAA,EACrD;AAEA,gBAAcA,aAAY;AACnB,SAAA,CAACA,eAAe,UAAU;AACrC;ACvBA,MAAM,eAAe;AAerB,MAAM,mBAA6D,CAAC,EAAE,WAAW,YAAY;AACzF,QAAM,CAAC,GAAG,UAAU,IAAI,gBAAgB;AAExCG,aAAAA,UAAU,MAAM;AACD,eAAA,EAAE,cAAc;AAC3B,WAAO,MAAM,WAAW,EAAE,cAAc,KAAM,CAAA;AAAA,EAAA,GAC/C,CAAC,UAAU,CAAC;AAGX,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACG,IAAI;AAAA,MACJ;AAAA,MACA,OAAO,iBAAE,OAAO,iBAAkB;AAAA,IAAM;AAAA,EAAA;AAGpD;AAEA,MAAeL,qBAAAA,WAAAA,KAAK,gBAAgB;AC5B7B,SAAS,eAAoE;AAC1E,QAAA,CAACE,aAAY,IAAI;AACjB,QAAA,CAAC,QAAQ,SAAS,IAAII,WAAA;AAAA,KACxBJ,iBAAA,gBAAAA,cAAc,WAAU;AAAA,EAAA;AAG5BG,aAAAA,UAAU,MAAM;AACZ,QAAIH,eAAc;AACd,gBAAUA,cAAa,MAAM;AACtB,aAAAA,cAAa,eAAe,CAAC,UAAkC;AAClE,kBAAU,KAAK;AAAA,MAAA,CAClB;AAAA,IACL;AAAA,EAAA,GACD,CAACA,aAAY,CAAC;AAEV,SAAA;AACX;ACfgB,SAAA,cAAc,eAAe,MAAc;AACvD,QAAM,SAAS;AACf,SAAOK,mBAAQ,MAAM;AACjB,QAAI,QAAQ;AACR,aAAO,eACDC,GAAA;AAAA,QACI,OAAO,QAAQ;AAAA,QACf,OAAO,QAAQ,UAAUC,GAAAA,MAAM;AAAA,MAAA,IAEnC,OAAO,QAAQ;AAAA,IAAA,OAClB;AACI,aAAA;AAAA,IACX;AAAA,EAAA,GACD,CAAC,QAAQ,cAAc,iCAAQ,QAAQ,SAAS,iCAAQ,QAAQ,KAAK,CAAC;AAC7E;ACfO,SAAS,qBAA0D;AAChE,QAAA,CAACP,aAAY,IAAI;AACjB,QAAA,CAAC,OAAO,QAAQ,IAAII,WAAAA,UAASJ,iBAAA,gBAAAA,cAAc,MAAM,UAAS,IAAI;AAEpEG,aAAAA,UAAU,MAAM;AACZ,QAAIH,eAAc;AACL,eAAAA,cAAa,MAAM,KAAK;AAC1B,aAAAA,cAAa,mBAAmB,CAAC,UAA6B;AACjE,iBAAS,KAAK;AAAA,MAAA,CACjB;AAAA,IACL;AAAA,EAAA,GACD,CAACA,aAAY,CAAC;AAEV,SAAA;AAAA,IACH;AAAA,IACA,MAAM,MAAMA,iBAAA,gBAAAA,cAAc,MAAM;AAAA,IAChC,OAAO,MAAMA,iBAAA,gBAAAA,cAAc,MAAM;AAAA,EAAM;AAE/C;ACnBO,SAAS,0BAAmC;AAC/C,QAAM,CAAC,UAAU,WAAW,IAAII,oBAAS,KAAK;AACxC,QAAA,CAACJ,aAAY,IAAI;AAEvBG,aAAAA,UAAU,MAAM;AACZ,QAAIH,eAAc;AACd,MAAAA,cAAa,mBAAmB,KAAK,MAAM,YAAY,IAAI,CAAC;AAAA,IAChE;AAAA,EAAA,GACD,CAACA,aAAY,CAAC;AAEV,SAAA;AACX;;;;;;;;;;;;;;;;;;"}