{"version": 3, "sources": ["../../../src/export/getPublicExpoManifest.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\n\nimport { LocaleMap, getResolvedLocalesAsync } from './getResolvedLocales';\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\n\n/** Get the public Expo manifest from the local project config. */\nexport async function getPublicExpoManifestAsync(\n  projectRoot: string,\n  { skipValidation }: { skipValidation?: boolean } = {}\n): Promise<ExpoConfig & { locales: LocaleMap; sdkVersion: string }> {\n  // Read the config in public mode which strips the `hooks`.\n  const { exp } = getConfig(projectRoot, {\n    isPublicConfig: true,\n    // This shouldn't be needed since the CLI is vendored in `expo`.\n    skipSDKVersionRequirement: true,\n  });\n\n  // Only allow projects to be published with UNVERSIONED if a correct token is set in env\n  if (\n    !skipValidation &&\n    exp.sdkVersion === 'UNVERSIONED' &&\n    !env.EXPO_SKIP_MANIFEST_VALIDATION_TOKEN\n  ) {\n    throw new CommandError('INVALID_OPTIONS', 'Cannot publish with sdkVersion UNVERSIONED.');\n  }\n\n  return {\n    ...exp,\n    locales: await getResolvedLocalesAsync(projectRoot, exp),\n    sdkVersion: exp.sdkVersion!,\n  };\n}\n"], "names": ["getPublicExpoManifestAsync", "projectRoot", "skipValidation", "exp", "getConfig", "isPublicConfig", "skipSDKVersionRequirement", "sdkVersion", "env", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN", "CommandError", "locales", "getResolvedLocalesAsync"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;;yBAPgB;;;;;;oCAEa;qBAC/B;wBACS;AAGtB,eAAeA,2BACpBC,WAAmB,EACnB,EAAEC,cAAc,EAAgC,GAAG,CAAC,CAAC;IAErD,2DAA2D;IAC3D,MAAM,EAAEC,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACH,aAAa;QACrCI,gBAAgB;QAChB,gEAAgE;QAChEC,2BAA2B;IAC7B;IAEA,wFAAwF;IACxF,IACE,CAACJ,kBACDC,IAAII,UAAU,KAAK,iBACnB,CAACC,QAAG,CAACC,mCAAmC,EACxC;QACA,MAAM,IAAIC,oBAAY,CAAC,mBAAmB;IAC5C;IAEA,OAAO;QACL,GAAGP,GAAG;QACNQ,SAAS,MAAMC,IAAAA,2CAAuB,EAACX,aAAaE;QACpDI,YAAYJ,IAAII,UAAU;IAC5B;AACF"}