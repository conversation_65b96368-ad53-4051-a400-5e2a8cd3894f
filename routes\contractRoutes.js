/**
 * API маршруты для работы со смарт-контрактами
 */

const express = require('express');
const router = express.Router();

/**
 * Создание контракта для устройства (новый метод)
 */
router.post('/create-device-contract', async (req, res) => {
  try {
    const { device_id, owner_address, price_per_minute, platform_fee_percent = 500 } = req.body;

    if (!device_id || !owner_address || !price_per_minute) {
      return res.status(400).json({
        success: false,
        message: 'Не указаны обязательные параметры: device_id, owner_address, price_per_minute'
      });
    }

    const { pool, tonService, logger } = req.app.locals;

    // Проверяем, не существует ли уже контракт для этого устройства
    const existingContract = await pool.query(
      'SELECT contract_address FROM device_contracts WHERE device_id = $1',
      [device_id]
    );

    if (existingContract.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Контракт для этого устройства уже существует',
        contractAddress: existingContract.rows[0].contract_address
      });
    }

    // Создаем контракт через новый сервис
    const contractResult = await tonService.createDeviceContract(
      device_id,
      owner_address,
      price_per_minute,
      platform_fee_percent
    );

    if (!contractResult.success) {
      return res.status(500).json({
        success: false,
        message: contractResult.message || 'Ошибка создания контракта'
      });
    }

    // Сохраняем информацию о контракте в базе данных
    await pool.query(`
      INSERT INTO device_contracts
      (device_id, contract_address, streamer_wallet, price_per_minute, platform_fee_percent, is_active, created_at)
      VALUES ($1, $2, $3, $4, $5, true, NOW())
    `, [
      device_id,
      contractResult.contractAddress,
      owner_address,
      price_per_minute,
      platform_fee_percent
    ]);

    logger.info(`📝 Создан контракт для устройства ${device_id}: ${contractResult.contractAddress}`);

    res.json({
      success: true,
      message: 'Контракт создан успешно',
      contractAddress: contractResult.contractAddress,
      deployMessage: contractResult.deployMessage,
      contractData: contractResult.contractData
    });

  } catch (error) {
    console.error('Ошибка создания контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании контракта'
    });
  }
});

/**
 * Деплой смарт-контракта для устройства (старый метод)
 */
router.post('/deploy-device-contract', async (req, res) => {
  const { device_id, price_per_minute, streamer_wallet } = req.body;

  if (!device_id || !price_per_minute || !streamer_wallet) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: device_id, price_per_minute, streamer_wallet'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Проверка, что устройство принадлежит стримеру
    const deviceCheck = await pool.query(
      'SELECT * FROM devices WHERE device_id = $1 AND wallet_address = $2',
      [device_id, streamer_wallet]
    );

    if (deviceCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Устройство не найдено или не принадлежит указанному кошельку'
      });
    }

    // Проверка, что контракт еще не создан
    const contractCheck = await pool.query(
      'SELECT * FROM device_contracts WHERE device_id = $1',
      [device_id]
    );

    if (contractCheck.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Контракт для этого устройства уже существует'
      });
    }

    // Создание контракта
    const contractData = await tonService.deployDeviceContract(
      device_id,
      streamer_wallet,
      price_per_minute
    );

    // Сохранение в базе данных
    await pool.query(`
      INSERT INTO device_contracts
      (device_id, contract_address, streamer_wallet, deploy_transaction_hash)
      VALUES ($1, $2, $3, $4)
    `, [
      device_id,
      contractData.contractAddress,
      streamer_wallet,
      null // Хеш транзакции будет добавлен после отправки
    ]);

    logger.info(`📄 Создан контракт для устройства ${device_id}: ${contractData.contractAddress}`);

    res.json({
      success: true,
      message: 'Контракт создан успешно',
      contractAddress: contractData.contractAddress,
      estimatedFee: contractData.estimatedFee,
      deployMessage: contractData.deployMessage
    });

  } catch (error) {
    console.error('Ошибка создания контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании контракта'
    });
  }
});

/**
 * Получение информации о контракте устройства
 */
router.get('/device/:device_id', async (req, res) => {
  const { device_id } = req.params;

  try {
    const { pool, tonService } = req.app.locals;

    // Получение контракта из базы данных
    const result = await pool.query(`
      SELECT dc.*, d.name as device_name
      FROM device_contracts dc
      LEFT JOIN devices d ON dc.device_id = d.device_id
      WHERE dc.device_id = $1
    `, [device_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт для устройства не найден'
      });
    }

    const contract = result.rows[0];

    // Получение актуальной информации из блокчейна
    const contractInfo = await tonService.getContractInfo(contract.contract_address);

    res.json({
      success: true,
      contract: {
        ...contract,
        ...contractInfo,
        device_name: contract.device_name
      }
    });

  } catch (error) {
    console.error('Ошибка получения информации о контракте:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении информации о контракте'
    });
  }
});

/**
 * Обновление настроек контракта
 */
router.put('/device/:device_id/settings', async (req, res) => {
  const { device_id } = req.params;
  const { price_per_minute, streamer_wallet } = req.body;

  if (!price_per_minute || !streamer_wallet) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: price_per_minute, streamer_wallet'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Проверка прав доступа
    const contractCheck = await pool.query(
      'SELECT * FROM device_contracts WHERE device_id = $1 AND streamer_wallet = $2',
      [device_id, streamer_wallet]
    );

    if (contractCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт не найден или у вас нет прав доступа'
      });
    }

    const contract = contractCheck.rows[0];

    // Создание транзакции для обновления цены
    const updateMessage = await tonService.setDevicePrice(
      contract.contract_address,
      price_per_minute,
      streamer_wallet
    );

    logger.info(`💰 Обновление цены для устройства ${device_id}: ${price_per_minute} TON/мин`);

    res.json({
      success: true,
      message: 'Настройки контракта будут обновлены',
      updateMessage
    });

  } catch (error) {
    console.error('Ошибка обновления настроек контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при обновлении настроек'
    });
  }
});

/**
 * Покупка времени управления
 */
router.post('/buy-time', async (req, res) => {
  const { device_id, duration_minutes, buyer_wallet } = req.body;

  if (!device_id || !duration_minutes || !buyer_wallet) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: device_id, duration_minutes, buyer_wallet'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Получение контракта устройства
    const contractResult = await pool.query(
      'SELECT * FROM device_contracts WHERE device_id = $1 AND is_active = true',
      [device_id]
    );

    if (contractResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Активный контракт для устройства не найден'
      });
    }

    const contract = contractResult.rows[0];

    // Проверка активной сессии управления
    const activeSession = await pool.query(`
      SELECT * FROM active_control_sessions
      WHERE device_id = $1 AND is_active = true AND end_time > NOW()
    `, [device_id]);

    if (activeSession.rows.length > 0 && activeSession.rows[0].controller_wallet !== buyer_wallet) {
      return res.status(409).json({
        success: false,
        message: 'Устройство уже управляется другим пользователем',
        currentController: activeSession.rows[0].controller_wallet,
        endTime: activeSession.rows[0].end_time
      });
    }

    // Создание транзакции покупки
    const buyMessage = await tonService.buyControlTime(
      contract.contract_address,
      duration_minutes,
      buyer_wallet
    );

    // Сохранение транзакции в базе данных (статус pending)
    const transactionResult = await pool.query(`
      INSERT INTO control_transactions
      (device_id, buyer_wallet, amount_ton, duration_minutes, transaction_hash, status)
      VALUES ($1, $2, $3, $4, $5, 'pending')
      RETURNING id
    `, [
      device_id,
      buyer_wallet,
      buyMessage.estimatedCost,
      duration_minutes,
      'pending_' + Date.now() // Временный хеш, будет обновлен после отправки
    ]);

    logger.info(`🛒 Создана заявка на покупку времени: устройство ${device_id}, ${duration_minutes} мин`);

    res.json({
      success: true,
      message: 'Транзакция подготовлена',
      transactionId: transactionResult.rows[0].id,
      buyMessage,
      estimatedCost: buyMessage.estimatedCost
    });

  } catch (error) {
    console.error('Ошибка создания транзакции покупки:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании транзакции'
    });
  }
});

/**
 * Подтверждение транзакции
 */
router.post('/confirm-transaction', async (req, res) => {
  const { transaction_hash, transaction_id } = req.body;

  if (!transaction_hash || !transaction_id) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: transaction_hash, transaction_id'
    });
  }

  try {
    const { pool, logger } = req.app.locals;

    // Обновление хеша транзакции
    const result = await pool.query(`
      UPDATE control_transactions
      SET transaction_hash = $1, updated_at = NOW()
      WHERE id = $2 AND status = 'pending'
      RETURNING *
    `, [transaction_hash, transaction_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Транзакция не найдена или уже обработана'
      });
    }

    logger.info(`✅ Подтверждена транзакция ${transaction_hash}`);

    res.json({
      success: true,
      message: 'Транзакция отправлена в блокчейн, ожидается подтверждение'
    });

  } catch (error) {
    console.error('Ошибка подтверждения транзакции:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при подтверждении транзакции'
    });
  }
});

/**
 * Получение статуса управления устройством
 */
router.get('/status/:device_id', async (req, res) => {
  const { device_id } = req.params;

  try {
    const { pool, tonService } = req.app.locals;

    // Получение контракта
    const contractResult = await pool.query(
      'SELECT contract_address FROM device_contracts WHERE device_id = $1',
      [device_id]
    );

    if (contractResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт для устройства не найден'
      });
    }

    const contractAddress = contractResult.rows[0].contract_address;

    // Получение статуса из блокчейна
    const blockchainStatus = await tonService.getControlStatus(contractAddress);

    // Получение статуса из базы данных
    const dbStatus = await pool.query(`
      SELECT * FROM active_control_sessions
      WHERE device_id = $1 AND is_active = true
      ORDER BY created_at DESC
      LIMIT 1
    `, [device_id]);

    res.json({
      success: true,
      status: {
        blockchain: blockchainStatus,
        database: dbStatus.rows[0] || null,
        deviceId: device_id
      }
    });

  } catch (error) {
    console.error('Ошибка получения статуса управления:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении статуса'
    });
  }
});

/**
 * Вывод заработанных средств стримера
 */
router.post('/withdraw-earnings', async (req, res) => {
  try {
    const { streamer_wallet } = req.body;

    if (!streamer_wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указан кошелек стримера'
      });
    }

    const { pool, tonService, logger } = req.app.locals;

    // Получаем все контракты стримера с накопленными средствами
    const contractsQuery = `
      SELECT device_id, contract_address, accumulated_earnings
      FROM device_contracts
      WHERE streamer_wallet = $1 AND accumulated_earnings > 0 AND contract_address IS NOT NULL
    `;

    const contractsResult = await pool.query(contractsQuery, [streamer_wallet]);

    if (contractsResult.rows.length === 0) {
      return res.json({
        success: false,
        message: 'Нет контрактов с накопленными средствами'
      });
    }

    let totalWithdrawn = 0;
    const withdrawalResults = [];

    // Инициируем вывод средств с каждого контракта
    for (const contract of contractsResult.rows) {
      try {
        const result = await tonService.withdrawEarnings(
          contract.contract_address,
          streamer_wallet
        );

        totalWithdrawn += parseFloat(contract.accumulated_earnings);
        withdrawalResults.push({
          device_id: contract.device_id,
          amount: contract.accumulated_earnings,
          transaction: result
        });

        // Записываем транзакцию вывода в базу данных
        await pool.query(`
          INSERT INTO streamer_earnings
          (streamer_wallet, device_id, amount_ton, transaction_hash, withdrawal_time)
          VALUES ($1, $2, $3, $4, NOW())
        `, [
          streamer_wallet,
          contract.device_id,
          contract.accumulated_earnings,
          'pending_' + Date.now()
        ]);

        // Обнуляем накопленные средства в контракте
        await pool.query(
          'UPDATE device_contracts SET accumulated_earnings = 0 WHERE device_id = $1',
          [contract.device_id]
        );

        logger.info(`💰 Вывод средств стримера ${streamer_wallet} с устройства ${contract.device_id}: ${contract.accumulated_earnings} TON`);

      } catch (error) {
        console.error(`Ошибка вывода средств с устройства ${contract.device_id}:`, error);
      }
    }

    logger.info(`💸 Общий вывод средств стримера ${streamer_wallet}: ${totalWithdrawn} TON с ${withdrawalResults.length} устройств`);

    res.json({
      success: true,
      message: `Инициирован вывод средств с ${withdrawalResults.length} устройств`,
      data: {
        totalWithdrawn,
        withdrawalResults
      }
    });

  } catch (error) {
    console.error('Ошибка вывода средств стримера:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка при выводе средств'
    });
  }
});

module.exports = router;
