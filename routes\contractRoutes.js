/**
 * API маршруты для работы со смарт-контрактами
 */

const express = require('express');
const router = express.Router();

/**
 * Деплой универсального контракта (выполняется один раз администратором)
 */
router.post('/deploy-universal-contract', async (req, res) => {
  try {
    const { admin_wallet } = req.body;

    if (!admin_wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указан кошелек администратора'
      });
    }

    const { tonService, logger } = req.app.locals;

    // Проверяем, не задеплоен ли уже контракт
    const contractExists = await tonService.checkUniversalContractExists();

    if (contractExists) {
      return res.status(400).json({
        success: false,
        message: 'Универсальный контракт уже задеплоен',
        contractAddress: tonService.getUniversalContractAddress()
      });
    }

    // Деплоим универсальный контракт
    const deployResult = await tonService.deployUniversalContract();

    if (!deployResult.success) {
      return res.status(500).json({
        success: false,
        message: deployResult.message || 'Ошибка деплоя контракта'
      });
    }

    logger.info(`🚀 Универсальный контракт задеплоен: ${deployResult.contractAddress}`);

    res.json({
      success: true,
      message: 'Универсальный контракт успешно задеплоен',
      contractAddress: deployResult.contractAddress
    });

  } catch (error) {
    console.error('Ошибка деплоя универсального контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при деплое контракта'
    });
  }
});

/**
 * Получение информации об универсальном контракте
 */
router.get('/universal-contract-info', async (req, res) => {
  try {
    const { tonService } = req.app.locals;

    const contractAddress = tonService.getUniversalContractAddress();

    if (!contractAddress) {
      return res.status(404).json({
        success: false,
        message: 'Универсальный контракт не инициализирован'
      });
    }

    const contractExists = await tonService.checkUniversalContractExists();
    const platformStats = await tonService.getPlatformStats();

    res.json({
      success: true,
      contractAddress,
      isDeployed: contractExists,
      platformStats
    });

  } catch (error) {
    console.error('Ошибка получения информации о контракте:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении информации о контракте'
    });
  }
});

/**
 * Деплой смарт-контракта для устройства (старый метод)
 */
router.post('/deploy-device-contract', async (req, res) => {
  const { device_id, price_per_minute, streamer_wallet } = req.body;

  if (!device_id || !price_per_minute || !streamer_wallet) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: device_id, price_per_minute, streamer_wallet'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Проверка, что устройство принадлежит стримеру
    const deviceCheck = await pool.query(
      'SELECT * FROM devices WHERE device_id = $1 AND wallet_address = $2',
      [device_id, streamer_wallet]
    );

    if (deviceCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Устройство не найдено или не принадлежит указанному кошельку'
      });
    }

    // Проверка, что контракт еще не создан
    const contractCheck = await pool.query(
      'SELECT * FROM device_contracts WHERE device_id = $1',
      [device_id]
    );

    if (contractCheck.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Контракт для этого устройства уже существует'
      });
    }

    // Создание контракта
    const contractData = await tonService.deployDeviceContract(
      device_id,
      streamer_wallet,
      price_per_minute
    );

    // Сохранение в базе данных
    await pool.query(`
      INSERT INTO device_contracts
      (device_id, contract_address, streamer_wallet, deploy_transaction_hash)
      VALUES ($1, $2, $3, $4)
    `, [
      device_id,
      contractData.contractAddress,
      streamer_wallet,
      null // Хеш транзакции будет добавлен после отправки
    ]);

    logger.info(`📄 Создан контракт для устройства ${device_id}: ${contractData.contractAddress}`);

    res.json({
      success: true,
      message: 'Контракт создан успешно',
      contractAddress: contractData.contractAddress,
      estimatedFee: contractData.estimatedFee,
      deployMessage: contractData.deployMessage
    });

  } catch (error) {
    console.error('Ошибка создания контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании контракта'
    });
  }
});

/**
 * Получение информации о контракте устройства
 */
router.get('/device/:device_id', async (req, res) => {
  const { device_id } = req.params;

  try {
    const { pool, tonService } = req.app.locals;

    // Получение контракта из базы данных
    const result = await pool.query(`
      SELECT dc.*, d.name as device_name
      FROM device_contracts dc
      LEFT JOIN devices d ON dc.device_id = d.device_id
      WHERE dc.device_id = $1
    `, [device_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт для устройства не найден'
      });
    }

    const contract = result.rows[0];

    // Получение актуальной информации из блокчейна
    const contractInfo = await tonService.getContractInfo(contract.contract_address);

    res.json({
      success: true,
      contract: {
        ...contract,
        ...contractInfo,
        device_name: contract.device_name
      }
    });

  } catch (error) {
    console.error('Ошибка получения информации о контракте:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении информации о контракте'
    });
  }
});

/**
 * Обновление настроек контракта
 */
router.put('/device/:device_id/settings', async (req, res) => {
  const { device_id } = req.params;
  const { price_per_minute, streamer_wallet } = req.body;

  if (!price_per_minute || !streamer_wallet) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: price_per_minute, streamer_wallet'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Проверка прав доступа
    const contractCheck = await pool.query(
      'SELECT * FROM device_contracts WHERE device_id = $1 AND streamer_wallet = $2',
      [device_id, streamer_wallet]
    );

    if (contractCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт не найден или у вас нет прав доступа'
      });
    }

    const contract = contractCheck.rows[0];

    // Создание транзакции для обновления цены
    const updateMessage = await tonService.setDevicePrice(
      contract.contract_address,
      price_per_minute,
      streamer_wallet
    );

    logger.info(`💰 Обновление цены для устройства ${device_id}: ${price_per_minute} TON/мин`);

    res.json({
      success: true,
      message: 'Настройки контракта будут обновлены',
      updateMessage
    });

  } catch (error) {
    console.error('Ошибка обновления настроек контракта:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при обновлении настроек'
    });
  }
});

/**
 * Покупка времени управления через универсальный контракт
 */
router.post('/buy-time', async (req, res) => {
  const { device_id, duration_minutes, buyer_wallet, streamer_nickname } = req.body;

  if (!device_id || !duration_minutes || !buyer_wallet || !streamer_nickname) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: device_id, duration_minutes, buyer_wallet, streamer_nickname'
    });
  }

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Получаем адрес стримера по нику
    const streamerResult = await pool.query(
      'SELECT wallet_address FROM streamers WHERE nickname = $1',
      [streamer_nickname]
    );

    if (streamerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Стример не найден'
      });
    }

    const streamerAddress = streamerResult.rows[0].wallet_address;

    // Проверяем активную сессию управления через универсальный контракт
    const activeSession = await tonService.getActiveSession(streamerAddress, device_id);

    if (activeSession.isActive && activeSession.controller !== buyer_wallet) {
      return res.status(409).json({
        success: false,
        message: 'Устройство уже управляется другим пользователем',
        currentController: activeSession.controller,
        endTime: activeSession.endTime
      });
    }

    // Создание транзакции покупки через универсальный контракт
    const buyMessage = await tonService.buyControlTime(
      streamerAddress,
      device_id,
      duration_minutes,
      buyer_wallet
    );

    // Генерируем ID транзакции
    const transactionId = `buy_time_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Сохранение транзакции в базе данных
    await pool.query(`
      INSERT INTO control_transactions
      (id, device_id, buyer_wallet, amount_ton, duration_minutes, status, created_at, memo)
      VALUES ($1, $2, $3, $4, $5, 'pending', NOW(), $6)
    `, [
      transactionId,
      device_id,
      buyer_wallet,
      buyMessage.estimatedCost,
      duration_minutes,
      buyMessage.memo
    ]);

    logger.info(`🛒 Создана заявка на покупку времени: устройство ${device_id}, стример ${streamer_nickname}, ${duration_minutes} мин`);

    res.json({
      success: true,
      message: 'Транзакция подготовлена для универсального контракта',
      transactionId,
      contractAddress: tonService.getUniversalContractAddress(),
      estimatedCost: buyMessage.estimatedCost,
      memo: buyMessage.memo,
      payload: buyMessage.body ? buyMessage.body.toBoc().toString('base64') : null
    });

  } catch (error) {
    console.error('Ошибка создания транзакции покупки:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании транзакции'
    });
  }
});

/**
 * Подтверждение транзакции
 */
router.post('/confirm-transaction', async (req, res) => {
  const { transaction_hash, transaction_id } = req.body;

  if (!transaction_hash || !transaction_id) {
    return res.status(400).json({
      success: false,
      message: 'Необходимы параметры: transaction_hash, transaction_id'
    });
  }

  try {
    const { pool, logger } = req.app.locals;

    // Обновление хеша транзакции
    const result = await pool.query(`
      UPDATE control_transactions
      SET transaction_hash = $1, updated_at = NOW()
      WHERE id = $2 AND status = 'pending'
      RETURNING *
    `, [transaction_hash, transaction_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Транзакция не найдена или уже обработана'
      });
    }

    logger.info(`✅ Подтверждена транзакция ${transaction_hash}`);

    res.json({
      success: true,
      message: 'Транзакция отправлена в блокчейн, ожидается подтверждение'
    });

  } catch (error) {
    console.error('Ошибка подтверждения транзакции:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при подтверждении транзакции'
    });
  }
});

/**
 * Получение статуса управления устройством
 */
router.get('/status/:device_id', async (req, res) => {
  const { device_id } = req.params;

  try {
    const { pool, tonService } = req.app.locals;

    // Получение контракта
    const contractResult = await pool.query(
      'SELECT contract_address FROM device_contracts WHERE device_id = $1',
      [device_id]
    );

    if (contractResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт для устройства не найден'
      });
    }

    const contractAddress = contractResult.rows[0].contract_address;

    // Получение статуса из блокчейна
    const blockchainStatus = await tonService.getControlStatus(contractAddress);

    // Получение статуса из базы данных
    const dbStatus = await pool.query(`
      SELECT * FROM active_control_sessions
      WHERE device_id = $1 AND is_active = true
      ORDER BY created_at DESC
      LIMIT 1
    `, [device_id]);

    res.json({
      success: true,
      status: {
        blockchain: blockchainStatus,
        database: dbStatus.rows[0] || null,
        deviceId: device_id
      }
    });

  } catch (error) {
    console.error('Ошибка получения статуса управления:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении статуса'
    });
  }
});

/**
 * Вывод заработанных средств стримера из универсального контракта
 */
router.post('/withdraw-earnings', async (req, res) => {
  try {
    const { streamer_wallet } = req.body;

    if (!streamer_wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указан кошелек стримера'
      });
    }

    const { pool, tonService, logger } = req.app.locals;

    // Получаем баланс стримера из универсального контракта
    const streamerBalance = await tonService.getStreamerBalance(streamer_wallet);

    if (parseFloat(streamerBalance) <= 0) {
      return res.json({
        success: false,
        message: 'Нет средств для вывода'
      });
    }

    // Создаем транзакцию вывода средств
    const withdrawMessage = await tonService.withdrawEarnings(streamer_wallet);

    if (!withdrawMessage.success) {
      return res.status(500).json({
        success: false,
        message: 'Ошибка создания транзакции вывода'
      });
    }

    // Записываем транзакцию вывода в базу данных
    await pool.query(`
      INSERT INTO streamer_earnings
      (streamer_wallet, amount_ton, transaction_hash, withdrawal_time)
      VALUES ($1, $2, $3, NOW())
    `, [
      streamer_wallet,
      streamerBalance,
      withdrawMessage.transactionId
    ]);

    logger.info(`💰 Инициирован вывод средств стримера ${streamer_wallet}: ${streamerBalance} TON`);

    res.json({
      success: true,
      message: 'Транзакция вывода средств подготовлена',
      data: {
        amount: streamerBalance,
        contractAddress: tonService.getUniversalContractAddress(),
        transactionId: withdrawMessage.transactionId,
        payload: withdrawMessage.body ? withdrawMessage.body.toBoc().toString('base64') : null
      }
    });

  } catch (error) {
    console.error('Ошибка вывода средств стримера:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка при выводе средств'
    });
  }
});

/**
 * Получение баланса стримера
 */
router.get('/streamer-balance/:wallet', async (req, res) => {
  try {
    const { wallet } = req.params;

    if (!wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указан кошелек стримера'
      });
    }

    const { tonService } = req.app.locals;

    const balance = await tonService.getStreamerBalance(wallet);

    res.json({
      success: true,
      balance,
      contractAddress: tonService.getUniversalContractAddress()
    });

  } catch (error) {
    console.error('Ошибка получения баланса стримера:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка при получении баланса'
    });
  }
});

module.exports = router;
