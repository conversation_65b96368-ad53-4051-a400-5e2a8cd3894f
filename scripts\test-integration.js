/**
 * Скрипт для тестирования интеграции смарт-контрактов
 * Проверяет все компоненты системы
 */

require('dotenv').config();
const { Pool } = require('pg');

// Импортируем наши сервисы
const TonService = require('../services/TonService');
const TransactionMonitorService = require('../services/TransactionMonitorService');
const blockchainConfig = require('../config/blockchain');

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
});

// Простой логгер для тестирования
const logger = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  warn: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`)
};

async function testDatabaseConnection() {
  console.log('\n🔍 Тестирование подключения к базе данных...');
  
  try {
    const client = await pool.connect();
    
    // Проверяем существование новых таблиц
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'device_contracts', 
        'control_transactions', 
        'active_control_sessions', 
        'streamer_earnings', 
        'contract_events'
      )
    `);
    
    console.log(`✅ Подключение к БД успешно. Найдено ${tables.rows.length} новых таблиц.`);
    
    // Проверяем структуру таблиц
    for (const table of tables.rows) {
      const columns = await client.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = $1
        ORDER BY ordinal_position
      `, [table.table_name]);
      
      console.log(`  📋 ${table.table_name}: ${columns.rows.length} колонок`);
    }
    
    client.release();
    return true;
  } catch (error) {
    console.log(`❌ Ошибка подключения к БД: ${error.message}`);
    return false;
  }
}

async function testBlockchainConfig() {
  console.log('\n🔍 Тестирование конфигурации блокчейна...');
  
  try {
    // Проверяем валидацию конфигурации
    blockchainConfig.validate();
    console.log('✅ Конфигурация блокчейна валидна');
    
    // Проверяем утилиты
    const isTestnet = blockchainConfig.utils.isTestnet();
    console.log(`  🌐 Сеть: ${isTestnet ? 'testnet' : 'mainnet'}`);
    
    // Проверяем лимиты
    const validDuration = blockchainConfig.checkLimits('controlDuration', 5);
    const validPrice = blockchainConfig.checkLimits('pricePerMinute', '0.1');
    console.log(`  ⏱️  Лимиты времени: ${validDuration ? 'OK' : 'FAIL'}`);
    console.log(`  💰 Лимиты цены: ${validPrice ? 'OK' : 'FAIL'}`);
    
    return true;
  } catch (error) {
    console.log(`❌ Ошибка конфигурации: ${error.message}`);
    return false;
  }
}

async function testTonService() {
  console.log('\n🔍 Тестирование TON сервиса...');
  
  try {
    const tonService = new TonService(blockchainConfig);
    console.log('✅ TON сервис инициализирован');
    
    // Тестируем утилитарные функции
    const deviceId = 'TEST:DEVICE:001';
    const hashedId = tonService.hashDeviceId(deviceId);
    console.log(`  🔗 Хеш устройства: ${hashedId.toString(16).substring(0, 16)}...`);
    
    // Тестируем создание сообщений (без отправки)
    try {
      const buyMessage = await tonService.buyControlTime(
        'EQTest123', // Тестовый адрес
        5, // 5 минут
        'EQBuyer123' // Тестовый покупатель
      );
      console.log('✅ Создание сообщения покупки времени работает');
    } catch (error) {
      console.log(`⚠️  Создание сообщения: ${error.message} (ожидаемо для тестового адреса)`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Ошибка TON сервиса: ${error.message}`);
    return false;
  }
}

async function testTransactionMonitor() {
  console.log('\n🔍 Тестирование мониторинга транзакций...');
  
  try {
    const tonService = new TonService(blockchainConfig);
    const monitor = new TransactionMonitorService(pool, logger, tonService);
    console.log('✅ Сервис мониторинга инициализирован');
    
    // Тестируем получение статистики
    const stats = await monitor.getMonitoringStats();
    console.log(`  📊 Статистика мониторинга получена: ${Object.keys(stats).length} статусов`);
    
    return true;
  } catch (error) {
    console.log(`❌ Ошибка мониторинга: ${error.message}`);
    return false;
  }
}

async function testApiRoutes() {
  console.log('\n🔍 Тестирование API маршрутов...');
  
  try {
    // Проверяем, что файлы маршрутов существуют и корректны
    const contractRoutes = require('../routes/contractRoutes');
    const transactionRoutes = require('../routes/transactionRoutes');
    
    console.log('✅ Маршруты контрактов загружены');
    console.log('✅ Маршруты транзакций загружены');
    
    return true;
  } catch (error) {
    console.log(`❌ Ошибка маршрутов: ${error.message}`);
    return false;
  }
}

async function testSmartContractManager() {
  console.log('\n🔍 Тестирование менеджера смарт-контрактов...');
  
  try {
    // Проверяем, что файл существует и корректен
    const fs = require('fs');
    const path = require('path');
    
    const managerPath = path.join(__dirname, '..', 'public', 'js', 'smart-contract-manager.js');
    const managerExists = fs.existsSync(managerPath);
    
    if (managerExists) {
      console.log('✅ Файл менеджера смарт-контрактов найден');
      
      const content = fs.readFileSync(managerPath, 'utf8');
      const hasMainMethods = [
        'deployDeviceContract',
        'buyControlTime',
        'getControlStatus',
        'confirmTransaction'
      ].every(method => content.includes(method));
      
      if (hasMainMethods) {
        console.log('✅ Все основные методы присутствуют');
      } else {
        console.log('⚠️  Некоторые методы отсутствуют');
      }
    } else {
      console.log('❌ Файл менеджера не найден');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Ошибка менеджера: ${error.message}`);
    return false;
  }
}

async function insertTestData() {
  console.log('\n🔍 Добавление тестовых данных...');
  
  try {
    const client = await pool.connect();
    
    // Добавляем тестового стримера
    await client.query(`
      INSERT INTO streamers (wallet_address, nickname, stream_url) 
      VALUES ($1, $2, $3)
      ON CONFLICT (wallet_address) DO UPDATE SET
        nickname = EXCLUDED.nickname,
        stream_url = EXCLUDED.stream_url
    `, [
      'EQTestStreamer123',
      'test_streamer',
      'https://youtube.com/live/test123'
    ]);
    
    // Добавляем тестовое устройство
    await client.query(`
      INSERT INTO devices (device_id, wallet_address, name)
      VALUES ($1, $2, $3)
      ON CONFLICT (device_id, wallet_address) DO UPDATE SET
        name = EXCLUDED.name
    `, [
      'TEST:DEVICE:001',
      'EQTestStreamer123',
      'Тестовое устройство'
    ]);
    
    // Добавляем запись в lot
    await client.query(`
      INSERT INTO lot (device_id, is_online, device_type)
      VALUES ($1, $2, $3)
      ON CONFLICT (device_id) DO UPDATE SET
        is_online = EXCLUDED.is_online,
        device_type = EXCLUDED.device_type
    `, [
      'TEST:DEVICE:001',
      true,
      'ESP32'
    ]);
    
    // Добавляем настройки управления
    await client.query(`
      INSERT INTO control_settings (wallet_address, mode, time_minutes, price_ton)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (wallet_address) DO UPDATE SET
        mode = EXCLUDED.mode,
        time_minutes = EXCLUDED.time_minutes,
        price_ton = EXCLUDED.price_ton
    `, [
      'EQTestStreamer123',
      'fixed-time',
      5,
      0.1
    ]);
    
    console.log('✅ Тестовые данные добавлены');
    client.release();
    return true;
  } catch (error) {
    console.log(`❌ Ошибка добавления тестовых данных: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Запуск полного тестирования интеграции смарт-контрактов\n');
  
  const tests = [
    { name: 'База данных', fn: testDatabaseConnection },
    { name: 'Конфигурация блокчейна', fn: testBlockchainConfig },
    { name: 'TON сервис', fn: testTonService },
    { name: 'Мониторинг транзакций', fn: testTransactionMonitor },
    { name: 'API маршруты', fn: testApiRoutes },
    { name: 'Менеджер смарт-контрактов', fn: testSmartContractManager },
    { name: 'Тестовые данные', fn: insertTestData }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Критическая ошибка в тесте "${test.name}": ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n📊 Результаты тестирования:');
  console.log(`✅ Пройдено: ${passed}`);
  console.log(`❌ Провалено: ${failed}`);
  console.log(`📈 Успешность: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 Все тесты пройдены! Система готова к использованию.');
  } else {
    console.log('\n⚠️  Некоторые тесты провалены. Проверьте ошибки выше.');
  }
  
  await pool.end();
}

// Запуск тестирования
runAllTests().catch(error => {
  console.error('💥 Критическая ошибка тестирования:', error);
  process.exit(1);
});
