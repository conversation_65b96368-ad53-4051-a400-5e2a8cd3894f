{"version": 3, "sources": ["../../../src/customize/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\n\nexport const expoCustomize: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Other options are parsed manually.\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow other options, we'll throw an error if unexpected values are passed.\n      permissive: true,\n    }\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Generate static project files`,\n      chalk`npx expo customize {dim [files...] -- [options]}`,\n      [\n        chalk`[files...]  List of files to generate`,\n        chalk`[options]   Options to pass to the install command`,\n        `-h, --help  Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo install -h` shows as fast as possible.\n  const { customizeAsync } = require('./customizeAsync') as typeof import('./customizeAsync');\n  const { logCmdError } = require('../utils/errors') as typeof import('../utils/errors');\n  const { resolveArgsAsync } = require('./resolveOptions') as typeof import('./resolveOptions');\n\n  const { variadic, options, extras } = await resolveArgsAsync(process.argv.slice(3)).catch(\n    logCmdError\n  );\n  return customizeAsync(variadic, options, extras).catch(logCmdError);\n};\n"], "names": ["expoCustomize", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "printHelp", "chalk", "join", "customizeAsync", "require", "logCmdError", "resolveArgsAsync", "variadic", "options", "extras", "process", "slice", "catch"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;;gEALK;;;;;;sBAG+B;;;;;;AAE1C,MAAMA,gBAAyB,OAAOC;IAC3C,MAAMC,OAAOC,IAAAA,2BAAqB,EAChC;QACE,qCAAqC;QACrC,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACA;QACEH;QACA,6EAA6E;QAC7EI,YAAY;IACd;IAGF,IAAIH,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,6BAA6B,CAAC,EAC/BC,IAAAA,gBAAK,CAAA,CAAC,gDAAgD,CAAC,EACvD;YACEA,IAAAA,gBAAK,CAAA,CAAC,qCAAqC,CAAC;YAC5CA,IAAAA,gBAAK,CAAA,CAAC,kDAAkD,CAAC;YACzD,CAAC,sBAAsB,CAAC;SACzB,CAACC,IAAI,CAAC;IAEX;IAEA,yFAAyF;IACzF,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;IACnC,MAAM,EAAEC,WAAW,EAAE,GAAGD,QAAQ;IAChC,MAAM,EAAEE,gBAAgB,EAAE,GAAGF,QAAQ;IAErC,MAAM,EAAEG,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,MAAMH,iBAAiBI,QAAQf,IAAI,CAACgB,KAAK,CAAC,IAAIC,KAAK,CACvFP;IAEF,OAAOF,eAAeI,UAAUC,SAASC,QAAQG,KAAK,CAACP;AACzD"}