/**
 * Простой тест основных функций
 */

const axios = require('axios');

const API_BASE = 'http://localhost:4000';

async function testBasicEndpoints() {
  console.log('🧪 Тестирование основных endpoints...\n');

  const tests = [
    {
      name: 'Получение устройств тестового стримера',
      url: `${API_BASE}/devices-by-nickname/test_streamer`,
      method: 'GET'
    },
    {
      name: 'Получение настроек режима управления',
      url: `${API_BASE}/control-mode-settings/EQTestStreamer123`,
      method: 'GET'
    },
    {
      name: 'Получение статистики мониторинга',
      url: `${API_BASE}/api/transactions/monitoring/stats`,
      method: 'GET'
    },
    {
      name: 'Получение статистики платформы',
      url: `${API_BASE}/api/transactions/platform/stats`,
      method: 'GET'
    },
    {
      name: 'Получение истории транзакций',
      url: `${API_BASE}/api/transactions/device/TEST:DEVICE:001`,
      method: 'GET'
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`🔍 ${test.name}...`);
      
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000
      });

      if (response.status === 200 && response.data) {
        console.log(`✅ Успешно (${response.status})`);
        if (response.data.success !== undefined) {
          console.log(`   Статус: ${response.data.success ? 'success' : 'error'}`);
          if (response.data.message) {
            console.log(`   Сообщение: ${response.data.message}`);
          }
        }
        passed++;
      } else {
        console.log(`❌ Неожиданный ответ (${response.status})`);
        failed++;
      }
    } catch (error) {
      if (error.response) {
        console.log(`⚠️  HTTP ошибка ${error.response.status}: ${error.response.statusText}`);
        if (error.response.status === 404 || error.response.status === 400) {
          console.log(`   Это ожидаемо для тестовых данных`);
          passed++;
        } else {
          failed++;
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`❌ Сервер недоступен`);
        failed++;
      } else {
        console.log(`❌ Ошибка: ${error.message}`);
        failed++;
      }
    }
    console.log('');
  }

  console.log('📊 Результаты тестирования:');
  console.log(`✅ Пройдено: ${passed}`);
  console.log(`❌ Провалено: ${failed}`);
  console.log(`📈 Успешность: ${Math.round((passed / (passed + failed)) * 100)}%`);

  return { passed, failed };
}

async function testSmartContractEndpoints() {
  console.log('\n🧪 Тестирование endpoints смарт-контрактов...\n');

  const tests = [
    {
      name: 'Получение несуществующего контракта',
      url: `${API_BASE}/api/contracts/device/NONEXISTENT`,
      method: 'GET',
      expectedStatus: 404
    },
    {
      name: 'Деплой контракта с неполными данными',
      url: `${API_BASE}/api/contracts/deploy-device-contract`,
      method: 'POST',
      data: { device_id: 'TEST' },
      expectedStatus: 400
    },
    {
      name: 'Покупка времени с неполными данными',
      url: `${API_BASE}/api/contracts/buy-time`,
      method: 'POST',
      data: { device_id: 'TEST' },
      expectedStatus: 400
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`🔍 ${test.name}...`);
      
      const config = {
        method: test.method,
        url: test.url,
        timeout: 5000
      };

      if (test.data) {
        config.data = test.data;
        config.headers = { 'Content-Type': 'application/json' };
      }

      const response = await axios(config);
      
      if (test.expectedStatus && response.status !== test.expectedStatus) {
        console.log(`❌ Ожидался статус ${test.expectedStatus}, получен ${response.status}`);
        failed++;
      } else {
        console.log(`✅ Корректный ответ (${response.status})`);
        passed++;
      }
    } catch (error) {
      if (error.response && test.expectedStatus && error.response.status === test.expectedStatus) {
        console.log(`✅ Ожидаемая ошибка (${error.response.status})`);
        if (error.response.data && error.response.data.message) {
          console.log(`   Сообщение: ${error.response.data.message}`);
        }
        passed++;
      } else if (error.response) {
        console.log(`❌ Неожиданная HTTP ошибка ${error.response.status}`);
        failed++;
      } else {
        console.log(`❌ Ошибка: ${error.message}`);
        failed++;
      }
    }
    console.log('');
  }

  console.log('📊 Результаты тестирования смарт-контрактов:');
  console.log(`✅ Пройдено: ${passed}`);
  console.log(`❌ Провалено: ${failed}`);
  console.log(`📈 Успешность: ${Math.round((passed / (passed + failed)) * 100)}%`);

  return { passed, failed };
}

async function runSimpleTests() {
  console.log('🚀 Запуск простого тестирования\n');

  const basicResults = await testBasicEndpoints();
  const contractResults = await testSmartContractEndpoints();

  const totalPassed = basicResults.passed + contractResults.passed;
  const totalFailed = basicResults.failed + contractResults.failed;

  console.log('\n🎯 Общие результаты:');
  console.log(`✅ Всего пройдено: ${totalPassed}`);
  console.log(`❌ Всего провалено: ${totalFailed}`);
  console.log(`📈 Общая успешность: ${Math.round((totalPassed / (totalPassed + totalFailed)) * 100)}%`);

  if (totalFailed === 0) {
    console.log('\n🎉 Все тесты пройдены! Система работает корректно.');
  } else if (totalPassed > totalFailed) {
    console.log('\n✅ Большинство тестов пройдено. Система в основном работает.');
  } else {
    console.log('\n⚠️  Много провалов. Требуется дополнительная отладка.');
  }
}

// Запуск тестирования
runSimpleTests().catch(error => {
  console.error('💥 Критическая ошибка:', error.message);
  process.exit(1);
});
