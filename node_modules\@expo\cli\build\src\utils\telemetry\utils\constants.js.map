{"version": 3, "sources": ["../../../../../src/utils/telemetry/utils/constants.ts"], "sourcesContent": ["import { env } from '../../env';\n\nexport const TELEMETRY_ENDPOINT = 'https://cdp.expo.dev/v1/batch';\n\nexport const TELEMETRY_TARGET =\n  env.EXPO_STAGING || env.EXPO_LOCAL\n    ? '24TKICqYKilXM480mA7ktgVDdea'\n    : '24TKR7CQAaGgIrLTgu3Fp4OdOkI'; // expo unified\n"], "names": ["TELEMETRY_ENDPOINT", "TELEMETRY_TARGET", "env", "EXPO_STAGING", "EXPO_LOCAL"], "mappings": ";;;;;;;;;;;IAEaA,kBAAkB;eAAlBA;;IAEAC,gBAAgB;eAAhBA;;;qBAJO;AAEb,MAAMD,qBAAqB;AAE3B,MAAMC,mBACXC,QAAG,CAACC,YAAY,IAAID,QAAG,CAACE,UAAU,GAC9B,gCACA,+BAA+B,eAAe"}