{"name": "@tonconnect/ui-react", "version": "2.0.11", "scripts": {"dev": "vite", "build": "tsc && vite build"}, "repository": {"type": "git", "url": "git+https://github.com/ton-connect/sdk.git"}, "homepage": "https://github.com/ton-connect/sdk/tree/main/packages/ui-react", "bugs": {"url": "https://github.com/ton-connect/sdk/issues"}, "keywords": ["TON", "Wallet", "ton-connect", "tonconnect", "Connect", "<PERSON><PERSON><PERSON><PERSON>", "sdk", "UI", "React"], "author": "tonconnect", "license": "Apache-2.0", "files": ["lib"], "main": "./lib/index.cjs", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "default": "./lib/index.cjs"}}, "devDependencies": {"@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@vitejs/plugin-react": "^3.0.0", "typescript": "^4.9.3", "vite": "^4.0.0", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "csstype": "^3.1.1", "vite-plugin-dts": "^1.7.1"}, "dependencies": {"@tonconnect/ui": "2.0.11"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "nx": {"tags": ["scope:ui-react"]}, "typedoc": {"entryPoint": "./src/library.ts"}}