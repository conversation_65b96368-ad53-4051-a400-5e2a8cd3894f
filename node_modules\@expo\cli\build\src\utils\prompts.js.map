{"version": 3, "sources": ["../../../src/utils/prompts.ts"], "sourcesContent": ["import assert from 'assert';\nimport prompts, { Choice, Options, PromptObject } from 'prompts';\n\nimport { AbortCommandError, CommandError } from './errors';\nimport { isInteractive } from './interactive';\n\nconst debug = require('debug')('expo:utils:prompts') as typeof console.log;\n\nexport type Question<V extends string = string> = PromptObject<V> & {\n  optionsPerPage?: number;\n};\n\nexport interface ExpoChoice<T> extends Choice {\n  value: T;\n}\n\ntype PromptOptions = { nonInteractiveHelp?: string } & Options;\n\nexport type NamelessQuestion = Omit<Question<'value'>, 'name' | 'type'>;\n\ntype InteractionOptions = { pause: boolean; canEscape?: boolean };\n\ntype InteractionCallback = (options: InteractionOptions) => void;\n\n/** Interaction observers for detecting when keystroke tracking should pause/resume. */\nconst listeners: InteractionCallback[] = [];\n\nexport default async function prompt(\n  questions: Question | Question[],\n  { nonInteractiveHelp, ...options }: PromptOptions = {}\n) {\n  questions = Array.isArray(questions) ? questions : [questions];\n  if (!isInteractive() && questions.length !== 0) {\n    let message = `Input is required, but 'npx expo' is in non-interactive mode.\\n`;\n    if (nonInteractiveHelp) {\n      message += nonInteractiveHelp;\n    } else {\n      const question = questions[0];\n      const questionMessage =\n        typeof question.message === 'function'\n          ? question.message(undefined, {}, question)\n          : question.message;\n\n      message += `Required input:\\n${(questionMessage || '').trim().replace(/^/gm, '> ')}`;\n    }\n    throw new CommandError('NON_INTERACTIVE', message);\n  }\n\n  pauseInteractions();\n  try {\n    const results = await prompts(questions, {\n      onCancel() {\n        throw new AbortCommandError();\n      },\n      ...options,\n    });\n\n    return results;\n  } finally {\n    resumeInteractions();\n  }\n}\n\n/**\n * Create a standard yes/no confirmation that can be cancelled.\n *\n * @param questions\n * @param options\n */\nexport async function confirmAsync(\n  questions: NamelessQuestion,\n  options?: PromptOptions\n): Promise<boolean> {\n  const { value } = await prompt(\n    {\n      initial: true,\n      ...questions,\n      name: 'value',\n      type: 'confirm',\n    },\n    options\n  );\n  return value ?? null;\n}\n\n/** Select an option from a list of options. */\nexport async function selectAsync<T>(\n  message: string,\n  choices: ExpoChoice<T>[],\n  options?: PromptOptions\n): Promise<T> {\n  const { value } = await prompt(\n    {\n      message,\n      choices,\n      name: 'value',\n      type: 'select',\n    },\n    options\n  );\n  return value ?? null;\n}\n\nexport const promptAsync = prompt;\n\n/** Used to pause/resume interaction observers while prompting (made for TerminalUI). */\nexport function addInteractionListener(callback: InteractionCallback) {\n  listeners.push(callback);\n}\n\nexport function removeInteractionListener(callback: InteractionCallback) {\n  const listenerIndex = listeners.findIndex((_callback) => _callback === callback);\n  assert(\n    listenerIndex >= 0,\n    'removeInteractionListener(): cannot remove an unregistered event listener.'\n  );\n  listeners.splice(listenerIndex, 1);\n}\n\n/** Notify all listeners that keypress observations must pause. */\nexport function pauseInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers paused');\n  for (const listener of listeners) {\n    listener({ pause: true, ...options });\n  }\n}\n\n/** Notify all listeners that keypress observations can start.. */\nexport function resumeInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers resumed');\n  for (const listener of listeners) {\n    listener({ pause: false, ...options });\n  }\n}\n\nexport function createSelectionFilter(): (input: any, choices: Choice[]) => Promise<any> {\n  function escapeRegex(string: string) {\n    return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n  }\n\n  return async (input: any, choices: Choice[]) => {\n    try {\n      const regex = new RegExp(escapeRegex(input), 'i');\n      return choices.filter((choice: any) => regex.test(choice.title));\n    } catch (error: any) {\n      debug('Error filtering choices', error);\n      return [];\n    }\n  };\n}\n"], "names": ["addInteractionListener", "<PERSON><PERSON><PERSON>", "createSelectionFilter", "prompt", "pauseInteractions", "promptAsync", "removeInteractionListener", "resumeInteractions", "selectAsync", "debug", "require", "listeners", "questions", "nonInteractiveHelp", "options", "Array", "isArray", "isInteractive", "length", "message", "question", "questionMessage", "undefined", "trim", "replace", "CommandError", "results", "prompts", "onCancel", "AbortCommandError", "value", "initial", "name", "type", "choices", "callback", "push", "listenerIndex", "findIndex", "_callback", "assert", "splice", "listener", "pause", "escapeRegex", "string", "input", "regex", "RegExp", "filter", "choice", "test", "title", "error"], "mappings": ";;;;;;;;;;;IA0GgBA,sBAAsB;eAAtBA;;IArCMC,YAAY;eAAZA;;IAkENC,qBAAqB;eAArBA;;IA5GhB,OAkCC;eAlC6BC;;IA6FdC,iBAAiB;eAAjBA;;IAjBHC,WAAW;eAAXA;;IAOGC,yBAAyB;eAAzBA;;IAkBAC,kBAAkB;eAAlBA;;IA1CMC,WAAW;eAAXA;;;;gEAtFH;;;;;;;gEACoC;;;;;;wBAEP;6BAClB;;;;;;AAE9B,MAAMC,QAAQC,QAAQ,SAAS;AAkB/B,qFAAqF,GACrF,MAAMC,YAAmC,EAAE;AAE5B,eAAeR,OAC5BS,SAAgC,EAChC,EAAEC,kBAAkB,EAAE,GAAGC,SAAwB,GAAG,CAAC,CAAC;IAEtDF,YAAYG,MAAMC,OAAO,CAACJ,aAAaA,YAAY;QAACA;KAAU;IAC9D,IAAI,CAACK,IAAAA,0BAAa,OAAML,UAAUM,MAAM,KAAK,GAAG;QAC9C,IAAIC,UAAU,CAAC,+DAA+D,CAAC;QAC/E,IAAIN,oBAAoB;YACtBM,WAAWN;QACb,OAAO;YACL,MAAMO,WAAWR,SAAS,CAAC,EAAE;YAC7B,MAAMS,kBACJ,OAAOD,SAASD,OAAO,KAAK,aACxBC,SAASD,OAAO,CAACG,WAAW,CAAC,GAAGF,YAChCA,SAASD,OAAO;YAEtBA,WAAW,CAAC,iBAAiB,EAAE,AAACE,CAAAA,mBAAmB,EAAC,EAAGE,IAAI,GAAGC,OAAO,CAAC,OAAO,OAAO;QACtF;QACA,MAAM,IAAIC,oBAAY,CAAC,mBAAmBN;IAC5C;IAEAf;IACA,IAAI;QACF,MAAMsB,UAAU,MAAMC,IAAAA,kBAAO,EAACf,WAAW;YACvCgB;gBACE,MAAM,IAAIC,yBAAiB;YAC7B;YACA,GAAGf,OAAO;QACZ;QAEA,OAAOY;IACT,SAAU;QACRnB;IACF;AACF;AAQO,eAAeN,aACpBW,SAA2B,EAC3BE,OAAuB;IAEvB,MAAM,EAAEgB,KAAK,EAAE,GAAG,MAAM3B,OACtB;QACE4B,SAAS;QACT,GAAGnB,SAAS;QACZoB,MAAM;QACNC,MAAM;IACR,GACAnB;IAEF,OAAOgB,SAAS;AAClB;AAGO,eAAetB,YACpBW,OAAe,EACfe,OAAwB,EACxBpB,OAAuB;IAEvB,MAAM,EAAEgB,KAAK,EAAE,GAAG,MAAM3B,OACtB;QACEgB;QACAe;QACAF,MAAM;QACNC,MAAM;IACR,GACAnB;IAEF,OAAOgB,SAAS;AAClB;AAEO,MAAMzB,cAAcF;AAGpB,SAASH,uBAAuBmC,QAA6B;IAClExB,UAAUyB,IAAI,CAACD;AACjB;AAEO,SAAS7B,0BAA0B6B,QAA6B;IACrE,MAAME,gBAAgB1B,UAAU2B,SAAS,CAAC,CAACC,YAAcA,cAAcJ;IACvEK,IAAAA,iBAAM,EACJH,iBAAiB,GACjB;IAEF1B,UAAU8B,MAAM,CAACJ,eAAe;AAClC;AAGO,SAASjC,kBAAkBU,UAA6C,CAAC,CAAC;IAC/EL,MAAM;IACN,KAAK,MAAMiC,YAAY/B,UAAW;QAChC+B,SAAS;YAAEC,OAAO;YAAM,GAAG7B,OAAO;QAAC;IACrC;AACF;AAGO,SAASP,mBAAmBO,UAA6C,CAAC,CAAC;IAChFL,MAAM;IACN,KAAK,MAAMiC,YAAY/B,UAAW;QAChC+B,SAAS;YAAEC,OAAO;YAAO,GAAG7B,OAAO;QAAC;IACtC;AACF;AAEO,SAASZ;IACd,SAAS0C,YAAYC,MAAc;QACjC,OAAOA,OAAOrB,OAAO,CAAC,yBAAyB;IACjD;IAEA,OAAO,OAAOsB,OAAYZ;QACxB,IAAI;YACF,MAAMa,QAAQ,IAAIC,OAAOJ,YAAYE,QAAQ;YAC7C,OAAOZ,QAAQe,MAAM,CAAC,CAACC,SAAgBH,MAAMI,IAAI,CAACD,OAAOE,KAAK;QAChE,EAAE,OAAOC,OAAY;YACnB5C,MAAM,2BAA2B4C;YACjC,OAAO,EAAE;QACX;IACF;AACF"}