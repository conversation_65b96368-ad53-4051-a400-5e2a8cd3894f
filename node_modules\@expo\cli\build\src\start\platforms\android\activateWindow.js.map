{"version": 3, "sources": ["../../../../../src/start/platforms/android/activateWindow.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport { execFileSync } from 'child_process';\n\nimport { Device } from './adb';\n\nconst debug = require('debug')('expo:start:platforms:android:activateWindow') as typeof console.log;\n\nfunction getUnixPID(port: number | string): string {\n  // Runs like `lsof -i:8081 -P -t -sTCP:LISTEN`\n  const args = [`-i:${port}`, '-P', '-t', '-sTCP:LISTEN'];\n  debug('lsof ' + args.join(' '));\n  return execFileSync('lsof', args, {\n    encoding: 'utf8',\n    stdio: ['pipe', 'pipe', 'ignore'],\n  })\n    .split('\\n')[0]\n    ?.trim?.();\n}\n\n/** Activate the Emulator window on macOS. */\nexport async function activateWindowAsync(device: Pick<Device, 'type' | 'pid'>): Promise<boolean> {\n  debug(`Activating window for device (pid: ${device.pid}, type: ${device.type})`);\n  if (\n    // only mac is supported for now.\n    process.platform !== 'darwin' ||\n    // can only focus emulators\n    device.type !== 'emulator'\n  ) {\n    return false;\n  }\n\n  // Google Emulator ID: `emulator-5554` -> `5554`\n  const androidPid = device.pid!.match(/-(\\d+)/)?.[1];\n  if (!androidPid) {\n    return false;\n  }\n  // Unix PID\n  const pid = getUnixPID(androidPid);\n\n  if (!pid) {\n    return false;\n  }\n  debug(`Activate window for pid:`, pid);\n  try {\n    await osascript.execAsync(`\n    tell application \"System Events\"\n      set frontmost of the first process whose unix id is ${pid} to true\n    end tell`);\n    return true;\n  } catch {\n    // noop -- this feature is very specific and subject to failure.\n    return false;\n  }\n}\n"], "names": ["activateWindowAsync", "debug", "require", "getUnixPID", "port", "execFileSync", "args", "join", "encoding", "stdio", "split", "trim", "device", "pid", "type", "process", "platform", "androidPid", "match", "osascript", "execAsync"], "mappings": ";;;;+BAoBsBA;;;eAAAA;;;;iEApBK;;;;;;;yBACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI7B,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,SAASC,WAAWC,IAAqB;QAIhCC,2BAAAA;IAHP,8CAA8C;IAC9C,MAAMC,OAAO;QAAC,CAAC,GAAG,EAAEF,MAAM;QAAE;QAAM;QAAM;KAAe;IACvDH,MAAM,UAAUK,KAAKC,IAAI,CAAC;IAC1B,QAAOF,uBAAAA,IAAAA,6BAAY,EAAC,QAAQC,MAAM;QAChCE,UAAU;QACVC,OAAO;YAAC;YAAQ;YAAQ;SAAS;IACnC,GACGC,KAAK,CAAC,KAAK,CAAC,EAAE,sBAJVL,4BAAAA,qBAKHM,IAAI,qBALDN,+BAAAA;AAMT;AAGO,eAAeL,oBAAoBY,MAAoC;QAYzDA;IAXnBX,MAAM,CAAC,mCAAmC,EAAEW,OAAOC,GAAG,CAAC,QAAQ,EAAED,OAAOE,IAAI,CAAC,CAAC,CAAC;IAC/E,IACE,iCAAiC;IACjCC,QAAQC,QAAQ,KAAK,YACrB,2BAA2B;IAC3BJ,OAAOE,IAAI,KAAK,YAChB;QACA,OAAO;IACT;IAEA,gDAAgD;IAChD,MAAMG,cAAaL,oBAAAA,OAAOC,GAAG,CAAEK,KAAK,CAAC,8BAAlBN,iBAA6B,CAAC,EAAE;IACnD,IAAI,CAACK,YAAY;QACf,OAAO;IACT;IACA,WAAW;IACX,MAAMJ,MAAMV,WAAWc;IAEvB,IAAI,CAACJ,KAAK;QACR,OAAO;IACT;IACAZ,MAAM,CAAC,wBAAwB,CAAC,EAAEY;IAClC,IAAI;QACF,MAAMM,aAAUC,SAAS,CAAC,CAAC;;0DAE2B,EAAEP,IAAI;YACpD,CAAC;QACT,OAAO;IACT,EAAE,OAAM;QACN,gEAAgE;QAChE,OAAO;IACT;AACF"}