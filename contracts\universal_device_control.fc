;; Universal Device Control Smart Contract
;; Единый контракт для управления всеми устройствами платформы
;; Использует memo для идентификации стримеров и устройств

#include "imports/stdlib.fc";

;; Константы операций
const op::buy_time = 0x1;
const op::withdraw_streamer_earnings = 0x2;
const op::withdraw_platform_fee = 0x3;
const op::emergency_stop = 0x4;
const op::update_device_settings = 0x5;

;; Константы ошибок
const error::unauthorized = 401;
const error::insufficient_payment = 402;
const error::invalid_memo = 403;
const error::invalid_time = 404;
const error::insufficient_balance = 405;

;; Структура storage:
;; platform_address - адрес платформы
;; platform_fee_percent - процент комиссии платформы (в базисных пунктах)
;; total_platform_fees - общая накопленная комиссия платформы
;; total_volume - общий объем транзакций
;; streamer_balances - словарь балансов стримеров (ключ: hash(streamer_address), значение: balance)
;; device_settings - словарь настроек устройств (ключ: hash(memo), значение: price_per_minute)
;; active_sessions - словарь активных сессий (ключ: hash(memo), значение: end_time + controller_address)

() save_data(slice platform_address, int platform_fee_percent, int total_platform_fees, 
             int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) impure {
    set_data(begin_cell()
        .store_slice(platform_address)
        .store_uint(platform_fee_percent, 16)
        .store_coins(total_platform_fees)
        .store_coins(total_volume)
        .store_ref(streamer_balances)
        .store_ref(device_settings)
        .store_ref(active_sessions)
        .end_cell());
}

(slice, int, int, int, cell, cell, cell) load_data() {
    slice ds = get_data().begin_parse();
    slice platform_address = ds~load_msg_addr();
    int platform_fee_percent = ds~load_uint(16);
    int total_platform_fees = ds~load_coins();
    int total_volume = ds~load_coins();
    cell streamer_balances = ds~load_ref();
    cell device_settings = ds~load_ref();
    cell active_sessions = ds~load_ref();
    return (platform_address, platform_fee_percent, total_platform_fees, 
            total_volume, streamer_balances, device_settings, active_sessions);
}

;; Функция для парсинга memo
;; Формат memo: "streamer_address:device_id:duration_minutes"
(slice, slice, int) parse_memo(slice memo_slice) {
    ;; Парсим memo строку для извлечения адреса стримера, device_id и времени
    ;; Для простоты используем первые 32 байта как hash
    int memo_hash = slice_hash(memo_slice);
    
    ;; Возвращаем hash как slice для совместимости
    slice streamer_hash = begin_cell().store_uint(memo_hash, 256).end_cell().begin_parse();
    slice device_hash = begin_cell().store_uint(memo_hash, 256).end_cell().begin_parse();
    
    return (streamer_hash, device_hash, 5); ;; По умолчанию 5 минут
}

() recv_internal(int my_balance, int msg_value, cell in_msg_full, slice in_msg_body) impure {
    if (in_msg_body.slice_empty?()) { 
        ;; Простой перевод с memo
        slice cs = in_msg_full.begin_parse();
        int flags = cs~load_uint(4);
        slice sender_address = cs~load_msg_addr();
        
        ;; Загружаем данные контракта
        (slice platform_address, int platform_fee_percent, int total_platform_fees, 
         int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();
        
        ;; Проверяем наличие memo в сообщении
        slice memo_slice = cs~load_msg_addr(); ;; Упрощенная версия, в реальности нужно парсить memo
        
        if (memo_slice.slice_empty?()) {
            ;; Нет memo - отклоняем транзакцию
            throw(error::invalid_memo);
        }
        
        ;; Парсим memo для получения информации о стримере и устройстве
        (slice streamer_hash, slice device_hash, int duration_minutes) = parse_memo(memo_slice);
        
        ;; Получаем настройки устройства
        (cell new_device_settings, int price_per_minute, int found) = device_settings.udict_get_ref?(256, slice_hash(device_hash));
        
        if (~ found) {
            ;; Устройство не найдено - используем цену по умолчанию 1 TON за минуту
            price_per_minute = 1000000000; ;; 1 TON в nanoTON
        }
        
        ;; Рассчитываем необходимую сумму
        int required_payment = price_per_minute * duration_minutes;
        
        if (msg_value < required_payment) {
            throw(error::insufficient_payment);
        }
        
        ;; Рассчитываем комиссии
        int platform_fee = (required_payment * platform_fee_percent) / 10000;
        int streamer_earnings = required_payment - platform_fee;
        
        ;; Обновляем баланс стримера
        int streamer_key = slice_hash(streamer_hash);
        (cell new_streamer_balances, int current_balance, int balance_found) = streamer_balances.udict_get_ref?(256, streamer_key);
        
        if (~ balance_found) {
            current_balance = 0;
        }
        
        current_balance += streamer_earnings;
        streamer_balances~udict_set_ref(256, streamer_key, begin_cell().store_coins(current_balance).end_cell());
        
        ;; Обновляем активную сессию
        int session_key = slice_hash(device_hash);
        int current_time = now();
        int end_time = current_time + (duration_minutes * 60);
        
        cell session_data = begin_cell()
            .store_uint(end_time, 32)
            .store_slice(sender_address)
            .end_cell();
        
        active_sessions~udict_set_ref(256, session_key, session_data);
        
        ;; Обновляем общие показатели
        total_platform_fees += platform_fee;
        total_volume += required_payment;
        
        ;; Сохраняем обновленные данные
        save_data(platform_address, platform_fee_percent, total_platform_fees, 
                  total_volume, streamer_balances, device_settings, active_sessions);
        
        return ();
    }

    ;; Обработка операций с кодом
    slice cs = in_msg_full.begin_parse();
    int flags = cs~load_uint(4);
    slice sender_address = cs~load_msg_addr();
    
    int op = in_msg_body~load_uint(32);
    int query_id = in_msg_body~load_uint(64);

    (slice platform_address, int platform_fee_percent, int total_platform_fees, 
     int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();

    if (op == op::withdraw_streamer_earnings) {
        ;; Вывод средств стримера
        int streamer_key = slice_hash(sender_address);
        (cell balance_cell, int current_balance, int found) = streamer_balances.udict_get_ref?(256, streamer_key);
        
        if (~ found) {
            throw(error::insufficient_balance);
        }
        
        if (current_balance <= 0) {
            throw(error::insufficient_balance);
        }
        
        ;; Обнуляем баланс стримера
        streamer_balances~udict_set_ref(256, streamer_key, begin_cell().store_coins(0).end_cell());
        
        ;; Сохраняем данные
        save_data(platform_address, platform_fee_percent, total_platform_fees, 
                  total_volume, streamer_balances, device_settings, active_sessions);
        
        ;; Отправляем средства стримеру
        var msg = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(sender_address)
            .store_coins(current_balance)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .end_cell();
        send_raw_message(msg, 1);
        
        return ();
    }

    if (op == op::withdraw_platform_fee) {
        ;; Вывод комиссии платформы (только для адреса платформы)
        throw_unless(error::unauthorized, equal_slices(sender_address, platform_address));
        
        if (total_platform_fees <= 0) {
            throw(error::insufficient_balance);
        }
        
        int amount_to_withdraw = total_platform_fees;
        total_platform_fees = 0;
        
        ;; Сохраняем данные
        save_data(platform_address, platform_fee_percent, total_platform_fees, 
                  total_volume, streamer_balances, device_settings, active_sessions);
        
        ;; Отправляем комиссию платформе
        var msg = begin_cell()
            .store_uint(0x18, 6)
            .store_slice(platform_address)
            .store_coins(amount_to_withdraw)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .end_cell();
        send_raw_message(msg, 1);
        
        return ();
    }

    if (op == op::update_device_settings) {
        ;; Обновление настроек устройства (только платформа)
        throw_unless(error::unauthorized, equal_slices(sender_address, platform_address));
        
        slice device_memo = in_msg_body~load_ref().begin_parse();
        int new_price = in_msg_body~load_coins();
        
        int device_key = slice_hash(device_memo);
        device_settings~udict_set_ref(256, device_key, begin_cell().store_coins(new_price).end_cell());
        
        ;; Сохраняем данные
        save_data(platform_address, platform_fee_percent, total_platform_fees, 
                  total_volume, streamer_balances, device_settings, active_sessions);
        
        return ();
    }

    throw(0xffff); ;; Неизвестная операция
}

;; Get-методы для чтения данных контракта

(int, int, int) get_platform_stats() method_id {
    (slice platform_address, int platform_fee_percent, int total_platform_fees, 
     int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();
    
    return (platform_fee_percent, total_platform_fees, total_volume);
}

int get_streamer_balance(slice streamer_address) method_id {
    (slice platform_address, int platform_fee_percent, int total_platform_fees, 
     int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();
    
    int streamer_key = slice_hash(streamer_address);
    (cell balance_cell, int current_balance, int found) = streamer_balances.udict_get_ref?(256, streamer_key);
    
    if (~ found) {
        return 0;
    }
    
    return current_balance;
}

(int, slice) get_active_session(slice device_memo) method_id {
    (slice platform_address, int platform_fee_percent, int total_platform_fees, 
     int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();
    
    int device_key = slice_hash(device_memo);
    (cell session_cell, slice session_data, int found) = active_sessions.udict_get_ref?(256, device_key);
    
    if (~ found) {
        return (0, begin_cell().store_uint(0, 2).end_cell().begin_parse()); ;; addr_none
    }
    
    int end_time = session_data~load_uint(32);
    slice controller = session_data~load_msg_addr();
    
    return (end_time, controller);
}

int get_device_price(slice device_memo) method_id {
    (slice platform_address, int platform_fee_percent, int total_platform_fees, 
     int total_volume, cell streamer_balances, cell device_settings, cell active_sessions) = load_data();
    
    int device_key = slice_hash(device_memo);
    (cell price_cell, int price, int found) = device_settings.udict_get_ref?(256, device_key);
    
    if (~ found) {
        return 1000000000; ;; 1 TON по умолчанию
    }
    
    return price;
}
