{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroTerminalReporter.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { Terminal } from 'metro-core';\nimport path from 'path';\n\nimport { logWarning, TerminalReporter } from './TerminalReporter';\nimport {\n  BuildPhase,\n  BundleDetails,\n  BundleProgress,\n  SnippetError,\n  TerminalReportableEvent,\n} from './TerminalReporter.types';\nimport { NODE_STDLIB_MODULES } from './externals';\nimport { env } from '../../../utils/env';\nimport { learnMore } from '../../../utils/link';\nimport {\n  logLikeMetro,\n  maybeSymbolicateAndFormatReactErrorLogAsync,\n  parseErrorStringToObject,\n} from '../serverLogLikeMetro';\n\nconst debug = require('debug')('expo:metro:logger') as typeof console.log;\n\nconst MAX_PROGRESS_BAR_CHAR_WIDTH = 16;\nconst DARK_BLOCK_CHAR = '\\u2593';\nconst LIGHT_BLOCK_CHAR = '\\u2591';\n/**\n * Extends the default Metro logger and adds some additional features.\n * Also removes the giant Metro logo from the output.\n */\nexport class MetroTerminalReporter extends TerminalReporter {\n  constructor(\n    public projectRoot: string,\n    terminal: Terminal\n  ) {\n    super(terminal);\n  }\n\n  _log(event: TerminalReportableEvent): void {\n    switch (event.type) {\n      case 'unstable_server_log':\n        if (typeof event.data?.[0] === 'string') {\n          const message = event.data[0];\n          if (message.match(/JavaScript logs have moved/)) {\n            // Hide this very loud message from upstream React Native in favor of the note in the terminal UI:\n            // The \"› Press j │ open debugger\"\n\n            // logger?.info(\n            //   '\\u001B[1m\\u001B[7m💡 JavaScript logs have moved!\\u001B[22m They can now be ' +\n            //     'viewed in React Native DevTools. Tip: Type \\u001B[1mj\\u001B[22m in ' +\n            //     'the terminal to open (requires Google Chrome or Microsoft Edge).' +\n            //     '\\u001B[27m',\n            // );\n            return;\n          }\n\n          if (!env.EXPO_DEBUG) {\n            // In the context of developing an iOS app or website, the MetroInspectorProxy \"connection\" logs are very confusing.\n            // Here we'll hide them behind EXPO_DEBUG or DEBUG=expo:*. In the future we can reformat them to clearly indicate that the \"Connection\" is regarding the debugger.\n            // These logs are also confusing because they can say \"connection established\" even when the debugger is not in a usable state. Really they belong in a UI or behind some sort of debug logging.\n            if (message.match(/Connection (closed|established|failed|terminated)/i)) {\n              // Skip logging.\n              return;\n            }\n          }\n        }\n        break;\n      case 'client_log': {\n        if (this.shouldFilterClientLog(event)) {\n          return;\n        }\n        const { level } = event;\n\n        if (!level) {\n          break;\n        }\n\n        const mode = event.mode === 'NOBRIDGE' || event.mode === 'BRIDGE' ? '' : (event.mode ?? '');\n        // @ts-expect-error\n        if (level === 'warn' || level === 'error') {\n          // Quick check to see if an unsymbolicated stack is being logged.\n          const msg = event.data.join('\\n');\n          if (msg.includes('.bundle//&platform=')) {\n            const parsed = parseErrorStringToObject(msg);\n\n            if (parsed) {\n              maybeSymbolicateAndFormatReactErrorLogAsync(this.projectRoot, level, parsed)\n                .then((res) => {\n                  // Overwrite the Metro terminal logging so we can improve the warnings, symbolicate stacks, and inject extra info.\n                  logLikeMetro(this.terminal.log.bind(this.terminal), level, mode, res);\n                })\n                .catch((e) => {\n                  // Fallback on the original error message if we can't symbolicate the stack.\n                  debug('Error formatting stack', e);\n\n                  // Overwrite the Metro terminal logging so we can improve the warnings, symbolicate stacks, and inject extra info.\n                  logLikeMetro(this.terminal.log.bind(this.terminal), level, mode, ...event.data);\n                });\n\n              return;\n            }\n          }\n        }\n\n        // Overwrite the Metro terminal logging so we can improve the warnings, symbolicate stacks, and inject extra info.\n        logLikeMetro(this.terminal.log.bind(this.terminal), level, mode, ...event.data);\n        return;\n      }\n    }\n    return super._log(event);\n  }\n\n  // Used for testing\n  _getElapsedTime(startTime: bigint): bigint {\n    return process.hrtime.bigint() - startTime;\n  }\n  /**\n   * Extends the bundle progress to include the current platform that we're bundling.\n   *\n   * @returns `iOS path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)`\n   */\n  _getBundleStatusMessage(progress: BundleProgress, phase: BuildPhase): string {\n    const env = getEnvironmentForBuildDetails(progress.bundleDetails);\n    const platform = env || getPlatformTagForBuildDetails(progress.bundleDetails);\n    const inProgress = phase === 'in_progress';\n\n    let localPath: string;\n\n    if (\n      typeof progress.bundleDetails?.customTransformOptions?.dom === 'string' &&\n      progress.bundleDetails.customTransformOptions.dom.includes(path.sep)\n    ) {\n      // Because we use a generated entry file for DOM components, we need to adjust the logging path so it\n      // shows a unique path for each component.\n      // Here, we take the relative import path and remove all the starting slashes.\n      localPath = progress.bundleDetails.customTransformOptions.dom.replace(/^(\\.?\\.[\\\\/])+/, '');\n    } else {\n      const inputFile = progress.bundleDetails.entryFile;\n\n      localPath = path.isAbsolute(inputFile)\n        ? path.relative(this.projectRoot, inputFile)\n        : inputFile;\n    }\n\n    if (!inProgress) {\n      const status = phase === 'done' ? `Bundled ` : `Bundling failed `;\n      const color = phase === 'done' ? chalk.green : chalk.red;\n\n      const startTime = this._bundleTimers.get(progress.bundleDetails.buildID!);\n\n      let time: string = '';\n\n      if (startTime != null) {\n        const elapsed: bigint = this._getElapsedTime(startTime);\n        const micro = Number(elapsed) / 1000;\n        const converted = Number(elapsed) / 1e6;\n        // If the milliseconds are < 0.5 then it will display as 0, so we display in microseconds.\n        if (converted <= 0.5) {\n          const tenthFractionOfMicro = ((micro * 10) / 1000).toFixed(0);\n          // Format as microseconds to nearest tenth\n          time = chalk.cyan.bold(`0.${tenthFractionOfMicro}ms`);\n        } else {\n          time = chalk.dim(converted.toFixed(0) + 'ms');\n        }\n      }\n\n      // iOS Bundled 150ms\n      const plural = progress.totalFileCount === 1 ? '' : 's';\n      return (\n        color(platform + status) +\n        time +\n        chalk.reset.dim(` ${localPath} (${progress.totalFileCount} module${plural})`)\n      );\n    }\n\n    const filledBar = Math.floor(progress.ratio * MAX_PROGRESS_BAR_CHAR_WIDTH);\n\n    const _progress = inProgress\n      ? chalk.green.bgGreen(DARK_BLOCK_CHAR.repeat(filledBar)) +\n        chalk.bgWhite.white(LIGHT_BLOCK_CHAR.repeat(MAX_PROGRESS_BAR_CHAR_WIDTH - filledBar)) +\n        chalk.bold(` ${(100 * progress.ratio).toFixed(1).padStart(4)}% `) +\n        chalk.dim(\n          `(${progress.transformedFileCount\n            .toString()\n            .padStart(progress.totalFileCount.toString().length)}/${progress.totalFileCount})`\n        )\n      : '';\n\n    return (\n      platform +\n      chalk.reset.dim(`${path.dirname(localPath)}${path.sep}`) +\n      chalk.bold(path.basename(localPath)) +\n      ' ' +\n      _progress\n    );\n  }\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void {\n    // Don't print a giant logo...\n    this.terminal.log(chalk.dim('Starting Metro Bundler'));\n  }\n\n  shouldFilterClientLog(event: { type: 'client_log'; data: unknown[] }): boolean {\n    return isAppRegistryStartupMessage(event.data);\n  }\n\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return 'bundleDetails' in event && event.bundleDetails?.bundleType === 'map';\n  }\n\n  /** Print the cache clear message. */\n  transformCacheReset(): void {\n    logWarning(\n      this.terminal,\n      chalk`Bundler cache is empty, rebuilding {dim (this may take a minute)}`\n    );\n  }\n\n  /** One of the first logs that will be printed */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {\n    // this.terminal.log('Dependency graph is loading...');\n    if (hasReducedPerformance) {\n      // Extends https://github.com/facebook/metro/blob/347b1d7ed87995d7951aaa9fd597c04b06013dac/packages/metro/src/lib/TerminalReporter.js#L283-L290\n      this.terminal.log(\n        chalk.red(\n          [\n            'Metro is operating with reduced performance.',\n            'Please fix the problem above and restart Metro.',\n          ].join('\\n')\n        )\n      );\n    }\n  }\n\n  _logBundlingError(error: SnippetError): void {\n    const moduleResolutionError = formatUsingNodeStandardLibraryError(this.projectRoot, error);\n    const cause = error.cause as undefined | { _expoImportStack?: string };\n    if (moduleResolutionError) {\n      let message = maybeAppendCodeFrame(moduleResolutionError, error.message);\n      if (cause?._expoImportStack) {\n        message += `\\n\\n${cause?._expoImportStack}`;\n      }\n      return this.terminal.log(message);\n    }\n    if (cause?._expoImportStack) {\n      error.message += `\\n\\n${cause._expoImportStack}`;\n    }\n    return super._logBundlingError(error);\n  }\n}\n\n/**\n * Formats an error where the user is attempting to import a module from the Node.js standard library.\n * Exposed for testing.\n *\n * @param error\n * @returns error message or null if not a module resolution error\n */\nexport function formatUsingNodeStandardLibraryError(\n  projectRoot: string,\n  error: SnippetError\n): string | null {\n  if (!error.message) {\n    return null;\n  }\n  const { targetModuleName, originModulePath } = error;\n  if (!targetModuleName || !originModulePath) {\n    return null;\n  }\n  const relativePath = path.relative(projectRoot, originModulePath);\n\n  const DOCS_PAGE_URL =\n    'https://docs.expo.dev/workflow/using-libraries/#using-third-party-libraries';\n\n  if (isNodeStdLibraryModule(targetModuleName)) {\n    if (originModulePath.includes('node_modules')) {\n      return [\n        `The package at \"${chalk.bold(\n          relativePath\n        )}\" attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    } else {\n      return [\n        `You attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\" from \"${chalk.bold(relativePath)}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    }\n  }\n  return `Unable to resolve \"${targetModuleName}\" from \"${relativePath}\"`;\n}\n\nexport function isNodeStdLibraryModule(moduleName: string): boolean {\n  return /^node:/.test(moduleName) || NODE_STDLIB_MODULES.includes(moduleName);\n}\n\n/** If the code frame can be found then append it to the existing message.  */\nfunction maybeAppendCodeFrame(message: string, rawMessage: string): string {\n  const codeFrame = stripMetroInfo(rawMessage);\n  if (codeFrame) {\n    message += '\\n' + codeFrame;\n  }\n  return message;\n}\n\n/**\n * Remove the Metro cache clearing steps if they exist.\n * In future versions we won't need this.\n * Returns the remaining code frame logs.\n */\nexport function stripMetroInfo(errorMessage: string): string | null {\n  // Newer versions of Metro don't include the list.\n  if (!errorMessage.includes('4. Remove the cache')) {\n    return null;\n  }\n  const lines = errorMessage.split('\\n');\n  const index = lines.findIndex((line) => line.includes('4. Remove the cache'));\n  if (index === -1) {\n    return null;\n  }\n  return lines.slice(index + 1).join('\\n');\n}\n\n/** @returns if the message matches the initial startup log */\nfunction isAppRegistryStartupMessage(body: any[]): boolean {\n  return (\n    body.length === 1 &&\n    (/^Running application \"main\" with appParams:/.test(body[0]) ||\n      /^Running \"main\" with \\{/.test(body[0]))\n  );\n}\n\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getPlatformTagForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  const platform = bundleDetails?.platform ?? null;\n  if (platform) {\n    const formatted = { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform;\n    return `${chalk.bold(formatted)} `;\n  }\n\n  return '';\n}\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getEnvironmentForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  // Expo CLI will pass `customTransformOptions.environment = 'node'` when bundling for the server.\n  const env = bundleDetails?.customTransformOptions?.environment ?? null;\n  if (env === 'node') {\n    return chalk.bold('λ') + ' ';\n  } else if (env === 'react-server') {\n    return chalk.bold(`RSC(${getPlatformTagForBuildDetails(bundleDetails).trim()})`) + ' ';\n  }\n\n  if (\n    bundleDetails?.customTransformOptions?.dom &&\n    typeof bundleDetails?.customTransformOptions?.dom === 'string'\n  ) {\n    return chalk.bold(`DOM`) + ' ';\n  }\n\n  return '';\n}\n"], "names": ["MetroTerminalReporter", "formatUsingNodeStandardLibraryError", "isNodeStdLibraryModule", "stripMetroInfo", "debug", "require", "MAX_PROGRESS_BAR_CHAR_WIDTH", "DARK_BLOCK_CHAR", "LIGHT_BLOCK_CHAR", "TerminalReporter", "constructor", "projectRoot", "terminal", "_log", "event", "type", "data", "message", "match", "env", "EXPO_DEBUG", "shouldFilterClientLog", "level", "mode", "msg", "join", "includes", "parsed", "parseErrorStringToObject", "maybeSymbolicateAndFormatReactErrorLogAsync", "then", "res", "logLikeMetro", "log", "bind", "catch", "e", "_getElapsedTime", "startTime", "process", "hrtime", "bigint", "_getBundleStatusMessage", "progress", "phase", "getEnvironmentForBuildDetails", "bundleDetails", "platform", "getPlatformTagForBuildDetails", "inProgress", "localPath", "customTransformOptions", "dom", "path", "sep", "replace", "inputFile", "entryFile", "isAbsolute", "relative", "status", "color", "chalk", "green", "red", "_bundleTimers", "get", "buildID", "time", "elapsed", "micro", "Number", "converted", "tenthFractionOfMicro", "toFixed", "cyan", "bold", "dim", "plural", "totalFileCount", "reset", "<PERSON><PERSON><PERSON>", "Math", "floor", "ratio", "_progress", "bgGreen", "repeat", "bgWhite", "white", "padStart", "transformedFileCount", "toString", "length", "dirname", "basename", "_logInitializing", "port", "hasReducedPerformance", "isAppRegistryStartupMessage", "shouldFilterBundleEvent", "bundleType", "transformCacheReset", "logWarning", "dependencyGraphLoading", "_logBundlingError", "error", "moduleResolutionError", "cause", "maybeAppendCodeFrame", "_expoImportStack", "targetModuleName", "originModulePath", "relativePath", "DOCS_PAGE_URL", "learnMore", "moduleName", "test", "NODE_STDLIB_MODULES", "rawMessage", "codeFrame", "errorMessage", "lines", "split", "index", "findIndex", "line", "slice", "body", "formatted", "ios", "android", "web", "environment", "trim"], "mappings": ";;;;;;;;;;;IA8BaA,qBAAqB;eAArBA;;IAoOGC,mCAAmC;eAAnCA;;IAwCAC,sBAAsB;eAAtBA;;IAkBAC,cAAc;eAAdA;;;;gEA5TE;;;;;;;gEAED;;;;;;kCAE4B;2BAQT;qBAChB;sBACM;oCAKnB;;;;;;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,8BAA8B;AACpC,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AAKlB,MAAMR,8BAA8BS,kCAAgB;IACzDC,YACE,AAAOC,WAAmB,EAC1BC,QAAkB,CAClB;QACA,KAAK,CAACA,gBAHCD,cAAAA;IAIT;IAEAE,KAAKC,KAA8B,EAAQ;QACzC,OAAQA,MAAMC,IAAI;YAChB,KAAK;oBACQD;gBAAX,IAAI,SAAOA,cAAAA,MAAME,IAAI,qBAAVF,WAAY,CAAC,EAAE,MAAK,UAAU;oBACvC,MAAMG,UAAUH,MAAME,IAAI,CAAC,EAAE;oBAC7B,IAAIC,QAAQC,KAAK,CAAC,+BAA+B;wBAC/C,kGAAkG;wBAClG,kCAAkC;wBAElC,gBAAgB;wBAChB,oFAAoF;wBACpF,8EAA8E;wBAC9E,2EAA2E;wBAC3E,oBAAoB;wBACpB,KAAK;wBACL;oBACF;oBAEA,IAAI,CAACC,QAAG,CAACC,UAAU,EAAE;wBACnB,oHAAoH;wBACpH,kKAAkK;wBAClK,gMAAgM;wBAChM,IAAIH,QAAQC,KAAK,CAAC,uDAAuD;4BACvE,gBAAgB;4BAChB;wBACF;oBACF;gBACF;gBACA;YACF,KAAK;gBAAc;oBACjB,IAAI,IAAI,CAACG,qBAAqB,CAACP,QAAQ;wBACrC;oBACF;oBACA,MAAM,EAAEQ,KAAK,EAAE,GAAGR;oBAElB,IAAI,CAACQ,OAAO;wBACV;oBACF;oBAEA,MAAMC,OAAOT,MAAMS,IAAI,KAAK,cAAcT,MAAMS,IAAI,KAAK,WAAW,KAAMT,MAAMS,IAAI,IAAI;oBACxF,mBAAmB;oBACnB,IAAID,UAAU,UAAUA,UAAU,SAAS;wBACzC,iEAAiE;wBACjE,MAAME,MAAMV,MAAME,IAAI,CAACS,IAAI,CAAC;wBAC5B,IAAID,IAAIE,QAAQ,CAAC,wBAAwB;4BACvC,MAAMC,SAASC,IAAAA,4CAAwB,EAACJ;4BAExC,IAAIG,QAAQ;gCACVE,IAAAA,+DAA2C,EAAC,IAAI,CAAClB,WAAW,EAAEW,OAAOK,QAClEG,IAAI,CAAC,CAACC;oCACL,kHAAkH;oCAClHC,IAAAA,gCAAY,EAAC,IAAI,CAACpB,QAAQ,CAACqB,GAAG,CAACC,IAAI,CAAC,IAAI,CAACtB,QAAQ,GAAGU,OAAOC,MAAMQ;gCACnE,GACCI,KAAK,CAAC,CAACC;oCACN,4EAA4E;oCAC5EhC,MAAM,0BAA0BgC;oCAEhC,kHAAkH;oCAClHJ,IAAAA,gCAAY,EAAC,IAAI,CAACpB,QAAQ,CAACqB,GAAG,CAACC,IAAI,CAAC,IAAI,CAACtB,QAAQ,GAAGU,OAAOC,SAAST,MAAME,IAAI;gCAChF;gCAEF;4BACF;wBACF;oBACF;oBAEA,kHAAkH;oBAClHgB,IAAAA,gCAAY,EAAC,IAAI,CAACpB,QAAQ,CAACqB,GAAG,CAACC,IAAI,CAAC,IAAI,CAACtB,QAAQ,GAAGU,OAAOC,SAAST,MAAME,IAAI;oBAC9E;gBACF;QACF;QACA,OAAO,KAAK,CAACH,KAAKC;IACpB;IAEA,mBAAmB;IACnBuB,gBAAgBC,SAAiB,EAAU;QACzC,OAAOC,QAAQC,MAAM,CAACC,MAAM,KAAKH;IACnC;IACA;;;;GAIC,GACDI,wBAAwBC,QAAwB,EAAEC,KAAiB,EAAU;YAQlED,gDAAAA;QAPT,MAAMxB,MAAM0B,8BAA8BF,SAASG,aAAa;QAChE,MAAMC,WAAW5B,OAAO6B,8BAA8BL,SAASG,aAAa;QAC5E,MAAMG,aAAaL,UAAU;QAE7B,IAAIM;QAEJ,IACE,SAAOP,0BAAAA,SAASG,aAAa,sBAAtBH,iDAAAA,wBAAwBQ,sBAAsB,qBAA9CR,+CAAgDS,GAAG,MAAK,YAC/DT,SAASG,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAAC1B,QAAQ,CAAC2B,eAAI,CAACC,GAAG,GACnE;YACA,qGAAqG;YACrG,0CAA0C;YAC1C,8EAA8E;YAC9EJ,YAAYP,SAASG,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAACG,OAAO,CAAC,kBAAkB;QAC1F,OAAO;YACL,MAAMC,YAAYb,SAASG,aAAa,CAACW,SAAS;YAElDP,YAAYG,eAAI,CAACK,UAAU,CAACF,aACxBH,eAAI,CAACM,QAAQ,CAAC,IAAI,CAAChD,WAAW,EAAE6C,aAChCA;QACN;QAEA,IAAI,CAACP,YAAY;YACf,MAAMW,SAAShB,UAAU,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACjE,MAAMiB,QAAQjB,UAAU,SAASkB,gBAAK,CAACC,KAAK,GAAGD,gBAAK,CAACE,GAAG;YAExD,MAAM1B,YAAY,IAAI,CAAC2B,aAAa,CAACC,GAAG,CAACvB,SAASG,aAAa,CAACqB,OAAO;YAEvE,IAAIC,OAAe;YAEnB,IAAI9B,aAAa,MAAM;gBACrB,MAAM+B,UAAkB,IAAI,CAAChC,eAAe,CAACC;gBAC7C,MAAMgC,QAAQC,OAAOF,WAAW;gBAChC,MAAMG,YAAYD,OAAOF,WAAW;gBACpC,0FAA0F;gBAC1F,IAAIG,aAAa,KAAK;oBACpB,MAAMC,uBAAuB,AAAC,CAAA,AAACH,QAAQ,KAAM,IAAG,EAAGI,OAAO,CAAC;oBAC3D,0CAA0C;oBAC1CN,OAAON,gBAAK,CAACa,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,EAAEH,qBAAqB,EAAE,CAAC;gBACtD,OAAO;oBACLL,OAAON,gBAAK,CAACe,GAAG,CAACL,UAAUE,OAAO,CAAC,KAAK;gBAC1C;YACF;YAEA,oBAAoB;YACpB,MAAMI,SAASnC,SAASoC,cAAc,KAAK,IAAI,KAAK;YACpD,OACElB,MAAMd,WAAWa,UACjBQ,OACAN,gBAAK,CAACkB,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,EAAE3B,UAAU,EAAE,EAAEP,SAASoC,cAAc,CAAC,OAAO,EAAED,OAAO,CAAC,CAAC;QAEhF;QAEA,MAAMG,YAAYC,KAAKC,KAAK,CAACxC,SAASyC,KAAK,GAAG9E;QAE9C,MAAM+E,YAAYpC,aACda,gBAAK,CAACC,KAAK,CAACuB,OAAO,CAAC/E,gBAAgBgF,MAAM,CAACN,cAC3CnB,gBAAK,CAAC0B,OAAO,CAACC,KAAK,CAACjF,iBAAiB+E,MAAM,CAACjF,8BAA8B2E,cAC1EnB,gBAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE,AAAC,CAAA,MAAMjC,SAASyC,KAAK,AAAD,EAAGV,OAAO,CAAC,GAAGgB,QAAQ,CAAC,GAAG,EAAE,CAAC,IAChE5B,gBAAK,CAACe,GAAG,CACP,CAAC,CAAC,EAAElC,SAASgD,oBAAoB,CAC9BC,QAAQ,GACRF,QAAQ,CAAC/C,SAASoC,cAAc,CAACa,QAAQ,GAAGC,MAAM,EAAE,CAAC,EAAElD,SAASoC,cAAc,CAAC,CAAC,CAAC,IAEtF;QAEJ,OACEhC,WACAe,gBAAK,CAACkB,KAAK,CAACH,GAAG,CAAC,GAAGxB,eAAI,CAACyC,OAAO,CAAC5C,aAAaG,eAAI,CAACC,GAAG,EAAE,IACvDQ,gBAAK,CAACc,IAAI,CAACvB,eAAI,CAAC0C,QAAQ,CAAC7C,cACzB,MACAmC;IAEJ;IAEAW,iBAAiBC,IAAY,EAAEC,qBAA8B,EAAQ;QACnE,8BAA8B;QAC9B,IAAI,CAACtF,QAAQ,CAACqB,GAAG,CAAC6B,gBAAK,CAACe,GAAG,CAAC;IAC9B;IAEAxD,sBAAsBP,KAA8C,EAAW;QAC7E,OAAOqF,4BAA4BrF,MAAME,IAAI;IAC/C;IAEAoF,wBAAwBtF,KAA8B,EAAW;YAC5BA;QAAnC,OAAO,mBAAmBA,SAASA,EAAAA,uBAAAA,MAAMgC,aAAa,qBAAnBhC,qBAAqBuF,UAAU,MAAK;IACzE;IAEA,mCAAmC,GACnCC,sBAA4B;QAC1BC,IAAAA,4BAAU,EACR,IAAI,CAAC3F,QAAQ,EACbkD,IAAAA,gBAAK,CAAA,CAAC,iEAAiE,CAAC;IAE5E;IAEA,+CAA+C,GAC/C0C,uBAAuBN,qBAA8B,EAAQ;QAC3D,uDAAuD;QACvD,IAAIA,uBAAuB;YACzB,+IAA+I;YAC/I,IAAI,CAACtF,QAAQ,CAACqB,GAAG,CACf6B,gBAAK,CAACE,GAAG,CACP;gBACE;gBACA;aACD,CAACvC,IAAI,CAAC;QAGb;IACF;IAEAgF,kBAAkBC,KAAmB,EAAQ;QAC3C,MAAMC,wBAAwB1G,oCAAoC,IAAI,CAACU,WAAW,EAAE+F;QACpF,MAAME,QAAQF,MAAME,KAAK;QACzB,IAAID,uBAAuB;YACzB,IAAI1F,UAAU4F,qBAAqBF,uBAAuBD,MAAMzF,OAAO;YACvE,IAAI2F,yBAAAA,MAAOE,gBAAgB,EAAE;gBAC3B7F,WAAW,CAAC,IAAI,EAAE2F,yBAAAA,MAAOE,gBAAgB,EAAE;YAC7C;YACA,OAAO,IAAI,CAAClG,QAAQ,CAACqB,GAAG,CAAChB;QAC3B;QACA,IAAI2F,yBAAAA,MAAOE,gBAAgB,EAAE;YAC3BJ,MAAMzF,OAAO,IAAI,CAAC,IAAI,EAAE2F,MAAME,gBAAgB,EAAE;QAClD;QACA,OAAO,KAAK,CAACL,kBAAkBC;IACjC;AACF;AASO,SAASzG,oCACdU,WAAmB,EACnB+F,KAAmB;IAEnB,IAAI,CAACA,MAAMzF,OAAO,EAAE;QAClB,OAAO;IACT;IACA,MAAM,EAAE8F,gBAAgB,EAAEC,gBAAgB,EAAE,GAAGN;IAC/C,IAAI,CAACK,oBAAoB,CAACC,kBAAkB;QAC1C,OAAO;IACT;IACA,MAAMC,eAAe5D,eAAI,CAACM,QAAQ,CAAChD,aAAaqG;IAEhD,MAAME,gBACJ;IAEF,IAAIhH,uBAAuB6G,mBAAmB;QAC5C,IAAIC,iBAAiBtF,QAAQ,CAAC,iBAAiB;YAC7C,OAAO;gBACL,CAAC,gBAAgB,EAAEoC,gBAAK,CAACc,IAAI,CAC3BqC,cACA,wDAAwD,EAAEnD,gBAAK,CAACc,IAAI,CACpEmC,kBACA,EAAE,CAAC;gBACL,CAAC,sFAAsF,CAAC;gBACxFI,IAAAA,eAAS,EAACD;aACX,CAACzF,IAAI,CAAC;QACT,OAAO;YACL,OAAO;gBACL,CAAC,0DAA0D,EAAEqC,gBAAK,CAACc,IAAI,CACrEmC,kBACA,QAAQ,EAAEjD,gBAAK,CAACc,IAAI,CAACqC,cAAc,EAAE,CAAC;gBACxC,CAAC,sFAAsF,CAAC;gBACxFE,IAAAA,eAAS,EAACD;aACX,CAACzF,IAAI,CAAC;QACT;IACF;IACA,OAAO,CAAC,mBAAmB,EAAEsF,iBAAiB,QAAQ,EAAEE,aAAa,CAAC,CAAC;AACzE;AAEO,SAAS/G,uBAAuBkH,UAAkB;IACvD,OAAO,SAASC,IAAI,CAACD,eAAeE,8BAAmB,CAAC5F,QAAQ,CAAC0F;AACnE;AAEA,4EAA4E,GAC5E,SAASP,qBAAqB5F,OAAe,EAAEsG,UAAkB;IAC/D,MAAMC,YAAYrH,eAAeoH;IACjC,IAAIC,WAAW;QACbvG,WAAW,OAAOuG;IACpB;IACA,OAAOvG;AACT;AAOO,SAASd,eAAesH,YAAoB;IACjD,kDAAkD;IAClD,IAAI,CAACA,aAAa/F,QAAQ,CAAC,wBAAwB;QACjD,OAAO;IACT;IACA,MAAMgG,QAAQD,aAAaE,KAAK,CAAC;IACjC,MAAMC,QAAQF,MAAMG,SAAS,CAAC,CAACC,OAASA,KAAKpG,QAAQ,CAAC;IACtD,IAAIkG,UAAU,CAAC,GAAG;QAChB,OAAO;IACT;IACA,OAAOF,MAAMK,KAAK,CAACH,QAAQ,GAAGnG,IAAI,CAAC;AACrC;AAEA,4DAA4D,GAC5D,SAAS0E,4BAA4B6B,IAAW;IAC9C,OACEA,KAAKnC,MAAM,KAAK,KACf,CAAA,8CAA8CwB,IAAI,CAACW,IAAI,CAAC,EAAE,KACzD,0BAA0BX,IAAI,CAACW,IAAI,CAAC,EAAE,CAAA;AAE5C;AAEA,gEAAgE,GAChE,SAAShF,8BAA8BF,aAAoC;IACzE,MAAMC,WAAWD,CAAAA,iCAAAA,cAAeC,QAAQ,KAAI;IAC5C,IAAIA,UAAU;QACZ,MAAMkF,YAAY;YAAEC,KAAK;YAAOC,SAAS;YAAWC,KAAK;QAAM,CAAC,CAACrF,SAAS,IAAIA;QAC9E,OAAO,GAAGe,gBAAK,CAACc,IAAI,CAACqD,WAAW,CAAC,CAAC;IACpC;IAEA,OAAO;AACT;AACA,gEAAgE,GAChE,SAASpF,8BAA8BC,aAAoC;QAE7DA,uCAQVA,wCACOA;IAVT,iGAAiG;IACjG,MAAM3B,MAAM2B,CAAAA,kCAAAA,wCAAAA,cAAeK,sBAAsB,qBAArCL,sCAAuCuF,WAAW,KAAI;IAClE,IAAIlH,QAAQ,QAAQ;QAClB,OAAO2C,gBAAK,CAACc,IAAI,CAAC,OAAO;IAC3B,OAAO,IAAIzD,QAAQ,gBAAgB;QACjC,OAAO2C,gBAAK,CAACc,IAAI,CAAC,CAAC,IAAI,EAAE5B,8BAA8BF,eAAewF,IAAI,GAAG,CAAC,CAAC,IAAI;IACrF;IAEA,IACExF,CAAAA,kCAAAA,yCAAAA,cAAeK,sBAAsB,qBAArCL,uCAAuCM,GAAG,KAC1C,QAAON,kCAAAA,yCAAAA,cAAeK,sBAAsB,qBAArCL,uCAAuCM,GAAG,MAAK,UACtD;QACA,OAAOU,gBAAK,CAACc,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI;IAC7B;IAEA,OAAO;AACT"}