<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Настройки стримера</title>
    <!-- Подключаем TON Connect UI -->
    <script src="https://unpkg.com/@tonconnect/ui@2.0.11/dist/tonconnect-ui.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <style>
        /* TON Connect стили - как на странице streamer.html */
        #ton-connect {
            position: fixed;
            top: 15px;
            right: 15px;
            z-index: 1000;
        }
        /* Базовые переменные */
        :root {
            --primary: #2196f3;
            --primary-dark: #1976d2;
            --success: #4caf50;
            --error: #f44336;
            --warning: #ff9800;
            --info: #00bcd4;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --radius: 12px;
            --transition: all 0.3s ease;
        }
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f6f8fd 0%, #dfe6f6 100%);
            min-height: 100vh;
            color: #2c3e50;
        }
        .container {
            max-width: 1100px;
            margin: 40px auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 2.5em;
            color: var(--primary);
            margin-bottom: 40px;
            text-align: center;
            font-weight: 600;
        }
        .section {
            margin-bottom: 40px;
            padding: 30px;
            padding-bottom: 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: var(--radius);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .section h2 {
            margin: 0 0 25px 0;
            font-size: 1.6em;
            color: var(--primary-dark);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section h2::before {
            content: "";
            display: block;
            width: 4px;
            height: 24px;
            background: var(--primary);
            border-radius: 2px;
        }
        input[type="text"],
        input[type="url"] {
            width: calc(100% - 34px);
            padding: 15px;
            margin-bottom: 15px;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius);
            font-size: 16px;
            transition: var(--transition);
            background: white;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            box-sizing: border-box;
        }
        input[type="text"]:focus,
        input[type="url"]:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.25);
            outline: none;
        }
        input[type="text"]:hover,
        input[type="url"]:hover {
            background: white;
            border-color: var(--primary-dark);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        input:disabled {
            background-color: var(--gray-100) !important;
            border-color: var(--gray-200) !important;
            cursor: not-allowed;
            opacity: 0.7;
        }
        /* Стили для кнопок (кроме TON Connect) */
        .section button {
            background: linear-gradient(45deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--radius);
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }
        .section button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: translateX(-100%);
            transition: var(--transition);
        }
        .section button:hover::after {
            transform: translateX(100%);
        }
        .section button:disabled {
            background: var(--gray-300) !important;
            cursor: not-allowed;
            opacity: 0.7;
        }
        .section button:disabled::after {
            display: none;
        }
        /* Таблица устройств */
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }
        th, td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }
        th {
            background: var(--gray-100);
            font-weight: 600;
            color: var(--gray-600);
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:hover td {
            background: rgba(255, 255, 255, 0.95);
        }
        /* Статус устройства */
        .status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .online {
            background: rgba(76, 175, 80, 0.1);
            color: #2e7d32;
        }
        .offline {
            background: rgba(244, 67, 54, 0.1);
            color: #c62828;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        .online .status-dot {
            background: var(--success);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
        }
        .offline .status-dot {
            background: var(--error);
            box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
        }
        /* Кнопки действий */
        .delete-btn {
            background: var(--error);
            padding: 8px;
            border-radius: 8px;
            min-width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .delete-btn:hover {
            background: #d32f2f;
        }
        /* Сообщения */
        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: var(--radius);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .success {
            background: rgba(76, 175, 80, 0.1);
            color: #2e7d32;
            border: 1px solid rgba(76, 175, 80, 0.2);
        }
        .error {
            background: rgba(244, 67, 54, 0.1);
            color: #c62828;
            border: 1px solid rgba(244, 67, 54, 0.2);
        }
        /* Адаптивность */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 20px;
            }
            .section {
                padding: 20px;
            }
            table {
                display: block;
                overflow-x: auto;
            }
            th, td {
                padding: 12px;
            }
        }
        /* Стили для Toastify */
        .toast-success {
            background: linear-gradient(45deg, var(--success), #45a049) !important;
        }
        .toast-error {
            background: linear-gradient(45deg, var(--error), #d32f2f) !important;
        }
        .toast-info {
            background: linear-gradient(45deg, var(--info), #00acc1) !important;
        }
        .toast-warning {
            background: linear-gradient(45deg, var(--warning), #f57c00) !important;
        }
        #streamer-link {
            text-decoration: none;
        }
        #go-to-stream {
            width: 100%;
            justify-content: center;
            font-size: 18px;
            padding: 15px 30px;
        }
        #go-to-stream:not(:disabled) {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
            100% {
                transform: scale(1);
            }
        }
        /* Добавляем стили для неактивных элементов */
        input:disabled,
        button:disabled {
            background-color: var(--gray-100) !important;
            border-color: var(--gray-200) !important;
            cursor: not-allowed !important;
            opacity: 0.7 !important;
        }
        /* Стиль для сообщения об авторизации */
        .auth-message {
            background: rgba(255, 152, 0, 0.1);
            padding: 10px;
            border-radius: var(--radius);
            border: 1px solid rgba(255, 152, 0, 0.2);
            margin-top: 10px;
        }
        /* Контейнер для полей ввода */
        .input-container {
            padding: 0 2px;
            margin-bottom: 20px;
        }

        /* Стили для режима управления */
        .control-mode-selector {
            margin-bottom: 20px;
        }

        .radio-group {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .radio-group:hover {
            background-color: #f5f5f5;
        }

        .radio-group input[type="radio"] {
            margin-right: 10px;
        }

        .radio-group label {
            font-weight: bold;
            font-size: 16px;
            display: inline-block;
            margin-bottom: 5px;
        }

        .mode-description {
            margin: 5px 0 0 25px;
            color: #666;
            font-size: 14px;
        }

        #fixed-time-settings {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        #fixed-time-settings h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }

        #fixed-time-settings .input-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        #fixed-time-settings label {
            width: 200px;
            font-weight: normal;
        }

        #fixed-time-settings input {
            width: 100px;
        }

        .settings-info {
            background-color: #e8f4ff;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 14px;
            color: #0066cc;
            border-left: 4px solid #0066cc;
        }

        /* Стили для отображения заработка */
        .earnings-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: var(--radius);
            text-align: center;
            margin: 15px 0;
            box-shadow: var(--shadow);
        }

        .earnings-amount {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .earnings-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .earnings-details {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .earnings-stat {
            text-align: center;
        }

        .earnings-stat-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .earnings-stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: var(--gray-600);
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Кнопка TON Connect -->
    <div id="ton-connect"></div>

    <div class="container">
        <h1>Настройки стримера</h1>

        <!-- 1. Выбор ника – обязательное поле -->
        <div class="section">
            <h2>1. Выбор ника</h2>
            <div class="input-container">
                <input type="text" id="streamer-nickname" placeholder="Введите ваш ник" />
            </div>
            <button onclick="updateNickname()">Сохранить ник</button>
            <p id="nickname-status" style="color: red; font-weight: bold;"></p>
        </div>

        <!-- 2. Привязка устройства (только если ник указан) -->
        <div class="section" id="device-binding-section">
            <h2>2. Привязка устройства</h2>
            <div class="input-container">
                <input type="text" id="device-mac" placeholder="MAC-адрес устройства" />
                <input type="text" id="device-name" placeholder="Имя устройства (опционально)" />
            </div>
            <button onclick="bindDevice()">Привязать устройство</button>
            <div id="bind-message" class="message"></div>
        </div>

        <!-- 3. Привязка стрима -->
        <div class="section">
            <h2>3. Привязка стрима</h2>
            <div class="input-container">
                <input type="url" id="stream-url" placeholder="https://www.youtube.com/live/..." />
            </div>
            <button onclick="saveStream()">Сохранить</button>
            <div id="stream-message" class="message"></div>
        </div>

        <!-- 4. Настройки режима управления устройствами -->
        <div class="section">
            <h2>4. Режим управления устройствами</h2>
            <div class="control-mode-selector">
                <div class="radio-group">
                    <input type="radio" id="donation-battle" name="control-mode" value="donation-battle" checked>
                    <label for="donation-battle">Битва донатов</label>
                    <p class="mode-description">Кто больше задонатит, тот управляет устройством</p>
                </div>
                <div class="radio-group">
                    <input type="radio" id="fixed-time" name="control-mode" value="fixed-time">
                    <label for="fixed-time">Покупка фиксированного времени</label>
                    <p class="mode-description">Пользователь покупает фиксированное время управления устройством</p>
                </div>
            </div>

            <div id="fixed-time-settings" style="display: none;">
                <h3>Настройки фиксированного времени</h3>
                <div class="input-container">
                    <label for="control-time">Время управления (минуты):</label>
                    <input type="number" id="control-time" min="1" max="60" value="5" />
                </div>
                <div class="input-container">
                    <label for="control-price">Цена (TON):</label>
                    <input type="number" id="control-price" min="0.1" step="0.1" value="1.0" />
                </div>
                <p class="settings-info">Пользователь сможет купить указанное время управления за указанную цену.</p>
            </div>

            <button onclick="saveControlMode()">Сохранить настройки режима</button>
            <div id="control-mode-message" class="message"></div>
        </div>

        <!-- 5. Вывод заработанных средств -->
        <div class="section">
            <h2>5. 💰 Вывод заработанных средств</h2>
            <div id="earnings-info">
                <div class="loading">Загрузка информации о заработке...</div>
            </div>
            <button id="withdraw-earnings" onclick="withdrawEarnings()" disabled>
                💸 Вывести средства
            </button>
            <div id="withdraw-message" class="message"></div>
        </div>

        <!-- 6. Финиш -->
        <div class="section">
            <h2>6. Финиш</h2>
            <button id="go-to-stream" disabled onclick="goToStreamerPage()">Перейти к стриму</button>
        </div>

        <!-- 7. Список устройств -->
        <div class="section">
            <h2>7. Ваши устройства</h2>
            <table id="devices-table">
                <thead>
                    <tr>
                        <th>MAC-адрес</th>
                        <th>Имя</th>
                        <th>Статус</th>
                        <th>Последнее подключение</th>
                        <th>Отключение</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody id="devices-list">
                    <!-- Устройства будут добавлены через JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let walletAddress = null;
        let tonConnectUI;
        // Кэшируем часто используемые элементы
        const elements = {
            nickname: document.getElementById("streamer-nickname"),
            streamUrl: document.getElementById("stream-url"),
            devicesList: document.getElementById("devices-list"),
            deviceMac: document.getElementById("device-mac"),
            deviceName: document.getElementById("device-name"),
            controlTime: document.getElementById("control-time"),
            controlPrice: document.getElementById("control-price"),
            donationBattle: document.getElementById("donation-battle"),
            fixedTime: document.getElementById("fixed-time"),
            fixedTimeSettings: document.getElementById("fixed-time-settings")
        };

        // Добавляем обработчики для радиокнопок режима управления
        document.getElementById("donation-battle").addEventListener('change', function() {
            if (this.checked) {
                document.getElementById("fixed-time-settings").style.display = 'none';
            }
        });

        document.getElementById("fixed-time").addEventListener('change', function() {
            if (this.checked) {
                document.getElementById("fixed-time-settings").style.display = 'block';
            }
        });

        document.addEventListener("DOMContentLoaded", async () => {
            // Сначала делаем все поля неактивными
            updateElementsState(false);

            // Инициализируем TON Connect - как на странице streamer.html
            tonConnectUI = new TON_CONNECT_UI.TonConnectUI({
                manifestUrl: 'http://localhost:4000/tonconnect-manifest.json'
            });

            // Устанавливаем buttonRootId с задержкой
            setTimeout(() => {
                tonConnectUI.uiOptions = { buttonRootId: 'ton-connect' };
            }, 100);

            // Добавляем обработчик изменения статуса кошелька
            tonConnectUI.onStatusChange(handleWalletStatusChange);

            // Проверяем статус авторизации после инициализации
            const wallet = await tonConnectUI?.getWalletInfo();
            if (!wallet) {
                updateElementsState(false);
            }
            initStreamerLink();
            // Следим за изменениями ника для включения/отключения привязки устройств
            elements.nickname.addEventListener("input", updateDeviceBindingState);
            // Вызываем сразу, чтобы установить корректное состояние
            updateDeviceBindingState();

            // Инициализация режима управления
            initControlModeSettings();
        });

        // Обработчик изменения статуса кошелька
        async function handleWalletStatusChange(wallet) {
            if (wallet) {
                walletAddress = wallet.account.address;
                console.log(`🔑 Авторизован с адресом: ${walletAddress}`);
                updateElementsState(true);
                await Promise.all([
                    fetchNickname(),
                    fetchStreamUrl(),
                    fetchDevices(),
                    fetchControlModeSettings(), // Загружаем настройки режима управления
                    loadEarningsInfo() // Загружаем информацию о заработке
                ]);
            } else {
                walletAddress = null;
                updateElementsState(false);
                clearFields();
            }
        }

        // Очистка полей
        function clearFields() {
            elements.nickname.value = "";
            elements.streamUrl.value = "";
            elements.devicesList.innerHTML = "";
        }

        // Обновляем состояние всех элементов в зависимости от авторизации
        function updateElementsState(isAuthorized) {
            const allInputs = document.querySelectorAll('input');
            const allButtons = document.querySelectorAll('button:not(.ton-connect)');
            const allLinks = document.querySelectorAll('a');

            [...allInputs, ...allButtons].forEach(el => {
                el.disabled = !isAuthorized;
                el.style.opacity = isAuthorized ? '1' : '0.5';
                el.style.cursor = isAuthorized ? 'pointer' : 'not-allowed';
            });

            allLinks.forEach(link => {
                link.style.pointerEvents = isAuthorized ? 'auto' : 'none';
                link.style.opacity = isAuthorized ? '1' : '0.5';
            });

            // Сообщение об авторизации
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                let authMessage = section.querySelector('.auth-message');
                if (!isAuthorized) {
                    if (!authMessage) {
                        authMessage = document.createElement('div');
                        authMessage.className = 'auth-message';
                        authMessage.style.color = 'var(--warning)';
                        authMessage.style.marginTop = '10px';
                        authMessage.style.fontSize = '14px';
                        authMessage.innerHTML = '⚠️ Для доступа необходимо подключить кошелек';
                        section.appendChild(authMessage);
                    }
                } else if (authMessage) {
                    authMessage.remove();
                }
            });
        }

        // Обновление состояния секции привязки устройств в зависимости от наличия ника
        function updateDeviceBindingState() {
            const nicknameVal = elements.nickname.value.trim();
            const deviceSection = document.getElementById("device-binding-section");
            const inputs = deviceSection.querySelectorAll("input, button");
            if (nicknameVal === "") {
                inputs.forEach(el => {
                    el.disabled = true;
                    el.style.opacity = '0.5';
                });
            } else {
                inputs.forEach(el => {
                    el.disabled = false;
                    el.style.opacity = '1';
                });
            }
        }

        // Оптимизированная функция показа уведомлений
        function showToast(message, type = 'info') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                stopOnFocus: true,
                className: `toast-${type}`,
                style: {
                    background: "linear-gradient(to right, #00b09b, #96c93d)",
                    borderRadius: "10px",
                    padding: "15px 25px",
                    fontSize: "16px",
                    fontWeight: "500"
                }
            }).showToast();
        }

        // Инициализация ссылки на стрим
        function initStreamerLink() {
            const nicknameInput = elements.nickname;
            const streamerButton = document.getElementById("go-to-stream");

            (async function () {
                while (!walletAddress) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                try {
                    const response = await fetch(`http://localhost:4000/get-nickname/${walletAddress}`);
                    const data = await response.json();
                    if (data.success && data.nickname) {
                        nicknameInput.value = data.nickname;
                        streamerButton.disabled = false;
                    }
                } catch (error) {
                    console.error("Ошибка загрузки ника:", error);
                }
            })();

            nicknameInput.addEventListener("input", function () {
                const nickname = this.value.trim();
                streamerButton.disabled = !nickname;
                updateDeviceBindingState();
            });
        }

        // Функция для перехода на страницу стримера
        async function goToStreamerPage() {
            try {
                // Получаем ник из базы данных
                const response = await fetch(`http://localhost:4000/get-nickname/${walletAddress}`);
                const data = await response.json();

                if (data.success && data.nickname) {
                    // Используем ник из базы данных
                    window.open(`http://localhost:4000/streamer/${data.nickname}`, '_blank');
                } else {
                    // Если ник не найден в базе, используем значение из поля ввода
                    const nickname = elements.nickname.value.trim();
                    if (nickname) {
                        window.open(`http://localhost:4000/streamer/${nickname}`, '_blank');
                    } else {
                        showToast("Сначала сохраните ник!", "warning");
                    }
                }
            } catch (error) {
                console.error("Ошибка при получении ника:", error);
                showToast("Ошибка при переходе на страницу стримера", "error");
            }
        }

        async function updateNickname() {
            const nickname = elements.nickname.value.trim();
            const streamerButton = document.getElementById("go-to-stream");
            const statusEl = document.getElementById("nickname-status");

            if (!nickname) {
                statusEl.style.color = "red";
                statusEl.textContent = "Введите ник!";
                return;
            }

            try {
                const response = await fetch("http://localhost:4000/update-nickname", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ walletAddress, nickname })
                });
                const data = await response.json();

                if (data.success) {
                    statusEl.style.color = "green";
                    statusEl.textContent = "Ник сохранён!";
                    streamerButton.disabled = false;
                    updateDeviceBindingState();
                } else {
                    statusEl.style.color = "red";
                    statusEl.textContent = data.message;
                }
            } catch (error) {
                console.error("Ошибка сохранения ника:", error);
                statusEl.style.color = "red";
                statusEl.textContent = "Ошибка сервера";
            }
        }

        async function fetchStreamUrl() {
            if (!walletAddress) return;
            try {
                const response = await fetch(`http://localhost:4000/get-stream-url/${walletAddress}`);
                const data = await response.json();
                if (data.success && data.streamUrl) {
                    document.getElementById("stream-url").value = data.streamUrl;
                } else {
                    console.log("Ссылка на трансляцию не найдена.");
                }
            } catch (error) {
                console.error("Ошибка загрузки streamUrl:", error);
            }
        }

        // Функция для определения платформы по URL
        function detectStreamPlatform(url) {
            if (url.includes("youtube.com")) {
                const match = url.match(/(?:youtube\.com\/live\/|youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/);
                return match ? { platform: "youtube", id: match[1] } : null;
            }
            if (url.includes("twitch.tv")) {
                const match = url.match(/twitch\.tv\/([\w-]+)/);
                return match ? { platform: "twitch", id: match[1] } : null;
            }
            if (url.includes("vkvideo.ru")) {
                const match = url.match(/vkvideo\.ru\/([\w-]+)/);
                return match ? { platform: "vk", id: match[1] } : null;
            }
            if (url.includes("kick.com")) {
                const match = url.match(/kick\.com\/([\w-]+)/);
                return match ? { platform: "kick", id: match[1] } : null;
            }
            return null;
        }

        // Функция сохранения ссылки на стрим
        async function saveStream() {
            const url = document.getElementById("stream-url").value.trim();
            const statusText = document.getElementById("stream-message");
            if (!walletAddress || !url) {
                statusText.textContent = "Необходимо ввести ссылку!";
                return;
            }
            try {
                const response = await fetch("http://localhost:4000/update-stream-url", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ walletAddress, streamUrl: url }),
                });
                const data = await response.json();
                if (data.success) {
                    statusText.style.color = "green";
                    statusText.textContent = "Ссылка на трансляцию успешно сохранена!";
                } else {
                    statusText.style.color = "red";  // <-- устанавливаем красный цвет
                    statusText.textContent = "Ошибка сохранения трансляции.";
                }
            } catch (error) {
                console.error("Ошибка сохранения streamUrl:", error);
                statusText.textContent = "Ошибка при сохранении трансляции.";
            }
        }

        // Загрузка ника
        async function fetchNickname() {
            try {
                const response = await fetch(`http://localhost:4000/get-nickname/${walletAddress}`);
                const data = await response.json();
                if (data.success) {
                    elements.nickname.value = data.nickname || "";
                    updateDeviceBindingState();
                }
            } catch (error) {
                console.error("Ошибка загрузки ника:", error.message);
            }
        }

        // Оптимизированная функция загрузки устройств
        async function fetchDevices() {
            try {
                const data = await apiRequest(`http://localhost:4000/devices/${walletAddress}`);
                const validDevices = data.devices.filter(device => device.mac);
                elements.devicesList.innerHTML = validDevices.length
                    ? validDevices.map(createDeviceRow).join('')
                    : '<tr><td colspan="6">Нет зарегистрированных устройств</td></tr>';
            } catch (error) {
                elements.devicesList.innerHTML = '<tr><td colspan="6">Ошибка загрузки устройств</td></tr>';
            }
        }

        // Функция форматирования даты в читаемый формат
        function formatDate(dateString) {
            if (!dateString) return "Нет данных";

            try {
                const date = new Date(dateString);

                // Проверяем, что дата валидна
                if (isNaN(date.getTime())) return "Неверная дата";

                // Форматируем дату и время
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');

                return `${day}.${month}.${year} ${hours}:${minutes}`;
            } catch (error) {
                console.error("Error formatting date:", error);
                return "Ошибка форматирования";
            }
        }

        // Создание строки для таблицы устройств
        function createDeviceRow(device) {
            // Форматируем даты подключения и отключения
            const formattedConnectedAt = formatDate(device.connectedAt);
            const formattedDisconnectedAt = formatDate(device.disconnectedAt);

            return `
                <tr>
                    <td>${device.mac}</td>
                    <td>${device.name || "Без имени"}</td>
                    <td class="${device.online ? 'online' : 'offline'}">
                        <div class="status ${device.online ? 'online' : 'offline'}">
                            <span class="status-dot"></span>
                            ${device.online ? "🟢 Онлайн" : "🔴 Оффлайн"}
                        </div>
                    </td>
                    <td>${formattedConnectedAt}</td>
                    <td>${formattedDisconnectedAt}</td>
                    <td><button class="delete-btn" onclick="deleteDevice('${device.mac}')">🗑️</button></td>
                </tr>
            `;
        }

        // Привязка устройства (доступна только если ник указан)
        async function bindDevice() {
            const mac = elements.deviceMac.value.trim();
            const name = elements.deviceName.value.trim();
            if (!walletAddress || !mac) {
                showToast("Адрес кошелька и MAC-адрес обязательны!", "error");
                return;
            }
            // Если ник не заполнен, не разрешаем добавление устройства
            if (elements.nickname.value.trim() === "") {
                showToast("Сначала введите ник!", "error");
                return;
            }
            try {
                const response = await fetch("http://localhost:4000/link-device", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ walletAddress, mac, name }),
                });
                const data = await response.json();
                if (!response.ok) throw new Error(data.message);
                showToast(data.message, "success");
                await fetchDevices();
                elements.deviceMac.value = "";
                elements.deviceName.value = "";
            } catch (error) {
                console.error("Ошибка привязки устройства:", error.message);
                showToast("Ошибка при привязке устройства: " + error.message, "error");
            }
        }

        // Удаление устройства
        async function deleteDevice(mac) {
            if (!confirm("Вы уверены, что хотите отвязать это устройство?")) return;
            try {
                const response = await fetch("http://localhost:4000/delete-device", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ walletAddress, mac }),
                });
                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.message || "Ошибка удаления устройства.");
                }
                showToast("Устройство успешно удалено!", "success");
                await fetchDevices();
            } catch (error) {
                console.error("Ошибка удаления устройства:", error.message);
                showToast("Ошибка при удалении устройства. Попробуйте позже.", "error");
            }
        }

        // Выход из TON Connect
        function logout() {
            console.log("🚪 Осуществляется выход...");
            walletAddress = null;
            tonConnectUI.disconnect();
            localStorage.clear();
            sessionStorage.clear();
            showToast("Вы вышли из аккаунта", "info");
            updateElementsState(false);
        }

        // Инициализация настроек режима управления
        function initControlModeSettings() {
            // Загружаем сохраненные настройки при авторизации
            if (walletAddress) {
                fetchControlModeSettings();
            }

            // Инициализируем начальное состояние
            // Проверяем текущий выбранный режим
            if (elements.fixedTime.checked) {
                elements.fixedTimeSettings.style.display = 'block';
            } else {
                elements.fixedTimeSettings.style.display = 'none';
            }
        }



        // Загрузка настроек режима управления
        async function fetchControlModeSettings() {
            try {
                const response = await fetch(`http://localhost:4000/control-mode-settings/${walletAddress}`);
                const data = await response.json();

                if (data.success && data.settings) {
                    const { mode, time, price } = data.settings;

                    // Устанавливаем режим
                    if (mode === 'fixed-time') {
                        elements.fixedTime.checked = true;
                        elements.donationBattle.checked = false;
                        // Показываем настройки фиксированного времени
                        elements.fixedTimeSettings.style.display = 'block';
                    } else {
                        elements.donationBattle.checked = true;
                        elements.fixedTime.checked = false;
                        // Скрываем настройки фиксированного времени
                        elements.fixedTimeSettings.style.display = 'none';
                    }

                    // Устанавливаем значения для фиксированного времени
                    if (time) elements.controlTime.value = time;
                    if (price) elements.controlPrice.value = price;
                }
            } catch (error) {
                console.error('Ошибка загрузки настроек режима:', error);
            }
        }

        // Сохранение настроек режима управления
        async function saveControlMode() {
            if (!walletAddress) {
                showToast('Вы должны быть авторизованы', 'error');
                return;
            }

            const mode = elements.fixedTime.checked ? 'fixed-time' : 'donation-battle';
            const time = document.getElementById("control-time").value;
            const price = document.getElementById("control-price").value;

            // Проверка валидности для режима фиксированного времени
            if (mode === 'fixed-time') {
                if (!time || time < 1 || time > 60) {
                    showToast('Время должно быть от 1 до 60 минут', 'error');
                    return;
                }

                if (!price || price < 0.1) {
                    showToast('Цена должна быть не менее 0.1 TON', 'error');
                    return;
                }
            }

            try {
                const messageEl = document.getElementById('control-mode-message');
                messageEl.textContent = 'Сохранение...';
                messageEl.style.color = '#666';

                const response = await fetch('http://localhost:4000/update-control-mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        walletAddress,
                        mode,
                        time: mode === 'fixed-time' ? time : null,
                        price: mode === 'fixed-time' ? price : null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    messageEl.textContent = 'Настройки режима успешно сохранены!';
                    messageEl.style.color = 'green';
                    showToast('Настройки режима сохранены', 'success');
                } else {
                    messageEl.textContent = data.message || 'Ошибка сохранения настроек';
                    messageEl.style.color = 'red';
                    showToast(data.message || 'Ошибка сохранения настроек', 'error');
                }
            } catch (error) {
                console.error('Ошибка сохранения настроек режима:', error);
                const messageEl = document.getElementById('control-mode-message');
                messageEl.textContent = 'Ошибка сервера при сохранении настроек';
                messageEl.style.color = 'red';
                showToast('Ошибка сервера при сохранении настроек', 'error');
            }
        }

        // Загрузка информации о заработке
        async function loadEarningsInfo() {
            if (!walletAddress) return;

            try {
                const response = await fetch(`/api/transactions/earnings/${walletAddress}`);
                const data = await response.json();

                if (data.success) {
                    displayEarningsInfo(data.data);
                } else {
                    document.getElementById('earnings-info').innerHTML =
                        '<div class="error">Ошибка загрузки данных о заработке</div>';
                }
            } catch (error) {
                console.error('Ошибка загрузки заработка:', error);
                document.getElementById('earnings-info').innerHTML =
                    '<div class="error">Ошибка подключения к серверу</div>';
            }
        }

        // Отображение информации о заработке
        function displayEarningsInfo(earningsData) {
            const totalEarnings = parseFloat(earningsData.totalEarnings || 0);
            const availableForWithdraw = parseFloat(earningsData.availableForWithdraw || 0);
            const totalTransactions = earningsData.totalTransactions || 0;
            const devicesCount = earningsData.devicesCount || 0;

            const earningsHtml = `
                <div class="earnings-display">
                    <div class="earnings-amount">${availableForWithdraw.toFixed(4)} TON</div>
                    <div class="earnings-label">Доступно для вывода</div>

                    <div class="earnings-details">
                        <div class="earnings-stat">
                            <div class="earnings-stat-value">${totalEarnings.toFixed(4)}</div>
                            <div class="earnings-stat-label">Всего заработано</div>
                        </div>
                        <div class="earnings-stat">
                            <div class="earnings-stat-value">${totalTransactions}</div>
                            <div class="earnings-stat-label">Транзакций</div>
                        </div>
                        <div class="earnings-stat">
                            <div class="earnings-stat-value">${devicesCount}</div>
                            <div class="earnings-stat-label">Устройств</div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('earnings-info').innerHTML = earningsHtml;

            // Активируем кнопку вывода, если есть средства
            const withdrawBtn = document.getElementById('withdraw-earnings');
            if (availableForWithdraw > 0) {
                withdrawBtn.disabled = false;
                withdrawBtn.textContent = `💸 Вывести ${availableForWithdraw.toFixed(4)} TON`;
            } else {
                withdrawBtn.disabled = true;
                withdrawBtn.textContent = '💸 Нет средств для вывода';
            }
        }

        // Вывод заработанных средств
        async function withdrawEarnings() {
            if (!walletAddress) {
                showToast('Подключите кошелек', 'error');
                return;
            }

            if (!confirm('Вы уверены, что хотите вывести все накопленные средства?')) {
                return;
            }

            try {
                const withdrawBtn = document.getElementById('withdraw-earnings');
                const originalText = withdrawBtn.textContent;
                withdrawBtn.disabled = true;
                withdrawBtn.textContent = '⏳ Обработка...';

                const response = await fetch('/api/contracts/withdraw-earnings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        streamer_wallet: walletAddress
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast('Вывод средств инициирован! Проверьте ваш кошелек.', 'success');
                    document.getElementById('withdraw-message').innerHTML =
                        '<div class="success">✅ Вывод средств успешно инициирован!</div>';

                    // Обновляем информацию о заработке через несколько секунд
                    setTimeout(() => {
                        loadEarningsInfo();
                    }, 3000);
                } else {
                    showToast(`Ошибка: ${data.message}`, 'error');
                    document.getElementById('withdraw-message').innerHTML =
                        `<div class="error">❌ ${data.message}</div>`;
                    withdrawBtn.disabled = false;
                    withdrawBtn.textContent = originalText;
                }
            } catch (error) {
                console.error('Ошибка вывода средств:', error);
                showToast('Ошибка при выводе средств', 'error');
                document.getElementById('withdraw-message').innerHTML =
                    '<div class="error">❌ Ошибка подключения к серверу</div>';

                const withdrawBtn = document.getElementById('withdraw-earnings');
                withdrawBtn.disabled = false;
                withdrawBtn.textContent = '💸 Вывести средства';
            }
        }

        // Унифицированная функция для API-запросов
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, options);
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.message || 'Ошибка запроса');
                }
                return data;
            } catch (error) {
                console.error(`Ошибка API запроса: ${error.message}`);
                showToast(error.message, 'error');
                throw error;
            }
        }
    </script>
</body>
</html>
