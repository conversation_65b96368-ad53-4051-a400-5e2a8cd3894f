{"name": "@tonconnect/ui", "version": "2.0.11", "scripts": {"start": "vite --host", "dev": "vite", "build": "tsc --noEmit --emitDeclarationOnly false && vite build && rollup -c rollup.config.mjs && vite build -c vite.cdn-config.ts && node ./scripts/patch-build_ssr-fix.js"}, "nx": {"tags": ["scope:ui"]}, "repository": {"type": "git", "url": "git+https://github.com/ton-connect/sdk.git"}, "homepage": "https://github.com/ton-connect/sdk/tree/main/packages/ui", "bugs": {"url": "https://github.com/ton-connect/sdk/issues"}, "keywords": ["TON", "Wallet", "ton-connect", "tonconnect", "Connect", "<PERSON><PERSON><PERSON><PERSON>", "sdk", "UI"], "author": "tonconnect", "license": "Apache-2.0", "files": ["lib", "dist"], "main": "./lib/index.cjs", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs", "default": "./lib/index.cjs"}}, "dependencies": {"@tonconnect/sdk": "3.0.6", "classnames": "^2.3.2", "deepmerge": "^4.2.2", "ua-parser-js": "^1.0.35", "csstype": "^3.1.1"}, "devDependencies": {"@floating-ui/dom": "^1.0.12", "@rollup/plugin-replace": "5.0.5", "@solid-primitives/i18n": "^1.1.2", "@types/node": "^18.11.17", "@types/ua-parser-js": "^0.7.36", "eslint-plugin-solid": "^0.7.3", "is-plain-object": "^5.0.0", "qrcode-generator": "^1.4.4", "rollup": "^3.8.0", "rollup-plugin-dts": "^5.0.0", "solid-devtools": "^0.24.7", "solid-floating-ui": "^0.2.0", "solid-js": "^1.5.1", "solid-styled-components": "^0.28.5", "solid-transition-group": "^0.0.12", "typescript": "^4.8.2", "vite": "^3.0.9", "vite-plugin-solid": "^2.3.0"}, "typedoc": {"entryPoint": "./src/library.ts"}}