{"version": 3, "sources": ["../../../../../src/start/platforms/ios/promptAppleDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { getBestSimulatorAsync } from './getBestSimulator';\nimport { Device } from './simctl';\nimport { createSelectionFilter, promptAsync } from '../../../utils/prompts';\n\n/**\n * Sort the devices so the last simulator that was opened (user's default) is the first suggested.\n *\n * @param devices list of devices to sort.\n * @param osType optional sort by operating system.\n */\nexport async function sortDefaultDeviceToBeginningAsync<T extends { udid: string }>(\n  devices: T[],\n  osType?: Device['osType']\n): Promise<T[]> {\n  const defaultId = await getBestSimulatorAsync({ osType });\n  if (defaultId) {\n    let iterations = 0;\n    while (devices[0].udid !== defaultId && iterations < devices.length) {\n      devices.push(devices.shift()!);\n      iterations++;\n    }\n  }\n  return devices;\n}\n\n/** Prompt the user to select an Apple device, sorting the most likely option to the beginning. */\nexport async function promptAppleDeviceAsync(\n  devices: Device[],\n  osType?: Device['osType']\n): Promise<Device> {\n  devices = await sortDefaultDeviceToBeginningAsync(devices, osType);\n  const results = await promptAppleDeviceInternalAsync(devices);\n  return devices.find(({ udid }) => results === udid)!;\n}\n\nasync function promptAppleDeviceInternalAsync(devices: Device[]): Promise<string> {\n  // TODO: provide an option to add or download more simulators\n  // TODO: Add support for physical devices too.\n\n  const { value } = await promptAsync({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a simulator',\n    choices: devices.map((item) => {\n      const isActive = item.state === 'Booted';\n      const format = isActive ? chalk.bold : (text: string) => text;\n      return {\n        title: `${format(item.name)} ${chalk.dim(`(${item.osVersion})`)}`,\n        value: item.udid,\n      };\n    }),\n    suggest: createSelectionFilter(),\n  });\n\n  return value;\n}\n"], "names": ["promptAppleDeviceAsync", "sortDefaultDeviceToBeginningAsync", "devices", "osType", "defaultId", "getBestSimulatorAsync", "iterations", "udid", "length", "push", "shift", "results", "promptAppleDeviceInternalAsync", "find", "value", "promptAsync", "type", "name", "limit", "message", "choices", "map", "item", "isActive", "state", "format", "chalk", "bold", "text", "title", "dim", "osVersion", "suggest", "createSelectionFilter"], "mappings": ";;;;;;;;;;;IA4BsBA,sBAAsB;eAAtBA;;IAhBAC,iCAAiC;eAAjCA;;;;gEAZJ;;;;;;kCAEoB;yBAEa;;;;;;AAQ5C,eAAeA,kCACpBC,OAAY,EACZC,MAAyB;IAEzB,MAAMC,YAAY,MAAMC,IAAAA,uCAAqB,EAAC;QAAEF;IAAO;IACvD,IAAIC,WAAW;QACb,IAAIE,aAAa;QACjB,MAAOJ,OAAO,CAAC,EAAE,CAACK,IAAI,KAAKH,aAAaE,aAAaJ,QAAQM,MAAM,CAAE;YACnEN,QAAQO,IAAI,CAACP,QAAQQ,KAAK;YAC1BJ;QACF;IACF;IACA,OAAOJ;AACT;AAGO,eAAeF,uBACpBE,OAAiB,EACjBC,MAAyB;IAEzBD,UAAU,MAAMD,kCAAkCC,SAASC;IAC3D,MAAMQ,UAAU,MAAMC,+BAA+BV;IACrD,OAAOA,QAAQW,IAAI,CAAC,CAAC,EAAEN,IAAI,EAAE,GAAKI,YAAYJ;AAChD;AAEA,eAAeK,+BAA+BV,OAAiB;IAC7D,6DAA6D;IAC7D,8CAA8C;IAE9C,MAAM,EAAEY,KAAK,EAAE,GAAG,MAAMC,IAAAA,oBAAW,EAAC;QAClCC,MAAM;QACNC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,SAASlB,QAAQmB,GAAG,CAAC,CAACC;YACpB,MAAMC,WAAWD,KAAKE,KAAK,KAAK;YAChC,MAAMC,SAASF,WAAWG,gBAAK,CAACC,IAAI,GAAG,CAACC,OAAiBA;YACzD,OAAO;gBACLC,OAAO,GAAGJ,OAAOH,KAAKL,IAAI,EAAE,CAAC,EAAES,gBAAK,CAACI,GAAG,CAAC,CAAC,CAAC,EAAER,KAAKS,SAAS,CAAC,CAAC,CAAC,GAAG;gBACjEjB,OAAOQ,KAAKf,IAAI;YAClB;QACF;QACAyB,SAASC,IAAAA,8BAAqB;IAChC;IAEA,OAAOnB;AACT"}