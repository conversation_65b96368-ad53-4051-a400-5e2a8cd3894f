/***************************************************
 * server.js
 ***************************************************/
require("dotenv").config();
const express = require("express");
const cors = require("cors");
const path = require("path");
const { Pool } = require("pg");
const winston = require("winston");

// Подключаем модуль, в котором инициализируется WebSocket-сервер
const initWsServer = require("./wsServer");
const realtime = require("./realtime");

// Подключаем сервисы для работы со смарт-контрактами
const TonService = require("./services/TonService");
const TransactionMonitorService = require("./services/TransactionMonitorService");
const blockchainConfig = require("./config/blockchain");

// ========================
// 1. Настройка базы данных и логирования
// ========================
const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
});

const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.printf(({ timestamp, level, message }) => `[${timestamp}] ${level}: ${message}`)
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "service.log" }),
  ],
});

// ========================
// 2. Инициализация сервисов блокчейна
// ========================
let tonService = null;
let transactionMonitor = null;

try {
  // Инициализация TON сервиса
  tonService = new TonService(blockchainConfig);

  // Инициализация мониторинга транзакций
  transactionMonitor = new TransactionMonitorService(pool, logger, tonService);

  console.log('✅ Blockchain services initialized');
} catch (error) {
  console.error('❌ Failed to initialize blockchain services:', error.message);
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
}

// ========================
// 3. Запуск WebSocket-сервера (порт 9090)
// ========================
const { wss, devices } = initWsServer(pool, logger);

// ========================
// 4. HTTP API для фронтенда (на порту 4000)
// ========================
const app = express();
app.use(express.json());
app.use(cors());

// Добавляем сервисы в app.locals для доступа в маршрутах
app.locals.pool = pool;
app.locals.logger = logger;
app.locals.tonService = tonService;
app.locals.transactionMonitor = transactionMonitor;

app.use("/config", express.static(path.join(__dirname, "config")));

// Подключаем новые маршруты для смарт-контрактов
const contractRoutes = require("./routes/contractRoutes");
const transactionRoutes = require("./routes/transactionRoutes");

app.use("/api/contracts", contractRoutes);
app.use("/api/transactions", transactionRoutes);

// --------------------------------------------------------
// ВАЖНО: здесь HTTP_PORT из .env или 4000 по умолчанию
// --------------------------------------------------------
const HTTP_PORT = process.env.HTTP_PORT || 4000;

// ========================
// Данные о донатах и управлении
// ========================
let donationQueue = [];
let currentLeader = null; // Обновляется при донате

// Хранилище для текущих покупателей времени и таймеров
// Формат: { streamerWallet: { buyer: "адрес покупателя", endTime: timestamp, timeLeft: секунды } }
let timeControlBuyers = {};

// ========================
// Отдача статических файлов
// ========================
app.use(express.static(path.join(__dirname, "public")));

/**
 * Получение устройств по нику (devices-by-nickname)
 */
app.get("/devices-by-nickname/:nickname", async (req, res) => {
  const { nickname } = req.params;
  try {
    // 1) Находим wallet_address в таблице streamers (по нику)
    const streamerRow = await pool.query(
      "SELECT wallet_address FROM streamers WHERE nickname = $1 LIMIT 1",
      [nickname]
    );
    if (streamerRow.rowCount === 0) {
      return res.status(404).json({ success: false, message: "Стример не найден" });
    }
    const walletAddress = streamerRow.rows[0].wallet_address;

    // 2) Запрашиваем устройства из таблицы devices
    const result = await pool.query(`
      SELECT d.device_id, d.name, l.is_online, l.connected_at, l.disconnected_at, l.device_type
      FROM devices d
      LEFT JOIN lot l ON d.device_id = l.device_id
      WHERE d.wallet_address = $1
    `, [walletAddress]);

    // 3) Формируем JSON
    const devicesList = result.rows.map((row) => ({
      mac: row.device_id,
      name: row.name || "Без имени",
      online: row.is_online,
      connectedAt: row.connected_at,
      disconnectedAt: row.disconnected_at,
      device_type: row.device_type
    }));
    return res.json({ success: true, devices: devicesList });
  } catch (err) {
    console.error("Ошибка /devices-by-nickname:", err);
    return res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Список устройств по кошельку (не по нику)
 */
app.get("/devices/:walletAddress", async (req, res) => {
  const { walletAddress } = req.params;
  if (!walletAddress) {
    return res.status(400).json({ success: false, message: "Адрес кошелька обязателен!" });
  }
  try {
    const result = await pool.query(`
      SELECT d.device_id, d.name, l.is_online, l.connected_at, l.disconnected_at
      FROM devices d
      LEFT JOIN lot l ON d.device_id = l.device_id
      WHERE d.wallet_address = $1
    `, [walletAddress]);

    const devicesList = result.rows.map(row => ({
      mac: row.device_id,
      name: row.name || "Без имени",
      online: row.is_online,
      connectedAt: row.connected_at,
      disconnectedAt: row.disconnected_at,
    }));
    res.json({ success: true, devices: devicesList });
  } catch (err) {
    console.error("❌ Ошибка получения устройств:", err);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Удаление (отвязка) устройства
 */
/**
 * Удаление (отвязка) устройства (теперь — полный DELETE)
 */
app.post("/delete-device", async (req, res) => {
  const { walletAddress, mac } = req.body;
  if (!walletAddress || !mac) {
    return res.status(400).json({
      success: false,
      message: "Адрес кошелька и MAC-адрес обязательны!"
    });
  }

  try {
    // 1) Проверяем, есть ли устройство у данного стримера
    const deviceResult = await pool.query(
      "SELECT * FROM devices WHERE device_id = $1 AND wallet_address = $2",
      [mac, walletAddress]
    );
    if (deviceResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Устройство не найдено или не привязано к вашему аккаунту."
      });
    }

    // 2) Удаляем полностью строку из таблицы devices
    await pool.query(
      "DELETE FROM devices WHERE device_id = $1 AND wallet_address = $2",
      [mac, walletAddress]
    );

    return res.json({
      success: true,
      message: "Устройство успешно удалено."
    });

  } catch (error) {
    console.error("Ошибка удаления устройства:", error.message);
    return res.status(500).json({
      success: false,
      message: "Ошибка сервера. Попробуйте позже."
    });
  }
});


/**
 * Привязка устройства к стримеру
 */
app.post("/link-device", async (req, res) => {
  const { walletAddress, mac, name } = req.body;
  if (!walletAddress || !mac) {
    return res.status(400).json({
      success: false,
      message: "Адрес кошелька и MAC-адрес обязательны!"
    });
  }
  try {
    // Сначала проверим, есть ли вообще такое устройство в lot
    const lotDevice = await pool.query("SELECT * FROM lot WHERE device_id = $1", [mac]);
    if (lotDevice.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Устройство с MAC-адресом ${mac} не зарегистрировано в системе.`
      });
    }
    // Проверим, не привязано ли уже к данному кошельку
    const existingDevice = await pool.query(
      "SELECT * FROM devices WHERE device_id = $1 AND wallet_address = $2",
      [mac, walletAddress]
    );
    if (existingDevice.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: `Устройство ${mac} уже привязано к вашему аккаунту.`
      });
    }
    // Если в таблице devices есть запись walletAddress + device_id IS NULL
    const deviceWithNullMac = await pool.query(
      "SELECT * FROM devices WHERE wallet_address = $1 AND device_id IS NULL",
      [walletAddress]
    );
    if (deviceWithNullMac.rows.length > 0) {
      // Тогда просто делаем UPDATE
      await pool.query(
        "UPDATE devices SET device_id = $1, name = $2 WHERE wallet_address = $3 AND device_id IS NULL",
        [mac, name || null, walletAddress]
      );
      return res.json({ success: true, message: `Устройство ${mac} привязано к аккаунту.` });
    }
    // Если ничего не нашли – делаем INSERT
    await pool.query(`
      INSERT INTO devices (device_id, wallet_address, name)
      VALUES ($1, $2, $3)
    `, [mac, walletAddress, name || null]);

    res.json({
      success: true,
      message: `Устройство ${mac} успешно привязано.`
    });
  } catch (err) {
    console.error("❌ Ошибка привязки устройства:", err.message);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Получение stream URL для кошелька (из таблицы streamers)
 */
app.get("/get-stream-url/:walletAddress", async (req, res) => {
  const { walletAddress } = req.params;
  try {
    // ВАЖНО: читаем из streamers, а не из devices
    const result = await pool.query(`
      SELECT stream_url
      FROM streamers
      WHERE wallet_address = $1
      LIMIT 1
    `, [walletAddress]);
    if (result.rows.length > 0 && result.rows[0].stream_url) {
      res.json({ success: true, streamUrl: result.rows[0].stream_url });
    } else {
      res.json({ success: false, message: "Ссылка на трансляцию не найдена" });
    }
  } catch (error) {
    console.error("Ошибка получения streamUrl:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Обновление stream URL в таблице streamers
 */
app.post("/update-stream-url", async (req, res) => {
  const { walletAddress, streamUrl } = req.body;
  if (!walletAddress || !streamUrl) {
    return res
      .status(400)
      .json({ success: false, message: "Недостаточно данных для обновления трансляции" });
  }

  let streamPlatform = null, streamId = null;
  if (streamUrl.includes("youtube.com")) {
    const match = streamUrl.match(/(?:youtube\.com\/live\/|youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/);
    if (match) {
      streamPlatform = "youtube";
      streamId = match[1];
    }
  } else if (streamUrl.includes("twitch.tv")) {
    const match = streamUrl.match(/twitch\.tv\/([\w-]+)/);
    if (match) {
      streamPlatform = "twitch";
      streamId = match[1];
    }
  } else if (streamUrl.includes("vkvideo.ru")) {
    const match = streamUrl.match(/vkvideo\.ru\/([\w-]+)/);
    if (match) {
      streamPlatform = "vk";
      streamId = match[1];
    }
  } else if (streamUrl.includes("kick.com")) {
    const match = streamUrl.match(/kick\.com\/([\w-]+)/);
    if (match) {
      streamPlatform = "kick";
      streamId = match[1];
    }
  }

  if (!streamPlatform || !streamId) {
    return res
      .status(400)
      .json({ success: false, message: "Неверный формат ссылки на трансляцию" });
  }

  try {
    // Запись в streamers
    await pool.query(`
      UPDATE streamers
      SET stream_url = $1,
          stream_platform = $2,
          stream_id = $3
      WHERE wallet_address = $4
    `, [streamUrl, streamPlatform, streamId, walletAddress]);

    res.json({ success: true, message: "Ссылка на трансляцию обновлена!" });
  } catch (error) {
    console.error("Ошибка обновления streamUrl:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Страница стримера
 */
app.get("/streamer/:nickname", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "streamer.html"));
});

/**
 * Загрузка информации о стриме из streamers (по нику)
 */
app.get("/api/stream/:nickname", async (req, res) => {
  const { nickname } = req.params;
  try {
    // Теперь берём из таблицы streamers
    const result = await pool.query(`
      SELECT stream_platform, stream_id
      FROM streamers
      WHERE nickname = $1
    `, [nickname]);
    if (result.rowCount === 0) {
      return res.status(404).json({ success: false, message: "Стример не найден" });
    }
    res.json({ success: true, stream: result.rows[0] });
  } catch (error) {
    console.error("Ошибка получения стрим-данных:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Обновление ника стримера (таблица streamers)
 */
app.post("/update-nickname", async (req, res) => {
  const { walletAddress, nickname } = req.body;
  if (!walletAddress || !nickname) {
    return res.status(400).json({ success: false, message: "Кошелек и ник обязательны!" });
  }
  try {
    // Проверка уникальности ника
    const checkNick = await pool.query(`
      SELECT * FROM streamers
      WHERE nickname = $1 AND wallet_address <> $2
    `, [nickname, walletAddress]);
    if (checkNick.rows.length > 0) {
      return res.status(409).json({ success: false, message: "Этот ник уже занят. Выберите другой." });
    }

    // Пытаемся обновить
    const upd = await pool.query(`
      UPDATE streamers
      SET nickname = $1
      WHERE wallet_address = $2
      RETURNING *
    `, [nickname, walletAddress]);

    if (upd.rowCount === 0) {
      // Если нет – создаём новую запись
      await pool.query(`
        INSERT INTO streamers (wallet_address, nickname)
        VALUES ($1, $2)
      `, [walletAddress, nickname]);
      return res.json({
        success: true,
        message: "Ник сохранён! Запись для стримера создана.",
        nickname
      });
    } else {
      return res.json({
        success: true,
        message: "Ник обновлён!",
        nickname
      });
    }
  } catch (error) {
    console.error("Ошибка update-nickname:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Получение ника стримера из streamers
 */
app.get("/get-nickname/:walletAddress", async (req, res) => {
  const { walletAddress } = req.params;
  if (!walletAddress) {
    return res.status(400).json({ success: false, message: "Адрес кошелька обязателен!" });
  }
  try {
    const result = await pool.query(`
      SELECT nickname
      FROM streamers
      WHERE wallet_address = $1
    `, [walletAddress]);
    if (result.rowCount === 0 || !result.rows[0].nickname) {
      return res.json({ success: true, nickname: "" });
    }
    res.json({ success: true, nickname: result.rows[0].nickname });
  } catch (error) {
    console.error("Ошибка get-nickname:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Страница настроек стримера
 */
app.get("/streamer-settings", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "streamer-settings.html"));
});

// Админ-панель платформы
app.get("/admin-panel", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "admin-panel.html"));
});

/**
 * TON Connect: отдача манифеста
 */
app.get("/tonconnect-manifest.json", (req, res) => {
  res.setHeader("Content-Type", "application/json");
  res.json({
    url: "http://localhost:4000",
    name: "Битва донатов",
    iconUrl: "https://yourserver.com/icon.png",
  });
});

/**
 * Получение донатов и текущего лидера
 */
app.get("/donations", (req, res) => {
  res.json({
    success: true,
    donations: donationQueue,
    leader: currentLeader
  });
});

/**
 * Получение настроек режима управления
 */
app.get("/control-mode-settings/:walletAddress", async (req, res) => {
  const { walletAddress } = req.params;
  try {
    // Проверяем, есть ли запись в таблице control_settings
    const result = await pool.query(
      "SELECT mode, time_minutes, price_ton FROM control_settings WHERE wallet_address = $1",
      [walletAddress]
    );

    if (result.rowCount === 0) {
      // Если нет записи, возвращаем настройки по умолчанию
      return res.json({
        success: true,
        settings: {
          mode: "donation-battle",
          time: 5,
          price: 1.0
        }
      });
    }

    // Если запись есть, возвращаем её данные
    const { mode, time_minutes, price_ton } = result.rows[0];
    res.json({
      success: true,
      settings: {
        mode: mode || "donation-battle",
        time: time_minutes || 5,
        price: price_ton || 1.0
      }
    });
  } catch (error) {
    console.error("Ошибка получения настроек режима:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Обновление настроек режима управления
 */
app.post("/update-control-mode", async (req, res) => {
  const { walletAddress, mode, time, price } = req.body;

  if (!walletAddress || !mode) {
    return res.status(400).json({ success: false, message: "Недостаточно параметров" });
  }

  try {
    // Проверяем, есть ли уже запись для этого кошелька
    const existingRecord = await pool.query(
      "SELECT * FROM control_settings WHERE wallet_address = $1",
      [walletAddress]
    );

    if (existingRecord.rowCount === 0) {
      // Если записи нет, создаем новую
      await pool.query(
        "INSERT INTO control_settings (wallet_address, mode, time_minutes, price_ton) VALUES ($1, $2, $3, $4)",
        [walletAddress, mode, time, price]
      );
    } else {
      // Если запись есть, обновляем её
      await pool.query(
        "UPDATE control_settings SET mode = $2, time_minutes = $3, price_ton = $4 WHERE wallet_address = $1",
        [walletAddress, mode, time, price]
      );
    }

    res.json({ success: true, message: "Настройки режима успешно сохранены" });
  } catch (error) {
    console.error("Ошибка обновления настроек режима:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Получение информации о режиме управления стримера
 */
app.get("/streamer-control-mode/:nickname", async (req, res) => {
  const { nickname } = req.params;
  try {
    // Находим wallet_address стримера по нику
    const streamerResult = await pool.query(
      "SELECT wallet_address FROM streamers WHERE nickname = $1",
      [nickname]
    );

    if (streamerResult.rowCount === 0) {
      return res.status(404).json({ success: false, message: "Стример не найден" });
    }

    const walletAddress = streamerResult.rows[0].wallet_address;

    // Получаем настройки режима управления
    const settingsResult = await pool.query(
      "SELECT mode, time_minutes, price_ton FROM control_settings WHERE wallet_address = $1",
      [walletAddress]
    );

    // По умолчанию - режим битвы донатов
    let mode = "donation-battle";
    let timeMinutes = 5;
    let priceTon = 1.0;

    if (settingsResult.rowCount > 0) {
      mode = settingsResult.rows[0].mode || "donation-battle";
      timeMinutes = settingsResult.rows[0].time_minutes || 5;
      priceTon = settingsResult.rows[0].price_ton || 1.0;
    }

    // Проверяем, есть ли активный покупатель времени
    let timeControl = null;
    if (timeControlBuyers[walletAddress]) {
      // Создаем копию объекта без свойства timerId, которое нельзя сериализовать
      timeControl = {
        buyer: timeControlBuyers[walletAddress].buyer,
        streamerNickname: timeControlBuyers[walletAddress].streamerNickname,
        timeLeft: timeControlBuyers[walletAddress].timeLeft,
        endTime: timeControlBuyers[walletAddress].endTime
      };
    }

    res.json({
      success: true,
      controlMode: {
        mode,
        timeMinutes,
        priceTon,
        timeControl
      }
    });
  } catch (error) {
    console.error("Ошибка получения режима управления:", error);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Авторизация через TON Connect
 */
app.post("/auth", async (req, res) => {
  const { user } = req.body;
  if (!user) {
    return res.status(400).json({ success: false, message: "Адрес TON-кошелька обязателен!" });
  }
  console.log(`✅ Пользователь ${user} авторизовался через TON Connect`);
  res.json({ success: true, message: "Авторизация успешна" });
});

/**
 * Обработка донатов
 * - В режиме битвы донатов: новый донат должен быть больше текущего лидера
 * - В режиме фиксированного времени: донат должен быть равен установленной цене
 */
app.post("/donate", async (req, res) => {
  const { user, amount, streamerNickname } = req.body;
  if (!user || !amount || !streamerNickname) {
    return res.status(400).json({ success: false, message: "Неверные параметры!" });
  }

  try {
    // Получаем адрес кошелька стримера по нику
    const streamerResult = await pool.query(
      "SELECT wallet_address FROM streamers WHERE nickname = $1",
      [streamerNickname]
    );

    if (streamerResult.rowCount === 0) {
      return res.status(404).json({ success: false, message: "Стример не найден" });
    }

    const streamerWallet = streamerResult.rows[0].wallet_address;

    // Получаем настройки режима управления
    const settingsResult = await pool.query(
      "SELECT mode, time_minutes, price_ton FROM control_settings WHERE wallet_address = $1",
      [streamerWallet]
    );

    // По умолчанию - режим битвы донатов
    let mode = "donation-battle";
    let timeMinutes = 5;
    let priceTon = 1.0;

    if (settingsResult.rowCount > 0) {
      mode = settingsResult.rows[0].mode || "donation-battle";
      timeMinutes = settingsResult.rows[0].time_minutes || 5;
      priceTon = settingsResult.rows[0].price_ton || 1.0;
    }

    // Обрабатываем донат в зависимости от режима
    if (mode === "donation-battle") {
      // Режим битвы донатов
      if (currentLeader && amount <= currentLeader.amount) {
        return res.status(400).json({
          success: false,
          message: `Минимальный донат должен быть больше ${currentLeader.amount} TON!`,
        });
      }

      donationQueue.push({ user, amount, streamerNickname });
      currentLeader = { user, amount, streamerNickname };
      console.log(`🔥 Новый лидер: ${currentLeader.user}, управление передано`);

      // Оповещаем всех клиентов об обновлении донатов
      realtime.broadcastUpdate({
        type: 'donation_update',
        donation: { user, amount, streamerNickname }
      });

      return res.json({
        success: true,
        message: "Донат принят! Теперь вы управляете устройством!",
        leader: currentLeader,
        nextAmount: (parseFloat(amount) + 0.1).toFixed(1),
        mode: "donation-battle"
      });
    } else if (mode === "fixed-time") {
      // Режим фиксированного времени

      // Проверяем, что сумма доната соответствует цене
      if (parseFloat(amount) < parseFloat(priceTon)) {
        return res.status(400).json({
          success: false,
          message: `Для покупки времени управления необходимо отправить ${priceTon} TON`,
        });
      }

      // Проверяем, есть ли уже активный покупатель времени
      if (timeControlBuyers[streamerWallet] && timeControlBuyers[streamerWallet].buyer !== user) {
        return res.status(400).json({
          success: false,
          message: `В данный момент устройством управляет другой пользователь. Попробуйте позже.`,
        });
      }

      // Устанавливаем время управления
      let timeLeftSeconds = timeMinutes * 60; // в секундах
      let endTime;

      // Если это тот же пользователь, добавляем время к уже существующему
      if (timeControlBuyers[streamerWallet] && timeControlBuyers[streamerWallet].buyer === user) {
        // Вычисляем точное оставшееся время на основе абсолютного времени окончания
        const now = Date.now();
        const remainingMs = Math.max(0, timeControlBuyers[streamerWallet].endTime - now);
        const currentRemainingSeconds = Math.ceil(remainingMs / 1000);

        // Добавляем новое время к точному оставшемуся времени
        timeLeftSeconds += currentRemainingSeconds;
        endTime = now + (timeLeftSeconds * 1000);
        console.log(`⭐ Пользователь ${user} добавил еще ${timeMinutes} минут. Общее время: ${timeLeftSeconds} секунд`);
      } else {
        // Новая покупка времени
        endTime = Date.now() + (timeLeftSeconds * 1000);
      }

      // Сохраняем текущий timerId, если он есть
      const currentTimerId = timeControlBuyers[streamerWallet] ? timeControlBuyers[streamerWallet].timerId : null;

      timeControlBuyers[streamerWallet] = {
        buyer: user,
        streamerNickname: streamerNickname,
        endTime: endTime,
        timeLeft: timeLeftSeconds,
        timerId: currentTimerId // Сохраняем текущий timerId
      };

      // Запускаем таймер для обновления оставшегося времени
      startTimeControlTimer(streamerWallet);

      // Создаем уникальный ID покупки
      const purchaseId = Date.now().toString() + Math.random().toString(36).substring(2, 15);

      // Добавляем ID покупки в объект timeControl для WebSocket-сообщения
      realtime.broadcastUpdate({
        type: 'time_control_update',
        timeControl: {
          buyer: user,
          streamerNickname,
          timeLeft: timeLeftSeconds,
          endTime,
          purchaseId // Добавляем ID покупки
        }
      });

      // Формируем сообщение в зависимости от того, добавляется ли время или это первая покупка
      const isAdditionalTime = timeLeftSeconds > timeMinutes * 60;
      const message = isAdditionalTime
        ? `Вы добавили еще ${timeMinutes} минут управления. Общее время: ${Math.floor(timeLeftSeconds / 60)} мин.`
        : `Вы приобрели ${timeMinutes} минут управления устройством!`;

      return res.json({
        success: true,
        message,
        purchaseId, // Добавляем ID покупки
        timeControl: {
          timeLeft: timeLeftSeconds,
          endTime,
          purchaseId // Добавляем ID покупки и в объект timeControl
        },
        mode: "fixed-time"
      });
    }
  } catch (error) {
    console.error("Ошибка обработки доната:", error);
    return res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Функция для запуска таймера обратного отсчета времени управления
 */
function startTimeControlTimer(streamerWallet) {
  // Очищаем существующий таймер, если он есть
  if (timeControlBuyers[streamerWallet] && timeControlBuyers[streamerWallet].timerId) {
    clearInterval(timeControlBuyers[streamerWallet].timerId);
  }

  // Используем абсолютное время окончания вместо отсчета секунд
  // Это более точный способ отслеживания времени
  const endTime = Date.now() + (timeControlBuyers[streamerWallet].timeLeft * 1000);
  timeControlBuyers[streamerWallet].endTime = endTime;

  // Функция для вычисления оставшегося времени
  const updateRemainingTime = () => {
    if (!timeControlBuyers[streamerWallet]) {
      return false; // Покупатель удален
    }

    // Вычисляем оставшееся время на основе абсолютного времени окончания
    const now = Date.now();
    const remainingMs = Math.max(0, timeControlBuyers[streamerWallet].endTime - now);
    const remainingSeconds = Math.ceil(remainingMs / 1000); // Округляем вверх, чтобы не заканчивалось раньше времени

    // Обновляем значение timeLeft
    timeControlBuyers[streamerWallet].timeLeft = remainingSeconds;

    // Проверяем, не закончилось ли время
    if (remainingSeconds <= 0) {
      // Время истекло, удаляем покупателя
      const buyer = timeControlBuyers[streamerWallet].buyer;
      const timerId = timeControlBuyers[streamerWallet].timerId;

      // Очищаем таймер и удаляем покупателя
      clearInterval(timerId);
      delete timeControlBuyers[streamerWallet];

      // Оповещаем всех клиентов об окончании времени
      realtime.broadcastUpdate({
        type: 'time_control_ended',
        streamerWallet,
        previousBuyer: buyer
      });

      console.log(`⏰ Время управления для ${buyer} закончилось`);
      return false; // Сигнал о завершении
    }

    // Отправляем обновления каждые 5 секунд или при кратных 5 секундах
    if (remainingSeconds % 5 === 0) {
      realtime.broadcastUpdate({
        type: 'time_control_update',
        timeControl: {
          buyer: timeControlBuyers[streamerWallet].buyer,
          streamerNickname: timeControlBuyers[streamerWallet].streamerNickname,
          timeLeft: remainingSeconds,
          endTime: timeControlBuyers[streamerWallet].endTime
        }
      });
    }

    return true; // Продолжаем обновление
  };

  // Запускаем таймер с интервалом в 1 секунду
  const timerId = setInterval(() => {
    // Если функция вернула false, значит время истекло или покупатель удален
    if (!updateRemainingTime()) {
      clearInterval(timerId);
    }
  }, 1000);

  // Сохраняем ID таймера для возможности очистки позже
  timeControlBuyers[streamerWallet].timerId = timerId;

  // Сразу выполняем первое обновление
  updateRemainingTime();
}

/**
 * Управление устройством (в зависимости от режима)
 */
app.post("/control", async (req, res) => {
  const { user, device_id, command, streamerNickname } = req.body;
  if (!user || !device_id || !command || !streamerNickname) {
    return res.status(400).json({
      success: false,
      message: "Недостаточно параметров!"
    });
  }

  try {
    // Получаем адрес кошелька стримера по нику
    const streamerResult = await pool.query(
      "SELECT wallet_address FROM streamers WHERE nickname = $1",
      [streamerNickname]
    );

    if (streamerResult.rowCount === 0) {
      return res.status(404).json({ success: false, message: "Стример не найден" });
    }

    const streamerWallet = streamerResult.rows[0].wallet_address;

    // Получаем настройки режима управления
    const settingsResult = await pool.query(
      "SELECT mode FROM control_settings WHERE wallet_address = $1",
      [streamerWallet]
    );

    // По умолчанию - режим битвы донатов
    let mode = "donation-battle";

    if (settingsResult.rowCount > 0) {
      mode = settingsResult.rows[0].mode || "donation-battle";
    }

    // Проверяем права доступа в зависимости от режима
    let hasAccess = false;

    if (mode === "donation-battle") {
      // В режиме битвы донатов проверяем, является ли пользователь лидером
      hasAccess = currentLeader && user === currentLeader.user && streamerNickname === currentLeader.streamerNickname;

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "Вы не лидер! Донатьте больше, чтобы управлять!"
        });
      }
    } else if (mode === "fixed-time") {
      // В режиме фиксированного времени проверяем, является ли пользователь текущим покупателем времени
      hasAccess = timeControlBuyers[streamerWallet] &&
                 timeControlBuyers[streamerWallet].buyer === user &&
                 timeControlBuyers[streamerWallet].streamerNickname === streamerNickname;

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "Вы не можете управлять устройством. Купите время управления!"
        });
      }
    }

    // Отправляем команду на устройство
    if (devices[device_id]) {
      devices[device_id].send(command);
      console.log(`✅ Команда ${command} отправлена от ${user} на устройство ${device_id}`);
      res.json({ success: true, message: `Команда ${command} выполнена на устройстве ${device_id}!` });
    } else {
      res.status(404).json({ success: false, message: "Устройство не подключено" });
    }
  } catch (error) {
    console.error("❌ Ошибка отправки команды:", error.message);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Обновление спонсорского ника
 */
app.post("/update-sponsor", async (req, res) => {
  const { walletAddress, nickname } = req.body;
  if (!walletAddress) {
    return res.status(400).json({ success: false, message: "Нет адреса кошелька!" });
  }
  try {
    const finalNick = nickname && nickname.trim() !== "" ? nickname.trim() : "anonim";
    // Проверяем, существует ли уже такой ник у другого пользователя
    const check = await pool.query(`
      SELECT wallet_address
      FROM sponsor
      WHERE nickname = $1 AND wallet_address <> $2
    `, [finalNick, walletAddress]);
    if (check.rows.length > 0) {
      return res.status(409).json({ success: false, message: "Этот ник уже занят." });
    }
    await pool.query(`
      INSERT INTO sponsor (wallet_address, nickname)
      VALUES ($1, $2)
      ON CONFLICT (wallet_address) DO UPDATE
        SET nickname = EXCLUDED.nickname
    `, [walletAddress, finalNick]);
    res.json({ success: true, message: "Ник обновлён!" });
  } catch (err) {
    console.error("Ошибка update-sponsor:", err.message);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Получение спонсорского ника по кошельку
 */
app.get("/get-sponsor/:walletAddress", async (req, res) => {
  const { walletAddress } = req.params;
  try {
    const result = await pool.query(`
      SELECT nickname
      FROM sponsor
      WHERE wallet_address = $1
    `, [walletAddress]);
    if (result.rows.length > 0) {
      return res.json({ success: true, nickname: result.rows[0].nickname });
    }
    res.json({ success: true, nickname: "anonim" });
  } catch (err) {
    console.error("Ошибка get-sponsor:", err.message);
    res.status(500).json({ success: false, message: "Ошибка сервера" });
  }
});

/**
 * Проверка уникальности ника
 */
app.get("/check-nickname/:nickname", async (req, res) => {
  const { nickname } = req.params;
  try {
    const result = await pool.query(`
      SELECT wallet_address
      FROM sponsor
      WHERE nickname = $1
    `, [nickname]);
    if (result.rows.length > 0) {
      res.json({ available: false, message: "Этот ник уже занят." });
    } else {
      res.json({ available: true });
    }
  } catch (err) {
    console.error("Ошибка проверки ника:", err.message);
    res.status(500).json({ available: false, message: "Ошибка сервера" });
  }
});

// Запуск HTTP API сервера
app.listen(HTTP_PORT, async () => {
  console.log(`✅ Сервер запущен на http://localhost:${HTTP_PORT}`);

  // Инициализация realtime WebSocket сервера
  try {
    realtime.initRealtimeServer();
    realtime.setupRealtimeHandlers();
    console.log('🔗 Realtime WebSocket сервер инициализирован');
  } catch (error) {
    console.error('❌ Failed to start realtime server:', error.message);
  }

  // Запуск мониторинга транзакций
  if (transactionMonitor && blockchainConfig.monitoring.enabled) {
    try {
      await transactionMonitor.startMonitoring();
      console.log('🔍 Transaction monitoring started');
    } catch (error) {
      console.error('❌ Failed to start transaction monitoring:', error.message);
    }
  }
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down gracefully...');

  if (transactionMonitor) {
    transactionMonitor.stopMonitoring();
  }

  if (pool) {
    await pool.end();
  }

  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');

  if (transactionMonitor) {
    transactionMonitor.stopMonitoring();
  }

  if (pool) {
    await pool.end();
  }

  process.exit(0);
});
