;; Universal Device Control Smart Contract
;; Универсальный смарт-контракт для управления устройствами

#include "imports/stdlib.fc";

;; Storage structure
;; platform_address:MsgAddress platform_fee:uint8 active_sessions:dict
global slice platform_address;
global int platform_fee;
global cell active_sessions;

;; Operation codes
const op::buy_control_time = 0x1;
const op::withdraw_earnings = 0x2;
const op::set_platform_fee = 0x3;
const op::emergency_stop = 0x4;

;; Error codes
const error::insufficient_payment = 100;
const error::device_busy = 101;
const error::unauthorized = 102;
const error::invalid_time = 103;

;; Load storage data
(slice, int, cell) load_data() inline {
    slice ds = get_data().begin_parse();
    return (
        ds~load_msg_addr(),  ;; platform_address
        ds~load_uint(8),     ;; platform_fee (percentage)
        ds~load_dict()       ;; active_sessions
    );
}

;; Save storage data
() save_data(slice platform_addr, int fee, cell sessions) impure inline {
    set_data(
        begin_cell()
            .store_slice(platform_addr)
            .store_uint(fee, 8)
            .store_dict(sessions)
        .end_cell()
    );
}

;; Get session key from streamer address and device ID
int get_session_key(slice streamer_addr, slice device_id) inline {
    return string_hash(streamer_addr) + string_hash(device_id);
}

;; Check if device is currently controlled
(int, int, slice) get_active_session(slice streamer_addr, slice device_id) method_id {
    (slice platform_addr, int fee, cell sessions) = load_data();
    int key = get_session_key(streamer_addr, device_id);
    
    (slice session_data, int found) = sessions.udict_get?(256, key);
    if (~ found) {
        return (0, 0, null()); ;; Not active
    }
    
    int end_time = session_data~load_uint(32);
    slice controller = session_data~load_msg_addr();
    
    if (now() >= end_time) {
        return (0, 0, null()); ;; Session expired
    }
    
    return (-1, end_time, controller); ;; Active session
}

;; Buy control time for device
() buy_control_time(slice streamer_addr, slice device_id, int duration_minutes, int price_per_minute) impure {
    int total_cost = price_per_minute * duration_minutes;
    
    ;; Check payment amount
    throw_unless(error::insufficient_payment, msg_value() >= total_cost);
    
    ;; Check if device is available
    (int is_active, int end_time, slice current_controller) = get_active_session(streamer_addr, device_id);
    throw_if(error::device_busy, is_active);
    
    ;; Calculate session end time
    int session_end = now() + (duration_minutes * 60);
    
    ;; Store session data
    (slice platform_addr, int fee, cell sessions) = load_data();
    int key = get_session_key(streamer_addr, device_id);
    
    cell session_data = begin_cell()
        .store_uint(session_end, 32)
        .store_slice(sender_address())
    .end_cell();
    
    sessions~udict_set(256, key, session_data.begin_parse());
    
    ;; Calculate platform fee
    int platform_amount = (total_cost * fee) / 100;
    int streamer_amount = total_cost - platform_amount;
    
    ;; Send payment to streamer
    if (streamer_amount > 0) {
        var msg = begin_cell()
            .store_uint(0x10, 6)
            .store_slice(streamer_addr)
            .store_coins(streamer_amount)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .store_uint(0, 32)
            .store_slice("Device control payment")
        .end_cell();
        send_raw_message(msg, 1);
    }
    
    ;; Send platform fee
    if (platform_amount > 0) {
        var msg = begin_cell()
            .store_uint(0x10, 6)
            .store_slice(platform_addr)
            .store_coins(platform_amount)
            .store_uint(0, 1 + 4 + 4 + 64 + 32 + 1 + 1)
            .store_uint(0, 32)
            .store_slice("Platform fee")
        .end_cell();
        send_raw_message(msg, 1);
    }
    
    save_data(platform_addr, fee, sessions);
}

;; Main message handler
() recv_internal(int my_balance, int msg_value, cell in_msg_full, slice in_msg_body) impure {
    if (in_msg_body.slice_empty?()) {
        return (); ;; Ignore empty messages
    }
    
    slice cs = in_msg_full.begin_parse();
    int flags = cs~load_uint(4);
    if (flags & 1) {
        return (); ;; Ignore bounced messages
    }
    
    slice sender_addr = cs~load_msg_addr();
    
    int op = in_msg_body~load_uint(32);
    
    if (op == op::buy_control_time) {
        slice streamer_addr = in_msg_body~load_msg_addr();
        slice device_id = in_msg_body~load_ref().begin_parse();
        int duration_minutes = in_msg_body~load_uint(16);
        int price_per_minute = in_msg_body~load_coins();
        
        buy_control_time(streamer_addr, device_id, duration_minutes, price_per_minute);
        return ();
    }
    
    if (op == op::set_platform_fee) {
        (slice platform_addr, int fee, cell sessions) = load_data();
        throw_unless(error::unauthorized, equal_slices(sender_addr, platform_addr));
        
        int new_fee = in_msg_body~load_uint(8);
        save_data(platform_addr, new_fee, sessions);
        return ();
    }
    
    throw(0xffff); ;; Unknown operation
}

;; Get methods for external queries
(int, int, slice) get_device_session(slice streamer_addr, slice device_id) method_id {
    return get_active_session(streamer_addr, device_id);
}

int get_platform_fee() method_id {
    (slice platform_addr, int fee, cell sessions) = load_data();
    return fee;
}
