{"version": 3, "sources": ["../../../src/install/applyPlugins.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport * as Log from '../log';\n\n/**\n * A convenience feature for automatically applying Expo Config Plugins to the `app.json` after installing them.\n * This should be dropped in favor of autolinking in the future.\n */\nexport async function applyPluginsAsync(projectRoot: string, packages: string[]) {\n  const { autoAddConfigPluginsAsync } = await import('./utils/autoAddConfigPlugins.js');\n\n  try {\n    const { exp } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n\n    // Only auto add plugins if the plugins array is defined or if the project is using SDK +42.\n    await autoAddConfigPluginsAsync(\n      projectRoot,\n      exp,\n      // Split any possible NPM tags. i.e. `expo@latest` -> `expo`\n      packages.map((pkg) => pkg.split('@')[0]).filter(Boolean)\n    );\n  } catch (error: any) {\n    // If we fail to apply plugins, the log a warning and continue.\n    if (error.isPluginError) {\n      Log.warn(`Skipping config plugin check: ` + error.message);\n      return;\n    }\n    // Any other error, rethrow.\n    throw error;\n  }\n}\n"], "names": ["applyPluginsAsync", "projectRoot", "packages", "autoAddConfigPluginsAsync", "exp", "getConfig", "skipSDKVersionRequirement", "map", "pkg", "split", "filter", "Boolean", "error", "isPluginError", "Log", "warn", "message"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;yBARI;;;;;;6DAEL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMd,eAAeA,kBAAkBC,WAAmB,EAAEC,QAAkB;IAC7E,MAAM,EAAEC,yBAAyB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAEnD,IAAI;QACF,MAAM,EAAEC,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACJ,aAAa;YAAEK,2BAA2B;QAAK;QAEzE,4FAA4F;QAC5F,MAAMH,0BACJF,aACAG,KACA,4DAA4D;QAC5DF,SAASK,GAAG,CAAC,CAACC,MAAQA,IAAIC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAEC,MAAM,CAACC;IAEpD,EAAE,OAAOC,OAAY;QACnB,+DAA+D;QAC/D,IAAIA,MAAMC,aAAa,EAAE;YACvBC,KAAIC,IAAI,CAAC,CAAC,8BAA8B,CAAC,GAAGH,MAAMI,OAAO;YACzD;QACF;QACA,4BAA4B;QAC5B,MAAMJ;IACR;AACF"}