/**
 * API маршруты для работы с транзакциями и заработками
 */

const express = require('express');
const router = express.Router();

/**
 * История транзакций устройства
 */
router.get('/device/:device_id', async (req, res) => {
  const { device_id } = req.params;
  const { page = 1, limit = 20, status } = req.query;

  try {
    const { pool } = req.app.locals;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE device_id = $1';
    let params = [device_id];

    if (status) {
      whereClause += ' AND status = $2';
      params.push(status);
    }

    // Получение транзакций с пагинацией
    const result = await pool.query(`
      SELECT
        ct.*,
        acs.controller_wallet,
        acs.start_time,
        acs.end_time,
        acs.is_active as session_active
      FROM control_transactions ct
      LEFT JOIN active_control_sessions acs ON ct.id = acs.transaction_id
      ${whereClause}
      ORDER BY ct.created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `, [...params, limit, offset]);

    // Подсчет общего количества
    const countResult = await pool.query(`
      SELECT COUNT(*) as total
      FROM control_transactions
      ${whereClause}
    `, params);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        transactions: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Ошибка получения истории транзакций:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении истории транзакций'
    });
  }
});

/**
 * Активные сессии управления стримера
 */
router.get('/active-sessions/:streamer_wallet', async (req, res) => {
  const { streamer_wallet } = req.params;

  try {
    const { pool } = req.app.locals;

    const result = await pool.query(`
      SELECT
        acs.*,
        ct.amount_ton,
        ct.transaction_hash,
        dc.contract_address,
        d.name as device_name,
        EXTRACT(EPOCH FROM (acs.end_time - NOW())) as seconds_remaining
      FROM active_control_sessions acs
      JOIN device_contracts dc ON acs.device_id = dc.device_id
      JOIN control_transactions ct ON acs.transaction_id = ct.id
      LEFT JOIN devices d ON acs.device_id = d.device_id
      WHERE dc.streamer_wallet = $1
        AND acs.is_active = true
        AND acs.end_time > NOW()
      ORDER BY acs.end_time ASC
    `, [streamer_wallet]);

    res.json({
      success: true,
      data: {
        activeSessions: result.rows,
        count: result.rows.length
      }
    });

  } catch (error) {
    console.error('Ошибка получения активных сессий:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении активных сессий'
    });
  }
});

/**
 * Статистика заработка стримера
 */
router.get('/earnings/:streamer_wallet', async (req, res) => {
  const { streamer_wallet } = req.params;
  const { period = '30d' } = req.query;

  try {
    const { pool } = req.app.locals;

    // Определение периода
    let intervalClause;
    switch (period) {
      case '24h':
        intervalClause = "INTERVAL '24 hours'";
        break;
      case '7d':
        intervalClause = "INTERVAL '7 days'";
        break;
      case '30d':
        intervalClause = "INTERVAL '30 days'";
        break;
      case '90d':
        intervalClause = "INTERVAL '90 days'";
        break;
      default:
        intervalClause = "INTERVAL '30 days'";
    }

    // Общая статистика заработка
    const totalEarningsResult = await pool.query(`
      SELECT
        COUNT(ct.id) as total_transactions,
        SUM(ct.amount_ton) as total_earned,
        SUM(ct.duration_minutes) as total_minutes_sold,
        AVG(ct.amount_ton) as avg_transaction_amount
      FROM control_transactions ct
      JOIN device_contracts dc ON ct.device_id = dc.device_id
      WHERE dc.streamer_wallet = $1
        AND ct.status = 'confirmed'
        AND ct.created_at > NOW() - ${intervalClause}
    `, [streamer_wallet]);

    // Заработок по устройствам
    const deviceEarningsResult = await pool.query(`
      SELECT
        ct.device_id,
        d.name as device_name,
        COUNT(ct.id) as transaction_count,
        SUM(ct.amount_ton) as total_earned,
        SUM(ct.duration_minutes) as total_minutes,
        MAX(ct.created_at) as last_transaction
      FROM control_transactions ct
      JOIN device_contracts dc ON ct.device_id = dc.device_id
      LEFT JOIN devices d ON ct.device_id = d.device_id
      WHERE dc.streamer_wallet = $1
        AND ct.status = 'confirmed'
        AND ct.created_at > NOW() - ${intervalClause}
      GROUP BY ct.device_id, d.name
      ORDER BY total_earned DESC
    `, [streamer_wallet]);

    // Заработок по дням (для графика)
    const dailyEarningsResult = await pool.query(`
      SELECT
        DATE(ct.created_at) as date,
        COUNT(ct.id) as transaction_count,
        SUM(ct.amount_ton) as daily_earned,
        SUM(ct.duration_minutes) as daily_minutes
      FROM control_transactions ct
      JOIN device_contracts dc ON ct.device_id = dc.device_id
      WHERE dc.streamer_wallet = $1
        AND ct.status = 'confirmed'
        AND ct.created_at > NOW() - ${intervalClause}
      GROUP BY DATE(ct.created_at)
      ORDER BY date ASC
    `, [streamer_wallet]);

    // Топ покупатели
    const topBuyersResult = await pool.query(`
      SELECT
        ct.buyer_wallet,
        COUNT(ct.id) as purchase_count,
        SUM(ct.amount_ton) as total_spent,
        SUM(ct.duration_minutes) as total_minutes,
        MAX(ct.created_at) as last_purchase
      FROM control_transactions ct
      JOIN device_contracts dc ON ct.device_id = dc.device_id
      WHERE dc.streamer_wallet = $1
        AND ct.status = 'confirmed'
        AND ct.created_at > NOW() - ${intervalClause}
      GROUP BY ct.buyer_wallet
      ORDER BY total_spent DESC
      LIMIT 10
    `, [streamer_wallet]);

    res.json({
      success: true,
      data: {
        period,
        totalEarnings: totalEarningsResult.rows[0],
        deviceEarnings: deviceEarningsResult.rows,
        dailyEarnings: dailyEarningsResult.rows,
        topBuyers: topBuyersResult.rows
      }
    });

  } catch (error) {
    console.error('Ошибка получения статистики заработка:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении статистики заработка'
    });
  }
});

/**
 * Вывод заработанных средств
 */
router.post('/withdraw/:streamer_wallet', async (req, res) => {
  const { streamer_wallet } = req.params;
  const { device_id } = req.body;

  try {
    const { pool, tonService, logger } = req.app.locals;

    // Проверка прав доступа
    const contractCheck = await pool.query(
      'SELECT * FROM device_contracts WHERE streamer_wallet = $1 AND device_id = $2',
      [streamer_wallet, device_id]
    );

    if (contractCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Контракт не найден или у вас нет прав доступа'
      });
    }

    const contract = contractCheck.rows[0];

    // Создание транзакции вывода
    const withdrawMessage = await tonService.withdrawEarnings(
      contract.contract_address,
      streamer_wallet
    );

    // Запись в историю выводов
    await pool.query(`
      INSERT INTO streamer_earnings
      (streamer_wallet, device_id, amount_ton, transaction_hash, withdrawal_time)
      VALUES ($1, $2, $3, $4, NOW())
    `, [
      streamer_wallet,
      device_id,
      0, // Сумма будет обновлена после подтверждения
      'pending_' + Date.now()
    ]);

    logger.info(`💸 Запрос на вывод средств: ${streamer_wallet}, устройство ${device_id}`);

    res.json({
      success: true,
      message: 'Транзакция вывода подготовлена',
      withdrawMessage
    });

  } catch (error) {
    console.error('Ошибка создания транзакции вывода:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при создании транзакции вывода'
    });
  }
});

/**
 * Получение статистики мониторинга
 */
router.get('/monitoring/stats', async (req, res) => {
  try {
    const { transactionMonitor } = req.app.locals;

    if (!transactionMonitor) {
      return res.status(503).json({
        success: false,
        message: 'Сервис мониторинга недоступен'
      });
    }

    const stats = await transactionMonitor.getMonitoringStats();

    res.json({
      success: true,
      data: {
        monitoringStats: stats,
        isMonitoring: transactionMonitor.isMonitoring,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Ошибка получения статистики мониторинга:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении статистики мониторинга'
    });
  }
});

/**
 * Получение общей статистики платформы
 */
router.get('/platform/stats', async (req, res) => {
  try {
    const { pool } = req.app.locals;

    // Общая статистика
    const totalStatsResult = await pool.query(`
      SELECT
        COUNT(DISTINCT dc.device_id) as total_devices,
        COUNT(DISTINCT dc.streamer_wallet) as total_streamers,
        COUNT(DISTINCT ct.buyer_wallet) as total_buyers,
        COUNT(ct.id) as total_transactions,
        SUM(ct.amount_ton) as total_volume,
        AVG(ct.amount_ton) as avg_transaction_amount
      FROM device_contracts dc
      LEFT JOIN control_transactions ct ON dc.device_id = ct.device_id
        AND ct.status = 'confirmed'
    `);

    // Статистика за последние 24 часа
    const recentStatsResult = await pool.query(`
      SELECT
        COUNT(ct.id) as recent_transactions,
        SUM(ct.amount_ton) as recent_volume,
        COUNT(DISTINCT ct.buyer_wallet) as recent_buyers
      FROM control_transactions ct
      WHERE ct.status = 'confirmed'
        AND ct.created_at > NOW() - INTERVAL '24 hours'
    `);

    // Топ устройства по заработку
    const topDevicesResult = await pool.query(`
      SELECT
        ct.device_id,
        d.name as device_name,
        dc.streamer_wallet,
        COUNT(ct.id) as transaction_count,
        SUM(ct.amount_ton) as total_earned
      FROM control_transactions ct
      JOIN device_contracts dc ON ct.device_id = dc.device_id
      LEFT JOIN devices d ON ct.device_id = d.device_id
      WHERE ct.status = 'confirmed'
      GROUP BY ct.device_id, d.name, dc.streamer_wallet
      ORDER BY total_earned DESC
      LIMIT 10
    `);

    // Добавляем информацию о накопленных комиссиях
    const platformFeesResult = await pool.query(`
      SELECT
        SUM(dc.accumulated_platform_fee) as total_platform_fees,
        COUNT(CASE WHEN dc.accumulated_platform_fee > 0 THEN 1 END) as devices_with_fees
      FROM device_contracts dc
    `);

    const totalStats = {
      ...totalStatsResult.rows[0],
      ...platformFeesResult.rows[0]
    };

    res.json({
      success: true,
      data: {
        totalStats,
        recentStats: recentStatsResult.rows[0],
        topDevices: topDevicesResult.rows
      }
    });

  } catch (error) {
    console.error('Ошибка получения статистики платформы:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка сервера при получении статистики платформы'
    });
  }
});

/**
 * Получение устройств с накопленными комиссиями (для админ-панели)
 */
router.get('/platform/devices-with-fees', async (req, res) => {
  try {
    const { pool } = req.app.locals;

    const query = `
      SELECT
        dc.device_id,
        dc.accumulated_platform_fee,
        dc.contract_address,
        d.name as device_name,
        s.nickname as streamer_nickname,
        dc.streamer_wallet
      FROM device_contracts dc
      LEFT JOIN devices d ON dc.device_id = d.device_id
      LEFT JOIN streamers s ON dc.streamer_wallet = s.wallet_address
      WHERE dc.accumulated_platform_fee > 0
      ORDER BY dc.accumulated_platform_fee DESC
    `;

    const result = await pool.query(query);

    res.json({
      success: true,
      data: {
        devices: result.rows
      }
    });
  } catch (error) {
    console.error('Ошибка получения устройств с комиссиями:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка получения устройств с комиссиями'
    });
  }
});

/**
 * Вывод всех накопленных комиссий платформы (для админ-панели)
 */
router.post('/platform/withdraw-all-fees', async (req, res) => {
  try {
    const { admin_wallet } = req.body;

    if (!admin_wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указан кошелек администратора'
      });
    }

    const { pool, tonService, logger } = req.app.locals;

    // Получаем все устройства с накопленными комиссиями
    const devicesQuery = `
      SELECT device_id, accumulated_platform_fee, contract_address
      FROM device_contracts
      WHERE accumulated_platform_fee > 0 AND contract_address IS NOT NULL
    `;

    const devicesResult = await pool.query(devicesQuery);

    if (devicesResult.rows.length === 0) {
      return res.json({
        success: false,
        message: 'Нет устройств с накопленными комиссиями'
      });
    }

    let totalWithdrawn = 0;
    const withdrawalResults = [];

    // Инициируем вывод комиссий с каждого контракта
    for (const device of devicesResult.rows) {
      try {
        const result = await tonService.withdrawPlatformFee(
          device.contract_address,
          admin_wallet
        );

        if (result.success) {
          totalWithdrawn += parseFloat(device.accumulated_platform_fee);
          withdrawalResults.push({
            device_id: device.device_id,
            amount: device.accumulated_platform_fee,
            transaction_id: result.transactionId
          });

          // Обнуляем накопленную комиссию в базе данных
          await pool.query(
            'UPDATE device_contracts SET accumulated_platform_fee = 0 WHERE device_id = $1',
            [device.device_id]
          );

          logger.info(`💰 Вывод комиссии с устройства ${device.device_id}: ${device.accumulated_platform_fee} TON`);
        }
      } catch (error) {
        console.error(`Ошибка вывода комиссии с устройства ${device.device_id}:`, error);
      }
    }

    logger.info(`💸 Общий вывод комиссий платформы: ${totalWithdrawn} TON с ${withdrawalResults.length} устройств`);

    res.json({
      success: true,
      message: `Инициирован вывод комиссий с ${withdrawalResults.length} устройств`,
      data: {
        totalWithdrawn,
        withdrawalResults
      }
    });
  } catch (error) {
    console.error('Ошибка вывода всех комиссий:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка при выводе комиссий'
    });
  }
});

/**
 * Вывод комиссии с конкретного устройства (для админ-панели)
 */
router.post('/platform/withdraw-device-fee', async (req, res) => {
  try {
    const { device_id, admin_wallet } = req.body;

    if (!device_id || !admin_wallet) {
      return res.status(400).json({
        success: false,
        message: 'Не указаны обязательные параметры'
      });
    }

    const { pool, tonService, logger } = req.app.locals;

    // Получаем информацию о контракте устройства
    const contractQuery = `
      SELECT accumulated_platform_fee, contract_address
      FROM device_contracts
      WHERE device_id = $1 AND accumulated_platform_fee > 0
    `;

    const contractResult = await pool.query(contractQuery, [device_id]);

    if (contractResult.rows.length === 0) {
      return res.json({
        success: false,
        message: 'Устройство не найдено или нет накопленных комиссий'
      });
    }

    const contract = contractResult.rows[0];

    // Инициируем вывод комиссии
    const result = await tonService.withdrawPlatformFee(
      contract.contract_address,
      admin_wallet
    );

    if (result.success) {
      // Обнуляем накопленную комиссию в базе данных
      await pool.query(
        'UPDATE device_contracts SET accumulated_platform_fee = 0 WHERE device_id = $1',
        [device_id]
      );

      logger.info(`💰 Вывод комиссии с устройства ${device_id}: ${contract.accumulated_platform_fee} TON`);

      res.json({
        success: true,
        message: 'Вывод комиссии инициирован',
        data: {
          device_id,
          amount: contract.accumulated_platform_fee,
          transaction_id: result.transactionId
        }
      });
    } else {
      res.json({
        success: false,
        message: result.message || 'Ошибка при выводе комиссии'
      });
    }
  } catch (error) {
    console.error('Ошибка вывода комиссии устройства:', error);
    res.status(500).json({
      success: false,
      message: 'Ошибка при выводе комиссии'
    });
  }
});

module.exports = router;
