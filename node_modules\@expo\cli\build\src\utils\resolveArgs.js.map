{"version": 3, "sources": ["../../../src/utils/resolveArgs.ts"], "sourcesContent": ["import arg, { Spec } from 'arg';\n\nimport { replaceValue } from './array';\nimport { CommandError } from './errors';\n\n/** Split up arguments that are formatted like `--foo=bar` or `-f=\"bar\"` to `['--foo', 'bar']` */\nfunction splitArgs(args: string[]): string[] {\n  const result: string[] = [];\n\n  for (const arg of args) {\n    if (arg.startsWith('-')) {\n      const [key, ...props] = arg.split('=');\n      result.push(key);\n      if (props.length) {\n        result.push(props.join('='));\n      }\n    } else {\n      result.push(arg);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Enables the resolution of arguments that can either be a string or a boolean.\n *\n * @param args arguments that were passed to the command.\n * @param rawMap raw map of arguments that are passed to the command.\n * @param extraArgs extra arguments and aliases that should be resolved as string or boolean.\n * @returns parsed arguments and project root.\n */\nexport async function resolveStringOrBooleanArgsAsync(\n  args: string[],\n  rawMap: arg.Spec,\n  extraArgs: arg.Spec\n) {\n  args = splitArgs(args);\n\n  // Assert any missing arguments\n  assertUnknownArgs(\n    {\n      ...rawMap,\n      ...extraArgs,\n    },\n    args\n  );\n\n  // Collapse aliases into fully qualified arguments.\n  args = collapseAliases(extraArgs, args);\n\n  // Resolve all of the string or boolean arguments and the project root.\n  return _resolveStringOrBooleanArgs({ ...rawMap, ...extraArgs }, args);\n}\n\n/**\n * Enables the resolution of boolean arguments that can be formatted like `--foo=true` or `--foo false`\n *\n * @param args arguments that were passed to the command.\n * @param rawMap raw map of arguments that are passed to the command.\n * @param extraArgs extra arguments and aliases that should be resolved as string or boolean.\n * @returns parsed arguments and project root.\n */\nexport async function resolveCustomBooleanArgsAsync(\n  args: string[],\n  rawMap: arg.Spec,\n  extraArgs: arg.Spec\n) {\n  const results = await resolveStringOrBooleanArgsAsync(args, rawMap, extraArgs);\n\n  return {\n    ...results,\n    args: Object.fromEntries(\n      Object.entries(results.args).map(([key, value]) => {\n        if (extraArgs[key]) {\n          if (typeof value === 'string') {\n            if (!['true', 'false'].includes(value)) {\n              throw new CommandError(\n                'BAD_ARGS',\n                `Invalid boolean argument: ${key}=${value}. Expected one of: true, false`\n              );\n            }\n            return [key, value === 'true'];\n          }\n        }\n        return [key, value];\n      })\n    ),\n  };\n}\n\nexport function _resolveStringOrBooleanArgs(arg: Spec, args: string[]) {\n  // Default project root, if a custom one is defined then it will overwrite this.\n  let projectRoot: string = '.';\n  // The resolved arguments.\n  const settings: Record<string, string | boolean | undefined> = {};\n\n  // Create a list of possible arguments, this will filter out aliases.\n  const possibleArgs = Object.entries(arg)\n    .filter(([, value]) => typeof value !== 'string')\n    .map(([key]) => key);\n\n  // Loop over arguments in reverse order so we can resolve if a value belongs to a flag.\n  for (let i = args.length - 1; i > -1; i--) {\n    const value = args[i];\n    // At this point we should have converted all aliases to fully qualified arguments.\n    if (value.startsWith('--')) {\n      // If we ever find an argument then it must be a boolean because we are checking in reverse\n      // and removing arguments from the array if we find a string.\n      // We don't override arguments that are already set\n      if (!(value in settings)) {\n        settings[value] = true;\n      }\n    } else {\n      // Get the previous argument in the array.\n      const nextValue = i > 0 ? args[i - 1] : null;\n      if (nextValue && possibleArgs.includes(nextValue)) {\n        // We don't override arguments that are already set\n        if (!(nextValue in settings)) {\n          settings[nextValue] = value;\n        }\n        i--;\n      } else if (\n        // If the last value is not a flag and it doesn't have a recognized flag before it (instead having a string value or nothing)\n        // then it must be the project root.\n        i ===\n        args.length - 1\n      ) {\n        projectRoot = value;\n      } else {\n        // This will asserts if two strings are passed in a row and not at the end of the line.\n        throw new CommandError('BAD_ARGS', `Unknown argument: ${value}`);\n      }\n    }\n  }\n\n  return {\n    args: settings,\n    projectRoot,\n  };\n}\n\n/** Convert all aliases to fully qualified flag names. */\nexport function collapseAliases(arg: Spec, args: string[]): string[] {\n  const aliasMap = getAliasTuples(arg);\n\n  for (const [arg, alias] of aliasMap) {\n    args = replaceValue(args, arg, alias);\n  }\n\n  // Assert if there are duplicate flags after we collapse the aliases.\n  assertDuplicateArgs(args, aliasMap);\n  return args;\n}\n\n/** Assert that the spec has unknown arguments. */\nexport function assertUnknownArgs(arg: Spec, args: string[]) {\n  const allowedArgs = Object.keys(arg);\n  const unknownArgs = args.filter((arg) => !allowedArgs.includes(arg) && arg.startsWith('-'));\n  if (unknownArgs.length > 0) {\n    throw new CommandError(`Unknown arguments: ${unknownArgs.join(', ')}`);\n  }\n}\n\nfunction getAliasTuples(arg: Spec): [string, string][] {\n  return Object.entries(arg).filter(([, value]) => typeof value === 'string') as [string, string][];\n}\n\n/** Asserts that a duplicate flag has been used, this naively throws without knowing if an alias or flag were used as the duplicate. */\nexport function assertDuplicateArgs(args: string[], argNameAliasTuple: [string, string][]) {\n  for (const [argName, argNameAlias] of argNameAliasTuple) {\n    if (args.filter((a) => [argName, argNameAlias].includes(a)).length > 1) {\n      throw new CommandError(\n        'BAD_ARGS',\n        `Can only provide one instance of ${argName} or ${argNameAlias}`\n      );\n    }\n  }\n}\n\nexport function assertNonBooleanArg(argName: string, arg: any): asserts arg is string | string[] {\n  if (arg == null || typeof arg === 'boolean') {\n    throw new CommandError('BAD_ARGS', `Expected input for arg ${argName}`);\n  }\n}\n"], "names": ["_resolveStringOrBooleanArgs", "assertDuplicateArgs", "assertNonBooleanArg", "assertUnknownArgs", "collapseAliases", "resolveCustomBooleanArgsAsync", "resolveStringOrBooleanArgsAsync", "splitArgs", "args", "result", "arg", "startsWith", "key", "props", "split", "push", "length", "join", "rawMap", "extraArgs", "results", "Object", "fromEntries", "entries", "map", "value", "includes", "CommandError", "projectRoot", "settings", "<PERSON><PERSON>rgs", "filter", "i", "nextValue", "aliasMap", "get<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "alias", "replaceValue", "<PERSON><PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON><PERSON>", "argNameAliasTuple", "argName", "argNameAlias", "a"], "mappings": ";;;;;;;;;;;IA2FgBA,2BAA2B;eAA3BA;;IA8EAC,mBAAmB;eAAnBA;;IAWAC,mBAAmB;eAAnBA;;IAxBAC,iBAAiB;eAAjBA;;IAbAC,eAAe;eAAfA;;IAhFMC,6BAA6B;eAA7BA;;IA/BAC,+BAA+B;eAA/BA;;;uBA9BO;wBACA;AAE7B,+FAA+F,GAC/F,SAASC,UAAUC,IAAc;IAC/B,MAAMC,SAAmB,EAAE;IAE3B,KAAK,MAAMC,OAAOF,KAAM;QACtB,IAAIE,IAAIC,UAAU,CAAC,MAAM;YACvB,MAAM,CAACC,KAAK,GAAGC,MAAM,GAAGH,IAAII,KAAK,CAAC;YAClCL,OAAOM,IAAI,CAACH;YACZ,IAAIC,MAAMG,MAAM,EAAE;gBAChBP,OAAOM,IAAI,CAACF,MAAMI,IAAI,CAAC;YACzB;QACF,OAAO;YACLR,OAAOM,IAAI,CAACL;QACd;IACF;IAEA,OAAOD;AACT;AAUO,eAAeH,gCACpBE,IAAc,EACdU,MAAgB,EAChBC,SAAmB;IAEnBX,OAAOD,UAAUC;IAEjB,+BAA+B;IAC/BL,kBACE;QACE,GAAGe,MAAM;QACT,GAAGC,SAAS;IACd,GACAX;IAGF,mDAAmD;IACnDA,OAAOJ,gBAAgBe,WAAWX;IAElC,uEAAuE;IACvE,OAAOR,4BAA4B;QAAE,GAAGkB,MAAM;QAAE,GAAGC,SAAS;IAAC,GAAGX;AAClE;AAUO,eAAeH,8BACpBG,IAAc,EACdU,MAAgB,EAChBC,SAAmB;IAEnB,MAAMC,UAAU,MAAMd,gCAAgCE,MAAMU,QAAQC;IAEpE,OAAO;QACL,GAAGC,OAAO;QACVZ,MAAMa,OAAOC,WAAW,CACtBD,OAAOE,OAAO,CAACH,QAAQZ,IAAI,EAAEgB,GAAG,CAAC,CAAC,CAACZ,KAAKa,MAAM;YAC5C,IAAIN,SAAS,CAACP,IAAI,EAAE;gBAClB,IAAI,OAAOa,UAAU,UAAU;oBAC7B,IAAI,CAAC;wBAAC;wBAAQ;qBAAQ,CAACC,QAAQ,CAACD,QAAQ;wBACtC,MAAM,IAAIE,oBAAY,CACpB,YACA,CAAC,0BAA0B,EAAEf,IAAI,CAAC,EAAEa,MAAM,8BAA8B,CAAC;oBAE7E;oBACA,OAAO;wBAACb;wBAAKa,UAAU;qBAAO;gBAChC;YACF;YACA,OAAO;gBAACb;gBAAKa;aAAM;QACrB;IAEJ;AACF;AAEO,SAASzB,4BAA4BU,GAAS,EAAEF,IAAc;IACnE,gFAAgF;IAChF,IAAIoB,cAAsB;IAC1B,0BAA0B;IAC1B,MAAMC,WAAyD,CAAC;IAEhE,qEAAqE;IACrE,MAAMC,eAAeT,OAAOE,OAAO,CAACb,KACjCqB,MAAM,CAAC,CAAC,GAAGN,MAAM,GAAK,OAAOA,UAAU,UACvCD,GAAG,CAAC,CAAC,CAACZ,IAAI,GAAKA;IAElB,uFAAuF;IACvF,IAAK,IAAIoB,IAAIxB,KAAKQ,MAAM,GAAG,GAAGgB,IAAI,CAAC,GAAGA,IAAK;QACzC,MAAMP,QAAQjB,IAAI,CAACwB,EAAE;QACrB,mFAAmF;QACnF,IAAIP,MAAMd,UAAU,CAAC,OAAO;YAC1B,2FAA2F;YAC3F,6DAA6D;YAC7D,mDAAmD;YACnD,IAAI,CAAEc,CAAAA,SAASI,QAAO,GAAI;gBACxBA,QAAQ,CAACJ,MAAM,GAAG;YACpB;QACF,OAAO;YACL,0CAA0C;YAC1C,MAAMQ,YAAYD,IAAI,IAAIxB,IAAI,CAACwB,IAAI,EAAE,GAAG;YACxC,IAAIC,aAAaH,aAAaJ,QAAQ,CAACO,YAAY;gBACjD,mDAAmD;gBACnD,IAAI,CAAEA,CAAAA,aAAaJ,QAAO,GAAI;oBAC5BA,QAAQ,CAACI,UAAU,GAAGR;gBACxB;gBACAO;YACF,OAAO,IACL,6HAA6H;YAC7H,oCAAoC;YACpCA,MACAxB,KAAKQ,MAAM,GAAG,GACd;gBACAY,cAAcH;YAChB,OAAO;gBACL,uFAAuF;gBACvF,MAAM,IAAIE,oBAAY,CAAC,YAAY,CAAC,kBAAkB,EAAEF,OAAO;YACjE;QACF;IACF;IAEA,OAAO;QACLjB,MAAMqB;QACND;IACF;AACF;AAGO,SAASxB,gBAAgBM,GAAS,EAAEF,IAAc;IACvD,MAAM0B,WAAWC,eAAezB;IAEhC,KAAK,MAAM,CAACA,KAAK0B,MAAM,IAAIF,SAAU;QACnC1B,OAAO6B,IAAAA,mBAAY,EAAC7B,MAAME,KAAK0B;IACjC;IAEA,qEAAqE;IACrEnC,oBAAoBO,MAAM0B;IAC1B,OAAO1B;AACT;AAGO,SAASL,kBAAkBO,GAAS,EAAEF,IAAc;IACzD,MAAM8B,cAAcjB,OAAOkB,IAAI,CAAC7B;IAChC,MAAM8B,cAAchC,KAAKuB,MAAM,CAAC,CAACrB,MAAQ,CAAC4B,YAAYZ,QAAQ,CAAChB,QAAQA,IAAIC,UAAU,CAAC;IACtF,IAAI6B,YAAYxB,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAIW,oBAAY,CAAC,CAAC,mBAAmB,EAAEa,YAAYvB,IAAI,CAAC,OAAO;IACvE;AACF;AAEA,SAASkB,eAAezB,GAAS;IAC/B,OAAOW,OAAOE,OAAO,CAACb,KAAKqB,MAAM,CAAC,CAAC,GAAGN,MAAM,GAAK,OAAOA,UAAU;AACpE;AAGO,SAASxB,oBAAoBO,IAAc,EAAEiC,iBAAqC;IACvF,KAAK,MAAM,CAACC,SAASC,aAAa,IAAIF,kBAAmB;QACvD,IAAIjC,KAAKuB,MAAM,CAAC,CAACa,IAAM;gBAACF;gBAASC;aAAa,CAACjB,QAAQ,CAACkB,IAAI5B,MAAM,GAAG,GAAG;YACtE,MAAM,IAAIW,oBAAY,CACpB,YACA,CAAC,iCAAiC,EAAEe,QAAQ,IAAI,EAAEC,cAAc;QAEpE;IACF;AACF;AAEO,SAASzC,oBAAoBwC,OAAe,EAAEhC,GAAQ;IAC3D,IAAIA,OAAO,QAAQ,OAAOA,QAAQ,WAAW;QAC3C,MAAM,IAAIiB,oBAAY,CAAC,YAAY,CAAC,uBAAuB,EAAEe,SAAS;IACxE;AACF"}