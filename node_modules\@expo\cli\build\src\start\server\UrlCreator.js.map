{"version": 3, "sources": ["../../../../src/start/server/UrlCreator.ts"], "sourcesContent": ["import assert from 'assert';\nimport { URL } from 'url';\n\nimport * as Log from '../../log';\nimport { getIpAddress } from '../../utils/ip';\n\nconst debug = require('debug')('expo:start:server:urlCreator') as typeof console.log;\n\nexport interface CreateURLOptions {\n  /** URL scheme to use when opening apps in custom runtimes. */\n  scheme?: string | null;\n  /** Type of dev server host to use. */\n  hostType?: 'localhost' | 'lan' | 'tunnel';\n  /** Requested hostname. */\n  hostname?: string | null;\n}\n\ninterface UrlComponents {\n  port: string;\n  hostname: string;\n  protocol: string;\n}\nexport class UrlCreator {\n  constructor(\n    public defaults: CreateURLOptions | undefined,\n    private bundlerInfo: { port: number; getTunnelUrl?: () => string | null }\n  ) {}\n\n  /**\n   * Return a URL for the \"loading\" interstitial page that is used to disambiguate which\n   * native runtime to open the dev server with.\n   *\n   * @param options options for creating the URL\n   * @param platform when opening the URL from the CLI to a connected device we can specify the platform as a query parameter, otherwise it will be inferred from the unsafe user agent sniffing.\n   *\n   * @returns URL like `http://localhost:8081/_expo/loading?platform=ios`\n   * @returns URL like `http://localhost:8081/_expo/loading` when no platform is provided.\n   */\n  public constructLoadingUrl(options: CreateURLOptions, platform: string | null): string {\n    const url = new URL('_expo/loading', this.constructUrl({ scheme: 'http', ...options }));\n    if (platform) {\n      url.search = new URLSearchParams({ platform }).toString();\n    }\n    const loadingUrl = url.toString();\n    debug(`Loading URL: ${loadingUrl}`);\n    return loadingUrl;\n  }\n\n  /** Create a URI for launching in a native dev client. Returns `null` when no `scheme` can be resolved. */\n  public constructDevClientUrl(options?: CreateURLOptions): null | string {\n    const protocol = options?.scheme || this.defaults?.scheme;\n\n    if (\n      !protocol ||\n      // Prohibit the use of http(s) in dev client URIs since they'll never be valid.\n      ['http', 'https'].includes(protocol.toLowerCase()) ||\n      // Prohibit the use of `_` characters in the protocol, Node will throw an error when parsing these URLs\n      protocol.includes('_')\n    ) {\n      debug(`Invalid protocol for dev client URL: ${protocol}`);\n      return null;\n    }\n\n    const manifestUrl = this.constructUrl({\n      ...options,\n      scheme: this.defaults?.hostType === 'tunnel' ? 'https' : 'http',\n    });\n    const devClientUrl = `${protocol}://expo-development-client/?url=${encodeURIComponent(\n      manifestUrl\n    )}`;\n    debug(`Dev client URL: ${devClientUrl} -- manifestUrl: ${manifestUrl} -- %O`, options);\n    return devClientUrl;\n  }\n\n  /** Create a generic URL. */\n  public constructUrl(options?: Partial<CreateURLOptions> | null): string {\n    const urlComponents = this.getUrlComponents({\n      ...this.defaults,\n      ...options,\n    });\n    const url = joinUrlComponents(urlComponents);\n    debug(`URL: ${url}`);\n    return url;\n  }\n\n  /** Get the URL components from the Ngrok server URL. */\n  private getTunnelUrlComponents(options: Pick<CreateURLOptions, 'scheme'>): UrlComponents | null {\n    const tunnelUrl = this.bundlerInfo.getTunnelUrl?.();\n    if (!tunnelUrl) {\n      return null;\n    }\n    const parsed = new URL(tunnelUrl);\n    return {\n      port: parsed.port,\n      hostname: parsed.hostname,\n      protocol: options.scheme ?? 'http',\n    };\n  }\n\n  private getUrlComponents(options: CreateURLOptions): UrlComponents {\n    // Proxy comes first.\n    const proxyURL = getProxyUrl();\n    if (proxyURL) {\n      return getUrlComponentsFromProxyUrl(options, proxyURL);\n    }\n\n    // Ngrok.\n    if (options.hostType === 'tunnel') {\n      const components = this.getTunnelUrlComponents(options);\n      if (components) {\n        return components;\n      }\n      Log.warn('Tunnel URL not found (it might not be ready yet), falling back to LAN URL.');\n    } else if (options.hostType === 'localhost' && !options.hostname) {\n      options.hostname = 'localhost';\n    }\n\n    return {\n      hostname: getDefaultHostname(options),\n      port: this.bundlerInfo.port.toString(),\n      protocol: options.scheme ?? 'http',\n    };\n  }\n}\n\nfunction getUrlComponentsFromProxyUrl(\n  options: Pick<CreateURLOptions, 'scheme'>,\n  url: string\n): UrlComponents {\n  const parsedProxyUrl = new URL(url);\n  let protocol = options.scheme ?? 'http';\n  if (parsedProxyUrl.protocol === 'https:') {\n    if (protocol === 'http') {\n      protocol = 'https';\n    }\n    if (!parsedProxyUrl.port) {\n      parsedProxyUrl.port = '443';\n    }\n  }\n  return {\n    port: parsedProxyUrl.port,\n    hostname: parsedProxyUrl.hostname,\n    protocol,\n  };\n}\n\nfunction getDefaultHostname(options: Pick<CreateURLOptions, 'hostname'>) {\n  // TODO: Drop REACT_NATIVE_PACKAGER_HOSTNAME\n  if (process.env.REACT_NATIVE_PACKAGER_HOSTNAME) {\n    return process.env.REACT_NATIVE_PACKAGER_HOSTNAME.trim();\n  } else if (options.hostname === 'localhost') {\n    // Restrict the use of `localhost`\n    // TODO: Note why we do this.\n    return '127.0.0.1';\n  }\n\n  return options.hostname || getIpAddress();\n}\n\nfunction joinUrlComponents({ protocol, hostname, port }: Partial<UrlComponents>): string {\n  assert(hostname, 'hostname cannot be inferred.');\n  const validProtocol = protocol ? `${protocol}://` : '';\n\n  const url = `${validProtocol}${hostname}`;\n\n  if (port) {\n    return url + `:${port}`;\n  }\n\n  return url;\n}\n\n/** @deprecated */\nfunction getProxyUrl(): string | undefined {\n  return process.env.EXPO_PACKAGER_PROXY_URL;\n}\n\n// TODO: Drop the undocumented env variables:\n// REACT_NATIVE_PACKAGER_HOSTNAME\n// EXPO_PACKAGER_PROXY_URL\n"], "names": ["UrlCreator", "debug", "require", "constructor", "defaults", "bundlerInfo", "constructLoadingUrl", "options", "platform", "url", "URL", "constructUrl", "scheme", "search", "URLSearchParams", "toString", "loadingUrl", "constructDevClientUrl", "protocol", "includes", "toLowerCase", "manifestUrl", "hostType", "devClientUrl", "encodeURIComponent", "urlComponents", "getUrlComponents", "joinUrlComponents", "getTunnelUrlComponents", "tunnelUrl", "getTunnelUrl", "parsed", "port", "hostname", "proxyURL", "getProxyUrl", "getUrlComponentsFromProxyUrl", "components", "Log", "warn", "getDefaultHostname", "parsedProxyUrl", "process", "env", "REACT_NATIVE_PACKAGER_HOSTNAME", "trim", "getIpAddress", "assert", "validProtocol", "EXPO_PACKAGER_PROXY_URL"], "mappings": ";;;;+BAsBaA;;;eAAAA;;;;gEAtBM;;;;;;;yBACC;;;;;;6DAEC;oBACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAgBxB,MAAMF;IACXG,YACE,AAAOC,QAAsC,EAC7C,AAAQC,WAAiE,CACzE;aAFOD,WAAAA;aACCC,cAAAA;IACP;IAEH;;;;;;;;;GASC,GACD,AAAOC,oBAAoBC,OAAyB,EAAEC,QAAuB,EAAU;QACrF,MAAMC,MAAM,IAAIC,CAAAA,MAAE,KAAC,CAAC,iBAAiB,IAAI,CAACC,YAAY,CAAC;YAAEC,QAAQ;YAAQ,GAAGL,OAAO;QAAC;QACpF,IAAIC,UAAU;YACZC,IAAII,MAAM,GAAG,IAAIC,gBAAgB;gBAAEN;YAAS,GAAGO,QAAQ;QACzD;QACA,MAAMC,aAAaP,IAAIM,QAAQ;QAC/Bd,MAAM,CAAC,aAAa,EAAEe,YAAY;QAClC,OAAOA;IACT;IAEA,wGAAwG,GACxG,AAAOC,sBAAsBV,OAA0B,EAAiB;YAClC,gBAe1B;QAfV,MAAMW,WAAWX,CAAAA,2BAAAA,QAASK,MAAM,OAAI,iBAAA,IAAI,CAACR,QAAQ,qBAAb,eAAeQ,MAAM;QAEzD,IACE,CAACM,YACD,+EAA+E;QAC/E;YAAC;YAAQ;SAAQ,CAACC,QAAQ,CAACD,SAASE,WAAW,OAC/C,uGAAuG;QACvGF,SAASC,QAAQ,CAAC,MAClB;YACAlB,MAAM,CAAC,qCAAqC,EAAEiB,UAAU;YACxD,OAAO;QACT;QAEA,MAAMG,cAAc,IAAI,CAACV,YAAY,CAAC;YACpC,GAAGJ,OAAO;YACVK,QAAQ,EAAA,kBAAA,IAAI,CAACR,QAAQ,qBAAb,gBAAekB,QAAQ,MAAK,WAAW,UAAU;QAC3D;QACA,MAAMC,eAAe,GAAGL,SAAS,gCAAgC,EAAEM,mBACjEH,cACC;QACHpB,MAAM,CAAC,gBAAgB,EAAEsB,aAAa,iBAAiB,EAAEF,YAAY,MAAM,CAAC,EAAEd;QAC9E,OAAOgB;IACT;IAEA,0BAA0B,GAC1B,AAAOZ,aAAaJ,OAA0C,EAAU;QACtE,MAAMkB,gBAAgB,IAAI,CAACC,gBAAgB,CAAC;YAC1C,GAAG,IAAI,CAACtB,QAAQ;YAChB,GAAGG,OAAO;QACZ;QACA,MAAME,MAAMkB,kBAAkBF;QAC9BxB,MAAM,CAAC,KAAK,EAAEQ,KAAK;QACnB,OAAOA;IACT;IAEA,sDAAsD,GACtD,AAAQmB,uBAAuBrB,OAAyC,EAAwB;QAC9F,MAAMsB,YAAY,IAAI,CAACxB,WAAW,CAACyB,YAAY,oBAA7B,IAAI,CAACzB,WAAW,CAACyB,YAAY,MAA7B,IAAI,CAACzB,WAAW;QAClC,IAAI,CAACwB,WAAW;YACd,OAAO;QACT;QACA,MAAME,SAAS,IAAIrB,CAAAA,MAAE,KAAC,CAACmB;QACvB,OAAO;YACLG,MAAMD,OAAOC,IAAI;YACjBC,UAAUF,OAAOE,QAAQ;YACzBf,UAAUX,QAAQK,MAAM,IAAI;QAC9B;IACF;IAEQc,iBAAiBnB,OAAyB,EAAiB;QACjE,qBAAqB;QACrB,MAAM2B,WAAWC;QACjB,IAAID,UAAU;YACZ,OAAOE,6BAA6B7B,SAAS2B;QAC/C;QAEA,SAAS;QACT,IAAI3B,QAAQe,QAAQ,KAAK,UAAU;YACjC,MAAMe,aAAa,IAAI,CAACT,sBAAsB,CAACrB;YAC/C,IAAI8B,YAAY;gBACd,OAAOA;YACT;YACAC,KAAIC,IAAI,CAAC;QACX,OAAO,IAAIhC,QAAQe,QAAQ,KAAK,eAAe,CAACf,QAAQ0B,QAAQ,EAAE;YAChE1B,QAAQ0B,QAAQ,GAAG;QACrB;QAEA,OAAO;YACLA,UAAUO,mBAAmBjC;YAC7ByB,MAAM,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAACjB,QAAQ;YACpCG,UAAUX,QAAQK,MAAM,IAAI;QAC9B;IACF;AACF;AAEA,SAASwB,6BACP7B,OAAyC,EACzCE,GAAW;IAEX,MAAMgC,iBAAiB,IAAI/B,CAAAA,MAAE,KAAC,CAACD;IAC/B,IAAIS,WAAWX,QAAQK,MAAM,IAAI;IACjC,IAAI6B,eAAevB,QAAQ,KAAK,UAAU;QACxC,IAAIA,aAAa,QAAQ;YACvBA,WAAW;QACb;QACA,IAAI,CAACuB,eAAeT,IAAI,EAAE;YACxBS,eAAeT,IAAI,GAAG;QACxB;IACF;IACA,OAAO;QACLA,MAAMS,eAAeT,IAAI;QACzBC,UAAUQ,eAAeR,QAAQ;QACjCf;IACF;AACF;AAEA,SAASsB,mBAAmBjC,OAA2C;IACrE,4CAA4C;IAC5C,IAAImC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C,OAAOF,QAAQC,GAAG,CAACC,8BAA8B,CAACC,IAAI;IACxD,OAAO,IAAItC,QAAQ0B,QAAQ,KAAK,aAAa;QAC3C,kCAAkC;QAClC,6BAA6B;QAC7B,OAAO;IACT;IAEA,OAAO1B,QAAQ0B,QAAQ,IAAIa,IAAAA,gBAAY;AACzC;AAEA,SAASnB,kBAAkB,EAAET,QAAQ,EAAEe,QAAQ,EAAED,IAAI,EAA0B;IAC7Ee,IAAAA,iBAAM,EAACd,UAAU;IACjB,MAAMe,gBAAgB9B,WAAW,GAAGA,SAAS,GAAG,CAAC,GAAG;IAEpD,MAAMT,MAAM,GAAGuC,gBAAgBf,UAAU;IAEzC,IAAID,MAAM;QACR,OAAOvB,MAAM,CAAC,CAAC,EAAEuB,MAAM;IACzB;IAEA,OAAOvB;AACT;AAEA,gBAAgB,GAChB,SAAS0B;IACP,OAAOO,QAAQC,GAAG,CAACM,uBAAuB;AAC5C,EAEA,6CAA6C;CAC7C,iCAAiC;CACjC,0BAA0B"}