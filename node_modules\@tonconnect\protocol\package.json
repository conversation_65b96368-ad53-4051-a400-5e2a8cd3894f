{"name": "@tonconnect/protocol", "version": "2.2.6", "repository": {"type": "git", "url": "git+https://github.com/ton-connect/sdk.git"}, "homepage": "https://github.com/ton-connect/sdk/tree/main/packages/protocol", "bugs": {"url": "https://github.com/ton-connect/sdk/issues"}, "keywords": ["TON", "Wallet", "ton-connect", "Connect", "<PERSON><PERSON><PERSON><PERSON>"], "author": "tonconnect", "license": "Apache-2.0", "scripts": {"build": "npx rimraf types-dist && npx rimraf lib && npx rollup -c rollup.config.mjs && tsc && api-extractor run && npx rimraf types-dist", "test": "jest"}, "files": ["lib"], "main": "./lib/cjs/index.cjs", "module": "./lib/esm/index.mjs", "types": "./lib/types/index.d.ts", "exports": {".": {"types": "./lib/types/index.d.ts", "require": "./lib/cjs/index.cjs", "import": "./lib/esm/index.mjs", "default": "./lib/cjs/index.cjs"}}, "dependencies": {"tweetnacl-util": "^0.15.1", "tweetnacl": "^1.0.3"}, "devDependencies": {"jest": "^29.2.2", "ts-jest": "^29.0.3", "@types/jest": "^29.2.0", "rollup": "^3.18.0", "@rollup/plugin-typescript": "^11.0.0"}, "nx": {"tags": ["scope:protocol"]}, "typedoc": {"entryPoint": "./src/index.ts"}}