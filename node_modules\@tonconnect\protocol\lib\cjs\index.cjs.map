{"version": 3, "file": "index.cjs", "sources": ["../../src/models/wallet-message/wallet-event/connect-event.ts", "../../src/models/wallet-message/wallet-response/send-transaction-rpc-response.ts", "../../src/models/wallet-message/wallet-response/sign-data-rpc-response.ts", "../../src/models/wallet-message/wallet-response/disconnect-rpc-response.ts", "../../src/models/CHAIN.ts", "../../src/utils/base64.ts", "../../src/utils/binary.ts", "../../src/utils/web-api.ts", "../../src/crypto/session-crypto.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null], "names": ["CONNECT_EVENT_ERROR_CODES", "CONNECT_ITEM_ERROR_CODES", "SEND_TRANSACTION_ERROR_CODES", "SIGN_DATA_ERROR_CODES", "DISCONNECT_ERROR_CODES", "CHAIN", "nacl"], "mappings": ";;;;;;;;;;;;AAuBYA,2CAQX;AARD,CAAA,UAAY,yBAAyB,EAAA;AACjC,IAAA,yBAAA,CAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,yBAAA,CAAA,yBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACrB,IAAA,yBAAA,CAAA,yBAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,0BAA4B,CAAA;AAC5B,IAAA,yBAAA,CAAA,yBAAA,CAAA,wBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,wBAA0B,CAAA;AAC1B,IAAA,yBAAA,CAAA,yBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,yBAAA,CAAA,yBAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,yBAAA,CAAA,yBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EARWA,iCAAyB,KAAzBA,iCAAyB,GAQpC,EAAA,CAAA,CAAA,CAAA;AA6BWC,0CAGX;AAHD,CAAA,UAAY,wBAAwB,EAAA;AAChC,IAAA,wBAAA,CAAA,wBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,wBAAA,CAAA,wBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EAHWA,gCAAwB,KAAxBA,gCAAwB,GAGnC,EAAA,CAAA,CAAA;;AC/CWC,8CAMX;AAND,CAAA,UAAY,4BAA4B,EAAA;AACpC,IAAA,4BAAA,CAAA,4BAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,4BAAA,CAAA,4BAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACrB,IAAA,4BAAA,CAAA,4BAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,4BAAA,CAAA,4BAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,4BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EANWA,oCAA4B,KAA5BA,oCAA4B,GAMvC,EAAA,CAAA,CAAA;;ACLWC,uCAMX;AAND,CAAA,UAAY,qBAAqB,EAAA;AAC7B,IAAA,qBAAA,CAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACrB,IAAA,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,qBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,qBAAA,CAAA,qBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EANWA,6BAAqB,KAArBA,6BAAqB,GAMhC,EAAA,CAAA,CAAA;;ACTWC,wCAKX;AALD,CAAA,UAAY,sBAAsB,EAAA;AAC9B,IAAA,sBAAA,CAAA,sBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB,CAAA;AACjB,IAAA,sBAAA,CAAA,sBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB,CAAA;AACrB,IAAA,sBAAA,CAAA,sBAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,sBAAA,CAAA,sBAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC9B,CAAC,EALWA,8BAAsB,KAAtBA,8BAAsB,GAKjC,EAAA,CAAA,CAAA;;ACnBWC,uBAGX;AAHD,CAAA,UAAY,KAAK,EAAA;AACb,IAAA,KAAA,CAAA,SAAA,CAAA,GAAA,MAAgB,CAAA;AAChB,IAAA,KAAA,CAAA,SAAA,CAAA,GAAA,IAAc,CAAA;AAClB,CAAC,EAHWA,aAAK,KAALA,aAAK,GAGhB,EAAA,CAAA,CAAA;;ACDD,SAAS,gBAAgB,CAAC,KAAiB,EAAE,OAAgB,EAAA;IACzD,MAAM,OAAO,GAAGC,wBAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACzC,IAAI,CAAC,OAAO,EAAE;AACV,QAAA,OAAO,OAAO,CAAC;AAClB,KAAA;AAED,IAAA,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa,EAAE,OAAgB,EAAA;AACvD,IAAA,IAAI,OAAO,EAAE;AACT,QAAA,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACrC,KAAA;AAED,IAAA,OAAOA,wBAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,MAAM,CAAC,KAAmC,EAAE,OAAO,GAAG,KAAK,EAAA;AAChE,IAAA,IAAI,UAAsB,CAAC;IAE3B,IAAI,KAAK,YAAY,UAAU,EAAE;QAC7B,UAAU,GAAG,KAAK,CAAC;AACtB,KAAA;AAAM,SAAA;AACH,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC3B,YAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACjC,SAAA;AAED,QAAA,UAAU,GAAGA,wBAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACvC,KAAA;AAED,IAAA,OAAO,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,MAAM,CACX,KAAa,EACb,OAAO,GAAG,KAAK,EAAA;IAMf,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE7D,OAAO;QACH,QAAQ,GAAA;AACJ,YAAA,OAAOA,wBAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;SAC7C;QACD,QAAQ,GAAA;YACJ,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CAACA,wBAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAM,CAAC;AAC9D,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;SACJ;QACD,YAAY,GAAA;AACR,YAAA,OAAO,iBAAiB,CAAC;SAC5B;KACJ,CAAC;AACN,CAAC;AAEY,MAAA,MAAM,GAAG;IAClB,MAAM;IACN,MAAM;;;AChEM,SAAA,iBAAiB,CAAC,OAAmB,EAAE,OAAmB,EAAA;AACtE,IAAA,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACpE,IAAA,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACzB,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,OAAO,WAAW,CAAC;AACvB,CAAC;AAEe,SAAA,kBAAkB,CAAC,KAAiB,EAAE,KAAa,EAAA;AAC/D,IAAA,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,QAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC7C,KAAA;IAED,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrC,IAAA,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAClC,CAAC;AAEK,SAAU,WAAW,CAAC,SAAqB,EAAA;IAC7C,IAAI,SAAS,GAAG,EAAE,CAAC;AACnB,IAAA,SAAS,CAAC,OAAO,CAAC,IAAI,IAAG;QACrB,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,SAAS,CAAC;AACrB,CAAC;AACK,SAAU,cAAc,CAAC,SAAiB,EAAA;AAC5C,IAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAC5B,QAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,SAAS,CAAA,cAAA,CAAgB,CAAC,CAAC;AAChE,KAAA;IACD,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACpD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1C,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3D,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAClB;;SCjCgB,MAAM,GAAA;IAClB,QACI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,EAC7F;AACN;;MCAa,aAAa,CAAA;AAOtB,IAAA,WAAA,CAAY,OAAiB,EAAA;QANZ,IAAW,CAAA,WAAA,GAAG,EAAE,CAAC;QAO9B,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACtF,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;KACxD;IAEO,aAAa,GAAA;AACjB,QAAA,OAAOA,0BAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;KAC7B;AAEO,IAAA,uBAAuB,CAAC,OAAgB,EAAA;QAC5C,OAAO;AACH,YAAA,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5C,YAAA,SAAS,EAAE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;SAC/C,CAAC;KACL;IAEO,WAAW,GAAA;QACf,OAAOA,0BAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC7C;IAEM,OAAO,CAAC,OAAe,EAAE,iBAA6B,EAAA;QACzD,MAAM,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACzD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACjC,QAAA,MAAM,SAAS,GAAGA,0BAAI,CAAC,GAAG,CACtB,cAAc,EACd,KAAK,EACL,iBAAiB,EACjB,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,CAAC;AACF,QAAA,OAAO,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;KAC9C;IAEM,OAAO,CAAC,OAAmB,EAAE,eAA2B,EAAA;AAC3D,QAAA,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/E,MAAM,SAAS,GAAGA,0BAAI,CAAC,GAAG,CAAC,IAAI,CAC3B,eAAe,EACf,KAAK,EACL,eAAe,EACf,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,MAAM,IAAI,KAAK,CACX,CAAA,8BAAA,EAAiC,OAAO,CAAC,QAAQ,EAAE,CAAA,mBAAA,EAAsB,eAAe,CAAC,QAAQ,EAAE,uBAAuB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,0BAA0B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA,CAAE,CAC3N,CAAC;AACL,SAAA;QAED,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC9C;IAEM,gBAAgB,GAAA;QACnB,OAAO;YACH,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YAC9C,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;SACjD,CAAC;KACL;AACJ;;;;;;;;;;"}