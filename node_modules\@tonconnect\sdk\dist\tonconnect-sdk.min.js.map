{"version": 3, "file": "tonconnect-sdk.min.js", "mappings": "wIAEa,EAAAA,qBAAuB,O,yGCFpC,cAAS,mFAAAC,iBAAiB,G,0GCA1B,gBAKA,MAAaA,UAA0B,EAAAC,gBACrBC,WACV,MAAO,wCACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMR,EAAkBS,UAClD,EATJ,qB,sGCLA,cAAS,+EAAAC,aAAa,G,sGCAtB,gBAKA,MAAaA,UAAsB,EAAAT,gBACjBC,WACV,MAAO,oCACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAME,EAAcD,UAC9C,EATJ,iB,0GCLA,gBAKA,MAAaE,UAA0B,EAAAV,gBACrBC,WACV,MAAO,6CACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMG,EAAkBF,UAClD,EATJ,qB,ijBCLA,aACA,aACA,aACA,aACA,YACA,aACA,cAAS,iFAAAR,eAAe,IACxB,cAAS,8EAAAW,YAAY,G,yGCPrB,aAAS,kFAAAC,gBAAgB,G,kHCAzB,gBAKA,MAAaC,UAAkC,EAAAb,gBAC7BC,WACV,MAAO,6KACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMM,EAA0BL,UAC1D,EATJ,6B,8GCLA,gBAKA,MAAaM,UAA8B,EAAAd,gBACzBC,WACV,MAAO,qNACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMO,EAAsBN,UACtD,EATJ,yB,wGCLA,gBAKA,MAAaI,UAAyB,EAAAZ,gBACpBC,WACV,MAAO,wCACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMK,EAAiBJ,UACjD,EATJ,oB,ygBCLA,Y,ygBCAA,aACA,W,wGCDA,gBAKA,MAAaO,UAAwB,EAAAf,gBACnBC,WACV,MAAO,wCACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMQ,EAAgBP,UAChD,EATJ,mB,yHCLA,cAAS,iFAAAO,eAAe,IACxB,aAAS,iFAAAC,eAAe,G,uGCDxB,gBAKA,MAAaA,UAAwB,EAAAhB,gBACnBC,WACV,MAAO,2EACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMS,EAAgBR,UAChD,EATJ,mB,kHCLA,cAAS,2FAAAS,yBAAyB,G,kHCAlC,gBAKA,MAAaA,UAAkC,EAAAjB,gBAC7BC,WACV,MAAO,iHACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMU,EAA0BT,UAC1D,EATJ,6B,sGCFA,MAAaR,UAAwBkB,MAOjChB,YACIiB,EACAC,GAIAhB,MAAMe,EAASC,GAEfb,KAAKY,QAAU,GAAGnB,EAAgBqB,UAAUd,KAAKL,YAAYoB,OACzDf,KAAKN,KAAO,KAAOM,KAAKN,KAAO,KAChCkB,EAAU,KAAOA,EAAU,KAE9Bd,OAAOC,eAAeC,KAAMP,EAAgBQ,UAChD,CAjBcP,WACV,MAAO,EACX,EALJ,oBACmB,EAAAoB,OAAS,yB,qGCJ5B,gBAKA,MAAaV,UAAqB,EAAAX,gBAC9BE,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMI,EAAaH,UAC7C,EALJ,gB,uKCLA,cAAS,6FAAAe,2BAA2B,IACpC,cAAS,yFAAAC,uBAAuB,IAChC,cAAS,wFAAAC,sBAAsB,G,oHCF/B,gBAKA,MAAaF,UAAoC,EAAAvB,gBAC/BC,WACV,MAAO,iIACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMgB,EAA4Bf,UAC5D,EATJ,+B,gHCLA,gBAKA,MAAagB,UAAgC,EAAAxB,gBAC3BC,WACV,MAAO,kFACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMiB,EAAwBhB,UACxD,EATJ,2B,+GCLA,gBAKA,MAAaiB,UAA+B,EAAAzB,gBAC1BC,WACV,MAAO,8FACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMkB,EAAuBjB,UACvD,EATJ,0B,qHCLA,gBAKA,MAAakB,UAAqC,EAAA1B,gBAChCC,WACV,MAAO,kDACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMmB,EAA6BlB,UAC7D,EATJ,gC,0GCLA,gBAKA,MAAamB,UAA0B,EAAA3B,gBACrBC,WACV,MAAO,oDACX,CAEAC,eAAeC,GACXC,SAASD,GAETE,OAAOC,eAAeC,KAAMoB,EAAkBnB,UAClD,EATJ,qB,0GCLA,cAAS,mFAAAmB,iBAAiB,G,upCCA1B,aACA,aACA,aAEA,cAAS,yEAAAC,UAAU,IACnB,cAAS,oFAAAC,kBAAkB,IAO3B,YACI,8FAAAC,4BAA4B,IAC5B,4FAAAC,0BAA0B,IAC1B,gGAAAC,8BAA8B,IAC9B,uGAAAC,qCAAqC,IACrC,qGAAAC,mCAAmC,IACnC,yGAAAC,uCAAuC,IACvC,0FAAAC,wBAAwB,IACxB,wGAAAC,sCAAsC,IACtC,qGAAAC,mCAAmC,IACnC,8FAAAC,4BAA4B,IAC5B,2FAAAC,yBAAyB,IACzB,4FAAAC,0BAA0B,IAC1B,mFAAAC,iBAAiB,IA2BrB,cAAS,wFAAAC,sBAAsB,IAE/B,cACI,uEAAAC,KAAK,IAUL,0FAAAC,wBAAwB,IACxB,2FAAAC,yBAAyB,IACzB,8FAAAC,4BAA4B,IAEhC,cAAS,uFAAAC,qBAAqB,IAC9B,cAAS,+EAAAC,aAAa,IAAE,6FAAAC,2BAA2B,G,ygBCtEnD,WACA,Y,qlBCDA,aACA,Y,qSCMA,cAQI,+FAAAC,6BAA6B,IAC7B,+FAAAC,6BAA6B,IAC7B,wFAAAC,sBAAsB,IACtB,oFAAAC,kBAAkB,IAClB,sFAAAC,oBAAoB,G,iHCExB,sCACIC,GAEA,MAAO,gBAAiBA,CAC5B,C,4BC6HA,SAAgBL,EACZK,GAEA,OAAOH,EAAuBG,IAAUA,EAAMC,QAClD,CAgBA,SAAgBJ,EAAuBG,GACnC,MAAO,gBAAiBA,CAC5B,C,6LAtBA,kCAUA,yCACIA,GAEA,OAAOL,EAA8BK,IAAUA,EAAME,QACzD,EAMA,2BAQA,8BAAmCF,GAC/B,MAAO,cAAeA,CAC1B,EAMA,gCAAqCA,GACjC,MAAO,gBAAiBA,CAC5B,C,4GC5LA,gBACA,UACA,UAEA,UACA,UAEMG,EACF,CACI,CAAC,EAAAb,0BAA0Bc,eAAgB,EAAAjD,aAC3C,CAAC,EAAAmC,0BAA0Be,oBAAqB,EAAAjD,iBAChD,CAAC,EAAAkC,0BAA0BgB,mBAAoB,EAAA/C,gBAC/C,CAAC,EAAA+B,0BAA0BiB,mBAAoB,EAAA/C,gBAC/C,CAAC,EAAA8B,0BAA0BkB,0BAA2B,EAAAlD,sBACtD,CAAC,EAAAgC,0BAA0BmB,wBAAyB,EAAApD,2BAe/C,EAAAqD,oBAAsB,IAZnC,MACIC,WAAWC,GACP,IAAIC,EAA2C,EAAA1D,aAM/C,OAJIyD,EAAME,QAAQX,IACdU,EAAmBV,EAAwBS,EAAME,OAAS,EAAA3D,cAGvD,IAAI0D,EAAiBD,EAAMjD,QACtC,E,gGCjBJ,kBAOWoD,QACHC,GAEA,MAAO,UAAWA,CACtB,E,8GCpBJ,gBAOA,UACA,UAEA,UAGMC,EAA4F,CAC9F,CAAC,EAAA1B,6BAA6Ba,eAAgB,EAAAjD,aAC9C,CAAC,EAAAoC,6BAA6Bc,oBAAqB,EAAAjD,iBACnD,CAAC,EAAAmC,6BAA6Be,mBAAoB,EAAA/C,gBAClD,CAAC,EAAAgC,6BAA6BgB,mBAAoB,EAAA/C,iBAGtD,MAAM0D,UAA8B,EAAAC,UAChCC,oBACIC,GAEA,MAAO,CACHC,OAAQ,kBACRC,OAAQ,CAACC,KAAKC,UAAUJ,IAEhC,CAEAK,mBAAmBV,GACf,IAAIH,EAA2C,EAAA1D,aAM/C,MAJI6D,EAASJ,MAAME,QAAQG,IACvBJ,EAAmBI,EAAsBD,EAASJ,MAAME,OAAS,EAAA3D,cAG/D,IAAI0D,EAAiBG,EAASJ,MAAMjD,QAC9C,CAEAgE,uBACIC,GAEA,MAAO,CACHC,IAAKD,EAAYE,OAEzB,EAGS,EAAAC,sBAAwB,IAAIb,C,4aCjDzC,gBACA,UAEA,UAEA,UACA,OACA,QACA,gBACA,UACA,UACA,UACA,UAEA,sBAiDIxE,YACIsF,EACgBC,EACAC,EACRC,EACAC,GAHQ,KAAAH,UAAAA,EACA,KAAAC,UAAAA,EACR,KAAAC,SAAAA,EACA,KAAAC,eAAAA,EArDK,KAAAC,QAAU,SAEV,KAAAC,SAAW,UAEX,KAAAC,iBAAmB,YAEnB,KAAAC,WAAa,IAEb,KAAAC,sBAAwB,IAExB,KAAAC,mBAAqB,IAE9B,KAAAC,aAAc,IAAAC,iBAClB,CAAOC,EAAsBC,IAAqD,EAAD,gCAC7E,MAAMC,EAAoB,CACtBd,UAAWlF,KAAKkF,UAChBI,QAAStF,KAAKsF,QACdH,UAAWnF,KAAKmF,UAChBc,qBAAsBjG,KAAKiG,qBAC3BC,aAAclG,KAAKmG,cAAcC,KAAKpG,MACtCqG,eAAgBrG,KAAKsG,gBAAgBF,KAAKpG,MAC1C8F,OAAQA,EACRC,kBAAmBA,GAEvB,aAqOZ,SAAiCQ,G,yCAC7B,aAAa,IAAAC,UACT,CAAOC,EAASC,EAAQC,IAAiB,EAAD,gC,MACpC,MACMb,GADkB,IAAAc,uBAAsBD,EAAab,QAC5BA,OAE/B,GAAIA,EAAOe,QAEP,YADAH,EAAO,IAAI,EAAAjH,gBAAgB,8BAI/B,MAAMqH,EAAM,IAAIC,KAAI,IAAAC,cAAaT,EAAOrB,UAAWqB,EAAOjB,UAC1DwB,EAAIG,aAAaC,OAAO,YAAaX,EAAOpB,WAE5C,MAAMgC,QAAoBZ,EAAON,qBAAqBmB,iBAKtD,GAJID,GACAL,EAAIG,aAAaC,OAAO,gBAAiBC,GAGzCrB,EAAOe,QAEP,YADAH,EAAO,IAAI,EAAAjH,gBAAgB,8BAI/B,MAAMmG,EAAc,IAAIyB,YAAYP,EAAIQ,YAExC1B,EAAY2B,QAAiBC,GAAiC,EAAD,gCACzD,GAAI1B,EAAOe,QAGP,OAFAjB,EAAY6B,aACZf,EAAO,IAAI,EAAAjH,gBAAgB,8BAI/B,IACI,MAAMiI,QAAoBnB,EAAOL,aAAaN,EAAa4B,GACvDE,IAAgB9B,GAChBA,EAAY6B,QAGZC,GAAeA,IAAgB9B,GAC/Ba,EAAQiB,E,CAEd,MAAOC,GACL/B,EAAY6B,QACZf,EAAOiB,E,CAEf,IACA/B,EAAYgC,OAAS,KACjB,GAAI9B,EAAOe,QAGP,OAFAjB,EAAY6B,aACZf,EAAO,IAAI,EAAAjH,gBAAgB,8BAG/BgH,EAAQb,EAAY,EAExBA,EAAYiC,UAAaC,IACrB,GAAIhC,EAAOe,QAGP,OAFAjB,EAAY6B,aACZf,EAAO,IAAI,EAAAjH,gBAAgB,8BAG/B8G,EAAOF,eAAeyB,EAAM,EAGnB,QAAb,EAAAvB,EAAOT,cAAM,SAAEiC,iBAAiB,SAAS,KACrCnC,EAAY6B,QACZf,EAAO,IAAI,EAAAjH,gBAAgB,6BAA6B,GAEhE,KACA,CAAE+G,QAASD,EAAOR,kBAAmBD,OAAQS,EAAOT,QAE5D,G,CA5SyBkC,CAAkBhC,EACnC,MACOiC,GAA0B,EAAD,gCAC5BA,EAASR,OACb,MA2BAzH,KAAKiG,qBAAuB,IAAI,EAAAiC,yBAAyBjD,EAASC,EACtE,CAzBYiD,cACR,MAAMvC,EAAc5F,KAAK4F,YAAYwC,UACrC,OAAOxC,aAAW,EAAXA,EAAayC,cAAehB,YAAYiB,IACnD,CAEYC,eACR,MAAM3C,EAAc5F,KAAK4F,YAAYwC,UACrC,OAAOxC,aAAW,EAAXA,EAAayC,cAAehB,YAAYiB,IACnD,CAEYE,mBACR,MAAM5C,EAAc5F,KAAK4F,YAAYwC,UACrC,OAAOxC,aAAW,EAAXA,EAAayC,cAAehB,YAAYoB,UACnD,CAcaC,gBAAgB7H,G,+CACnBb,KAAK4F,YAAY+C,OAAO9H,aAAO,EAAPA,EAASiF,OAAQjF,aAAO,EAAPA,EAASkF,kBAC5D,G,CAmBa6C,KACThI,EACAiI,EACAC,EACAC,G,+CAGA,MAAMlI,EAAqE,CAAC,EAChD,iBAAjBkI,EACPlI,EAAQmI,IAAMD,GAEdlI,EAAQmI,IAAMD,aAAY,EAAZA,EAAcC,IAC5BnI,EAAQiF,OAASiD,aAAY,EAAZA,EAAcjD,OAC/BjF,EAAQoI,SAAWF,aAAY,EAAZA,EAAcE,UAGrC,MAAMnC,EAAM,IAAIC,KAAI,IAAAC,cAAahH,KAAKkF,UAAWlF,KAAKuF,WACtDuB,EAAIG,aAAaC,OAAO,YAAalH,KAAKmF,WAC1C2B,EAAIG,aAAaC,OAAO,KAAM2B,GAC9B/B,EAAIG,aAAaC,OAAO,QAAQrG,aAAO,EAAPA,EAASmI,MAAOhJ,KAAKyF,YAAY6B,YACjER,EAAIG,aAAaC,OAAO,QAAS4B,GACjC,MAAMI,EAAO,EAAAC,OAAOC,OAAOxI,SAErB,IAAAyI,iBACIxI,GAAW,EAAD,gCACZ,MAAMoD,QAAiBjE,KAAKsJ,KAAKxC,EAAKoC,EAAMrI,EAAQiF,QAEpD,IAAK7B,EAASsF,GACV,MAAM,IAAI,EAAA9J,gBAAgB,8BAA8BwE,EAASuF,SAEzE,KACA,CACIP,SAA2B,QAAjB,EAAApI,aAAO,EAAPA,EAASoI,gBAAQ,QAAIQ,OAAOC,iBACtCC,QAAS3J,KAAK2F,mBACdG,OAAQjF,aAAO,EAAPA,EAASiF,Q,IAKtB8D,QACH5J,KAAK4F,YAAYiE,UAAUC,OAAMnC,IAAK,IAAAoC,UAAS,wBAAwBpC,MAC3E,CAEaqC,U,+CAEHhK,KAAK4F,YAAYqE,SADQ,EAEnC,G,CAEaxC,Q,+CACHzH,KAAK4F,YAAYiE,UAAUC,OAAMnC,IAAK,IAAAoC,UAAS,wBAAwBpC,MACjF,G,CAEOuC,YAAY9E,GACfpF,KAAKoF,SAAWA,CACpB,CAEO+E,kBAAkB9E,GACrBrF,KAAKqF,eAAiBA,CAC1B,CAEciE,KAAKxC,EAAUoC,EAAcpD,G,yCACvC,MAAM7B,QAAiBmG,MAAMtD,EAAK,CAC9BvC,OAAQ,OACR2E,KAAMA,EACNpD,OAAQA,IAGZ,IAAK7B,EAASsF,GACV,MAAM,IAAI,EAAA9J,gBAAgB,8BAA8BwE,EAASuF,UAGrE,OAAOvF,CACX,G,CAEckC,cAAcP,EAA0B+B,G,yCAClD,GAAI3H,KAAKwI,aAEL,MADA5C,EAAY6B,QACN,IAAI,EAAAhI,gBAAgB,mCAG9B,IAAIO,KAAKmI,QAAT,CAOA,GAAInI,KAAKuI,SAGL,OAFA3C,EAAY6B,SACZ,IAAA4C,UAAS,wBAAwBrK,KAAK0F,uCACzB1F,KAAK4F,YAAYqE,SAASjK,KAAK0F,uBAGhD,MAAM,IAAI,EAAAjG,gBAAgB,8B,CAZtB,IACIO,KAAKqF,eAAesC,E,CACtB,MAAOA,GAAG,CAWpB,G,CAEcrB,gBAAgBqB,G,yCAC1B,GAAIA,EAAE2C,OAAStK,KAAKwF,iBAChB,OAKJ,SAFMxF,KAAKiG,qBAAqBsE,iBAAiB5C,EAAER,aAE/CnH,KAAKuI,SACL,OAGJ,IAAIiC,EACJ,IACIA,EAAwB/F,KAAKgG,MAAM9C,EAAE2C,K,CACvC,MAAO3C,GACL,MAAM,IAAI,EAAAlI,gBAAgB,wCAAwCkI,EAAE2C,O,CAExEtK,KAAKoF,SAASoF,EAClB,G,+aChNJ,gBAaA,UAEA,UACA,UAOA,UAGA,UACA,UACA,UACA,UACA,UAEA,MAAaE,EAoCT/K,YACqBsF,EACA0F,GADA,KAAA1F,QAAAA,EACA,KAAA0F,uBAAAA,EA3BL,KAAAC,KAAO,OAEN,KAAAC,sBAAwB,QAIxB,KAAAC,gBAAkB,IAAIC,IAK/B,KAAAC,QAAuD,KAEvD,KAAAC,QAAgC,KAEhC,KAAAC,gBAAmC,GAEnC,KAAAC,UAAoE,GAE3D,KAAAC,yBAA2B,KAE3B,KAAAC,sBAAwB,IAUrCrL,KAAKsL,kBAAoB,IAAI,EAAAC,wBAAwBtG,EACzD,CA1COuG,mBAAyBvG,G,yCAC5B,MAAMwG,EAA0B,IAAI,EAAAF,wBAAwBtG,GACtDyG,QAAmBD,EAAwBE,oBAEjD,OAAI,IAAAC,yBAAwBF,GACjB,IAAIhB,EAAezF,EAASyG,EAAWG,kBAE3C,IAAInB,EAAezF,EAAS,CAAEC,UAAWwG,EAAWV,QAAQ9F,WACvE,G,CAoCO4G,QACHlL,EACAC,G,MAKA,MAAMkL,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QACnC,QAApB,EAAA9F,KAAK+L,uBAAe,SAAEC,QACtBhM,KAAK+L,gBAAkBA,EAEvB/L,KAAKiM,gBAEL,MAAMC,EAAgB,IAAI,EAAAC,cAE1BnM,KAAKgL,QAAU,CACXkB,gBACAhH,UACI,cAAelF,KAAK2K,uBACd3K,KAAK2K,uBAAuBzF,UAC5B,IAGdlF,KAAKsL,kBACAc,gBAAgB,CACbxB,KAAM,OACNiB,iBAAkB7L,KAAK2K,uBACvBuB,kBAEHG,MAAK,IAAY,EAAD,gCACTN,EAAgBjG,OAAOe,gBAIrB,IAAAwC,iBACFiD,I,MACI,OAAAtM,KAAKuM,aAAaL,EAAe,CAC7BnG,kBAC8B,QAA1B,EAAAlF,aAAO,EAAPA,EAASkF,yBAAiB,QAAI/F,KAAKoL,yBACvCtF,OAAQwG,aAAQ,EAARA,EAAUxG,QACpB,GACN,CACImD,SAAUQ,OAAOC,iBACjBC,QAAS3J,KAAKqL,sBACdvF,OAAQiG,EAAgBjG,SAGpC,MAEJ,MAAM0G,EACF,kBAAmBxM,KAAK2K,wBACxB3K,KAAK2K,uBAAuB6B,cACtBxM,KAAK2K,uBAAuB6B,cAC5BxM,KAAK6K,sBAEf,OAAO7K,KAAKyM,sBAAsBD,EAAe5L,EACrD,CAEa8L,kBAAkB7L,G,iDAI3B,MAAMkL,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QAIvD,GAHoB,QAApB,EAAA9F,KAAK+L,uBAAe,SAAEC,QACtBhM,KAAK+L,gBAAkBA,EAEnBA,EAAgBjG,OAAOe,QACvB,OAGJ7G,KAAKiM,gBACL,MAAMU,QAAyB3M,KAAKsL,kBAAkBK,oBACtD,IAAKgB,EACD,OAGJ,GAAIZ,EAAgBjG,OAAOe,QACvB,OAGJ,MAAMd,EAA8C,QAA1B,EAAAlF,aAAO,EAAPA,EAASkF,yBAAiB,QAAI/F,KAAKoL,yBAE7D,IAAI,IAAAQ,yBAAwBe,GASxB,OARA3M,KAAKgL,QAAU,CACXkB,cAAeS,EAAiBT,cAChChH,UACI,cAAelF,KAAK2K,uBACd3K,KAAK2K,uBAAuBzF,UAC5B,UAGDlF,KAAKuM,aAAaI,EAAiBT,cAAe,CAC3DnG,kBAAmBA,EACnBD,OAAQiG,aAAe,EAAfA,EAAiBjG,SAIjC,GAAI8G,MAAMC,QAAQ7M,KAAK2K,wBACnB,MAAM,IAAI,EAAAlL,gBACN,6FAmBR,GAfAO,KAAKgL,QAAU2B,EAAiB3B,QAE5BhL,KAAKiL,WACL,IAAAZ,UAAS,6DACHrK,KAAKiL,QAAQxD,SAGvBzH,KAAKiL,QAAU,IAAI,EAAA6B,cACf9M,KAAKiF,QACLjF,KAAK2K,uBAAuBzF,UAC5ByH,EAAiB3B,QAAQkB,cAAc/G,UACvCnF,KAAK+M,gBAAgB3G,KAAKpG,MAC1BA,KAAKgN,sBAAsB5G,KAAKpG,QAGhC+L,EAAgBjG,OAAOe,QAA3B,CAKA7G,KAAKmL,UAAU8B,SAAQ7H,GAAYA,EAASuH,EAAiBO,gBAG7D,UACU,IAAA7D,iBACFxI,GACIb,KAAKiL,QAASvC,gBAAgB,CAC1B3C,kBAAmBA,EACnBD,OAAQjF,EAAQiF,UAExB,CACImD,SAAUQ,OAAOC,iBACjBC,QAAS3J,KAAKqL,sBACdvF,OAAQiG,EAAgBjG,Q,CAGlC,MAAO6B,GAEL,kBADM3H,KAAKmN,WAAW,CAAErH,OAAQiG,EAAgBjG,S,MAkBjDsH,YACH9I,EACA+I,GAKA,MAAMxM,EAIF,CAAC,EASL,MARsC,mBAA3BwM,EACPxM,EAAQyM,cAAgBD,GAExBxM,EAAQyM,cAAgBD,aAAsB,EAAtBA,EAAwBC,cAChDzM,EAAQiF,OAASuH,aAAsB,EAAtBA,EAAwBvH,OACzCjF,EAAQoI,SAAWoE,aAAsB,EAAtBA,EAAwBpE,UAGxC,IAAIsE,SAAQ,CAAO9G,EAASC,IAAW,EAAD,gC,MACzC,IAAK1G,KAAKiL,UAAYjL,KAAKgL,WAAa,oBAAqBhL,KAAKgL,SAC9D,MAAM,IAAI,EAAAvL,gBAAgB,iDAG9B,MAAM+N,SAAYxN,KAAKsL,kBAAkBmC,uBAAuBnG,iBAC1DtH,KAAKsL,kBAAkBoC,4BAE7B,IAAArD,UAAS,4BAA6B,OAAF,wBAAO/F,GAAO,CAAEkJ,QAEpD,MAAMG,EAAiB3N,KAAKgL,QAASkB,cAAc0B,QAC/CnJ,KAAKC,UAAU,OAAD,wBAAMJ,GAAO,CAAEkJ,SAC7B,IAAAK,gBAAe7N,KAAKgL,QAAQ8C,kBAGhC,UACU9N,KAAKiL,QAAQrC,KACf+E,EACA3N,KAAKgL,QAAQ8C,gBACbxJ,EAAQC,OACR,CAAE0E,SAAUpI,aAAO,EAAPA,EAASoI,SAAUnD,OAAQjF,aAAO,EAAPA,EAASiF,SAE9B,QAAtB,EAAAjF,aAAO,EAAPA,EAASyM,qBAAa,iBACtBtN,KAAK8K,gBAAgBiD,IAAIP,EAAGlG,WAAYb,E,CAC1C,MAAOkB,GACLjB,EAAOiB,E,CAEf,KACJ,CAEOqG,kBACHhO,KAAKiM,gBACLjM,KAAKmL,UAAY,GACjBnL,KAAKgL,QAAU,KACfhL,KAAKiL,QAAU,IACnB,CAEakC,WAAWtM,G,yCACpB,OAAO,IAAI0M,SAAc9G,GAAW,EAAD,gCAC/B,IAAIwH,GAAS,EACTC,EAAkD,KACtD,MAAMZ,EAAgB,KACbW,IACDA,GAAS,EACTjO,KAAKmO,yBAAyB9B,KAAK5F,G,EAI3C,IACIzG,KAAKiM,gBAEL,MAAMF,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QACvDoI,EAAYE,YAAW,KACnBrC,EAAgBC,OAAO,GACxBhM,KAAKoL,gCAEFpL,KAAKoN,YACP,CAAE7I,OAAQ,aAAcC,OAAQ,IAChC,CACI8I,cAAeA,EACfxH,OAAQiG,EAAgBjG,OACxBmD,SAAU,G,CAGpB,MAAOtB,IACL,IAAA0C,UAAS,oBAAqB1C,GAEzBsG,GACDjO,KAAKmO,yBAAyB9B,KAAK5F,E,SAGnCyH,GACAG,aAAaH,GAGjBZ,G,CAER,KACJ,G,CAEOgB,OAAOC,GAEV,OADAvO,KAAKmL,UAAUqD,KAAKD,GACb,IAAOvO,KAAKmL,UAAYnL,KAAKmL,UAAUsD,QAAOrJ,GAAYA,IAAamJ,GAClF,CAEO3E,Q,MACS,QAAZ,EAAA5J,KAAKiL,eAAO,SAAErB,QACd5J,KAAKkL,gBAAgB+B,SAAQyB,GAAUA,EAAO9E,SAClD,CAEaI,U,yCACT,MAAM2E,EAAW3O,KAAKkL,gBAAgB0D,KAAIF,GAAUA,EAAO1E,YACvDhK,KAAKiL,SACL0D,EAASH,KAAKxO,KAAKiL,QAAQjB,iBAEzBuD,QAAQsB,IAAIF,EACtB,G,CAEcG,wBACV7D,EACA/F,EACAsF,G,yCAEA,GAAKxK,KAAKkL,gBAAgB6D,SAAS9D,GAgBnC,OAXAjL,KAAKiM,cAAc,CAAE+C,OAAQ/D,IAEzBjL,KAAKiL,WACL,IAAAZ,UAAS,6DACHrK,KAAKiL,QAAQxD,SAGvBzH,KAAKgL,QAAS9F,UAAYA,EAC1BlF,KAAKiL,QAAUA,EACfjL,KAAKiL,QAAQd,kBAAkBnK,KAAKgN,sBAAsB5G,KAAKpG,OAC/DA,KAAKiL,QAAQf,YAAYlK,KAAK+M,gBAAgB3G,KAAKpG,OAC5CA,KAAK+M,gBAAgBvC,SAflBS,EAAQxD,OAgBtB,G,CAEcsF,gBAAgBvC,G,yCAC1B,MAAMyE,EAA+BxK,KAAKgG,MACtCzK,KAAKgL,QAASkB,cAAcgD,QACxB,EAAA/F,OAAOgG,OAAO3E,EAAsB5J,SAASwO,gBAC7C,IAAAvB,gBAAerD,EAAsB6E,QAM7C,IAFA,IAAAhF,UAAS,2BAA4B4E,KAE/B,UAAWA,GAAgB,CAC7B,MAAMzB,EAAKyB,EAAczB,GAAGlG,WACtBb,EAAUzG,KAAK8K,gBAAgBwE,IAAI9B,GACzC,OAAK/G,GAKLA,EAAQwI,QACRjP,KAAK8K,gBAAgByE,OAAO/B,SALxB,IAAAnD,UAAS,eAAemD,mC,CAShC,QAAyBgC,IAArBP,EAAczB,GAAkB,CAChC,MAAMiC,QAAezP,KAAKsL,kBAAkBoE,uBAE5C,QAAeF,IAAXC,GAAwBR,EAAczB,IAAMiC,EAI5C,YAHA,IAAA1F,UACI,uBAAuBkF,EAAczB,0DAA0DiC,OAK3E,YAAxBR,EAAcnH,cACR9H,KAAKsL,kBAAkBqE,uBAAuBV,EAAczB,I,CAK1E,MAAMrC,EAAYnL,KAAKmL,UAEK,YAAxB8D,EAAcnH,cACR9H,KAAK4P,cAAcX,EAAezE,EAAsB6E,OAGtC,eAAxBJ,EAAcnH,SACd,IAAAuC,UAAS,gEACHrK,KAAKmO,0BAGfhD,EAAU8B,SAAQ7H,GAAYA,EAAS6J,IAC3C,G,CAEcjC,sBAAsBrF,G,yCAChC,MAAM,IAAI,EAAAlI,gBAAgB,gBAAgBgF,KAAKC,UAAUiD,KAC7D,G,CAEciI,cACV1C,EACAY,G,yCAEA9N,KAAKgL,QAAU,OAAH,wBACLhL,KAAKgL,SAAQ,CAChB8C,oBAGJ,MAAM+B,EAAmC3C,EAAa4C,QAAQC,MAAMC,MAChEC,GAAsB,aAAdA,EAAKlP,OAGXmP,EAAkB,+BACjBhD,GAAY,CACf4C,QAAS,OAAF,wBACA5C,EAAa4C,SAAO,CACvBC,MAAO,CAACF,aAIV7P,KAAKsL,kBAAkBc,gBAAgB,CACzCxB,KAAM,OACNI,QAAShL,KAAKgL,QACdmF,kBAAmBjD,EAAaM,GAChCN,aAAcgD,EACdE,iBAAkB,GAE1B,G,CAEcjC,yB,yCACVnO,KAAKgO,wBACChO,KAAKsL,kBAAkB+E,kBACjC,G,CAEQ5D,sBAAsBD,EAAuB5L,GACjD,OAAI,IAAA8B,eAAc8J,GACPxM,KAAKsQ,wBAAwB9D,EAAe5L,GAGhDZ,KAAKuQ,6BAA6B/D,EAAe5L,EAC5D,CAEQ2P,6BAA6B/D,EAAuB5L,GACxD,MAAMkG,EAAM,IAAIC,IAAIyF,GAIpB,OAHA1F,EAAIG,aAAaC,OAAO,IAAK,EAAAsJ,iBAAiBlJ,YAC9CR,EAAIG,aAAaC,OAAO,KAAMlH,KAAKgL,QAASkB,cAAc/G,WAC1D2B,EAAIG,aAAaC,OAAO,IAAKzC,KAAKC,UAAU9D,IACrCkG,EAAIQ,UACf,CAEQgJ,wBAAwB9D,EAAuB5L,GACnD,MACM6P,EADYzQ,KAAKuQ,6BAA6B,cAAe3P,GACtC8P,MAAM,KAAK,GAElCC,EAAW,eAAgB,IAAAhO,6BAA4B8N,GAGvDG,EAAuB5Q,KAAK6Q,oBAAoBrE,GAEhD1F,EAAM,IAAIC,IAAI6J,GAEpB,OADA9J,EAAIG,aAAaC,OAAO,WAAYyJ,GAC7B7J,EAAIQ,UACf,CAGQuJ,oBAAoBrE,GACxB,MAAM1F,EAAM,IAAIC,IAAIyF,GAOpB,OALI1F,EAAIG,aAAa6J,IAAI,YACrBhK,EAAIG,aAAasI,OAAO,UACxBzI,EAAIiK,UAAY,UAGbjK,EAAIQ,UACf,CAEciF,aACVL,EACArL,G,yCAKA,OAAI+L,MAAMC,QAAQ7M,KAAK2K,yBAEnB3K,KAAKkL,gBAAgB0D,KAAIF,GAAUA,EAAOjH,QAAQqC,UAGlD9J,KAAKkL,gBAAkBlL,KAAK2K,uBAAuBiE,KAAIoC,IACnD,MAAM/F,EAAU,IAAI,EAAA6B,cAChB9M,KAAKiF,QACL+L,EAAO9L,UACPgH,EAAc/G,WACd,SACA,SAOJ,OAJA8F,EAAQf,aAAYtJ,GAChBZ,KAAK8O,wBAAwB7D,EAAS+F,EAAO9L,UAAWtE,KAGrDqK,CAAO,eAGZsC,QAAQ0D,WACVjR,KAAKkL,gBAAgB0D,KAAIF,IACrB,IAAArF,iBACKiD,I,MACG,OAAKtM,KAAKkL,gBAAgBgG,MAAKjB,GAAQA,IAASvB,IAIzCA,EAAOhG,gBAAgB,CAC1B3C,kBAC8B,QAA1B,EAAAlF,aAAO,EAAPA,EAASkF,yBAAiB,QAAI/F,KAAKoL,yBACvCtF,OAAQwG,EAASxG,SANV4I,EAAOjH,OAOhB,GAEN,CACIwB,SAAUQ,OAAOC,iBACjBC,QAAS3J,KAAKqL,sBACdvF,OAAQjF,aAAO,EAAPA,EAASiF,eAQ7B9F,KAAKiL,WACL,IAAAZ,UAAS,6DACHrK,KAAKiL,QAAQxD,SAGvBzH,KAAKiL,QAAU,IAAI,EAAA6B,cACf9M,KAAKiF,QACLjF,KAAK2K,uBAAuBzF,UAC5BgH,EAAc/G,UACdnF,KAAK+M,gBAAgB3G,KAAKpG,MAC1BA,KAAKgN,sBAAsB5G,KAAKpG,aAEvBA,KAAKiL,QAAQvC,gBAAgB,CACtC3C,kBAAmBlF,aAAO,EAAPA,EAASkF,kBAC5BD,OAAQjF,aAAO,EAAPA,EAASiF,SAG7B,G,CAEQmG,cAAcpL,G,MACN,QAAZ,EAAAb,KAAKiL,eAAO,SAAExD,QACdzH,KAAKkL,gBACAuD,QAAOwB,GAAQA,KAASpP,aAAO,EAAPA,EAASmO,UACjC/B,SAAQyB,GAAUA,EAAOjH,UAC9BzH,KAAKkL,gBAAkB,EAC3B,EA7iBJ,kB,8GCOA,mCACIQ,GAEA,QAAS,iBAAkBA,EAC/B,C,+aC3CA,gBASA,UAKA,UAGA,UACA,UAEA,UAQA,MAAayF,EAoETxR,YAAYsF,EAAoCmM,GAAA,KAAAA,kBAAAA,EAZhC,KAAAxG,KAAO,WAEf,KAAAyG,oBAA2C,KAM3C,KAAAC,qBAAsB,EAEtB,KAAAnG,UAAoE,GAGxE,MAAMoG,EAAgDJ,EAAiBI,OACvE,IAAKJ,EAAiBK,uBAAuBD,EAAQH,GACjD,MAAM,IAAI,EAAAlQ,uBAGdlB,KAAKsL,kBAAoB,IAAI,EAAAC,wBAAwBtG,GACrDjF,KAAKyR,eAAiBF,EAAOH,GAAoBM,UACrD,CAzEOlG,mBAAyBvG,G,yCAC5B,MAAMwG,EAA0B,IAAI,EAAAF,wBAAwBtG,GACtDyG,QAAmBD,EAAwBkG,wBACjD,OAAO,IAAIR,EAAiBlM,EAASyG,EAAWkG,YACpD,G,CAEOpG,wBAAwB4F,GAC3B,OAAOD,EAAiBK,uBAAuBxR,KAAKuR,OAAQH,EAChE,CAEO5F,6BAA6B4F,GAChC,QAAID,EAAiBK,uBAAuBxR,KAAKuR,OAAQH,IAC9CpR,KAAKuR,OAAOH,GAAoBM,WAAWG,eAI1D,CAEOrG,qCACH,OAAKxL,KAAKuR,QAIS,IAAAO,oBACQrD,QAAO,EAAEsD,EAAG9O,MACnC,IAAA+O,wBAAuB/O,KAGZ2L,KAAI,EAAEgD,EAAaK,MAAY,CAC1ClR,KAAMkR,EAAOP,WAAWQ,WAAWnR,KACnCoR,QAASF,EAAOP,WAAWQ,WAAWE,SACtCC,SAAUJ,EAAOP,WAAWQ,WAAWI,UACvCC,SAAUN,EAAOP,WAAWQ,WAAWM,MACvCC,OAAQR,EAAOP,WAAWQ,WAAWO,OACrCb,cACA1O,UAAU,EACVC,SAAU8O,EAAOP,WAAWG,gBAC5Ba,UAAWT,EAAOP,WAAWQ,WAAWQ,cAjBjC,EAmBf,CAEQlH,8BACJ+F,EACAH,GAEA,QACMG,GACFH,KAAqBG,GACgC,iBAA9CA,EAAOH,IACd,eAAgBG,EAAOH,EAE/B,CAwBOtF,QAAQlL,GACXZ,KAAK2S,SAAS,EAAAnC,iBAAkB5P,EACpC,CAEa8L,oB,yCACT,KACI,IAAArC,UAAS,6CACT,MAAM6C,QAAqBlN,KAAKyR,eAAe/E,qBAC/C,IAAArC,UAAS,kDAAmD6C,GAEjC,YAAvBA,EAAapF,OACb9H,KAAK4S,oBACL5S,KAAKmL,UAAU8B,SAAQ7H,GAAYA,EAAS8H,YAEtClN,KAAKsL,kBAAkB+E,kB,CAEnC,MAAO1I,SACC3H,KAAKsL,kBAAkB+E,mBAC7BwC,QAAQhP,MAAM8D,E,CAEtB,G,CAEOqG,kBACChO,KAAKsR,qBACLtR,KAAKyR,eAAetE,aAExBnN,KAAK8S,mBACT,CAEa3F,a,yCACT,OAAO,IAAII,SAAQ9G,IACf,MAAM6G,EAAgB,KAClBtN,KAAK8S,oBACL9S,KAAKsL,kBAAkB+E,mBAAmBhE,KAAK5F,EAAQ,EAG3D,IACIzG,KAAKyR,eAAetE,aACpBG,G,CACF,MAAO3F,IACL,IAAA0C,UAAS1C,GAET3H,KAAKoN,YACD,CACI7I,OAAQ,aACRC,OAAQ,IAEZ8I,E,IAIhB,G,CAEQwF,oB,MACJ9S,KAAKsR,qBAAsB,EAC3BtR,KAAKmL,UAAY,GACO,QAAxB,EAAAnL,KAAKqR,2BAAmB,mBAC5B,CAEO/C,OAAOyE,GAEV,OADA/S,KAAKmL,UAAUqD,KAAKuE,GACb,IACF/S,KAAKmL,UAAYnL,KAAKmL,UAAUsD,QAAOrJ,GAAYA,IAAa2N,GACzE,CAea3F,YACT9I,EACA+I,G,+CAGA,MAAMxM,EAIF,CAAC,EACiC,mBAA3BwM,EACPxM,EAAQyM,cAAgBD,GAExBxM,EAAQyM,cAAgBD,aAAsB,EAAtBA,EAAwBC,cAChDzM,EAAQiF,OAASuH,aAAsB,EAAtBA,EAAwBvH,QAG7C,MAAM0H,SAAYxN,KAAKsL,kBAAkBmC,uBAAuBnG,iBAC1DtH,KAAKsL,kBAAkBoC,4BAE7B,IAAArD,UAAS,gCAAiC,OAAF,wBAAO/F,GAAO,CAAEkJ,QACxD,MAAMzI,EAAS/E,KAAKyR,eAAe7I,KAAQ,+BAAKtE,GAAO,CAAEkJ,QAIzD,OAHAzI,EAAOsH,MAAKpI,IAAY,IAAAoG,UAAS,2BAA4BpG,KACvC,QAAtB,EAAApD,aAAO,EAAPA,EAASyM,qBAAa,iBAEfvI,C,IAGG4N,SAASK,EAAyBpS,G,yCAC5C,KACI,IAAAyJ,UACI,uDAAuD2I,cACvDpS,GAEJ,MAAMsM,QAAqBlN,KAAKyR,eAAe3F,QAAQkH,EAAiBpS,IAExE,IAAAyJ,UAAS,sCAAuC6C,GAErB,YAAvBA,EAAapF,cACP9H,KAAK4P,gBACX5P,KAAK4S,qBAET5S,KAAKmL,UAAU8B,SAAQ7H,GAAYA,EAAS8H,I,CAC9C,MAAOvF,IACL,IAAA0C,UAAS,mCAAoC1C,GAC7C,MAAMsL,EAAkD,CACpDnL,MAAO,gBACPgI,QAAS,CACL/L,KAAM,EACNnD,QAAS+G,aAAC,EAADA,EAAGL,aAIpBtH,KAAKmL,UAAU8B,SAAQ7H,GAAYA,EAAS6N,I,CAEpD,G,CAEQL,oBACJ5S,KAAKsR,qBAAsB,EAC3BtR,KAAKqR,oBAAsBrR,KAAKyR,eAAenD,QAAO3G,KAClD,IAAA0C,UAAS,2BAA4B1C,GAEjC3H,KAAKsR,qBACLtR,KAAKmL,UAAU8B,SAAQ7H,GAAYA,EAASuC,KAGhC,eAAZA,EAAEG,OACF9H,KAAKmN,Y,GAGjB,CAEQyC,gBACJ,OAAO5P,KAAKsL,kBAAkBc,gBAAgB,CAC1CxB,KAAM,WACNgH,YAAa5R,KAAKoR,kBAClBhB,iBAAkB,GAE1B,EA1OJ,qBACmB,EAAAmB,QAAS,IAAA2B,Y,+GCnB5B,gBAqBA,kCAAuCjQ,GACnC,IACI,UAAK,IAAAkQ,aAAYlQ,EAAO,iBAAkB,IAAAkQ,aAAYlQ,EAAMyO,WAAY,iBAIjE,IAAA0B,eAAcnQ,EAAMyO,WAAWQ,WAAY,CAC9C,OACA,WACA,QACA,YACA,a,CAEN,SACE,OAAO,C,CAEf,C,4GC7Ca,EAAAmB,sBAAyC,CAClD,CACIjB,SAAU,kBACVrR,KAAM,SACNyR,MAAO,wCACPF,UAAW,qBACXgB,cAAe,oCACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,kDAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,YACVrR,KAAM,YACNyR,MAAO,mDACPC,OAAQ,gBACRH,UAAW,wBACXgB,cAAe,wCACfC,SAAU,kBACV7E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,mCAET,CACI8D,KAAM,KACN4I,IAAK,cAGbd,UAAW,CAAC,MAAO,UAAW,SAAU,UAAW,UAEvD,CACIN,SAAU,cACVrR,KAAM,cACNyR,MAAO,6CACPF,UAAW,yBACXgB,cAAe,kCACf5E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,eAET,CACI5I,KAAM,MACN9D,IAAK,qDAGb4L,UAAW,CAAC,SAAU,UAAW,QAAS,QAAS,MAAO,UAAW,YAEzE,CACIN,SAAU,SACVrR,KAAM,SACNyR,MAAO,yCACPF,UAAW,qBACXgB,cAAe,iCACf5E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,UAET,CACI5I,KAAM,MACN9D,IAAK,6CAGb4L,UAAW,CAAC,MAAO,YAEvB,CACIN,SAAU,kBACVrR,KAAM,gBACNyR,MAAO,mHACPF,UAAW,0BACXiB,SAAU,aACV7E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,mBAET,CACI5I,KAAM,MACN9D,IAAK,gDAGb4L,UAAW,CAAC,MAAO,UAAW,UAC9BY,cAAe,kCAEnB,CACIlB,SAAU,gBACVrR,KAAM,kBACNyR,MAAO,mEACPF,UAAW,2BACXgB,cAAe,4CACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,sDAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,uBACVrR,KAAM,sBACNyR,MAAO,8EACPF,UAAW,wCACXiB,SAAU,2CACV7E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,cAET,CACI5I,KAAM,MACN9D,IAAK,gDAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,SAClDY,cAAe,8CAEnB,CACIlB,SAAU,cACVrR,KAAM,WACNyR,MAAO,2CACPF,UAAW,uBACXgB,cAAe,sCACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,8CAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,eACVrR,KAAM,aACNyR,MAAO,kEACPF,UAAW,2BACXgB,cACI,sFACJ5E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,gBAET,CACI5I,KAAM,MACN9D,IAAK,sDAGb4L,UAAW,CAAC,SAAU,SAAU,UAAW,MAAO,YAEtD,CACIN,SAAU,MACVrR,KAAM,MACNyR,MAAO,gEACPF,UAAW,wBACXgB,cAAe,2CACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,mCAET,CACI8D,KAAM,KACN4I,IAAK,cAGbd,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,iBACVrR,KAAM,eACNyR,MAAO,oGACPF,UAAW,6BACXgB,cAAe,oCACfC,SAAU,cACV7E,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,kBAET,CACI5I,KAAM,MACN9D,IAAK,+DAGb4L,UAAW,CAAC,MAAO,UAAW,WAElC,CACIN,SAAU,WACVrR,KAAM,WACNyR,MAAO,sFACPF,UAAW,0BACXgB,cAAe,sCACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,uCAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,gBACVrR,KAAM,UACNyR,MAAO,4DACPC,OAAQ,GACRH,UAAW,0BACXgB,cAAe,sCACfC,SAAU,gBACV7E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,sDAET,CACI8D,KAAM,KACN4I,IAAK,kBAGbd,UAAW,CAAC,MAAO,UAAW,SAAU,YAE5C,CACIN,SAAU,aACVrR,KAAM,aACNyR,MAAO,gFACPF,UAAW,uBACX5D,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,iBAET,CACI5I,KAAM,MACN9D,IAAK,yDAGb4L,UAAW,CAAC,MAAO,WACnBY,cAAe,8DAEnB,CACIlB,SAAU,WACVrR,KAAM,WACNyR,MAAO,qGACPF,UAAW,4BACX5D,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,aAGbd,UAAW,CAAC,WAEhB,CACIN,SAAU,aACVrR,KAAM,aACNyR,MAAO,0DACPF,UAAW,0BACXgB,cAAe,+CACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,gDAGb4L,UAAW,CAAC,MAAO,UAAW,UAAW,QAAS,UAEtD,CACIN,SAAU,OACVrR,KAAM,OACNyR,MAAO,gDACPF,UAAW,mBACXgB,cAAe,4CACf5E,OAAQ,CACJ,CACI9D,KAAM,MACN9D,IAAK,6CAGb4L,UAAW,CAAC,MAAO,UAAW,QAAS,UAAW,UAEtD,CACIN,SAAU,aACVrR,KAAM,aACNyR,MAAO,sDACPF,UAAW,yBACX5D,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,eAGbd,UAAW,CAAC,SAAU,YAE1B,CACIN,SAAU,YACVrR,KAAM,aACNyR,MAAO,+CACPF,UACI,wFACJ5D,OAAQ,CACJ,CACI9D,KAAM,KACN4I,IAAK,cAGbd,UAAW,CAAC,W,uGCzTP,EAAAlC,iBAAmB,C,sbCAhC,gBACA,UAEA,UAYA,gCAGI7Q,YAA6BsF,GAAA,KAAAA,QAAAA,EAFZ,KAAAwO,SAAW,uCAEqB,CAEpCrH,gBAAgBV,G,yCACzB,GAAwB,aAApBA,EAAWd,KACX,OAAO5K,KAAKiF,QAAQyO,QAAQ1T,KAAKyT,SAAUhP,KAAKC,UAAUgH,IAG9D,KAAK,IAAAE,yBAAwBF,GAAa,CACtC,MAAMiI,EAA+B,CACjCC,eAAgBlI,EAAWV,QAAQkB,cAAc2H,mBACjD/F,gBAAiBpC,EAAWV,QAAQ8C,gBACpC5I,UAAWwG,EAAWV,QAAQ9F,WAG5B4O,EAAyC,CAC3ClJ,KAAM,OACNsC,aAAcxB,EAAWwB,aACzBlC,QAAS2I,EACTxD,kBAAmBzE,EAAWyE,kBAC9BC,iBAAkB1E,EAAW0E,kBAEjC,OAAOpQ,KAAKiF,QAAQyO,QAAQ1T,KAAKyT,SAAUhP,KAAKC,UAAUoP,G,CAG9D,MAAMA,EAAgD,CAClDlJ,KAAM,OACNiB,iBAAkBH,EAAWG,iBAC7BK,cAAeR,EAAWQ,cAAc2H,oBAG5C,OAAO7T,KAAKiF,QAAQyO,QAAQ1T,KAAKyT,SAAUhP,KAAKC,UAAUoP,GAC9D,G,CAEazD,mB,yCACT,OAAOrQ,KAAKiF,QAAQ8O,WAAW/T,KAAKyT,SACxC,G,CAEaO,gB,yCACT,MAAMC,QAAejU,KAAKiF,QAAQiP,QAAQlU,KAAKyT,UAC/C,IAAKQ,EACD,OAAO,KAGX,MAAMvI,EAAkCjH,KAAKgG,MAAMwJ,GAEnD,GAAwB,aAApBvI,EAAWd,KACX,OAAOc,EAGX,GAAI,iBAAkBA,EAAY,CAC9B,MAAMQ,EAAgB,IAAI,EAAAC,cAAcT,EAAWV,QAAQ4I,gBAC3D,MAAO,CACHhJ,KAAM,OACNsC,aAAcxB,EAAWwB,aACzBiD,kBAAmBzE,EAAWyE,kBAC9BC,iBAAkB1E,EAAW0E,iBAC7BpF,QAAS,CACLkB,gBACAhH,UAAWwG,EAAWV,QAAQ9F,UAC9B4I,gBAAiBpC,EAAWV,QAAQ8C,iB,CAKhD,MAAO,CACHlD,KAAM,OACNsB,cAAe,IAAI,EAAAC,cAAcT,EAAWQ,eAC5CL,iBAAkBH,EAAWG,iBAErC,G,CAEaF,oB,yCACT,MAAMD,QAAmB1L,KAAKgU,gBAC9B,IAAKtI,EACD,MAAM,IAAI,EAAAjM,gBACN,iEAIR,GAAwB,aAApBiM,EAAWd,KACX,MAAM,IAAI,EAAAnL,gBACN,6EAIR,OAAOiM,CACX,G,CAEayI,2B,yCACT,MAAMzI,QAAmB1L,KAAKgU,gBAC9B,IAAKtI,EACD,MAAM,IAAI,EAAAjM,gBACN,iEAIR,GAAwB,aAApBiM,EAAWd,KACX,MAAM,IAAI,EAAAnL,gBACN,6EAIR,KAAK,IAAAmM,yBAAwBF,GACzB,MAAM,IAAI,EAAAjM,gBACN,0EAIR,OAAOiM,CACX,G,CAEaiG,wB,yCACT,MAAMjG,QAAmB1L,KAAKgU,gBAE9B,IAAKtI,EACD,MAAM,IAAI,EAAAjM,gBACN,4EAIR,GAAyB,UAArBiM,aAAU,EAAVA,EAAYd,MACZ,MAAM,IAAI,EAAAnL,gBACN,oFAIR,OAAOiM,CACX,G,CAEa0I,uB,yCACT,MAAMH,QAAejU,KAAKiF,QAAQiP,QAAQlU,KAAKyT,UAC/C,OAAKQ,EAGgCxP,KAAKgG,MAAMwJ,GAC9BrJ,KAHP,IAIf,G,CAEa+E,uBAAuBnC,G,yCAChC,MAAM9B,QAAmB1L,KAAKgU,gBAC9B,GAAItI,GAAkC,SAApBA,EAAWd,QAAoB,IAAAgB,yBAAwBF,GAErE,OADAA,EAAWyE,kBAAoB3C,EACxBxN,KAAKoM,gBAAgBV,EAEpC,G,CAEagE,uB,yCACT,MAAMhE,QAAmB1L,KAAKgU,gBAC9B,GAAItI,GAAc,sBAAuBA,EACrC,OAAOA,EAAWyE,iBAI1B,G,CAEazC,2B,yCACT,MAAMhC,QAAmB1L,KAAKgU,gBAC9B,GAAItI,GAAc,qBAAsBA,EAAY,CAChD,MAAM+D,EAAS/D,EAAW0E,kBAAoB,EAE9C,OADA1E,EAAW0E,iBAAmBX,EAAS,EAChCzP,KAAKoM,gBAAgBV,E,CAEpC,G,CAEa+B,sB,yCACT,MAAM/B,QAAmB1L,KAAKgU,gBAC9B,OAAItI,GAAc,qBAAsBA,GAC7BA,EAAW0E,kBAGf,CACX,G,+aC5LJ,gBAKA,uBAGIzQ,cACIK,KAAKqU,cAAe,IAAAC,qBACxB,CAEaJ,QAAQV,G,yCACjB,OAAOxT,KAAKqU,aAAaH,QAAQV,EACrC,G,CAEaO,WAAWP,G,yCACpBxT,KAAKqU,aAAaN,WAAWP,EACjC,G,CAEaE,QAAQF,EAAavQ,G,yCAC9BjD,KAAKqU,aAAaX,QAAQF,EAAKvQ,EACnC,G,ubCrBJ,iCAGItD,YAA6BsF,EAAmBC,GAAnB,KAAAD,QAAAA,EACzBjF,KAAKyT,SAAW,4CAA8CvO,CAClE,CAEaqF,iBAAiBpD,G,yCAC1B,OAAOnH,KAAKiF,QAAQyO,QAAQ1T,KAAKyT,SAAUtM,EAC/C,G,CAEaoN,oB,yCACT,OAAOvU,KAAKiF,QAAQ8O,WAAW/T,KAAKyT,SACxC,G,CAEarM,iB,yCAET,aADqBpH,KAAKiF,QAAQiP,QAAQlU,KAAKyT,YAEpC,IAIf,G,wGCpBJ,MAAae,EAaT,cAVQ,KAAAvP,QAAkC,CAAC,CAUpB,CARhBuG,qBAKH,OAJKgJ,EAAgBC,WACjBD,EAAgBC,SAAW,IAAID,GAG5BA,EAAgBC,QAC3B,CAIWC,aACP,OAAO5U,OAAO6U,KAAK3U,KAAKiF,SAASyP,MACrC,CAEOE,QACH5U,KAAKiF,QAAU,CAAC,CACpB,CAEOiP,QAAQV,G,MACX,OAAwB,QAAjB,EAAAxT,KAAKiF,QAAQuO,UAAI,QAAI,IAChC,CAEOA,IAAIqB,G,MACP,MAAMF,EAAO7U,OAAO6U,KAAK3U,KAAKiF,SAC9B,OAAI4P,EAAQ,GAAKA,GAASF,EAAKD,OACpB,KAGO,QAAX,EAAAC,EAAKE,UAAM,QAAI,IAC1B,CAEOd,WAAWP,UACPxT,KAAKiF,QAAQuO,EACxB,CAEOE,QAAQF,EAAavQ,GACxBjD,KAAKiF,QAAQuO,GAAOvQ,CACxB,EA1CJ,mB,wwBCMA,gBACA,UACA,UACA,UACA,UACA,UAWA,UAIA,UACA,UACA,UACA,UAEA,UACA,UAEA,UACA,UAEA,UACA,UACA,UACA,UACA,UACA,UAEA,MAAa5B,EAwET1B,YAAYkB,GAgBR,GA1Da,KAAAiU,YAAc,IAAI,EAAAxT,mBAM3B,KAAAyT,QAAyB,KAEzB,KAAAC,SAA4B,KAE5B,KAAAC,0BAAqE,GAErE,KAAAC,+BAAqE,GA+BzElV,KAAKmV,aAAe,CAChBC,aAAavU,aAAO,EAAPA,EAASuU,eAAe,IAAAC,sBACrCpQ,SAASpE,aAAO,EAAPA,EAASoE,UAAW,IAAI,EAAAqQ,gBAGrCtV,KAAK8U,YAAc,IAAI,EAAAxT,mBAAmB,CACtCiU,kBAAmB1U,aAAO,EAAPA,EAAS0U,kBAC5BC,WAAY3U,aAAO,EAAPA,EAAS4U,wBAGzBzV,KAAK0V,QAAU,IAAI,EAAAC,kBAAkB,CACjCC,gBAAiB/U,aAAO,EAAPA,EAAS+U,gBAC1BrW,qBAAsB,EAAAA,wBAGrBS,KAAKmV,aAAaC,YACnB,MAAM,IAAI,EAAAjV,kBACN,qLAIRH,KAAKyL,wBAA0B,IAAI,EAAAF,wBAAwBvL,KAAKmV,aAAalQ,UAExEpE,aAAO,EAAPA,EAASgV,6BACV7V,KAAK8V,oCAEb,CA/EOtK,oBACH,OAAOxL,KAAK8U,YAAYiB,YAC5B,CA2BWC,gBACP,OAAwB,OAAjBhW,KAAK+U,OAChB,CAKWkB,c,MACP,OAAmB,QAAZ,EAAAjW,KAAK+U,eAAO,eAAEkB,UAAW,IACpC,CAKWhE,aACP,OAAOjS,KAAK+U,OAChB,CAEY9C,WAAOhP,GACfjD,KAAK+U,QAAU9R,EACfjD,KAAKiV,0BAA0BhI,SAAQsB,GAAYA,EAASvO,KAAK+U,UACrE,CAkCOgB,aACH,OAAO/V,KAAK8U,YAAYiB,YAC5B,CAQOG,eACH3H,EACApI,GAOA,OALAnG,KAAKiV,0BAA0BzG,KAAKD,GAChCpI,GACAnG,KAAKkV,+BAA+B1G,KAAKrI,GAGtC,KACHnG,KAAKiV,0BAA4BjV,KAAKiV,0BAA0BxG,QAC5DwB,GAAQA,IAAS1B,IAEjBpI,IACAnG,KAAKkV,+BAAiClV,KAAKkV,+BAA+BzG,QACtEwB,GAAQA,IAAS9J,I,CAIjC,CA+BO2F,QACHmG,EACAkE,G,QASA,MAAMtV,EAIF,CAAC,EAeL,GAdgC,iBAArBsV,GAAiC,aAAcA,IACtDtV,EAAQyD,QAAU6R,GAGU,iBAArBA,IACN,sBAAuBA,GACpB,WAAYA,GACZ,YAAaA,KAEjBtV,EAAQyD,QAAU6R,aAAgB,EAAhBA,EAAkB7R,QACpCzD,EAAQkF,kBAAoBoQ,aAAgB,EAAhBA,EAAkBpQ,kBAC9ClF,EAAQiF,OAASqQ,aAAgB,EAAhBA,EAAkBrQ,QAGnC9F,KAAKgW,UACL,MAAM,IAAI,EAAAhV,4BAGd,MAAM+K,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QAIvD,GAHoB,QAApB,EAAA9F,KAAK+L,uBAAe,SAAEC,QACtBhM,KAAK+L,gBAAkBA,EAEnBA,EAAgBjG,OAAOe,QACvB,MAAM,IAAI,EAAApH,gBAAgB,0BAa9B,OAVa,QAAb,EAAAO,KAAKgV,gBAAQ,SAAEhH,kBACfhO,KAAKgV,SAAWhV,KAAKoW,eAAenE,GAEpClG,EAAgBjG,OAAOiC,iBAAiB,SAAS,K,MAChC,QAAb,EAAA/H,KAAKgV,gBAAQ,SAAEhH,kBACfhO,KAAKgV,SAAW,IAAI,IAGxBhV,KAAK0V,QAAQW,yBAENrW,KAAKgV,SAASlJ,QAAQ9L,KAAKsW,qBAAqBzV,aAAO,EAAPA,EAASyD,SAAU,CACtEyB,kBAAmBlF,aAAO,EAAPA,EAASkF,kBAC5BD,OAAQiG,EAAgBjG,QAEhC,CAKa4G,kBAAkB7L,G,iDAI3Bb,KAAK0V,QAAQa,kCAEb,MAAMxK,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QAIvD,GAHoB,QAApB,EAAA9F,KAAK+L,uBAAe,SAAEC,QACtBhM,KAAK+L,gBAAkBA,EAEnBA,EAAgBjG,OAAOe,QAEvB,YADA7G,KAAK0V,QAAQc,8BAA8B,oCAK/C,MAAOC,EAAsBC,SAAwBnJ,QAAQsB,IAAI,CAC7D7O,KAAKyL,wBAAwB2I,uBAC7BpU,KAAK8U,YAAY6B,sBAGrB,GAAI5K,EAAgBjG,OAAOe,QAEvB,YADA7G,KAAK0V,QAAQc,8BAA8B,oCAI/C,IAAIxB,EAA4B,KAChC,IACI,OAAQyB,GACJ,IAAK,OACDzB,QAAiB,EAAAtK,eAAekM,YAAY5W,KAAKmV,aAAalQ,SAC9D,MACJ,IAAK,WACD+P,QAAiB,EAAA7D,iBAAiByF,YAAY5W,KAAKmV,aAAalQ,SAChE,MACJ,QACI,IAAIyR,EAGA,OAFA1B,EAAWhV,KAAKoW,eAAeM,G,CAK7C,SAKE,OAJA1W,KAAK0V,QAAQc,8BAA8B,kCACrCxW,KAAKyL,wBAAwB4E,mBACnC2E,SAAAA,EAAUhH,uBACVgH,EAAW,K,CAIf,GAAIjJ,EAAgBjG,OAAOe,QAGvB,OAFAmO,SAAAA,EAAUhH,uBACVhO,KAAK0V,QAAQc,8BAA8B,oCAI/C,IAAKxB,EAGD,OAFA,IAAAjL,UAAS,iCACT/J,KAAK0V,QAAQc,8BAA8B,4BAIlC,QAAb,EAAAxW,KAAKgV,gBAAQ,SAAEhH,kBACfhO,KAAKgV,SAAWA,EAChBA,EAAS1G,OAAOtO,KAAK6W,qBAAqBzQ,KAAKpG,OAE/C,MAAM8W,EAAiB,KACnB9W,KAAK0V,QAAQc,8BAA8B,oCAC3CxB,SAAAA,EAAUhH,kBACVgH,EAAW,IAAI,EAEnBjJ,EAAgBjG,OAAOiC,iBAAiB,QAAS+O,GAEjD,MAAMC,GAAwB,IAAA1N,iBACpBiD,GAAY,EAAD,sCACP0I,aAAQ,EAARA,EAAUtI,kBAAkB,CAC9B3G,kBAAmBlF,aAAO,EAAPA,EAASkF,kBAC5BD,OAAQwG,EAASxG,SAGrBiG,EAAgBjG,OAAOkR,oBAAoB,QAASF,GAChD9W,KAAKgW,UACLhW,KAAK0V,QAAQuB,kCAAkCjX,KAAKiS,QAEpDjS,KAAK0V,QAAQc,8BAA8B,8BAEnD,KACA,CACIvN,SAAUQ,OAAOC,iBACjBC,QAAS,IACT7D,OAAQjF,aAAO,EAAPA,EAASiF,SAGnBoR,EAA2B,IAAI3J,SACjC9G,GAAW2H,YAAW,IAAM3H,KAAW,QAE3C,OAAO8G,QAAQ4J,KAAK,CAACJ,EAAuBG,G,IAsBnCE,gBACTC,EACAhK,G,yCAQA,MAAMxM,EAGF,CAAC,EACiC,mBAA3BwM,EACPxM,EAAQyM,cAAgBD,GAExBxM,EAAQyM,cAAgBD,aAAsB,EAAtBA,EAAwBC,cAChDzM,EAAQiF,OAASuH,aAAsB,EAAtBA,EAAwBvH,QAG7C,MAAMiG,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QACvD,GAAIiG,EAAgBjG,OAAOe,QACvB,MAAM,IAAI,EAAApH,gBAAgB,mCAG9BO,KAAKsX,mBACL,IAAAC,6BAA4BvX,KAAKiS,OAAQuF,OAAOC,SAAU,CACtDC,uBAAwBL,EAAYM,SAASjD,SAGjD1U,KAAK0V,QAAQkC,iCAAiC5X,KAAKiS,OAAQoF,GAE3D,MAAM,WAAEQ,GAAsBR,EAAPS,EAAE,EAAKT,EAAxB,gBACAhI,EAAOgI,EAAYhI,MAAQrP,KAAKiW,QAAS8B,QACzCC,EAAUX,EAAYW,SAAWhY,KAAKiW,QAASgC,MAE/ChU,QAAiBjE,KAAKgV,SAAU5H,YAClC,EAAApI,sBAAsBX,oBAAoB,OAAD,wBAClCyT,GAAE,CACLI,YAAaL,EACbxI,OACA2I,aAEJ,CAAE1K,cAAezM,EAAQyM,cAAexH,OAAQiG,EAAgBjG,SAGpE,GAAI,EAAAd,sBAAsBhB,QAAQC,GAO9B,OANAjE,KAAK0V,QAAQyC,8BACTnY,KAAKiS,OACLoF,EACApT,EAASJ,MAAMjD,QACfqD,EAASJ,MAAME,MAEZ,EAAAiB,sBAAsBL,mBAAmBV,GAGpD,MAAMc,EAAS,EAAAC,sBAAsBJ,uBACjCX,GAGJ,OADAjE,KAAK0V,QAAQ0C,uBAAuBpY,KAAKiS,OAAQoF,EAAatS,GACvDA,CACX,G,CAKaoI,WAAWtM,G,+CACpB,IAAKb,KAAKgW,UACN,MAAM,IAAI,EAAA/U,wBAEd,MAAM8K,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QACjDuS,EAAsBrY,KAAK+L,gBAGjC,GAFA/L,KAAK+L,gBAAkBA,EAEnBA,EAAgBjG,OAAOe,QACvB,MAAM,IAAI,EAAApH,gBAAgB,0BAG9BO,KAAKsY,qBAAqB,cACP,QAAb,EAAAtY,KAAKgV,gBAAQ,eAAE7H,WAAW,CAC5BrH,OAAQiG,EAAgBjG,SAE5BuS,SAAAA,EAAqBrM,O,IAOlBuM,kB,MACyB,UAAX,QAAb,EAAAvY,KAAKgV,gBAAQ,eAAEpK,OAInB5K,KAAKgV,SAASpL,OAClB,CAKO4O,oB,MACH,MAA4B,UAAX,QAAb,EAAAxY,KAAKgV,gBAAQ,eAAEpK,MACR2C,QAAQ9G,UAGZzG,KAAKgV,SAAShL,SACzB,CAEQ8L,qCACJ,MAAM2C,GAAW,IAAAC,eACjB,GAAKD,EAIL,IACIA,EAAS1Q,iBAAiB,oBAAoB,KACtC0Q,EAASE,OACT3Y,KAAKuY,kBAELvY,KAAKwY,oBAAoB1O,O,IAGnC,MAAOnC,IACL,IAAAoC,UAAS,sDAAuDpC,E,CAExE,CAEQyO,eACJnE,GAEA,IAAI+C,EASJ,OANIA,GADCpI,MAAMC,QAAQoF,KAAW,IAAA2G,4BAA2B3G,GAC1C,IAAI,EAAAd,iBAAiBnR,KAAKmV,aAAalQ,QAASgN,EAAOL,aAEvD,IAAI,EAAAlH,eAAe1K,KAAKmV,aAAalQ,QAASgN,GAG7D+C,EAAS1G,OAAOtO,KAAK6W,qBAAqBzQ,KAAKpG,OACxCgV,CACX,CAEQ6B,qBAAqBlP,GACzB,OAAQA,EAAEG,OACN,IAAK,UACD9H,KAAK6Y,kBAAkBlR,EAAEmI,SACzB,MACJ,IAAK,gBACD9P,KAAK8Y,qBAAqBnR,EAAEmI,SAC5B,MACJ,IAAK,aACD9P,KAAKsY,qBAAqB,UAEtC,CAEQO,kBAAkB3L,GACtB,MAAM6L,EAAkD7L,EAAa6C,MAAMC,MACvEC,GAAsB,aAAdA,EAAKlP,OAGXiY,EAA8C9L,EAAa6C,MAAMC,MACnEC,GAAsB,cAAdA,EAAKlP,OAGjB,IAAKgY,EACD,MAAM,IAAI,EAAAtZ,gBAAgB,0CAG9B,MAAMwS,EAAiB,CACnBuF,OAAQtK,EAAasK,OACrBxC,SAAUhV,KAAKgV,SAAUpK,KACzBqL,QAAS,CACL8B,QAASgB,EAAehB,QACxBE,MAAOc,EAAef,QACtBiB,gBAAiBF,EAAeE,gBAChCC,UAAWH,EAAeG,YAI9BF,IACA/G,EAAOkH,aAAe,CAClBC,SAAUJ,IAIlBhZ,KAAKiS,OAASA,EAEdjS,KAAK0V,QAAQ2D,yBAAyBpH,EAC1C,CAEQ6G,qBAAqB7F,GACzB,MAAMpP,EAAQ,EAAAF,oBAAoBC,WAAWqP,GAM7C,GALAjT,KAAKkV,+BAA+BjI,SAAQ9G,GAAiBA,EAActC,MAE3E,IAAAwG,UAASxG,GACT7D,KAAK0V,QAAQ4D,qBAAqBrG,EAAkBrS,QAASqS,EAAkBlP,MAE3EF,aAAiB,EAAAtD,uBAAyBsD,aAAiB,EAAAvD,0BAE3D,MADA,IAAAyJ,UAASlG,GACHA,CAEd,CAEQyU,qBAAqBiB,GACzBvZ,KAAK0V,QAAQ8D,mBAAmBxZ,KAAKiS,OAAQsH,GAC7CvZ,KAAKiS,OAAS,IAClB,CAEQqF,kBACJ,IAAKtX,KAAKgW,UACN,MAAM,IAAI,EAAA/U,uBAElB,CAEQqV,qBAAqBhS,GACzB,MAAMyL,EAAuB,CACzB,CACIhP,KAAM,aAWd,OAPIuD,aAAO,EAAPA,EAAS8U,WACTrJ,EAAMvB,KAAK,CACPzN,KAAM,YACN+O,QAASxL,EAAQ8U,WAIlB,CACHhE,YAAapV,KAAKmV,aAAaC,YAC/BrF,QAER,EAhkBJ,eAC4B,EAAA+E,YAAc,IAAI,EAAAxT,mBAM5B,EAAAmY,iBAAoBC,GAC9B,EAAAvI,iBAAiBsI,iBAAiBC,GAMxB,EAAAC,sBAAyBD,GACnC,EAAAvI,iBAAiBwI,sBAAsBD,E,qbC/D/C,gBAMA,6CAKqB,KAAAnI,QAA6B,IAAA2B,YAuClD,CA/BiB0G,cACTC,EACAC,G,+CAEA,MAAMhS,EAAQ,IAAIiS,YAAeF,EAAW,CAAEG,OAAQF,IAC3C,QAAX,EAAA9Z,KAAKuR,cAAM,SAAEqI,cAAc9R,E,IAUlBC,iBACT8R,EACAzU,EACAvE,G,+CAOA,OALW,QAAX,EAAAb,KAAKuR,cAAM,SAAExJ,iBACT8R,EACAzU,EACAvE,GAEG,K,MACH,OAAW,QAAX,EAAAb,KAAKuR,cAAM,eAAEyF,oBACT6C,EACAzU,EACH,C,qbChDb,cAoBA,UAyCA,0BAiCIzF,YAAYkB,G,MA5BK,KAAAoZ,YAAc,eAUvB,KAAAC,oBAAqC,KAmBzCla,KAAK4V,gBAA0C,QAAxB,EAAA/U,aAAO,EAAPA,EAAS+U,uBAAe,QAAI,IAAI,EAAAxT,uBACvDpC,KAAKT,qBAAuBsB,EAAQtB,qBAEpCS,KAAKma,OAAOrQ,OAChB,CAlBIsQ,cACA,OAAO,IAAAjY,mBAAkB,CACrBkY,oBAAqBra,KAAKT,qBAC1B+a,mBAAoBta,KAAKka,qBAEjC,CAkBcC,O,yCACV,UACUna,KAAKua,2BACXva,KAAKka,0BAA4Bla,KAAKwa,4B,CACxC,MAAO7S,GAAG,CAChB,G,CAMc4S,2B,+CACJva,KAAK4V,gBAAgB7N,iBAAiB,+BAA+B,IAAY,EAAD,sCAC5E/H,KAAK4V,gBAAgBgE,cACvB,gCACA,IAAA1X,4BAA2BlC,KAAKT,sBAExC,KACJ,G,CAMcib,6B,yCACV,OAAO,IAAIjN,SAAQ,CAAO9G,EAASC,IAAW,EAAD,gCACzC,UACU1G,KAAK4V,gBAAgB7N,iBACvB,mCACCD,IACGrB,EAAQqB,EAAMkS,OAAOI,QAAQ,GAEjC,CAAEK,MAAM,UAGNza,KAAK4V,gBAAgBgE,cACvB,kCACA,IAAA3X,6B,CAEN,MAAO0F,GACLjB,EAAOiB,E,CAEf,KACJ,G,CAOQ+S,wBAAwBZ,GAC5B,IACI9Z,KAAK4V,gBACAgE,cAAc,GAAG5Z,KAAKia,cAAcH,EAAalP,OAAQkP,GACzDhQ,O,CACP,MAAOnC,GAAG,CAChB,CAMO0O,0BACAzW,GAEH,IACI,MAAMkI,GAAQ,IAAAvG,8BAA6BvB,KAAKoa,WAAYxa,GAC5DI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMO0R,4BACAzZ,GAEH,IACI,MAAMkI,GAAQ,IAAArG,gCAA+BzB,KAAKoa,WAAYxa,GAC9DI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMO2R,wBACA1Z,GAEH,IACI,MAAMkI,GAAQ,IAAAtG,4BAA2BxB,KAAKoa,WAAYxa,GAC1DI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMO4O,mCACA3W,GAEH,IACI,MAAMkI,GAAQ,IAAApG,uCAAsC1B,KAAKoa,WAAYxa,GACrEI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMOsP,qCACArX,GAEH,IACI,MAAMkI,GAAQ,IAAAlG,yCAAwC5B,KAAKoa,WAAYxa,GACvEI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMO6O,iCACA5W,GAEH,IACI,MAAMkI,GAAQ,IAAAnG,qCAAoC3B,KAAKoa,WAAYxa,GACnEI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMO6R,sBACA5Z,GAEH,IACI,MAAMkI,GAAQ,IAAAjG,0BAAyB7B,KAAKoa,WAAYxa,GACxDI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMOiQ,oCACAhY,GAEH,IACI,MAAMkI,GAAQ,IAAAhG,wCAAuC9B,KAAKoa,WAAYxa,GACtEI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMOyQ,0BACAxY,GAEH,IACI,MAAMkI,GAAQ,IAAA9F,8BAA6BhC,KAAKoa,WAAYxa,GAC5DI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,CAMOwQ,iCACAvY,GAEH,IACI,MAAMkI,GAAQ,IAAA/F,qCAAoC/B,KAAKoa,WAAYxa,GACnEI,KAAK0a,wBAAwB5S,E,CAC/B,MAAOH,GAAG,CAChB,E,0BC5NJ,SAAgBxF,EAAkBiY,GAC9B,MAAO,CACHC,oBAAqBD,EAAQC,oBAC7BC,mBAAoBF,EAAQE,mBAEpC,CA0CA,SAASK,EAAqBP,EAAkBnI,G,oBAC5C,MACM2I,GADiC,QAApB,EAAA3I,aAAM,EAANA,EAAQkH,oBAAY,eAAEC,WAAY,UAAWnH,EAAOkH,aAAaC,SAC5C,YAAc,WAEtD,MAAO,CACHyB,eAAwC,QAAxB,EAAe,QAAf,EAAA5I,aAAM,EAANA,EAAQgE,eAAO,eAAE8B,eAAO,QAAI,KAC5C+C,YAAmC,QAAtB,EAAA7I,aAAM,EAANA,EAAQuF,OAAOrF,eAAO,QAAI,KACvC4I,eAAyC,QAAzB,EAAA9I,aAAM,EAANA,EAAQuF,OAAOwD,kBAAU,QAAI,KAC7CC,UAAWL,EACXM,YAAa,OAAF,QACPC,SAAgC,QAAtB,EAAe,QAAf,EAAAlJ,aAAM,EAANA,EAAQgE,eAAO,eAAEgC,aAAK,QAAI,KACpCjD,SAA0B,QAAhB,EAAA/C,aAAM,EAANA,EAAQ+C,gBAAQ,QAAI,MAC3B7S,EAAkBiY,IAGjC,CAqPA,SAASgB,EACLnJ,EACAoF,G,YAEA,MAAO,CACHa,YAA2C,QAA9B,EAAAmD,OAAOhE,EAAYQ,mBAAW,QAAI,KAC/CxI,KAAkD,QAA5C,EAAgB,QAAhB,EAAAgI,EAAYhI,YAAI,QAAmB,QAAf,EAAA4C,aAAM,EAANA,EAAQgE,eAAO,eAAE8B,eAAO,QAAI,KACtDJ,SAAUN,EAAYM,SAAS/I,KAAIhO,I,QAAW,OAC1CmX,QAAwB,QAAf,EAAAnX,EAAQmX,eAAO,QAAI,KAC5BuD,OAAsB,QAAd,EAAA1a,EAAQ0a,cAAM,QAAI,KAC5B,IAEV,C,meArXA,uCACI,MAAO,CACH1Q,KAAM,kBAEd,EAoBA,sCAA2CwP,GACvC,MAAO,CACHxP,KAAM,mBACNwP,QAASA,EAEjB,EAyBA,sBAiFA,wCAA6CA,GACzC,MAAO,CACHxP,KAAM,qBACNsQ,YAAa/Y,EAAkBiY,GAEvC,EAqBA,0CACIA,EACAnI,GAEA,OAAO,OAAP,QACIrH,KAAM,uBACN2Q,YAAY,GACTZ,EAAqBP,EAASnI,GAEzC,EAkCA,sCACImI,EACAoB,EACAC,GAEA,MAAO,CACH7Q,KAAM,mBACN2Q,YAAY,EACZC,cAAeA,EACfE,WAAYD,QAAAA,EAAa,KACzBP,YAAa/Y,EAAkBiY,GAEvC,EA2BA,iDACIA,GAEA,MAAO,CACHxP,KAAM,+BACNsQ,YAAa/Y,EAAkBiY,GAEvC,EAqBA,mDACIA,EACAnI,GAEA,OAAO,OAAP,QACIrH,KAAM,iCACN2Q,YAAY,GACTZ,EAAqBP,EAASnI,GAEzC,EA6BA,+CACImI,EACAuB,GAEA,MAAO,CACH/Q,KAAM,6BACN2Q,YAAY,EACZC,cAAeG,EACfT,YAAa/Y,EAAkBiY,GAEvC,EAyEA,kDACIA,EACAnI,EACAoF,GAEA,OAAO,OAAP,sBACIzM,KAAM,kCACH+P,EAAqBP,EAASnI,IAC9BmJ,EAAsBnJ,EAAQoF,GAEzC,EA4BA,wCACI+C,EACAnI,EACAoF,EACAuE,GAEA,OAAO,OAAP,sBACIhR,KAAM,qBACN2Q,YAAY,EACZM,mBAAoBD,EAAkB9W,KACnC6V,EAAqBP,EAASnI,IAC9BmJ,EAAsBnJ,EAAQoF,GAEzC,EAiCA,+CACI+C,EACAnI,EACAoF,EACAsE,EACAF,GAEA,OAAO,OAAP,sBACI7Q,KAAM,6BACN2Q,YAAY,EACZC,cAAeG,EACfD,WAAYD,QAAAA,EAAa,MACtBd,EAAqBP,EAASnI,IAC9BmJ,EAAsBnJ,EAAQoF,GAEzC,EA+BA,oCACI+C,EACAnI,EACAsH,GAEA,OAAO,OAAP,QACI3O,KAAM,gBACN2O,MAAOA,GACJoB,EAAqBP,EAASnI,GAEzC,C,8GCpiBA,gBACA,UAUA,iCAAsC6J,EAAoBC,GAAW,GACjE,MAAM,GAAEC,EAAE,IAAEC,GAqBhB,SAAyBH,GACrB,IAAKA,EAAW/M,SAAS,KACrB,MAAM,IAAI,EAAAvP,kBAAkB,iBAAiBsc,gCAGjD,MAAMI,EAAQJ,EAAWpL,MAAM,KAC/B,GAAqB,IAAjBwL,EAAMxH,OACN,MAAM,IAAI,EAAAlV,kBACN,iBAAiBsc,0CAIzB,MAAME,EAAKG,SAASD,EAAM,IAC1B,GAAW,IAAPF,IAAoB,IAARA,EACZ,MAAM,IAAI,EAAAxc,kBACN,iBAAiBsc,iCAA0CE,eAInE,MAAMC,EAAMC,EAAM,GAClB,GAAoB,MAAhBD,aAAG,EAAHA,EAAKvH,QACL,MAAM,IAAI,EAAAlV,kBACN,iBAAiBsc,2CAAoDG,aAAG,EAAHA,EAAKvH,oBAIlF,MAAO,CACHsH,KACAC,IAAKG,EAAWH,GAExB,CAnDwBI,CAAgBP,GAEpC,IAAIQ,EAXgB,GAYhBP,IACAO,GAZY,KAehB,MAAMC,EAAO,IAAIC,UAAU,IAC3BD,EAAK,GAAKD,EACVC,EAAK,GAAKP,EACVO,EAAKxO,IAAIkO,EAAK,GAEd,MAAMQ,EAAsB,IAAIC,WAAW,IAM3C,OALAD,EAAoB1O,IAAIwO,GACxBE,EAAoB1O,IAuCxB,SAAezD,GAEX,IAAIqS,EAAM,EACV,MAAM/b,EAAU,IAAI8b,WAAWpS,EAAKoK,OAAS,GAC7C9T,EAAQmN,IAAIzD,GACZ,IAAK,IAAIsS,KAAQhc,EAAS,CACtB,IAAIic,EAAO,IACX,KAAOA,EAAO,GACVF,IAAQ,EACJC,EAAOC,IACPF,GAAO,GAEXE,IAAS,EACLF,EAAM,QACNA,GAAO,MACPA,GAdC,K,CAkBb,OAAO,IAAID,WAAW,CAACI,KAAKC,MAAMJ,EAAM,KAAMA,EAAM,KACxD,CA3D4BK,CAAMT,GAAO,IAEjB,EAAApT,OAAOC,OAAOqT,GAEbQ,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAC5D,EAwDA,MAAMC,EAAoC,CAAC,EAC3C,IAAK,IAAIC,EAAM,EAAGA,GAAO,IAAMA,IAAO,CAClC,IAAIC,EAAID,EAAI7V,SAAS,IACjB8V,EAAE1I,OAAS,IACX0I,EAAI,IAAMA,GAEdF,EAAUE,GAAKD,C,CAGnB,SAASf,EAAWH,GAEhB,MAAMoB,GADNpB,EAAMA,EAAIqB,eACU5I,OACpB,GAAI2I,EAAU,GAAM,EAChB,MAAM,IAAI,EAAAnd,cAAc,gDAAkD+b,GAE9E,MAAMvH,EAAS2I,EAAU,EACnBtY,EAAS,IAAI2X,WAAWhI,GAC9B,IAAK,IAAI6I,EAAI,EAAGA,EAAI7I,EAAQ6I,IAAK,CAC7B,MAAMC,EAAc,EAAJD,EACVE,EAAexB,EAAIyB,UAAUF,EAASA,EAAU,GACtD,IAAKN,EAAUS,eAAeF,GAC1B,MAAM,IAAI,EAAAvd,cAAc,0BAA4Bud,GAExD1Y,EAAOwY,GAAKL,EAAUO,E,CAE1B,OAAO1Y,CACX,C,6aCjHA,gBACA,UACA,UA4BA,0BACI6Y,EACA/c,G,iDAEA,MAAMoI,EAA4B,QAAjB,EAAApI,aAAO,EAAPA,EAASoI,gBAAQ,QAAI,GAChCU,EAA0B,QAAhB,EAAA9I,aAAO,EAAPA,EAAS8I,eAAO,QAAI,IAC9BoC,GAAkB,IAAAnF,uBAAsB/F,aAAO,EAAPA,EAASiF,QAEvD,GAAkB,mBAAP8X,EACP,MAAM,IAAI,EAAAne,gBAAgB,mCAAmCme,GAGjE,IACIC,EADAN,EAAI,EAGR,KAAOA,EAAItU,GAAU,CACjB,GAAI8C,EAAgBjG,OAAOe,QACvB,MAAM,IAAI,EAAApH,gBAAgB,0BAA0B8d,KAGxD,IACI,aAAaK,EAAG,CAAE9X,OAAQiG,EAAgBjG,Q,CAC5C,MAAOgY,GACLD,EAAYC,EACZP,IAEIA,EAAItU,UACE,IAAA8U,OAAMpU,G,EAKxB,MAAMkU,C,gHCxDV,iCAAsC/X,GAClC,MAAMiG,EAAkB,IAAIiS,gBAM5B,OALIlY,aAAM,EAANA,EAAQe,SACRkF,EAAgBC,QAEhBlG,SAAAA,EAAQiC,iBAAiB,SAAS,IAAMgE,EAAgBC,SAAS,CAAEyO,MAAM,IAEtE1O,CACX,C,oaCdA,gBAkBA,iBAA4BvF,EAAiB3F,G,yCACzC,OAAO,IAAI0M,SAAQ,CAAC9G,EAASC,K,QACzB,GAAmB,QAAf,EAAA7F,aAAO,EAAPA,EAASiF,cAAM,eAAEe,QAEjB,YADAH,EAAO,IAAI,EAAAjH,gBAAgB,kBAI/B,MAAMyO,EAAYE,YAAW,IAAM3H,KAAWD,GAC/B,QAAf,EAAA3F,aAAO,EAAPA,EAASiF,cAAM,SAAEiC,iBAAiB,SAAS,KACvCsG,aAAaH,GACbxH,EAAO,IAAI,EAAAjH,gBAAgB,iBAAiB,GAC9C,GAEV,G,qHC9BA,gBACA,UAEA,uCACIgY,EACA5W,GAEA,MAAMod,EAA2CxG,EAAS1I,SAAS,mBAC7DmP,EAAyBzG,EAASzH,MACpCmO,GAAWA,GAA8B,iBAAZA,GAAyC,oBAAjBA,EAAQpd,OAGjE,IAAKkd,IAA6CC,EAC9C,MAAM,IAAI,EAAA/c,6BAA6B,mDAG3C,GAAI+c,QAAiE1O,IAAvC0O,EAAuBE,aACjD,GAAIF,EAAuBE,YAAcvd,EAAQ6W,uBAC7C,MAAM,IAAI,EAAAvW,6BACN,6FAA6F+c,EAAuBE,oBAAoBvd,EAAQ6W,4CAM5J,IAAA2G,YACI,gJAER,C,uHC7BA,uBAA4Bze,GAEpB,IACIiT,QAAQyL,MAAM,uBAAwB1e,E,CACxC,SAAM,CAEhB,EAEA,uBAA4BA,GAEpB,IACIiT,QAAQhP,MAAM,uBAAwBjE,E,CACxC,SAAM,CAEhB,EAEA,yBAA8BA,GAEtB,IACIiT,QAAQ0L,KAAK,uBAAwB3e,E,CACvC,SAAM,CAEhB,C,6aCtBA,gBACA,UACA,UAoCA,0BACI4e,EACAC,GAEA,IAAIC,EAA4B,KAC5BC,EAA2B,KAC3BC,EAAoC,KACpCC,EAAoC,KACpC9S,EAA0C,KAG9C,MAAMpD,EAAS,CAAO7C,KAAyBlG,IAA2B,EAAD,gCAMrE,GALAif,EAAgB/Y,QAAAA,EAAU,KAE1BiG,SAAAA,EAAiBC,QACjBD,GAAkB,IAAAnF,uBAAsBd,GAEpCiG,EAAgBjG,OAAOe,QACvB,MAAM,IAAI,EAAApH,gBAAgB,iCAG9Bkf,EAAc/e,QAAAA,EAAQ,KAEtB,MAAMkf,EAAUN,EAASzS,EAAgBjG,UAAWlG,GACpDgf,EAAiBE,EACjB,MAAM7W,QAAiB6W,EAEvB,GAAIF,IAAmBE,GAAW7W,IAAayW,EAE3C,YADMD,EAAUxW,GACV,IAAI,EAAAxI,gBAAgB,4DAI9B,OADAif,EAAkBzW,EACXyW,CACX,IAgDA,MAAO,CACH/V,SACAP,QA/CY,IACLsW,QAAAA,EAAmB,KA+C1B7U,QA3CY,IAA2B,EAAD,gCACtC,IACI,MAAM5B,EAAWyW,EACjBA,EAAkB,KAElB,MAAMI,EAAUF,EAChBA,EAAiB,KAEjB,IACI7S,SAAAA,EAAiBC,O,CACnB,MAAOrE,GAAG,OAEN4F,QAAQ0D,WAAW,CACrBhJ,EAAWwW,EAAUxW,GAAYsF,QAAQ9G,UACzCqY,EAAUL,QAAgBK,GAAWvR,QAAQ9G,W,CAEnD,MAAOkB,GAAG,CAChB,IA2BIsC,SAxBoBN,GAAgC,EAAD,gCACnD,MAAM1B,EAAWyW,EACXI,EAAUF,EACVhf,EAAO+e,EACP7Y,EAAS+Y,EAIf,SAFM,IAAAd,OAAMpU,GAGR1B,IAAayW,GACbI,IAAYF,GACZhf,IAAS+e,GACT7Y,IAAW+Y,EAEX,aAAalW,EAAOkW,KAAqBjf,QAAAA,EAAQ,IAGrD,MAAM,IAAI,EAAAH,gBAAgB,6DAC9B,IAQJ,C,saC9HA,gBACA,UAqCA,mBAA2Bme,EAAmB/c,GAC1C,MAAM2F,EAAU3F,aAAO,EAAPA,EAAS2F,QACnBV,EAASjF,aAAO,EAAPA,EAASiF,OAElBiG,GAAkB,IAAAnF,uBAAsBd,GAE9C,OAAO,IAAIyH,SAAQ,CAAO9G,EAASC,IAAW,EAAD,gCACzC,GAAIqF,EAAgBjG,OAAOe,QAEvB,YADAH,EAAO,IAAI,EAAAjH,gBAAgB,sBAI/B,IAAIyO,OACmB,IAAZ1H,IACP0H,EAAYE,YAAW,KACnBrC,EAAgBC,QAChBtF,EAAO,IAAI,EAAAjH,gBAAgB,iBAAiB+G,OAAa,GAC1DA,IAGPuF,EAAgBjG,OAAOiC,iBACnB,SACA,KACIsG,aAAaH,GACbxH,EAAO,IAAI,EAAAjH,gBAAgB,qBAAqB,GAEpD,CAAEgb,MAAM,IAGZ,MAAM9T,EAAe,CAAEH,UAASwF,MAAOD,EAAgBjG,cACjD8X,GACF,IAAIhe,KACAyO,aAAaH,GACbzH,KAAW7G,EAAK,IAEpB,KACIyO,aAAaH,GACbxH,GAAQ,GAEZC,EAER,KACJ,C,4BChEA,SAAgByM,EACZnQ,EACA8b,GAEA,SAAK9b,GAA0B,iBAAVA,IAId8b,EAAaC,OAAMC,GAAeA,KAAehc,GAC5D,C,sFAhBA,uBACIA,EACAgc,GAEA,OAAO7L,EAAcnQ,EAAO,CAACgc,GACjC,EAEA,iB,4BChBA,SAAgBC,EAAmBpY,GAC/B,MAAsB,MAAlBA,EAAIqY,OAAO,GACJrY,EAAIqY,MAAM,GAAI,GAGlBrY,CACX,C,0IANA,uBAQA,wBAA6BA,EAAasY,GACtC,OAAOF,EAAmBpY,GAAO,IAAMsY,CAC3C,EAEA,yBAA8BC,GAC1B,IAAKA,EACD,OAAO,EAGX,MAAMvY,EAAM,IAAIC,IAAIsY,GACpB,MAAwB,QAAjBvY,EAAIwY,UAAuC,SAAjBxY,EAAIyY,QACzC,EAEA,uCAA4CC,GACxC,OAAOA,EACFC,WAAW,IAAK,OAChBA,WAAW,IAAK,OAChBA,WAAW,IAAK,OAChBA,WAAW,IAAK,KAChBA,WAAW,IAAK,MAChBA,WAAW,IAAK,KACzB,C,6KC7BA,gBACA,UAEA,SAAgBvM,IACZ,GAAsB,oBAAX3B,OAIX,OAAOA,MACX,CANA,cAYA,8BACI,MAAMA,EAAS2B,IACf,IAAK3B,EACD,MAAO,GAGX,IACI,OAAOzR,OAAO6U,KAAKpD,E,CACrB,SACE,MAAO,E,CAEf,EAEA,yBACI,GAAwB,oBAAbkH,SAIX,OAAOA,QACX,EAEA,gC,MACI,MAAMiH,EAAoB,QAAX,EAAAxM,WAAW,eAAEyM,SAASD,OACrC,OAAIA,EACOA,EAAS,4BAGb,EACX,EAKA,gCACI,GAgBJ,WAEI,IACI,MAA+B,oBAAjBrL,Y,CAChB,SACE,OAAO,C,CAEf,CAvBQuL,GACA,OAAOvL,aAGX,GA0BuB,oBAAZwL,SAA+C,MAApBA,QAAQC,UAA6C,MAAzBD,QAAQC,SAASC,KAzB/E,MAAM,IAAI,EAAAtgB,gBACN,oKAIR,OAAO,EAAA+U,gBAAgBwL,aAC3B,C,ibC5DA,gBACA,UAUA,UACA,UACA,UAEA,2BAUIrgB,YAAYkB,GATJ,KAAAof,iBAAiD,KAEjD,KAAAC,kCAAmD,KAI1C,KAAA3K,kBACb,sFAGI1U,aAAO,EAAPA,EAAS0U,qBACTvV,KAAKuV,kBAAoB1U,EAAQ0U,oBAGjC1U,aAAO,EAAPA,EAAS2U,cACTxV,KAAKwV,WAAa3U,EAAQ2U,WAElC,CAEaO,a,yCAqBT,OAnBI/V,KAAKwV,YACLxV,KAAKkgB,mCACLC,KAAKC,MAAQpgB,KAAKkgB,kCAAoClgB,KAAKwV,aAE3DxV,KAAKigB,iBAAmB,MAGvBjgB,KAAKigB,mBACNjgB,KAAKigB,iBAAmBjgB,KAAKqgB,mBAC7BrgB,KAAKigB,iBACA5T,MAAK,KACFrM,KAAKkgB,kCAAoCC,KAAKC,KAAK,IAEtDtW,OAAM,KACH9J,KAAKigB,iBAAmB,KACxBjgB,KAAKkgB,kCAAoC,IAAI,KAIlDlgB,KAAKigB,gBAChB,G,CAEatJ,oB,yCACT,MACM2J,SADoBtgB,KAAK+V,cACKtH,OAAO,EAAA5L,+BAE3C,OAA+B,IAA3Byd,EAAgB5L,OACT,KAGJ4L,EAAgB,EAC3B,G,CAEcD,mB,yCACV,IAAIvL,EAA+B,GAEnC,IACI,MAAMyL,QAAwBnW,MAAMpK,KAAKuV,mBAGzC,GAFAT,QAAoByL,EAAgBC,QAE/B5T,MAAMC,QAAQiI,GACf,MAAM,IAAI,EAAA1T,kBACN,6DAIR,MAAMqf,EAAqB3L,EAAYrG,QACnCwD,IAAWjS,KAAK0gB,yBAAyBzO,KAEzCwO,EAAmB/L,UACnB,IAAA3K,UACI,aAAa0W,EACR7R,KAAIqD,GAAUA,EAAOlR,OACrB4f,KACG,0EAIZ7L,EAAcA,EAAYrG,QAAOwD,GAAUjS,KAAK0gB,yBAAyBzO,K,CAE/E,MAAOtK,IACL,IAAAoC,UAASpC,GACTmN,EAAc,EAAAzB,qB,CAGlB,IAAIuN,EAA0D,GAC9D,IACIA,EAA2B,EAAAzP,iBAAiB0P,6B,CAC9C,MAAOlZ,IACL,IAAAoC,UAASpC,E,CAGb,OAAO3H,KAAK8gB,kBACR9gB,KAAK+gB,sCAAsCjM,GAC3C8L,EAER,G,CAEQG,sCAAsCC,GAC1C,OAAOA,EAAgBpS,KAAIoS,IACvB,MASMC,EATmC,CACrClgB,KAAMigB,EAAgBjgB,KACtBoR,QAAS6O,EAAgB5O,SACzBG,SAAUyO,EAAgBxO,MAC1BH,SAAU2O,EAAgB1O,UAC1BG,OAAQuO,EAAgBvO,OACxBC,UAAWsO,EAAgBtO,WAuB/B,OAlBAsO,EAAgBtS,OAAOzB,SAAQyB,IAQ3B,GAPoB,QAAhBA,EAAO9D,OACNqW,EAAkC/b,UAAYwJ,EAAO5H,IACrDma,EAAkCzU,cAC/BwU,EAAgB1N,cACnB2N,EAAkC1N,SAAWyN,EAAgBzN,UAG9C,OAAhB7E,EAAO9D,KAAe,CACtB,MAAMgH,EAAclD,EAAO8E,IAC1ByN,EAAsCrP,YAAcA,EACpDqP,EAAsC/d,SACnC,EAAAiO,iBAAiBsI,iBAAiB7H,GACrCqP,EAAsC9d,SACnC,EAAAgO,iBAAiBwI,sBAAsB/H,E,KAI5CqP,CAAY,GAE3B,CAEQH,kBAAkBI,EAAqBC,GAG3C,MAAO,IAFO,IAAIC,IAAIF,EAAMG,OAAOF,GAAOvS,KAAIqB,GAAQA,EAAKlP,QAE1CugB,UAAU1S,KAAI7N,IAC3B,MAAMwgB,EAAYL,EAAMlR,MAAKC,GAAQA,EAAKlP,OAASA,IAC7CygB,EAAYL,EAAMnR,MAAKC,GAAQA,EAAKlP,OAASA,IAEnD,OAAO,+BACCwgB,GAAa,OAAJ,UAASA,IAClBC,GAAa,OAAJ,UAASA,GACX,GAEvB,CAGQd,yBAAyBzd,GAC7B,IAAKA,GAA4B,iBAAVA,EACnB,OAAO,EASX,KANqB,SAAUA,GAET,UAAWA,GACX,cAAeA,GACX,cAAeA,GAHjB,aAAcA,GAYlC,OAAO,EAGX,IACMA,EAAiCyP,YAClC9F,MAAMC,QAAS5J,EAAiCyP,aAC/CzP,EAAkCyP,UAAUgC,OAE9C,OAAO,EAEX,KACM,WAAYzR,KACb2J,MAAMC,QAAS5J,EAA8ByL,UAC5CzL,EAAgCyL,OAAOgG,OAEzC,OAAO,EAGX,MAAMhG,EAAUzL,EAAgCyL,OAEhD,GAAIA,EAAOwC,MAAKjB,IAASA,GAAwB,iBAATA,KAAuB,SAAUA,KACrE,OAAO,EAGX,MAAMwR,EAAY/S,EAAOsB,MAAKC,GAA4C,QAAnCA,EAA0BrF,OAEjE,GAAI6W,MAEM,QAASA,KACTA,EAA8B3a,MAC9B7D,EAAoCqQ,eAEtC,OAAO,EAIf,MAAMoO,EAAWhT,EAAOsB,MAAKC,GAA4C,OAAnCA,EAA0BrF,OAEhE,UAAI8W,GACM,QAASA,GAAeA,EAA6BlO,IAMnE,E,mBClOH,SAASmO,EAAMC,GACd,aACqCC,EAAOC,QAASD,EAAOC,QAAUF,KAC7DD,EAAKI,OAEZJ,EAAKI,KAAO,CAAC,GAFKJ,EAAKI,KAAKC,KAAOJ,IAKvC,CARA,CAQE5hB,MAAM,WACN,aAEA,IAAIgiB,EAAO,CAAC,EAEZ,SAASC,EAAe7E,GACtB,IAAM,uFAAuF8E,KAAK9E,GAChG,MAAM,IAAI+E,UAAU,mBAExB,CA2DA,OAzDAH,EAAKI,WAAa,SAAShF,GACzB,GAAiB,iBAANA,EAAgB,MAAM,IAAI+E,UAAU,mBAC/C,IAAI5E,EAAG8E,EAAIC,SAASC,mBAAmBnF,IAAKoF,EAAI,IAAI9F,WAAW2F,EAAE3N,QACjE,IAAK6I,EAAI,EAAGA,EAAI8E,EAAE3N,OAAQ6I,IAAKiF,EAAEjF,GAAK8E,EAAEI,WAAWlF,GACnD,OAAOiF,CACT,EAEAR,EAAKU,WAAa,SAASC,GACzB,IAAIpF,EAAGH,EAAI,GACX,IAAKG,EAAI,EAAGA,EAAIoF,EAAIjO,OAAQ6I,IAAKH,EAAE5O,KAAK6M,OAAOuH,aAAaD,EAAIpF,KAChE,OAAOsF,mBAAmBC,OAAO1F,EAAEuD,KAAK,KAC1C,EAEoB,oBAAToC,UAGkB,IAAhBC,OAAO3T,MAEhB2S,EAAKiB,aAAe,SAAUN,GAC1B,OAAOK,OAAO3T,KAAKsT,GAAKrb,SAAS,SACrC,EAEA0a,EAAKkB,aAAe,SAAU9F,GAE5B,OADA6E,EAAe7E,GACR,IAAIV,WAAW9P,MAAM3M,UAAUkf,MAAMgE,KAAKH,OAAO3T,KAAK+N,EAAG,UAAW,GAC7E,IAIA4E,EAAKiB,aAAe,SAAUN,GAC5B,OAAO,IAAKK,OAAOL,GAAMrb,SAAS,SACpC,EAEA0a,EAAKkB,aAAe,SAAS9F,GAE3B,OADA6E,EAAe7E,GACR,IAAIV,WAAW9P,MAAM3M,UAAUkf,MAAMgE,KAAK,IAAIH,OAAO5F,EAAG,UAAW,GAC5E,IAMF4E,EAAKiB,aAAe,SAASN,GAC3B,IAAIpF,EAAGH,EAAI,GAAIgG,EAAMT,EAAIjO,OACzB,IAAK6I,EAAI,EAAGA,EAAI6F,EAAK7F,IAAKH,EAAE5O,KAAK6M,OAAOuH,aAAaD,EAAIpF,KACzD,OAAO8F,KAAKjG,EAAEuD,KAAK,IACrB,EAEAqB,EAAKkB,aAAe,SAAS9F,GAC3B6E,EAAe7E,GACf,IAAIG,EAAG8E,EAAIU,KAAK3F,GAAIoF,EAAI,IAAI9F,WAAW2F,EAAE3N,QACzC,IAAK6I,EAAI,EAAGA,EAAI8E,EAAE3N,OAAQ6I,IAAKiF,EAAEjF,GAAK8E,EAAEI,WAAWlF,GACnD,OAAOiF,CACT,GAIKR,CAET,G,iBChFA,SAAUD,GACV,aAQA,IAAIuB,EAAK,SAASnJ,GAChB,IAAIoD,EAAGgG,EAAI,IAAIC,aAAa,IAC5B,GAAIrJ,EAAM,IAAKoD,EAAI,EAAGA,EAAIpD,EAAKzF,OAAQ6I,IAAKgG,EAAEhG,GAAKpD,EAAKoD,GACxD,OAAOgG,CACT,EAGIE,EAAc,WAAuB,MAAM,IAAI9iB,MAAM,UAAY,EAEjE+iB,EAAK,IAAIhH,WAAW,IACpBiH,EAAK,IAAIjH,WAAW,IAAKiH,EAAG,GAAK,EAErC,IAAIC,EAAMN,IACNO,EAAMP,EAAG,CAAC,IACVQ,EAAUR,EAAG,CAAC,MAAQ,IACtBS,EAAIT,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIU,EAAKV,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,OACjIW,EAAIX,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,OAChIY,EAAIZ,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIa,EAAIb,EAAG,CAAC,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,QAEpI,SAASc,EAAKC,EAAG9G,EAAG+G,EAAGC,GACrBF,EAAE9G,GAAQ+G,GAAK,GAAM,IACrBD,EAAE9G,EAAE,GAAM+G,GAAK,GAAM,IACrBD,EAAE9G,EAAE,GAAM+G,GAAM,EAAK,IACrBD,EAAE9G,EAAE,GAAS,IAAJ+G,EACTD,EAAE9G,EAAE,GAAMgH,GAAK,GAAO,IACtBF,EAAE9G,EAAE,GAAMgH,GAAK,GAAO,IACtBF,EAAE9G,EAAE,GAAMgH,GAAM,EAAM,IACtBF,EAAE9G,EAAE,GAAS,IAAJgH,CACX,CAEA,SAASC,EAAGH,EAAGI,EAAIC,EAAGC,EAAIC,GACxB,IAAIrH,EAAE8E,EAAI,EACV,IAAK9E,EAAI,EAAGA,EAAIqH,EAAGrH,IAAK8E,GAAKgC,EAAEI,EAAGlH,GAAGmH,EAAEC,EAAGpH,GAC1C,OAAQ,EAAM8E,EAAI,IAAO,GAAM,CACjC,CAEA,SAASwC,EAAiBR,EAAGI,EAAIC,EAAGC,GAClC,OAAOH,EAAGH,EAAEI,EAAGC,EAAEC,EAAG,GACtB,CAEA,SAASG,EAAiBT,EAAGI,EAAIC,EAAGC,GAClC,OAAOH,EAAGH,EAAEI,EAAGC,EAAEC,EAAG,GACtB,CA4UA,SAASI,EAAoBC,EAAIC,EAAIC,EAAEC,IA1UvC,SAAsBC,EAAGC,EAAGH,EAAGC,GAsB7B,IArBA,IAmBeG,EAnBXC,EAAc,IAARJ,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EK,EAAc,IAARN,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EO,EAAc,IAARP,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EQ,EAAc,IAARR,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9ES,EAAc,IAART,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EU,EAAc,IAART,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EU,EAAc,IAARR,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9ES,EAAc,IAART,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EU,EAAc,IAARV,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EW,EAAc,IAARX,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EY,EAAc,IAARd,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ee,EAAc,IAARhB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EiB,EAAc,IAARjB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EkB,EAAc,IAARlB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EmB,EAAc,IAARnB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EoB,EAAc,IAARnB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAE9EoB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EACpEiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EACpEiB,EAAMhB,EAED/I,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BgJ,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAEpBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAElBlB,EAAG,GAAKmB,IAAQ,EAAI,IACpBnB,EAAG,GAAKmB,IAAQ,EAAI,IACpBnB,EAAG,GAAKmB,IAAO,GAAK,IACpBnB,EAAG,GAAKmB,IAAO,GAAK,IAEpBnB,EAAG,GAAKoB,IAAQ,EAAI,IACpBpB,EAAG,GAAKoB,IAAQ,EAAI,IACpBpB,EAAG,GAAKoB,IAAO,GAAK,IACpBpB,EAAG,GAAKoB,IAAO,GAAK,IAEpBpB,EAAG,GAAKqB,IAAQ,EAAI,IACpBrB,EAAG,GAAKqB,IAAQ,EAAI,IACpBrB,EAAE,IAAMqB,IAAO,GAAK,IACpBrB,EAAE,IAAMqB,IAAO,GAAK,IAEpBrB,EAAE,IAAMsB,IAAQ,EAAI,IACpBtB,EAAE,IAAMsB,IAAQ,EAAI,IACpBtB,EAAE,IAAMsB,IAAO,GAAK,IACpBtB,EAAE,IAAMsB,IAAO,GAAK,IAEpBtB,EAAE,IAAMuB,IAAQ,EAAI,IACpBvB,EAAE,IAAMuB,IAAQ,EAAI,IACpBvB,EAAE,IAAMuB,IAAO,GAAK,IACpBvB,EAAE,IAAMuB,IAAO,GAAK,IAEpBvB,EAAE,IAAMwB,IAAQ,EAAI,IACpBxB,EAAE,IAAMwB,IAAQ,EAAI,IACpBxB,EAAE,IAAMwB,IAAO,GAAK,IACpBxB,EAAE,IAAMwB,IAAO,GAAK,IAEpBxB,EAAE,IAAMyB,IAAQ,EAAI,IACpBzB,EAAE,IAAMyB,IAAQ,EAAI,IACpBzB,EAAE,IAAMyB,IAAO,GAAK,IACpBzB,EAAE,IAAMyB,IAAO,GAAK,IAEpBzB,EAAE,IAAM0B,IAAQ,EAAI,IACpB1B,EAAE,IAAM0B,IAAQ,EAAI,IACpB1B,EAAE,IAAM0B,IAAO,GAAK,IACpB1B,EAAE,IAAM0B,IAAO,GAAK,IAEpB1B,EAAE,IAAM2B,IAAQ,EAAI,IACpB3B,EAAE,IAAM2B,IAAQ,EAAI,IACpB3B,EAAE,IAAM2B,IAAO,GAAK,IACpB3B,EAAE,IAAM2B,IAAO,GAAK,IAEpB3B,EAAE,IAAM4B,IAAQ,EAAI,IACpB5B,EAAE,IAAM4B,IAAQ,EAAI,IACpB5B,EAAE,IAAM4B,IAAO,GAAK,IACpB5B,EAAE,IAAM4B,IAAO,GAAK,IAEpB5B,EAAE,IAAM6B,IAAS,EAAI,IACrB7B,EAAE,IAAM6B,IAAS,EAAI,IACrB7B,EAAE,IAAM6B,IAAQ,GAAK,IACrB7B,EAAE,IAAM6B,IAAQ,GAAK,IAErB7B,EAAE,IAAM8B,IAAS,EAAI,IACrB9B,EAAE,IAAM8B,IAAS,EAAI,IACrB9B,EAAE,IAAM8B,IAAQ,GAAK,IACrB9B,EAAE,IAAM8B,IAAQ,GAAK,IAErB9B,EAAE,IAAM+B,IAAS,EAAI,IACrB/B,EAAE,IAAM+B,IAAS,EAAI,IACrB/B,EAAE,IAAM+B,IAAQ,GAAK,IACrB/B,EAAE,IAAM+B,IAAQ,GAAK,IAErB/B,EAAE,IAAMgC,IAAS,EAAI,IACrBhC,EAAE,IAAMgC,IAAS,EAAI,IACrBhC,EAAE,IAAMgC,IAAQ,GAAK,IACrBhC,EAAE,IAAMgC,IAAQ,GAAK,IAErBhC,EAAE,IAAMiC,IAAS,EAAI,IACrBjC,EAAE,IAAMiC,IAAS,EAAI,IACrBjC,EAAE,IAAMiC,IAAQ,GAAK,IACrBjC,EAAE,IAAMiC,IAAQ,GAAK,IAErBjC,EAAE,IAAMkC,IAAS,EAAI,IACrBlC,EAAE,IAAMkC,IAAS,EAAI,IACrBlC,EAAE,IAAMkC,IAAQ,GAAK,IACrBlC,EAAE,IAAMkC,IAAQ,GAAK,GACvB,CA4IEC,CAAavC,EAAIC,EAAIC,EAAEC,EACzB,CAEA,SAASqC,EAAqBxC,EAAIC,EAAIC,EAAEC,IA7IxC,SAAuBC,EAAEC,EAAEH,EAAEC,GAsB3B,IArBA,IAmBeG,EAFXiB,EAjBc,IAARpB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAiBrEqB,EAhBK,IAARtB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAgB5DuB,EAfJ,IAARvB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAenDwB,EAdb,IAARxB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAc1CyB,EAbtB,IAARzB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAajC0B,EAZ/B,IAARzB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAYxB0B,EAXxC,IAARxB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAWfyB,EAVjD,IAARzB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAW9E0B,EAVc,IAAR1B,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAUrE2B,EATK,IAAR3B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAS5D4B,EARJ,IAAR9B,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAQjD+B,EAPf,IAARhC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAOtCiC,EAN1B,IAARjC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAM3BkC,EALrC,IAARlC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAKhBmC,EAJhD,IAARnC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAK9EoC,EAJc,IAARnC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAMzE5H,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BgJ,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAGrBF,EAAG,GAAKmB,IAAQ,EAAI,IACpBnB,EAAG,GAAKmB,IAAQ,EAAI,IACpBnB,EAAG,GAAKmB,IAAO,GAAK,IACpBnB,EAAG,GAAKmB,IAAO,GAAK,IAEpBnB,EAAG,GAAKwB,IAAQ,EAAI,IACpBxB,EAAG,GAAKwB,IAAQ,EAAI,IACpBxB,EAAG,GAAKwB,IAAO,GAAK,IACpBxB,EAAG,GAAKwB,IAAO,GAAK,IAEpBxB,EAAG,GAAK6B,IAAS,EAAI,IACrB7B,EAAG,GAAK6B,IAAS,EAAI,IACrB7B,EAAE,IAAM6B,IAAQ,GAAK,IACrB7B,EAAE,IAAM6B,IAAQ,GAAK,IAErB7B,EAAE,IAAMkC,IAAS,EAAI,IACrBlC,EAAE,IAAMkC,IAAS,EAAI,IACrBlC,EAAE,IAAMkC,IAAQ,GAAK,IACrBlC,EAAE,IAAMkC,IAAQ,GAAK,IAErBlC,EAAE,IAAMyB,IAAQ,EAAI,IACpBzB,EAAE,IAAMyB,IAAQ,EAAI,IACpBzB,EAAE,IAAMyB,IAAO,GAAK,IACpBzB,EAAE,IAAMyB,IAAO,GAAK,IAEpBzB,EAAE,IAAM0B,IAAQ,EAAI,IACpB1B,EAAE,IAAM0B,IAAQ,EAAI,IACpB1B,EAAE,IAAM0B,IAAO,GAAK,IACpB1B,EAAE,IAAM0B,IAAO,GAAK,IAEpB1B,EAAE,IAAM2B,IAAQ,EAAI,IACpB3B,EAAE,IAAM2B,IAAQ,EAAI,IACpB3B,EAAE,IAAM2B,IAAO,GAAK,IACpB3B,EAAE,IAAM2B,IAAO,GAAK,IAEpB3B,EAAE,IAAM4B,IAAQ,EAAI,IACpB5B,EAAE,IAAM4B,IAAQ,EAAI,IACpB5B,EAAE,IAAM4B,IAAO,GAAK,IACpB5B,EAAE,IAAM4B,IAAO,GAAK,GACtB,CAOES,CAAczC,EAAIC,EAAIC,EAAEC,EAC1B,CAEA,IAAIuC,EAAQ,IAAIhL,WAAW,CAAC,IAAK,IAAK,IAAK,GAAI,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,MAGhG,SAASiL,EAA0BxC,EAAEyC,EAAKC,EAAEC,EAAKtF,EAAEoC,EAAEM,GACnD,IACII,EAAG/H,EADHwK,EAAI,IAAIrL,WAAW,IAAK2H,EAAI,IAAI3H,WAAW,IAE/C,IAAKa,EAAI,EAAGA,EAAI,GAAIA,IAAKwK,EAAExK,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAKwK,EAAExK,GAAKqH,EAAErH,GACjC,KAAOiF,GAAK,IAAI,CAEd,IADAuC,EAAoBV,EAAE0D,EAAE7C,EAAEwC,GACrBnK,EAAI,EAAGA,EAAI,GAAIA,IAAK4H,EAAEyC,EAAKrK,GAAKsK,EAAEC,EAAKvK,GAAK8G,EAAE9G,GAEnD,IADA+H,EAAI,EACC/H,EAAI,EAAGA,EAAI,GAAIA,IAClB+H,EAAIA,GAAY,IAAPyC,EAAExK,IAAa,EACxBwK,EAAExK,GAAS,IAAJ+H,EACPA,KAAO,EAET9C,GAAK,GACLoF,GAAQ,GACRE,GAAQ,EACV,CACA,GAAItF,EAAI,EAEN,IADAuC,EAAoBV,EAAE0D,EAAE7C,EAAEwC,GACrBnK,EAAI,EAAGA,EAAIiF,EAAGjF,IAAK4H,EAAEyC,EAAKrK,GAAKsK,EAAEC,EAAKvK,GAAK8G,EAAE9G,GAEpD,OAAO,CACT,CAEA,SAASyK,EAAsB7C,EAAEyC,EAAKpF,EAAEoC,EAAEM,GACxC,IACII,EAAG/H,EADHwK,EAAI,IAAIrL,WAAW,IAAK2H,EAAI,IAAI3H,WAAW,IAE/C,IAAKa,EAAI,EAAGA,EAAI,GAAIA,IAAKwK,EAAExK,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAKwK,EAAExK,GAAKqH,EAAErH,GACjC,KAAOiF,GAAK,IAAI,CAEd,IADAuC,EAAoBV,EAAE0D,EAAE7C,EAAEwC,GACrBnK,EAAI,EAAGA,EAAI,GAAIA,IAAK4H,EAAEyC,EAAKrK,GAAK8G,EAAE9G,GAEvC,IADA+H,EAAI,EACC/H,EAAI,EAAGA,EAAI,GAAIA,IAClB+H,EAAIA,GAAY,IAAPyC,EAAExK,IAAa,EACxBwK,EAAExK,GAAS,IAAJ+H,EACPA,KAAO,EAET9C,GAAK,GACLoF,GAAQ,EACV,CACA,GAAIpF,EAAI,EAEN,IADAuC,EAAoBV,EAAE0D,EAAE7C,EAAEwC,GACrBnK,EAAI,EAAGA,EAAIiF,EAAGjF,IAAK4H,EAAEyC,EAAKrK,GAAK8G,EAAE9G,GAExC,OAAO,CACT,CAEA,SAAS0K,EAAc9C,EAAEyC,EAAKvF,EAAEuC,EAAEM,GAChC,IAAI9H,EAAI,IAAIV,WAAW,IACvB8K,EAAqBpK,EAAEwH,EAAEM,EAAEwC,GAE3B,IADA,IAAIQ,EAAK,IAAIxL,WAAW,GACfa,EAAI,EAAGA,EAAI,EAAGA,IAAK2K,EAAG3K,GAAKqH,EAAErH,EAAE,IACxC,OAAOyK,EAAsB7C,EAAEyC,EAAKvF,EAAE6F,EAAG9K,EAC3C,CAEA,SAAS+K,EAAkBhD,EAAEyC,EAAKC,EAAEC,EAAKzF,EAAEuC,EAAEM,GAC3C,IAAI9H,EAAI,IAAIV,WAAW,IACvB8K,EAAqBpK,EAAEwH,EAAEM,EAAEwC,GAE3B,IADA,IAAIQ,EAAK,IAAIxL,WAAW,GACfa,EAAI,EAAGA,EAAI,EAAGA,IAAK2K,EAAG3K,GAAKqH,EAAErH,EAAE,IACxC,OAAOoK,EAA0BxC,EAAEyC,EAAKC,EAAEC,EAAKzF,EAAE6F,EAAG9K,EACtD,CAOA,IAAIgL,EAAW,SAAS5U,GAQtB,IAAI6U,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAPhC5oB,KAAK6oB,OAAS,IAAInM,WAAW,IAC7B1c,KAAKujB,EAAI,IAAIuF,YAAY,IACzB9oB,KAAKskB,EAAI,IAAIwE,YAAY,IACzB9oB,KAAK+oB,IAAM,IAAID,YAAY,GAC3B9oB,KAAKgpB,SAAW,EAChBhpB,KAAKipB,IAAM,EAIXZ,EAAe,IAAV7U,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGxT,KAAKujB,EAAE,GAAkC,KAA7B,EACzD+E,EAAe,IAAV9U,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3B8E,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV/U,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3B+E,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVhV,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3BgF,IAAQ,EAAMC,GAAO,GAChFC,EAAe,IAAVjV,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGxT,KAAKujB,EAAE,GAAkC,KAA3BiF,IAAQ,EAAMC,GAAM,IAC/EzoB,KAAKujB,EAAE,GAAOkF,IAAQ,EAAM,KAC5BC,EAAe,IAAVlV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3BkF,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVnV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3BmF,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVpV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGxT,KAAKujB,EAAE,GAAkC,MAA3BoF,IAAQ,EAAMC,GAAO,GAChF5oB,KAAKujB,EAAE,GAAOqF,IAAQ,EAAM,IAE5B5oB,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDxT,KAAK+oB,IAAI,GAAe,IAAVvV,EAAI,KAAwB,IAAVA,EAAI,MAAe,CACrD,EAmUA,SAAS0V,EAAmBlE,EAAKmE,EAAQtB,EAAGC,EAAMlD,EAAGM,GACnD,IAAI9H,EAAI,IAAIgL,EAASlD,GAGrB,OAFA9H,EAAEgM,OAAOvB,EAAGC,EAAMlD,GAClBxH,EAAEiM,OAAOrE,EAAKmE,GACP,CACT,CAEA,SAASG,EAA0BhF,EAAGiF,EAAM1B,EAAGC,EAAMlD,EAAGM,GACtD,IAAIb,EAAI,IAAI3H,WAAW,IAEvB,OADAwM,EAAmB7E,EAAE,EAAEwD,EAAEC,EAAKlD,EAAEM,GACzBL,EAAiBP,EAAEiF,EAAKlF,EAAE,EACnC,CAEA,SAASmF,EAAiBrE,EAAE0C,EAAExF,EAAEuC,EAAEM,GAChC,IAAI3H,EACJ,GAAI8E,EAAI,GAAI,OAAQ,EAGpB,IAFA8F,EAAkBhD,EAAE,EAAE0C,EAAE,EAAExF,EAAEuC,EAAEM,GAC9BgE,EAAmB/D,EAAG,GAAIA,EAAG,GAAI9C,EAAI,GAAI8C,GACpC5H,EAAI,EAAGA,EAAI,GAAIA,IAAK4H,EAAE5H,GAAK,EAChC,OAAO,CACT,CAEA,SAASkM,EAAsB5B,EAAE1C,EAAE9C,EAAEuC,EAAEM,GACrC,IAAI3H,EACA8G,EAAI,IAAI3H,WAAW,IACvB,GAAI2F,EAAI,GAAI,OAAQ,EAEpB,GADA4F,EAAc5D,EAAE,EAAE,GAAGO,EAAEM,GACiC,IAApDoE,EAA0BnE,EAAG,GAAGA,EAAG,GAAG9C,EAAI,GAAGgC,GAAU,OAAQ,EAEnE,IADA8D,EAAkBN,EAAE,EAAE1C,EAAE,EAAE9C,EAAEuC,EAAEM,GACzB3H,EAAI,EAAGA,EAAI,GAAIA,IAAKsK,EAAEtK,GAAK,EAChC,OAAO,CACT,CAEA,SAASmM,EAASnG,EAAGoG,GACnB,IAAIpM,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKgG,EAAEhG,GAAU,EAALoM,EAAEpM,EACpC,CAEA,SAASqM,EAASxE,GAChB,IAAI7H,EAAGsM,EAAG1E,EAAI,EACd,IAAK5H,EAAI,EAAGA,EAAI,GAAIA,IAClBsM,EAAIzE,EAAE7H,GAAK4H,EAAI,MACfA,EAAIrI,KAAKC,MAAM8M,EAAI,OACnBzE,EAAE7H,GAAKsM,EAAQ,MAAJ1E,EAEbC,EAAE,IAAMD,EAAE,EAAI,IAAMA,EAAE,EACxB,CAEA,SAAS2E,EAASzE,EAAG0E,EAAGvH,GAEtB,IADA,IAAIwH,EAAG7E,IAAM3C,EAAE,GACNjF,EAAI,EAAGA,EAAI,GAAIA,IACtByM,EAAI7E,GAAKE,EAAE9H,GAAKwM,EAAExM,IAClB8H,EAAE9H,IAAMyM,EACRD,EAAExM,IAAMyM,CAEZ,CAEA,SAASC,EAAU7E,EAAGR,GACpB,IAAIrH,EAAG2M,EAAG1H,EACNqF,EAAIvE,IAAM0G,EAAI1G,IAClB,IAAK/F,EAAI,EAAGA,EAAI,GAAIA,IAAKyM,EAAEzM,GAAKqH,EAAErH,GAIlC,IAHAqM,EAASI,GACTJ,EAASI,GACTJ,EAASI,GACJE,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEtB,IADArC,EAAE,GAAKmC,EAAE,GAAK,MACTzM,EAAI,EAAGA,EAAI,GAAIA,IAClBsK,EAAEtK,GAAKyM,EAAEzM,GAAK,OAAWsK,EAAEtK,EAAE,IAAI,GAAM,GACvCsK,EAAEtK,EAAE,IAAM,MAEZsK,EAAE,IAAMmC,EAAE,IAAM,OAAWnC,EAAE,KAAK,GAAM,GACxCrF,EAAKqF,EAAE,KAAK,GAAM,EAClBA,EAAE,KAAO,MACTiC,EAASE,EAAGnC,EAAG,EAAErF,EACnB,CACA,IAAKjF,EAAI,EAAGA,EAAI,GAAIA,IAClB6H,EAAE,EAAE7H,GAAY,IAAPyM,EAAEzM,GACX6H,EAAE,EAAE7H,EAAE,GAAKyM,EAAEzM,IAAI,CAErB,CAEA,SAAS4M,EAASR,EAAGnH,GACnB,IAAI2C,EAAI,IAAIzI,WAAW,IAAK2F,EAAI,IAAI3F,WAAW,IAG/C,OAFAuN,EAAU9E,EAAGwE,GACbM,EAAU5H,EAAGG,GACNsC,EAAiBK,EAAG,EAAG9C,EAAG,EACnC,CAEA,SAAS+H,EAAST,GAChB,IAAItH,EAAI,IAAI3F,WAAW,IAEvB,OADAuN,EAAU5H,EAAGsH,GACC,EAAPtH,EAAE,EACX,CAEA,SAASgI,EAAYjF,EAAGR,GACtB,IAAIrH,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK6H,EAAE7H,GAAKqH,EAAE,EAAErH,IAAMqH,EAAE,EAAErH,EAAE,IAAM,GACtD6H,EAAE,KAAO,KACX,CAEA,SAASkF,EAAElF,EAAGuE,EAAGnH,GACf,IAAK,IAAIjF,EAAI,EAAGA,EAAI,GAAIA,IAAK6H,EAAE7H,GAAKoM,EAAEpM,GAAKiF,EAAEjF,EAC/C,CAEA,SAASgN,EAAEnF,EAAGuE,EAAGnH,GACf,IAAK,IAAIjF,EAAI,EAAGA,EAAI,GAAIA,IAAK6H,EAAE7H,GAAKoM,EAAEpM,GAAKiF,EAAEjF,EAC/C,CAEA,SAASiN,EAAEpF,EAAGuE,EAAGnH,GACf,IAAIqH,EAAG1E,EACJkD,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EACpE6B,EAAK,EAAIC,EAAK,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAC5DC,EAAKxJ,EAAE,GACPyJ,EAAKzJ,EAAE,GACP0J,EAAK1J,EAAE,GACP2J,EAAK3J,EAAE,GACP4J,EAAK5J,EAAE,GACP6J,EAAK7J,EAAE,GACP8J,EAAK9J,EAAE,GACP+J,EAAK/J,EAAE,GACPgK,EAAKhK,EAAE,GACPiK,EAAKjK,EAAE,GACPkK,EAAMlK,EAAE,IACRmK,EAAMnK,EAAE,IACRoK,EAAMpK,EAAE,IACRqK,EAAMrK,EAAE,IACRsK,EAAMtK,EAAE,IACRuK,EAAMvK,EAAE,IAGV6F,IADAwB,EAAIF,EAAE,IACIqC,EACV1D,GAAMuB,EAAIoC,EACV1D,GAAMsB,EAAIqC,EACV1D,GAAMqB,EAAIsC,EACV1D,GAAMoB,EAAIuC,EACV1D,GAAMmB,EAAIwC,EACV1D,GAAMkB,EAAIyC,EACV1D,GAAMiB,EAAI0C,EACV9B,GAAMZ,EAAI2C,EACV9B,GAAMb,EAAI4C,EACV9B,GAAOd,EAAI6C,EACX9B,GAAOf,EAAI8C,EACX9B,GAAOhB,EAAI+C,EACX9B,GAAOjB,EAAIgD,EACX9B,GAAOlB,EAAIiD,EACX9B,GAAOnB,EAAIkD,EAEXzE,IADAuB,EAAIF,EAAE,IACIqC,EACVzD,GAAMsB,EAAIoC,EACVzD,GAAMqB,EAAIqC,EACVzD,GAAMoB,EAAIsC,EACVzD,GAAMmB,EAAIuC,EACVzD,GAAMkB,EAAIwC,EACVzD,GAAMiB,EAAIyC,EACV7B,GAAMZ,EAAI0C,EACV7B,GAAMb,EAAI2C,EACV7B,GAAOd,EAAI4C,EACX7B,GAAOf,EAAI6C,EACX7B,GAAOhB,EAAI8C,EACX7B,GAAOjB,EAAI+C,EACX7B,GAAOlB,EAAIgD,EACX7B,GAAOnB,EAAIiD,EACX7B,GAAOpB,EAAIkD,EAEXxE,IADAsB,EAAIF,EAAE,IACIqC,EACVxD,GAAMqB,EAAIoC,EACVxD,GAAMoB,EAAIqC,EACVxD,GAAMmB,EAAIsC,EACVxD,GAAMkB,EAAIuC,EACVxD,GAAMiB,EAAIwC,EACV5B,GAAMZ,EAAIyC,EACV5B,GAAMb,EAAI0C,EACV5B,GAAOd,EAAI2C,EACX5B,GAAOf,EAAI4C,EACX5B,GAAOhB,EAAI6C,EACX5B,GAAOjB,EAAI8C,EACX5B,GAAOlB,EAAI+C,EACX5B,GAAOnB,EAAIgD,EACX5B,GAAOpB,EAAIiD,EACX5B,GAAOrB,EAAIkD,EAEXvE,IADAqB,EAAIF,EAAE,IACIqC,EACVvD,GAAMoB,EAAIoC,EACVvD,GAAMmB,EAAIqC,EACVvD,GAAMkB,EAAIsC,EACVvD,GAAMiB,EAAIuC,EACV3B,GAAMZ,EAAIwC,EACV3B,GAAMb,EAAIyC,EACV3B,GAAOd,EAAI0C,EACX3B,GAAOf,EAAI2C,EACX3B,GAAOhB,EAAI4C,EACX3B,GAAOjB,EAAI6C,EACX3B,GAAOlB,EAAI8C,EACX3B,GAAOnB,EAAI+C,EACX3B,GAAOpB,EAAIgD,EACX3B,GAAOrB,EAAIiD,EACX3B,GAAOtB,EAAIkD,EAEXtE,IADAoB,EAAIF,EAAE,IACIqC,EACVtD,GAAMmB,EAAIoC,EACVtD,GAAMkB,EAAIqC,EACVtD,GAAMiB,EAAIsC,EACV1B,GAAMZ,EAAIuC,EACV1B,GAAMb,EAAIwC,EACV1B,GAAOd,EAAIyC,EACX1B,GAAOf,EAAI0C,EACX1B,GAAOhB,EAAI2C,EACX1B,GAAOjB,EAAI4C,EACX1B,GAAOlB,EAAI6C,EACX1B,GAAOnB,EAAI8C,EACX1B,GAAOpB,EAAI+C,EACX1B,GAAOrB,EAAIgD,EACX1B,GAAOtB,EAAIiD,EACX1B,GAAOvB,EAAIkD,EAEXrE,IADAmB,EAAIF,EAAE,IACIqC,EACVrD,GAAMkB,EAAIoC,EACVrD,GAAMiB,EAAIqC,EACVzB,GAAMZ,EAAIsC,EACVzB,GAAMb,EAAIuC,EACVzB,GAAOd,EAAIwC,EACXzB,GAAOf,EAAIyC,EACXzB,GAAOhB,EAAI0C,EACXzB,GAAOjB,EAAI2C,EACXzB,GAAOlB,EAAI4C,EACXzB,GAAOnB,EAAI6C,EACXzB,GAAOpB,EAAI8C,EACXzB,GAAOrB,EAAI+C,EACXzB,GAAOtB,EAAIgD,EACXzB,GAAOvB,EAAIiD,EACXzB,GAAOxB,EAAIkD,EAEXpE,IADAkB,EAAIF,EAAE,IACIqC,EACVpD,GAAMiB,EAAIoC,EACVxB,GAAMZ,EAAIqC,EACVxB,GAAMb,EAAIsC,EACVxB,GAAOd,EAAIuC,EACXxB,GAAOf,EAAIwC,EACXxB,GAAOhB,EAAIyC,EACXxB,GAAOjB,EAAI0C,EACXxB,GAAOlB,EAAI2C,EACXxB,GAAOnB,EAAI4C,EACXxB,GAAOpB,EAAI6C,EACXxB,GAAOrB,EAAI8C,EACXxB,GAAOtB,EAAI+C,EACXxB,GAAOvB,EAAIgD,EACXxB,GAAOxB,EAAIiD,EACXxB,GAAOzB,EAAIkD,EAEXnE,IADAiB,EAAIF,EAAE,IACIqC,EACVvB,GAAMZ,EAAIoC,EACVvB,GAAMb,EAAIqC,EACVvB,GAAOd,EAAIsC,EACXvB,GAAOf,EAAIuC,EACXvB,GAAOhB,EAAIwC,EACXvB,GAAOjB,EAAIyC,EACXvB,GAAOlB,EAAI0C,EACXvB,GAAOnB,EAAI2C,EACXvB,GAAOpB,EAAI4C,EACXvB,GAAOrB,EAAI6C,EACXvB,GAAOtB,EAAI8C,EACXvB,GAAOvB,EAAI+C,EACXvB,GAAOxB,EAAIgD,EACXvB,GAAOzB,EAAIiD,EACXvB,GAAO1B,EAAIkD,EAEXtC,IADAZ,EAAIF,EAAE,IACIqC,EACVtB,GAAMb,EAAIoC,EACVtB,GAAOd,EAAIqC,EACXtB,GAAOf,EAAIsC,EACXtB,GAAOhB,EAAIuC,EACXtB,GAAOjB,EAAIwC,EACXtB,GAAOlB,EAAIyC,EACXtB,GAAOnB,EAAI0C,EACXtB,GAAOpB,EAAI2C,EACXtB,GAAOrB,EAAI4C,EACXtB,GAAOtB,EAAI6C,EACXtB,GAAOvB,EAAI8C,EACXtB,GAAOxB,EAAI+C,EACXtB,GAAOzB,EAAIgD,EACXtB,GAAO1B,EAAIiD,EACXtB,GAAO3B,EAAIkD,EAEXrC,IADAb,EAAIF,EAAE,IACIqC,EACVrB,GAAOd,EAAIoC,EACXrB,GAAOf,EAAIqC,EACXrB,GAAOhB,EAAIsC,EACXrB,GAAOjB,EAAIuC,EACXrB,GAAOlB,EAAIwC,EACXrB,GAAOnB,EAAIyC,EACXrB,GAAOpB,EAAI0C,EACXrB,GAAOrB,EAAI2C,EACXrB,GAAOtB,EAAI4C,EACXrB,GAAOvB,EAAI6C,EACXrB,GAAOxB,EAAI8C,EACXrB,GAAOzB,EAAI+C,EACXrB,GAAO1B,EAAIgD,EACXrB,GAAO3B,EAAIiD,EACXrB,GAAO5B,EAAIkD,EAEXpC,IADAd,EAAIF,EAAE,KACKqC,EACXpB,GAAOf,EAAIoC,EACXpB,GAAOhB,EAAIqC,EACXpB,GAAOjB,EAAIsC,EACXpB,GAAOlB,EAAIuC,EACXpB,GAAOnB,EAAIwC,EACXpB,GAAOpB,EAAIyC,EACXpB,GAAOrB,EAAI0C,EACXpB,GAAOtB,EAAI2C,EACXpB,GAAOvB,EAAI4C,EACXpB,GAAOxB,EAAI6C,EACXpB,GAAOzB,EAAI8C,EACXpB,GAAO1B,EAAI+C,EACXpB,GAAO3B,EAAIgD,EACXpB,GAAO5B,EAAIiD,EACXpB,GAAO7B,EAAIkD,EAEXnC,IADAf,EAAIF,EAAE,KACKqC,EACXnB,GAAOhB,EAAIoC,EACXnB,GAAOjB,EAAIqC,EACXnB,GAAOlB,EAAIsC,EACXnB,GAAOnB,EAAIuC,EACXnB,GAAOpB,EAAIwC,EACXnB,GAAOrB,EAAIyC,EACXnB,GAAOtB,EAAI0C,EACXnB,GAAOvB,EAAI2C,EACXnB,GAAOxB,EAAI4C,EACXnB,GAAOzB,EAAI6C,EACXnB,GAAO1B,EAAI8C,EACXnB,GAAO3B,EAAI+C,EACXnB,GAAO5B,EAAIgD,EACXnB,GAAO7B,EAAIiD,EACXnB,GAAO9B,EAAIkD,EAEXlC,IADAhB,EAAIF,EAAE,KACKqC,EACXlB,GAAOjB,EAAIoC,EACXlB,GAAOlB,EAAIqC,EACXlB,GAAOnB,EAAIsC,EACXlB,GAAOpB,EAAIuC,EACXlB,GAAOrB,EAAIwC,EACXlB,GAAOtB,EAAIyC,EACXlB,GAAOvB,EAAI0C,EACXlB,GAAOxB,EAAI2C,EACXlB,GAAOzB,EAAI4C,EACXlB,GAAO1B,EAAI6C,EACXlB,GAAO3B,EAAI8C,EACXlB,GAAO5B,EAAI+C,EACXlB,GAAO7B,EAAIgD,EACXlB,GAAO9B,EAAIiD,EACXlB,GAAO/B,EAAIkD,EAEXjC,IADAjB,EAAIF,EAAE,KACKqC,EACXjB,GAAOlB,EAAIoC,EACXjB,GAAOnB,EAAIqC,EACXjB,GAAOpB,EAAIsC,EACXjB,GAAOrB,EAAIuC,EACXjB,GAAOtB,EAAIwC,EACXjB,GAAOvB,EAAIyC,EACXjB,GAAOxB,EAAI0C,EACXjB,GAAOzB,EAAI2C,EACXjB,GAAO1B,EAAI4C,EACXjB,GAAO3B,EAAI6C,EACXjB,GAAO5B,EAAI8C,EACXjB,GAAO7B,EAAI+C,EACXjB,GAAO9B,EAAIgD,EACXjB,GAAO/B,EAAIiD,EACXjB,GAAOhC,EAAIkD,EAEXhC,IADAlB,EAAIF,EAAE,KACKqC,EACXhB,GAAOnB,EAAIoC,EACXhB,GAAOpB,EAAIqC,EACXhB,GAAOrB,EAAIsC,EACXhB,GAAOtB,EAAIuC,EACXhB,GAAOvB,EAAIwC,EACXhB,GAAOxB,EAAIyC,EACXhB,GAAOzB,EAAI0C,EACXhB,GAAO1B,EAAI2C,EACXhB,GAAO3B,EAAI4C,EACXhB,GAAO5B,EAAI6C,EACXhB,GAAO7B,EAAI8C,EACXhB,GAAO9B,EAAI+C,EACXhB,GAAO/B,EAAIgD,EACXhB,GAAOhC,EAAIiD,EACXhB,GAAOjC,EAAIkD,EAEX/B,IADAnB,EAAIF,EAAE,KACKqC,EAkBX1D,GAAO,IAhBP4C,GAAOrB,EAAIqC,GAiBX3D,GAAO,IAhBP4C,GAAOtB,EAAIsC,GAiBX3D,GAAO,IAhBP4C,GAAOvB,EAAIuC,GAiBX3D,GAAO,IAhBP4C,GAAOxB,EAAIwC,GAiBX3D,GAAO,IAhBP4C,GAAOzB,EAAIyC,GAiBX3D,GAAO,IAhBP4C,GAAO1B,EAAI0C,GAiBX3D,GAAO,IAhBP4C,GAAO3B,EAAI2C,GAiBX/B,GAAO,IAhBPgB,GAAO5B,EAAI4C,GAiBX/B,GAAO,IAhBPgB,GAAO7B,EAAI6C,GAiBX/B,GAAO,IAhBPgB,GAAO9B,EAAI8C,GAiBX/B,GAAO,IAhBPgB,GAAO/B,EAAI+C,GAiBX/B,GAAO,IAhBPgB,GAAOhC,EAAIgD,GAiBX/B,GAAO,IAhBPgB,GAAOjC,EAAIiD,GAiBX/B,GAAO,IAhBPgB,GAAOlC,EAAIkD,GAqBsC1E,GAAjDwB,GAnBAxB,GAAO,IAhBP4C,GAAOpB,EAAIoC,KAkCX9G,EAAI,GACU,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAKnD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACStB,GAAjDsB,EAAKtB,EAAKpD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKrD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKtD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKvD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKxD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKzD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSY,GAAjDZ,EAAKY,EAAKtF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSa,GAAjDb,EAAKa,EAAKvF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQc,GAAhDd,EAAIc,EAAMxF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQe,GAAhDf,EAAIe,EAAMzF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAM1F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAM3F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAM5F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQmB,GAAhDnB,EAAImB,EAAM7F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QAKSxB,GAAjDwB,GAJAxB,GAAMlD,EAAE,EAAI,IAAMA,EAAE,KAGpBA,EAAI,GACU,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAKnD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACStB,GAAjDsB,EAAKtB,EAAKpD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKrD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKtD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKvD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKxD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKzD,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSY,GAAjDZ,EAAKY,EAAKtF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACSa,GAAjDb,EAAKa,EAAKvF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQc,GAAhDd,EAAIc,EAAMxF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQe,GAAhDf,EAAIe,EAAMzF,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAM1F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAM3F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAM5F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACQmB,GAAhDnB,EAAImB,EAAM7F,EAAI,OAAgD,OAAzCA,EAAIrI,KAAKC,MAAM8M,EAAI,QACxCxB,GAAMlD,EAAE,EAAI,IAAMA,EAAE,GAEpBC,EAAG,GAAKiD,EACRjD,EAAG,GAAKkD,EACRlD,EAAG,GAAKmD,EACRnD,EAAG,GAAKoD,EACRpD,EAAG,GAAKqD,EACRrD,EAAG,GAAKsD,EACRtD,EAAG,GAAKuD,EACRvD,EAAG,GAAKwD,EACRxD,EAAG,GAAKqF,EACRrF,EAAG,GAAKsF,EACRtF,EAAE,IAAMuF,EACRvF,EAAE,IAAMwF,EACRxF,EAAE,IAAMyF,EACRzF,EAAE,IAAM0F,EACR1F,EAAE,IAAM2F,EACR3F,EAAE,IAAM4F,CACV,CAEA,SAASgC,EAAE5H,EAAGuE,GACZa,EAAEpF,EAAGuE,EAAGA,EACV,CAEA,SAASsD,EAAS7H,EAAG7H,GACnB,IACIoM,EADAxE,EAAI7B,IAER,IAAKqG,EAAI,EAAGA,EAAI,GAAIA,IAAKxE,EAAEwE,GAAKpM,EAAEoM,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IACpBqD,EAAE7H,EAAGA,GACI,IAANwE,GAAiB,IAANA,GAASa,EAAErF,EAAGA,EAAG5H,GAEjC,IAAKoM,EAAI,EAAGA,EAAI,GAAIA,IAAKvE,EAAEuE,GAAKxE,EAAEwE,EACpC,CAEA,SAASuD,EAAQ9H,EAAG7H,GAClB,IACIoM,EADAxE,EAAI7B,IAER,IAAKqG,EAAI,EAAGA,EAAI,GAAIA,IAAKxE,EAAEwE,GAAKpM,EAAEoM,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IAClBqD,EAAE7H,EAAGA,GACI,IAANwE,GAASa,EAAErF,EAAGA,EAAG5H,GAExB,IAAKoM,EAAI,EAAGA,EAAI,GAAIA,IAAKvE,EAAEuE,GAAKxE,EAAEwE,EACpC,CAEA,SAASwD,EAAkBpD,EAAGnF,EAAGS,GAC/B,IAC8B9B,EAAGhG,EAD7BwK,EAAI,IAAIrL,WAAW,IACnB2H,EAAI,IAAIb,aAAa,IACrBmG,EAAIrG,IAAMd,EAAIc,IAAM6B,EAAI7B,IACxBjB,EAAIiB,IAAM3b,EAAI2b,IAAM1B,EAAI0B,IAC5B,IAAK/F,EAAI,EAAGA,EAAI,GAAIA,IAAKwK,EAAExK,GAAKqH,EAAErH,GAIlC,IAHAwK,EAAE,IAAW,IAANnD,EAAE,IAAS,GAClBmD,EAAE,IAAI,IACNsC,EAAYhG,EAAEgB,GACT9H,EAAI,EAAGA,EAAI,GAAIA,IAClBiF,EAAEjF,GAAG8G,EAAE9G,GACP8E,EAAE9E,GAAGoM,EAAEpM,GAAG4H,EAAE5H,GAAG,EAGjB,IADAoM,EAAE,GAAGtH,EAAE,GAAG,EACL9E,EAAE,IAAKA,GAAG,IAAKA,EAElBuM,EAASH,EAAEnH,EADXe,EAAGwE,EAAExK,IAAI,MAAQ,EAAFA,GAAM,GAErBuM,EAAS3E,EAAE9C,EAAEkB,GACb+G,EAAE3iB,EAAEgiB,EAAExE,GACNoF,EAAEZ,EAAEA,EAAExE,GACNmF,EAAEnF,EAAE3C,EAAEH,GACNkI,EAAE/H,EAAEA,EAAEH,GACN2K,EAAE3K,EAAE1a,GACJqlB,EAAEpL,EAAE+H,GACJa,EAAEb,EAAExE,EAAEwE,GACNa,EAAErF,EAAE3C,EAAE7a,GACN2iB,EAAE3iB,EAAEgiB,EAAExE,GACNoF,EAAEZ,EAAEA,EAAExE,GACN6H,EAAExK,EAAEmH,GACJY,EAAEpF,EAAE9C,EAAET,GACN4I,EAAEb,EAAExE,EAAErB,GACNwG,EAAEX,EAAEA,EAAEtH,GACNmI,EAAErF,EAAEA,EAAEwE,GACNa,EAAEb,EAAEtH,EAAET,GACN4I,EAAEnI,EAAEG,EAAE6B,GACN2I,EAAExK,EAAE7a,GACJmiB,EAASH,EAAEnH,EAAEe,GACbuG,EAAS3E,EAAE9C,EAAEkB,GAEf,IAAKhG,EAAI,EAAGA,EAAI,GAAIA,IAClB8G,EAAE9G,EAAE,IAAIoM,EAAEpM,GACV8G,EAAE9G,EAAE,IAAI4H,EAAE5H,GACV8G,EAAE9G,EAAE,IAAIiF,EAAEjF,GACV8G,EAAE9G,EAAE,IAAI8E,EAAE9E,GAEZ,IAAI6P,EAAM/I,EAAEgJ,SAAS,IACjBC,EAAMjJ,EAAEgJ,SAAS,IAIrB,OAHAJ,EAASG,EAAIA,GACb5C,EAAE8C,EAAIA,EAAIF,GACVnD,EAAUF,EAAEuD,GACL,CACT,CAEA,SAASC,EAAuBxD,EAAGnF,GACjC,OAAOuI,EAAkBpD,EAAGnF,EAAGjB,EACjC,CAEA,SAAS6J,EAAmB9I,EAAGL,GAE7B,OADAZ,EAAYY,EAAG,IACRkJ,EAAuB7I,EAAGL,EACnC,CAEA,SAASoJ,EAAoBvI,EAAGR,EAAGL,GACjC,IAAIjH,EAAI,IAAIV,WAAW,IAEvB,OADAyQ,EAAkB/P,EAAGiH,EAAGK,GACjB8C,EAAqBtC,EAAGxB,EAAItG,EAAGsK,EACxC,CA53BAU,EAASnoB,UAAUytB,OAAS,SAAS7F,EAAGC,EAAM6F,GA2B5C,IA1BA,IACItF,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIzD,EAChCyI,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAFpCC,EAAQtuB,KAAKipB,IAAM,EAAI,KAIvBsF,EAAKvuB,KAAKskB,EAAE,GACZkK,EAAKxuB,KAAKskB,EAAE,GACZmK,EAAKzuB,KAAKskB,EAAE,GACZoK,EAAK1uB,KAAKskB,EAAE,GACZqK,EAAK3uB,KAAKskB,EAAE,GACZsK,EAAK5uB,KAAKskB,EAAE,GACZuK,EAAK7uB,KAAKskB,EAAE,GACZwK,EAAK9uB,KAAKskB,EAAE,GACZyK,EAAK/uB,KAAKskB,EAAE,GACZ0K,EAAKhvB,KAAKskB,EAAE,GAEZ2K,EAAKjvB,KAAKujB,EAAE,GACZ2L,EAAKlvB,KAAKujB,EAAE,GACZ4L,EAAKnvB,KAAKujB,EAAE,GACZ6L,EAAKpvB,KAAKujB,EAAE,GACZ8L,EAAKrvB,KAAKujB,EAAE,GACZ+L,EAAKtvB,KAAKujB,EAAE,GACZgM,EAAKvvB,KAAKujB,EAAE,GACZiM,EAAKxvB,KAAKujB,EAAE,GACZkM,EAAKzvB,KAAKujB,EAAE,GACZmM,EAAK1vB,KAAKujB,EAAE,GAEToK,GAAS,IAcdC,EAFAzI,EAAI,EAGJyI,IAdmDW,GAAmC,MAAtFlG,EAAkB,IAAbR,EAAEC,EAAM,IAA0B,IAAbD,EAAEC,EAAM,KAAc,IAcrCmH,EACXrB,IAdmDY,GAAmC,MAA3BnG,IAAO,IAAlEC,EAAkB,IAAbT,EAAEC,EAAM,IAA0B,IAAbD,EAAEC,EAAM,KAAc,IAAgC,KAcpE,EAAI4H,GAChB9B,IAdmDa,GAAmC,MAA3BnG,IAAO,IAAlEC,EAAkB,IAAbV,EAAEC,EAAM,IAA0B,IAAbD,EAAEC,EAAM,KAAc,IAAgC,KAcpE,EAAI2H,GAChB7B,IAdmDc,GAAmC,MAA3BnG,IAAQ,GAAnEC,EAAkB,IAAbX,EAAEC,EAAM,IAA0B,IAAbD,EAAEC,EAAM,KAAc,IAAgC,KAcpE,EAAI0H,GAEhBrK,GADAyI,IAdmDe,GAAmC,MAA3BnG,IAAQ,GAAnEC,EAAkB,IAAbZ,EAAEC,EAAM,IAA0B,IAAbD,EAAEC,EAAM,KAAc,IAA+B,MAcnE,EAAIyH,MACJ,GAAK3B,GAAM,KACvBA,IAfAgB,GAAQnG,IAAQ,EAAM,OAeV,EAAI6G,GAChB1B,IAfmDiB,GAAmC,MAA3BpG,IAAO,IAAlEC,EAAkB,IAAbb,EAAEC,EAAK,KAA2B,IAAbD,EAAEC,EAAK,MAAe,IAAgC,KAepE,EAAIuH,GAChBzB,IAfmDkB,GAAmC,MAA3BpG,IAAO,IAAlEC,EAAkB,IAAbd,EAAEC,EAAK,KAA2B,IAAbD,EAAEC,EAAK,MAAe,IAAgC,KAepE,EAAIsH,GAChBxB,IAfmDmB,GAAmC,MAA3BpG,IAAQ,GAAnEC,EAAkB,IAAbf,EAAEC,EAAK,KAA2B,IAAbD,EAAEC,EAAK,MAAe,IAAgC,KAepE,EAAIqH,GAIhBtB,EAFA1I,IADAyI,IAfAoB,GAAQpG,IAAO,EAAM0F,IAeT,EAAIY,MACH,GAGbrB,GAAMU,EAAKW,EACXrB,GAAMW,EAAKS,EACXpB,GAAMY,GAAM,EAAIiB,GAChB7B,GAAMa,GAAM,EAAIe,GAEhBtK,GADA0I,GAAMc,GAAM,EAAIa,MACJ,GAAK3B,GAAM,KACvBA,GAAMe,GAAM,EAAIW,GAChB1B,GAAMgB,GAAM,EAAIS,GAChBzB,GAAMiB,GAAM,EAAIO,GAChBxB,GAAMkB,GAAM,EAAIK,GAEhBjK,IADA0I,GAAMmB,GAAM,EAAIG,MACH,GAAKtB,GAAM,KAExBC,EAAK3I,EACL2I,GAAMS,EAAKY,EACXrB,GAAMU,EAAKU,EACXpB,GAAMW,EAAKQ,EACXnB,GAAMY,GAAM,EAAIgB,GAEhBvK,GADA2I,GAAMa,GAAM,EAAIc,MACJ,GAAK3B,GAAM,KACvBA,GAAMc,GAAM,EAAIY,GAChB1B,GAAMe,GAAM,EAAIU,GAChBzB,GAAMgB,GAAM,EAAIQ,GAChBxB,GAAMiB,GAAM,EAAIM,GAIhBtB,EAFA5I,IADA2I,GAAMkB,GAAM,EAAII,MACH,GAGbrB,GAAMQ,EAAKa,EACXrB,GAAMS,EAAKW,EACXpB,GAAMU,EAAKS,EACXnB,GAAMW,EAAKO,EAEX9J,GADA4I,GAAMY,GAAM,EAAIe,MACJ,GAAK3B,GAAM,KACvBA,GAAMa,GAAM,EAAIa,GAChB1B,GAAMc,GAAM,EAAIW,GAChBzB,GAAMe,GAAM,EAAIS,GAChBxB,GAAMgB,GAAM,EAAIO,GAIhBtB,EAFA7I,IADA4I,GAAMiB,GAAM,EAAIK,MACH,GAGbrB,GAAMO,EAAKc,EACXrB,GAAMQ,EAAKY,EACXpB,GAAMS,EAAKU,EACXnB,GAAMU,EAAKQ,EAEX/J,GADA6I,GAAMW,EAAKM,KACC,GAAKjB,GAAM,KACvBA,GAAMY,GAAM,EAAIc,GAChB1B,GAAMa,GAAM,EAAIY,GAChBzB,GAAMc,GAAM,EAAIU,GAChBxB,GAAMe,GAAM,EAAIQ,GAIhBtB,EAFA9I,IADA6I,GAAMgB,GAAM,EAAIM,MACH,GAGbrB,GAAMM,EAAKe,EACXrB,GAAMO,EAAKa,EACXpB,GAAMQ,EAAKW,EACXnB,GAAMS,EAAKS,EAEXhK,GADA8I,GAAMU,EAAKO,KACC,GAAKjB,GAAM,KACvBA,GAAMW,EAAKK,EACXhB,GAAMY,GAAM,EAAIa,GAChBzB,GAAMa,GAAM,EAAIW,GAChBxB,GAAMc,GAAM,EAAIS,GAIhBtB,EAFA/I,IADA8I,GAAMe,GAAM,EAAIO,MACH,GAGbrB,GAAMK,EAAKgB,EACXrB,GAAMM,EAAKc,EACXpB,GAAMO,EAAKY,EACXnB,GAAMQ,EAAKU,EAEXjK,GADA+I,GAAMS,EAAKQ,KACC,GAAKjB,GAAM,KACvBA,GAAMU,EAAKM,EACXhB,GAAMW,EAAKI,EACXf,GAAMY,GAAM,EAAIY,GAChBxB,GAAMa,GAAM,EAAIU,GAIhBtB,EAFAhJ,IADA+I,GAAMc,GAAM,EAAIQ,MACH,GAGbrB,GAAMI,EAAKiB,EACXrB,GAAMK,EAAKe,EACXpB,GAAMM,EAAKa,EACXnB,GAAMO,EAAKW,EAEXlK,GADAgJ,GAAMQ,EAAKS,KACC,GAAKjB,GAAM,KACvBA,GAAMS,EAAKO,EACXhB,GAAMU,EAAKK,EACXf,GAAMW,EAAKG,EACXd,GAAMY,GAAM,EAAIW,GAIhBtB,EAFAjJ,IADAgJ,GAAMa,GAAM,EAAIS,MACH,GAGbrB,GAAMG,EAAKkB,EACXrB,GAAMI,EAAKgB,EACXpB,GAAMK,EAAKc,EACXnB,GAAMM,EAAKY,EAEXnK,GADAiJ,GAAMO,EAAKU,KACC,GAAKjB,GAAM,KACvBA,GAAMQ,EAAKQ,EACXhB,GAAMS,EAAKM,EACXf,GAAMU,EAAKI,EACXd,GAAMW,EAAKE,EAIXZ,EAFAlJ,IADAiJ,GAAMY,GAAM,EAAIU,MACH,GAGbrB,GAAME,EAAKmB,EACXrB,GAAMG,EAAKiB,EACXpB,GAAMI,EAAKe,EACXnB,GAAMK,EAAKa,EAEXpK,GADAkJ,GAAMM,EAAKW,KACC,GAAKjB,GAAM,KACvBA,GAAMO,EAAKS,EACXhB,GAAMQ,EAAKO,EACXf,GAAMS,EAAKK,EACXd,GAAMU,EAAKG,EAUXX,EAJAX,EAAS,MADTzI,GADAA,IAFAA,IADAkJ,GAAMW,EAAKC,KACE,KAED,GAAK9J,EAAM,IAhILyI,GAAM,MAiIT,GAMfY,EAHAX,GADA1I,KAAW,GAKXsJ,EA5GkBX,GAAM,KA6GxBY,EA/FkBX,GAAM,KAgGxBY,EAlFkBX,GAAM,KAmFxBY,EArEkBX,GAAM,KAsExBY,EAxDkBX,GAAM,KAyDxBY,EA3CkBX,GAAM,KA4CxBY,EA9BkBX,GAAM,KA+BxBY,EAjBkBX,GAAM,KAmBxBvG,GAAQ,GACR6F,GAAS,GAEX3tB,KAAKskB,EAAE,GAAKiK,EACZvuB,KAAKskB,EAAE,GAAKkK,EACZxuB,KAAKskB,EAAE,GAAKmK,EACZzuB,KAAKskB,EAAE,GAAKoK,EACZ1uB,KAAKskB,EAAE,GAAKqK,EACZ3uB,KAAKskB,EAAE,GAAKsK,EACZ5uB,KAAKskB,EAAE,GAAKuK,EACZ7uB,KAAKskB,EAAE,GAAKwK,EACZ9uB,KAAKskB,EAAE,GAAKyK,EACZ/uB,KAAKskB,EAAE,GAAK0K,CACd,EAEA5G,EAASnoB,UAAUopB,OAAS,SAASsG,EAAKC,GACxC,IACIzK,EAAGtI,EAAM+E,EAAGrE,EADZsS,EAAI,IAAI/G,YAAY,IAGxB,GAAI9oB,KAAKgpB,SAAU,CAGjB,IAFAzL,EAAIvd,KAAKgpB,SACThpB,KAAK6oB,OAAOtL,KAAO,EACZA,EAAI,GAAIA,IAAKvd,KAAK6oB,OAAOtL,GAAK,EACrCvd,KAAKipB,IAAM,EACXjpB,KAAK0tB,OAAO1tB,KAAK6oB,OAAQ,EAAG,GAC9B,CAIA,IAFA1D,EAAInlB,KAAKskB,EAAE,KAAO,GAClBtkB,KAAKskB,EAAE,IAAM,KACR/G,EAAI,EAAGA,EAAI,GAAIA,IAClBvd,KAAKskB,EAAE/G,IAAM4H,EACbA,EAAInlB,KAAKskB,EAAE/G,KAAO,GAClBvd,KAAKskB,EAAE/G,IAAM,KAaf,IAXAvd,KAAKskB,EAAE,IAAW,EAAJa,EACdA,EAAInlB,KAAKskB,EAAE,KAAO,GAClBtkB,KAAKskB,EAAE,IAAM,KACbtkB,KAAKskB,EAAE,IAAMa,EACbA,EAAInlB,KAAKskB,EAAE,KAAO,GAClBtkB,KAAKskB,EAAE,IAAM,KACbtkB,KAAKskB,EAAE,IAAMa,EAEb0K,EAAE,GAAK7vB,KAAKskB,EAAE,GAAK,EACnBa,EAAI0K,EAAE,KAAO,GACbA,EAAE,IAAM,KACHtS,EAAI,EAAGA,EAAI,GAAIA,IAClBsS,EAAEtS,GAAKvd,KAAKskB,EAAE/G,GAAK4H,EACnBA,EAAI0K,EAAEtS,KAAO,GACbsS,EAAEtS,IAAM,KAKV,IAHAsS,EAAE,IAAM,KAERhT,GAAY,EAAJsI,GAAS,EACZ5H,EAAI,EAAGA,EAAI,GAAIA,IAAKsS,EAAEtS,IAAMV,EAEjC,IADAA,GAAQA,EACHU,EAAI,EAAGA,EAAI,GAAIA,IAAKvd,KAAKskB,EAAE/G,GAAMvd,KAAKskB,EAAE/G,GAAKV,EAAQgT,EAAEtS,GAa5D,IAXAvd,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,GAActkB,KAAKskB,EAAE,IAAM,IAChDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAM,IAChDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAO,GACjDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAO,GACjDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAO,GAAOtkB,KAAKskB,EAAE,IAAO,EAAMtkB,KAAKskB,EAAE,IAAM,IACpEtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAM,IAChDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAO,GACjDtkB,KAAKskB,EAAE,GAAoE,OAA7DtkB,KAAKskB,EAAE,KAAQ,EAAMtkB,KAAKskB,EAAE,IAAO,GAEjD1C,EAAI5hB,KAAKskB,EAAE,GAAKtkB,KAAK+oB,IAAI,GACzB/oB,KAAKskB,EAAE,GAAS,MAAJ1C,EACPrE,EAAI,EAAGA,EAAI,EAAGA,IACjBqE,GAAO5hB,KAAKskB,EAAE/G,GAAKvd,KAAK+oB,IAAIxL,GAAM,IAAMqE,IAAM,IAAO,EACrD5hB,KAAKskB,EAAE/G,GAAS,MAAJqE,EAGd+N,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAQ,GAAM5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,IACrCqL,EAAIC,EAAO,IAAO5vB,KAAKskB,EAAE,KAAO,EAAK,GACvC,EAEA8D,EAASnoB,UAAUmpB,OAAS,SAASvB,EAAGC,EAAM6F,GAC5C,IAAIpQ,EAAGuS,EAEP,GAAI9vB,KAAKgpB,SAAU,CAIjB,KAHA8G,EAAQ,GAAK9vB,KAAKgpB,UACP2E,IACTmC,EAAOnC,GACJpQ,EAAI,EAAGA,EAAIuS,EAAMvS,IACpBvd,KAAK6oB,OAAO7oB,KAAKgpB,SAAWzL,GAAKsK,EAAEC,EAAKvK,GAI1C,GAHAoQ,GAASmC,EACThI,GAAQgI,EACR9vB,KAAKgpB,UAAY8G,EACb9vB,KAAKgpB,SAAW,GAClB,OACFhpB,KAAK0tB,OAAO1tB,KAAK6oB,OAAQ,EAAG,IAC5B7oB,KAAKgpB,SAAW,CAClB,CASA,GAPI2E,GAAS,KACXmC,EAAOnC,EAASA,EAAQ,GACxB3tB,KAAK0tB,OAAO7F,EAAGC,EAAMgI,GACrBhI,GAAQgI,EACRnC,GAASmC,GAGPnC,EAAO,CACT,IAAKpQ,EAAI,EAAGA,EAAIoQ,EAAOpQ,IACrBvd,KAAK6oB,OAAO7oB,KAAKgpB,SAAWzL,GAAKsK,EAAEC,EAAKvK,GAC1Cvd,KAAKgpB,UAAY2E,CACnB,CACF,EA+jBA,IAAIoC,EAAqBvG,EACrBwG,EAA0BvG,EAc1BwG,EAAI,CACN,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,UAAY,UAAY,WACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,UAAY,WAAY,YAGtC,SAASC,EAAqBC,EAAIC,EAAIvI,EAAGjD,GAyBvC,IAxBA,IACIyL,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAIC,EAAI/T,EAAG2M,EAAG5F,EAAGC,EAAGoF,EAAGnH,EAAG2C,EAAG9C,EAH7BkP,EAAK,IAAIC,WAAW,IAAKC,EAAK,IAAID,WAAW,IAK7CE,EAAMvB,EAAG,GACTwB,EAAMxB,EAAG,GACTyB,EAAMzB,EAAG,GACT0B,EAAM1B,EAAG,GACT2B,EAAM3B,EAAG,GACT4B,EAAM5B,EAAG,GACT6B,EAAM7B,EAAG,GACT8B,EAAM9B,EAAG,GAET+B,EAAM9B,EAAG,GACT+B,EAAM/B,EAAG,GACTgC,EAAMhC,EAAG,GACTiC,EAAMjC,EAAG,GACTkC,EAAMlC,EAAG,GACTmC,EAAMnC,EAAG,GACToC,EAAMpC,EAAG,GACTqC,EAAMrC,EAAG,GAETsC,EAAM,EACH9N,GAAK,KAAK,CACf,IAAKrH,EAAI,EAAGA,EAAI,GAAIA,IAClB2M,EAAI,EAAI3M,EAAImV,EACZnB,EAAGhU,GAAMsK,EAAEqC,EAAE,IAAM,GAAOrC,EAAEqC,EAAE,IAAM,GAAOrC,EAAEqC,EAAE,IAAM,EAAKrC,EAAEqC,EAAE,GAC9DuH,EAAGlU,GAAMsK,EAAEqC,EAAE,IAAM,GAAOrC,EAAEqC,EAAE,IAAM,GAAOrC,EAAEqC,EAAE,IAAM,EAAKrC,EAAEqC,EAAE,GAEhE,IAAK3M,EAAI,EAAGA,EAAI,GAAIA,IA+HlB,GA9HA8S,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EAGNnB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EAON7I,EAAQ,OAFRpF,EAAIkO,GAEYjQ,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAI2N,GAIY5P,EAAIiC,IAAM,GAM1BqF,GAAS,OAFTpF,GAAM+N,IAAQ,GAAOR,GAAO,KAAcQ,IAAQ,GAAOR,GAAO,KAAcA,IAAQ,EAAYQ,GAAO,KAExF9P,GAAK+B,IAAM,GAC5BY,GAAS,OAJTb,GAAMwN,IAAQ,GAAOQ,GAAO,KAAcR,IAAQ,GAAOQ,GAAO,KAAcA,IAAQ,EAAYR,GAAO,KAIxFzP,GAAKiC,IAAM,GAM5BqF,GAAS,OAFTpF,EAAK+N,EAAMC,GAASD,EAAME,GAEThQ,GAAK+B,IAAM,GAC5BY,GAAS,OAJTb,EAAKwN,EAAMC,GAASD,EAAME,GAIT3P,GAAKiC,IAAM,GAG5BA,EAAI2L,EAAI,EAAF1S,GAGNoM,GAAS,OAFTpF,EAAI0L,EAAI,EAAF1S,EAAI,IAEOiF,GAAK+B,IAAM,GAC5BY,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAG5BA,EAAIiN,EAAGhU,EAAE,IAGQiF,IAFjB+B,EAAIkN,EAAGlU,EAAE,OAEmB,GAC5B4H,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAG5Ba,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,GAUXoF,EAAQ,OAFRpF,EAJA+M,EAAS,MAAJ3H,EAAanH,GAAK,IAMPA,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAJA+M,EAAS,MAAJlM,GAFL9C,GAAK8C,IAAM,KAEY,IAQP9C,EAAIiC,IAAM,GAM1BqF,GAAS,OAFTpF,GAAM2N,IAAQ,GAAOR,GAAO,IAAcA,IAAQ,EAAYQ,GAAO,KAAmBR,IAAQ,EAAYQ,GAAO,KAElG1P,GAAK+B,IAAM,GAC5BY,GAAS,OAJTb,GAAMoN,IAAQ,GAAOQ,GAAO,IAAcA,IAAQ,EAAYR,GAAO,KAAmBQ,IAAQ,EAAYR,GAAO,KAIlGrP,GAAKiC,IAAM,GAMX9B,IAFjB+B,EAAK2N,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,KAEX,GAC5BjN,GAAS,OAJTb,EAAKoN,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,GAItBvP,GAAKiC,IAAM,GAM5BsM,EAAW,OAHXzL,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,KACXlC,GAAK8C,IAAM,KAEgB,GAC3BiM,EAAW,MAAJzH,EAAenH,GAAK,GAM3BmH,EAAQ,OAFRpF,EAAIyM,GAEYxO,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIkM,GAIYnO,EAAIiC,IAAM,GAKT9B,IAFjB+B,EAAI+M,KAEwB,GAC5BnM,GAAS,OAJTb,EAAI+M,GAIahP,GAAKiC,IAAM,GAS5BqN,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EANAtB,EAAW,OAHXrL,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,KACXlC,GAAK8C,IAAM,KAEgB,GAO3B4M,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAENuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAdAtB,EAAW,MAAJrH,EAAenH,GAAK,GAe3B+P,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAEF7T,EAAE,IAAO,GACX,IAAK2M,EAAI,EAAGA,EAAI,GAAIA,IAElB5F,EAAIiN,EAAGrH,GAGPP,EAAQ,OAFRpF,EAAIkN,EAAGvH,IAES1H,EAAI+B,IAAM,GAC1BY,EAAQ,MAAJb,EAAYjC,EAAIiC,IAAM,GAE1BA,EAAIiN,GAAIrH,EAAE,GAAG,IAGbP,GAAS,OAFTpF,EAAIkN,GAAIvH,EAAE,GAAG,KAEI1H,GAAK+B,IAAM,GAC5BY,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAG5B+M,EAAKE,GAAIrH,EAAE,GAAG,IAKdP,GAAS,OAFTpF,IAFA+M,EAAKG,GAAIvH,EAAE,GAAG,OAED,EAAMmH,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAExE7O,GAAK+B,IAAM,GAC5BY,GAAS,OAJTb,GAAM+M,IAAO,EAAMC,GAAM,KAAaD,IAAO,EAAMC,GAAM,IAAYD,IAAO,GAI3DhP,GAAKiC,IAAM,GAG5B+M,EAAKE,GAAIrH,EAAE,IAAI,IAKE1H,IAFjB+B,IAFA+M,EAAKG,GAAIvH,EAAE,IAAI,OAEF,GAAOmH,GAAM,KAAcA,IAAO,GAAYC,GAAM,IAAmBA,IAAO,EAAMD,GAAM,OAE3E,GAC5BlM,GAAS,OAJTb,GAAM+M,IAAO,GAAOC,GAAM,KAAcA,IAAO,GAAYD,GAAM,GAAkBA,IAAO,GAIzEhP,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEXgN,EAAGrH,GAAU,MAAJ/E,EAAe9C,GAAK,GAC7BoP,EAAGvH,GAAU,MAAJP,EAAenH,GAAK,GASnCmH,EAAQ,OAFRpF,EAAI2N,GAEY1P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIoN,GAIYrP,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAKuB,EAAW,MAAJvM,EAAe9C,GAAK,GACnC+N,EAAG,GAAK8B,EAAW,MAAJvI,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAI4N,GAEY3P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIqN,GAIYtP,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAKwB,EAAW,MAAJxM,EAAe9C,GAAK,GACnC+N,EAAG,GAAK+B,EAAW,MAAJxI,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAI6N,GAEY5P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIsN,GAIYvP,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAKyB,EAAW,MAAJzM,EAAe9C,GAAK,GACnC+N,EAAG,GAAKgC,EAAW,MAAJzI,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAI8N,GAEY7P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIuN,GAIYxP,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAK0B,EAAW,MAAJ1M,EAAe9C,GAAK,GACnC+N,EAAG,GAAKiC,EAAW,MAAJ1I,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAI+N,GAEY9P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIwN,GAIYzP,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAK2B,EAAW,MAAJ3M,EAAe9C,GAAK,GACnC+N,EAAG,GAAKkC,EAAW,MAAJ3I,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAIgO,GAEY/P,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAIyN,GAIY1P,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAK4B,EAAW,MAAJ5M,EAAe9C,GAAK,GACnC+N,EAAG,GAAKmC,EAAW,MAAJ5I,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAIiO,GAEYhQ,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAI0N,GAIY3P,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAK6B,EAAW,MAAJ7M,EAAe9C,GAAK,GACnC+N,EAAG,GAAKoC,EAAW,MAAJ7I,EAAenH,GAAK,GAKnCmH,EAAQ,OAFRpF,EAAIkO,GAEYjQ,EAAI+B,IAAM,GAC1BY,EAAQ,OAJRb,EAAI2N,GAIY5P,EAAIiC,IAAM,GAE1BA,EAAI6L,EAAG,GAGU3N,IAFjB+B,EAAI6L,EAAG,MAEqB,GAC5BjL,GAAS,MAAJb,EAAYjC,GAAKiC,IAAM,GAI5BjC,IADA8C,IADA3C,IAHAmH,GAAS,MAAJpF,KAGM,MACA,MACA,GAEX4L,EAAG,GAAK8B,EAAW,MAAJ9M,EAAe9C,GAAK,GACnC+N,EAAG,GAAKqC,EAAW,MAAJ9I,EAAenH,GAAK,GAEnCkQ,GAAO,IACP9N,GAAK,GACP,CAEA,OAAOA,CACT,CAEA,SAAS+N,EAAY3N,EAAK6C,EAAGjD,GAC3B,IAGIrH,EAHA4S,EAAK,IAAIqB,WAAW,GACpBpB,EAAK,IAAIoB,WAAW,GACpBnN,EAAI,IAAI3H,WAAW,KAChB8F,EAAIoC,EAuBX,IArBAuL,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WAERC,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UAERF,EAAqBC,EAAIC,EAAIvI,EAAGjD,GAChCA,GAAK,IAEArH,EAAI,EAAGA,EAAIqH,EAAGrH,IAAK8G,EAAE9G,GAAKsK,EAAErF,EAAEoC,EAAErH,GAQrC,IAPA8G,EAAEO,GAAK,IAGPP,GADAO,EAAI,IAAI,KAAKA,EAAE,IAAI,EAAE,IACjB,GAAK,EACTR,EAAKC,EAAGO,EAAE,EAAKpC,EAAI,UAAc,EAAGA,GAAK,GACzC0N,EAAqBC,EAAIC,EAAI/L,EAAGO,GAE3BrH,EAAI,EAAGA,EAAI,EAAGA,IAAK6G,EAAKY,EAAK,EAAEzH,EAAG4S,EAAG5S,GAAI6S,EAAG7S,IAEjD,OAAO,CACT,CAEA,SAASqV,EAAIvN,EAAG0E,GACd,IAAIJ,EAAIrG,IAAMd,EAAIc,IAAM6B,EAAI7B,IACxBjB,EAAIiB,IAAM3b,EAAI2b,IAAM1B,EAAI0B,IACxBuM,EAAIvM,IAAMgB,EAAIhB,IAAM0G,EAAI1G,IAE5BiH,EAAEZ,EAAGtE,EAAE,GAAIA,EAAE,IACbkF,EAAEP,EAAGD,EAAE,GAAIA,EAAE,IACbS,EAAEb,EAAGA,EAAGK,GACRM,EAAE9H,EAAG6C,EAAE,GAAIA,EAAE,IACbiF,EAAEN,EAAGD,EAAE,GAAIA,EAAE,IACbS,EAAEhI,EAAGA,EAAGwH,GACRQ,EAAErF,EAAGE,EAAE,GAAI0E,EAAE,IACbS,EAAErF,EAAGA,EAAGnB,GACRwG,EAAEnI,EAAGgD,EAAE,GAAI0E,EAAE,IACbO,EAAEjI,EAAGA,EAAGA,GACRkI,EAAE5iB,EAAG6a,EAAGmH,GACRY,EAAE3I,EAAGS,EAAG8C,GACRmF,EAAEuF,EAAGxN,EAAG8C,GACRmF,EAAEhG,EAAG9B,EAAGmH,GAERa,EAAEnF,EAAE,GAAI1d,EAAGia,GACX4I,EAAEnF,EAAE,GAAIf,EAAGuL,GACXrF,EAAEnF,EAAE,GAAIwK,EAAGjO,GACX4I,EAAEnF,EAAE,GAAI1d,EAAG2c,EACb,CAEA,SAASuO,EAAMxN,EAAG0E,EAAGvH,GACnB,IAAIjF,EACJ,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IACjBuM,EAASzE,EAAE9H,GAAIwM,EAAExM,GAAIiF,EAEzB,CAEA,SAASsQ,GAAKvP,EAAG8B,GACf,IAAIvN,EAAKwL,IAAMyP,EAAKzP,IAAM0P,EAAK1P,IAC/B2J,EAAS+F,EAAI3N,EAAE,IACfmF,EAAE1S,EAAIuN,EAAE,GAAI2N,GACZxI,EAAEuI,EAAI1N,EAAE,GAAI2N,GACZ/I,EAAU1G,EAAGwP,GACbxP,EAAE,KAAO6G,EAAStS,IAAO,CAC3B,CAEA,SAASmb,GAAW5N,EAAG0E,EAAG3M,GACxB,IAAIoF,EAAGjF,EAKP,IAJAmM,EAASrE,EAAE,GAAIzB,GACf8F,EAASrE,EAAE,GAAIxB,GACf6F,EAASrE,EAAE,GAAIxB,GACf6F,EAASrE,EAAE,GAAIzB,GACVrG,EAAI,IAAKA,GAAK,IAAKA,EAEtBsV,EAAMxN,EAAG0E,EADTvH,EAAKpF,EAAGG,EAAE,EAAG,KAAS,EAAFA,GAAQ,GAE5BqV,EAAI7I,EAAG1E,GACPuN,EAAIvN,EAAGA,GACPwN,EAAMxN,EAAG0E,EAAGvH,EAEhB,CAEA,SAAS0Q,GAAW7N,EAAGjI,GACrB,IAAI2M,EAAI,CAACzG,IAAMA,IAAMA,IAAMA,KAC3BoG,EAASK,EAAE,GAAI9F,GACfyF,EAASK,EAAE,GAAI7F,GACfwF,EAASK,EAAE,GAAIlG,GACf2G,EAAET,EAAE,GAAI9F,EAAGC,GACX+O,GAAW5N,EAAG0E,EAAG3M,EACnB,CAEA,SAAS+V,GAAoBC,EAAIC,EAAIC,GACnC,IAEI/V,EAFA8E,EAAI,IAAI3F,WAAW,IACnB2I,EAAI,CAAC/B,IAAMA,IAAMA,IAAMA,KAY3B,IATKgQ,GAAQ7P,EAAY4P,EAAI,IAC7BV,EAAYtQ,EAAGgR,EAAI,IACnBhR,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET6Q,GAAW7N,EAAGhD,GACdyQ,GAAKM,EAAI/N,GAEJ9H,EAAI,EAAGA,EAAI,GAAIA,IAAK8V,EAAG9V,EAAE,IAAM6V,EAAG7V,GACvC,OAAO,CACT,CAEA,IAAIgW,GAAI,IAAI/P,aAAa,CAAC,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,KAEvK,SAASgQ,GAAKjQ,EAAGc,GACf,IAAIoP,EAAOlW,EAAG2M,EAAGhF,EACjB,IAAK3H,EAAI,GAAIA,GAAK,KAAMA,EAAG,CAEzB,IADAkW,EAAQ,EACHvJ,EAAI3M,EAAI,GAAI2H,EAAI3H,EAAI,GAAI2M,EAAIhF,IAAKgF,EACpC7F,EAAE6F,IAAMuJ,EAAQ,GAAKpP,EAAE9G,GAAKgW,GAAErJ,GAAK3M,EAAI,KACvCkW,EAAQ3W,KAAKC,OAAOsH,EAAE6F,GAAK,KAAO,KAClC7F,EAAE6F,IAAc,IAARuJ,EAEVpP,EAAE6F,IAAMuJ,EACRpP,EAAE9G,GAAK,CACT,CAEA,IADAkW,EAAQ,EACHvJ,EAAI,EAAGA,EAAI,GAAIA,IAClB7F,EAAE6F,IAAMuJ,GAASpP,EAAE,KAAO,GAAKkP,GAAErJ,GACjCuJ,EAAQpP,EAAE6F,IAAM,EAChB7F,EAAE6F,IAAM,IAEV,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK7F,EAAE6F,IAAMuJ,EAAQF,GAAErJ,GAC3C,IAAK3M,EAAI,EAAGA,EAAI,GAAIA,IAClB8G,EAAE9G,EAAE,IAAM8G,EAAE9G,IAAM,EAClBgG,EAAEhG,GAAY,IAAP8G,EAAE9G,EAEb,CAEA,SAASmW,GAAOnQ,GACd,IAA8BhG,EAA1B8G,EAAI,IAAIb,aAAa,IACzB,IAAKjG,EAAI,EAAGA,EAAI,GAAIA,IAAK8G,EAAE9G,GAAKgG,EAAEhG,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKgG,EAAEhG,GAAK,EAChCiW,GAAKjQ,EAAGc,EACV,CAGA,SAASsP,GAAYC,EAAI/L,EAAGjD,EAAGyO,GAC7B,IACI9V,EAAG2M,EADH7H,EAAI,IAAI3F,WAAW,IAAK4H,EAAI,IAAI5H,WAAW,IAAK6G,EAAI,IAAI7G,WAAW,IAC7D2H,EAAI,IAAIb,aAAa,IAC3B6B,EAAI,CAAC/B,IAAMA,IAAMA,IAAMA,KAE3BqP,EAAYtQ,EAAGgR,EAAI,IACnBhR,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET,IAAIwR,EAAQjP,EAAI,GAChB,IAAKrH,EAAI,EAAGA,EAAIqH,EAAGrH,IAAKqW,EAAG,GAAKrW,GAAKsK,EAAEtK,GACvC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKqW,EAAG,GAAKrW,GAAK8E,EAAE,GAAK9E,GAO7C,IALAoV,EAAYpP,EAAGqQ,EAAGvG,SAAS,IAAKzI,EAAE,IAClC8O,GAAOnQ,GACP2P,GAAW7N,EAAG9B,GACduP,GAAKc,EAAIvO,GAEJ9H,EAAI,GAAIA,EAAI,GAAIA,IAAKqW,EAAGrW,GAAK8V,EAAG9V,GAIrC,IAHAoV,EAAYrO,EAAGsP,EAAIhP,EAAI,IACvB8O,GAAOpP,GAEF/G,EAAI,EAAGA,EAAI,GAAIA,IAAK8G,EAAE9G,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK8G,EAAE9G,GAAKgG,EAAEhG,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAClB,IAAK2M,EAAI,EAAGA,EAAI,GAAIA,IAClB7F,EAAE9G,EAAE2M,IAAM5F,EAAE/G,GAAK8E,EAAE6H,GAKvB,OADAsJ,GAAKI,EAAGvG,SAAS,IAAKhJ,GACfwP,CACT,CAwCA,SAASC,GAAiBjM,EAAG+L,EAAIhP,EAAGwO,GAClC,IAAI7V,EACAyM,EAAI,IAAItN,WAAW,IAAK4H,EAAI,IAAI5H,WAAW,IAC3C2I,EAAI,CAAC/B,IAAMA,IAAMA,IAAMA,KACvByG,EAAI,CAACzG,IAAMA,IAAMA,IAAMA,KAE3B,GAAIsB,EAAI,GAAI,OAAQ,EAEpB,GA9CF,SAAmBrB,EAAG8B,GACpB,IAAI2E,EAAI1G,IAAMyQ,EAAMzQ,IAAM0Q,EAAM1Q,IAC5B2Q,EAAM3Q,IAAM4Q,EAAO5Q,IAAM6Q,EAAO7Q,IAChC8Q,EAAO9Q,IA2BX,OAzBAoG,EAASnG,EAAE,GAAIM,GACfwG,EAAY9G,EAAE,GAAI8B,GAClB2H,EAAEgH,EAAKzQ,EAAE,IACTiH,EAAEyJ,EAAKD,EAAKjQ,GACZwG,EAAEyJ,EAAKA,EAAKzQ,EAAE,IACd+G,EAAE2J,EAAK1Q,EAAE,GAAI0Q,GAEbjH,EAAEkH,EAAMD,GACRjH,EAAEmH,EAAMD,GACR1J,EAAE4J,EAAMD,EAAMD,GACd1J,EAAER,EAAGoK,EAAMJ,GACXxJ,EAAER,EAAGA,EAAGiK,GAER/G,EAAQlD,EAAGA,GACXQ,EAAER,EAAGA,EAAGgK,GACRxJ,EAAER,EAAGA,EAAGiK,GACRzJ,EAAER,EAAGA,EAAGiK,GACRzJ,EAAEjH,EAAE,GAAIyG,EAAGiK,GAEXjH,EAAE+G,EAAKxQ,EAAE,IACTiH,EAAEuJ,EAAKA,EAAKE,GACR9J,EAAS4J,EAAKC,IAAMxJ,EAAEjH,EAAE,GAAIA,EAAE,GAAIY,GAEtC6I,EAAE+G,EAAKxQ,EAAE,IACTiH,EAAEuJ,EAAKA,EAAKE,GACR9J,EAAS4J,EAAKC,IAAc,GAE5B5J,EAAS7G,EAAE,MAAS8B,EAAE,KAAK,GAAIkF,EAAEhH,EAAE,GAAIK,EAAKL,EAAE,IAElDiH,EAAEjH,EAAE,GAAIA,EAAE,GAAIA,EAAE,IACT,EACT,CAUM8Q,CAAUtK,EAAGqJ,GAAK,OAAQ,EAE9B,IAAK7V,EAAI,EAAGA,EAAIqH,EAAGrH,IAAKsK,EAAEtK,GAAKqW,EAAGrW,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKsK,EAAEtK,EAAE,IAAM6V,EAAG7V,GAUtC,GATAoV,EAAYrO,EAAGuD,EAAGjD,GAClB8O,GAAOpP,GACP2O,GAAW5N,EAAG0E,EAAGzF,GAEjB4O,GAAWnJ,EAAG6J,EAAGvG,SAAS,KAC1BuF,EAAIvN,EAAG0E,GACP+I,GAAK9I,EAAG3E,GAERT,GAAK,GACDE,EAAiB8O,EAAI,EAAG5J,EAAG,GAAI,CACjC,IAAKzM,EAAI,EAAGA,EAAIqH,EAAGrH,IAAKsK,EAAEtK,GAAK,EAC/B,OAAQ,CACV,CAEA,IAAKA,EAAI,EAAGA,EAAIqH,EAAGrH,IAAKsK,EAAEtK,GAAKqW,EAAGrW,EAAI,IACtC,OAAOqH,CACT,CAEA,IAqSM0P,GArSFC,GAA4B,GAC5BC,GAA8B,GAQ9BC,GAAwBD,GAGxBE,GAAoB,GACpBC,GAA6B,GAC7BC,GAA6B,GAiEjC,SAASC,GAAa3P,EAAGN,GACvB,GAAIM,EAAExQ,SAAW6f,GAA2B,MAAM,IAAI5zB,MAAM,gBAC5D,GAAIikB,EAAElQ,SAAW8f,GAA6B,MAAM,IAAI7zB,MAAM,iBAChE,CAOA,SAASm0B,KACP,IAAK,IAAIvX,EAAI,EAAGA,EAAIwX,UAAUrgB,OAAQ6I,IACpC,KAAMwX,UAAUxX,aAAcb,YAC5B,MAAM,IAAIyF,UAAU,kCAE1B,CAEA,SAAS6S,GAAQrS,GACf,IAAK,IAAIpF,EAAI,EAAGA,EAAIoF,EAAIjO,OAAQ6I,IAAKoF,EAAIpF,GAAK,CAChD,CAhFAwE,EAAKkT,SAAW,CACdzN,qBAAsBA,EACtBW,kBAAmBA,EACnBF,cAAeA,EACfN,0BAA2BA,EAC3BK,sBAAuBA,EACvBkB,mBAAoBA,EACpBI,0BAA2BA,EAC3BzE,iBAAkBA,EAClBC,iBAAkBA,EAClB0E,iBAAkBA,EAClBC,sBAAuBA,EACvB0D,kBAAmBA,EACnBI,uBAAwBA,EACxBE,oBAAqBA,EACrBsC,mBAAoBA,EACpBmF,WAxsBF,SAAoB/P,EAAG0C,EAAGxF,EAAGuC,EAAGF,EAAGL,GACjC,IAAIa,EAAI,IAAIxI,WAAW,IAEvB,OADA+Q,EAAoBvI,EAAGR,EAAGL,GACnB0L,EAAmB5K,EAAG0C,EAAGxF,EAAGuC,EAAGM,EACxC,EAqsBEiQ,gBAnsBF,SAAyBtN,EAAG1C,EAAG9C,EAAGuC,EAAGF,EAAGL,GACtC,IAAIa,EAAI,IAAIxI,WAAW,IAEvB,OADA+Q,EAAoBvI,EAAGR,EAAGL,GACnB2L,EAAwBnI,EAAG1C,EAAG9C,EAAGuC,EAAGM,EAC7C,EAgsBEsI,mBAAoBA,EACpBmF,YAAaA,EACbgB,YAAaA,GACbR,oBAAqBA,GACrBW,iBAAkBA,GAElBS,0BAA2BA,GAC3BC,4BAA6BA,GAC7BY,2BA1C+B,GA2C/BC,8BA1CkC,GA2ClCC,wBA1C4B,GA2C5BC,8BA1CkC,GA2ClCC,0BA1C8B,GA2C9BC,0BA1C8B,GA2C9BC,yBA1C6B,GA2C7BjB,sBAAuBA,GACvBkB,qBAlD+B,GAmD/BC,wBA1C4BP,GA2C5BX,kBAAmBA,GACnBC,2BAA4BA,GAC5BC,2BAA4BA,GAC5BiB,sBA1C0B,GA2C1BC,kBA1CsB,GA4CtBxS,GAAIA,EACJS,EAAGA,EACHwP,EAAGA,GACHtJ,UAAWA,EACXI,YAAaA,EACbG,EAAGA,EACHF,EAAGA,EACH0C,EAAGA,EACHzC,EAAGA,EACH2C,QAASA,EACT0F,IAAKA,EACLlJ,SAAUA,EACV8J,KAAMA,GACNP,WAAYA,GACZC,WAAYA,IA0BdnR,EAAKgU,YAAc,SAASnR,GAC1B,IAAIpC,EAAI,IAAI9F,WAAWkI,GAEvB,OADAnB,EAAYjB,EAAGoC,GACRpC,CACT,EAEAT,EAAKiU,UAAY,SAASC,EAAKC,EAAO1iB,GACpCshB,GAAgBmB,EAAKC,EAAO1iB,GAC5BqhB,GAAarhB,EAAK0iB,GAGlB,IAFA,IAAIrO,EAAI,IAAInL,WA3GmB,GA2GqBuZ,EAAIvhB,QACpDyQ,EAAI,IAAIzI,WAAWmL,EAAEnT,QAChB6I,EAAI,EAAGA,EAAI0Y,EAAIvhB,OAAQ6I,IAAKsK,EAAEtK,EA7GR,IA6GwC0Y,EAAI1Y,GAE3E,OADAiM,EAAiBrE,EAAG0C,EAAGA,EAAEnT,OAAQwhB,EAAO1iB,GACjC2R,EAAEkI,SA9GyB,GA+GpC,EAEAtL,EAAKiU,UAAUG,KAAO,SAASC,EAAKF,EAAO1iB,GACzCshB,GAAgBsB,EAAKF,EAAO1iB,GAC5BqhB,GAAarhB,EAAK0iB,GAGlB,IAFA,IAAI/Q,EAAI,IAAIzI,WApHsB,GAoHqB0Z,EAAI1hB,QACvDmT,EAAI,IAAInL,WAAWyI,EAAEzQ,QAChB6I,EAAI,EAAGA,EAAI6Y,EAAI1hB,OAAQ6I,IAAK4H,EAAE5H,EAtHL,IAsHwC6Y,EAAI7Y,GAC9E,OAAI4H,EAAEzQ,OAAS,IAC2C,IAAtD+U,EAAsB5B,EAAG1C,EAAGA,EAAEzQ,OAAQwhB,EAAO1iB,GADvB,KAEnBqU,EAAEwF,SA1HsB,GA2HjC,EAEAtL,EAAKiU,UAAUK,UAAY9B,GAC3BxS,EAAKiU,UAAUM,YAAc9B,GAC7BzS,EAAKiU,UAAUO,eA9HqB,GAgIpCxU,EAAKyU,WAAa,SAAS5R,EAAGS,GAE5B,GADAyP,GAAgBlQ,EAAGS,GA/He,KAgI9BT,EAAElQ,OAA0C,MAAM,IAAI/T,MAAM,cAChE,GAlI4B,KAkIxB0kB,EAAE3Q,OAAoC,MAAM,IAAI/T,MAAM,cAC1D,IAAIopB,EAAI,IAAIrN,WAnIgB,IAqI5B,OADAyQ,EAAkBpD,EAAGnF,EAAGS,GACjB0E,CACT,EAEAhI,EAAKyU,WAAWC,KAAO,SAAS7R,GAE9B,GADAkQ,GAAgBlQ,GAxIkB,KAyI9BA,EAAElQ,OAA0C,MAAM,IAAI/T,MAAM,cAChE,IAAIopB,EAAI,IAAIrN,WA3IgB,IA6I5B,OADA6Q,EAAuBxD,EAAGnF,GACnBmF,CACT,EAEAhI,EAAKyU,WAAWE,aA/IoB,GAgJpC3U,EAAKyU,WAAWG,mBAjJc,GAmJ9B5U,EAAKqU,IAAM,SAASH,EAAKC,EAAOhd,EAAW0d,GACzC,IAAI1R,EAAInD,EAAKqU,IAAIS,OAAO3d,EAAW0d,GACnC,OAAO7U,EAAKiU,UAAUC,EAAKC,EAAOhR,EACpC,EAEAnD,EAAKqU,IAAIS,OAAS,SAAS3d,EAAW0d,GACpC9B,GAAgB5b,EAAW0d,GAzE7B,SAAyBxD,EAAIC,GAC3B,GA/E8B,KA+E1BD,EAAG1e,OAAsC,MAAM,IAAI/T,MAAM,uBAC7D,GA/E8B,KA+E1B0yB,EAAG3e,OAAsC,MAAM,IAAI/T,MAAM,sBAC/D,CAuEEm2B,CAAgB5d,EAAW0d,GAC3B,IAAI1R,EAAI,IAAIxI,WAvJiB,IAyJ7B,OADA+Q,EAAoBvI,EAAGhM,EAAW0d,GAC3B1R,CACT,EAEAnD,EAAKqU,IAAIW,MAAQhV,EAAKiU,UAEtBjU,EAAKqU,IAAID,KAAO,SAASF,EAAKC,EAAOhd,EAAW0d,GAC9C,IAAI1R,EAAInD,EAAKqU,IAAIS,OAAO3d,EAAW0d,GACnC,OAAO7U,EAAKiU,UAAUG,KAAKF,EAAKC,EAAOhR,EACzC,EAEAnD,EAAKqU,IAAID,KAAKY,MAAQhV,EAAKiU,UAAUG,KAErCpU,EAAKqU,IAAIY,QAAU,WACjB,IAAI5D,EAAK,IAAI1W,WAxKiB,IAyK1B2W,EAAK,IAAI3W,WAxKiB,IA0K9B,OADA8Q,EAAmB4F,EAAIC,GAChB,CAACna,UAAWka,EAAIwD,UAAWvD,EACpC,EAEAtR,EAAKqU,IAAIY,QAAQC,cAAgB,SAASL,GAExC,GADA9B,GAAgB8B,GA9Kc,KA+K1BA,EAAUliB,OACZ,MAAM,IAAI/T,MAAM,uBAClB,IAAIyyB,EAAK,IAAI1W,WAlLiB,IAoL9B,OADA6Q,EAAuB6F,EAAIwD,GACpB,CAAC1d,UAAWka,EAAIwD,UAAW,IAAIla,WAAWka,GACnD,EAEA7U,EAAKqU,IAAIc,gBAvLuB,GAwLhCnV,EAAKqU,IAAIe,gBAvLuB,GAwLhCpV,EAAKqU,IAAIgB,gBAvLsB,GAwL/BrV,EAAKqU,IAAIE,YAAc7B,GACvB1S,EAAKqU,IAAIG,eAAiBxU,EAAKiU,UAAUO,eAEzCxU,EAAKsV,KAAO,SAASpB,EAAKW,GAExB,GADA9B,GAAgBmB,EAAKW,GACjBA,EAAUliB,SAAWkgB,GACvB,MAAM,IAAIj0B,MAAM,uBAClB,IAAI22B,EAAY,IAAI5a,WAAWgY,GAAkBuB,EAAIvhB,QAErD,OADAif,GAAY2D,EAAWrB,EAAKA,EAAIvhB,OAAQkiB,GACjCU,CACT,EAEAvV,EAAKsV,KAAKlB,KAAO,SAASmB,EAAWpe,GAEnC,GADA4b,GAAgBwC,EAAWpe,GACvBA,EAAUxE,SAAWigB,GACvB,MAAM,IAAIh0B,MAAM,uBAClB,IAAI42B,EAAM,IAAI7a,WAAW4a,EAAU5iB,QAC/B8iB,EAAO1D,GAAiByD,EAAKD,EAAWA,EAAU5iB,OAAQwE,GAC9D,GAAIse,EAAO,EAAG,OAAO,KAErB,IADA,IAAI3P,EAAI,IAAInL,WAAW8a,GACdja,EAAI,EAAGA,EAAIsK,EAAEnT,OAAQ6I,IAAKsK,EAAEtK,GAAKga,EAAIha,GAC9C,OAAOsK,CACT,EAEA9F,EAAKsV,KAAKI,SAAW,SAASxB,EAAKW,GAGjC,IAFA,IAAIU,EAAYvV,EAAKsV,KAAKpB,EAAKW,GAC3Bc,EAAM,IAAIhb,WAAWgY,IAChBnX,EAAI,EAAGA,EAAIma,EAAIhjB,OAAQ6I,IAAKma,EAAIna,GAAK+Z,EAAU/Z,GACxD,OAAOma,CACT,EAEA3V,EAAKsV,KAAKI,SAASE,OAAS,SAAS1B,EAAKyB,EAAKxe,GAE7C,GADA4b,GAAgBmB,EAAKyB,EAAKxe,GACtBwe,EAAIhjB,SAAWggB,GACjB,MAAM,IAAI/zB,MAAM,sBAClB,GAAIuY,EAAUxE,SAAWigB,GACvB,MAAM,IAAIh0B,MAAM,uBAClB,IAEI4c,EAFAqW,EAAK,IAAIlX,WAAWgY,GAAoBuB,EAAIvhB,QAC5CmT,EAAI,IAAInL,WAAWgY,GAAoBuB,EAAIvhB,QAE/C,IAAK6I,EAAI,EAAGA,EAAImX,GAAmBnX,IAAKqW,EAAGrW,GAAKma,EAAIna,GACpD,IAAKA,EAAI,EAAGA,EAAI0Y,EAAIvhB,OAAQ6I,IAAKqW,EAAGrW,EAAEmX,IAAqBuB,EAAI1Y,GAC/D,OAAQuW,GAAiBjM,EAAG+L,EAAIA,EAAGlf,OAAQwE,IAAc,CAC3D,EAEA6I,EAAKsV,KAAKL,QAAU,WAClB,IAAI5D,EAAK,IAAI1W,WAAWiY,IACpBtB,EAAK,IAAI3W,WAAWkY,IAExB,OADAzB,GAAoBC,EAAIC,GACjB,CAACna,UAAWka,EAAIwD,UAAWvD,EACpC,EAEAtR,EAAKsV,KAAKL,QAAQC,cAAgB,SAASL,GAEzC,GADA9B,GAAgB8B,GACZA,EAAUliB,SAAWkgB,GACvB,MAAM,IAAIj0B,MAAM,uBAElB,IADA,IAAIyyB,EAAK,IAAI1W,WAAWiY,IACfpX,EAAI,EAAGA,EAAI6V,EAAG1e,OAAQ6I,IAAK6V,EAAG7V,GAAKqZ,EAAU,GAAGrZ,GACzD,MAAO,CAACrE,UAAWka,EAAIwD,UAAW,IAAIla,WAAWka,GACnD,EAEA7U,EAAKsV,KAAKL,QAAQY,SAAW,SAASC,GAEpC,GADA/C,GAAgB+C,GA/OU,KAgPtBA,EAAKnjB,OACP,MAAM,IAAI/T,MAAM,iBAGlB,IAFA,IAAIyyB,EAAK,IAAI1W,WAAWiY,IACpBtB,EAAK,IAAI3W,WAAWkY,IACfrX,EAAI,EAAGA,EAAI,GAAIA,IAAK8V,EAAG9V,GAAKsa,EAAKta,GAE1C,OADA4V,GAAoBC,EAAIC,GAAI,GACrB,CAACna,UAAWka,EAAIwD,UAAWvD,EACpC,EAEAtR,EAAKsV,KAAKH,gBAAkBvC,GAC5B5S,EAAKsV,KAAKF,gBAAkBvC,GAC5B7S,EAAKsV,KAAKS,WA3PkB,GA4P5B/V,EAAKsV,KAAKU,gBAAkBrD,GAE5B3S,EAAKiW,KAAO,SAAS/B,GACnBnB,GAAgBmB,GAChB,IAAI3R,EAAI,IAAI5H,WA/PU,IAiQtB,OADAiW,EAAYrO,EAAG2R,EAAKA,EAAIvhB,QACjB4P,CACT,EAEAvC,EAAKiW,KAAKC,WApQc,GAsQxBlW,EAAK4V,OAAS,SAAStT,EAAGK,GAGxB,OAFAoQ,GAAgBzQ,EAAGK,GAEF,IAAbL,EAAE3P,QAA6B,IAAbgQ,EAAEhQ,QACpB2P,EAAE3P,SAAWgQ,EAAEhQ,QACkB,IAA7B8P,EAAGH,EAAG,EAAGK,EAAG,EAAGL,EAAE3P,OAC3B,EAEAqN,EAAKmW,QAAU,SAASta,GACtB6F,EAAc7F,CAChB,GAKM0W,GAAyB,oBAAT6D,KAAwBA,KAAK7D,QAAU6D,KAAKC,SAAY,OAC9D9D,GAAO+D,gBAGnBtW,EAAKmW,SAAQ,SAAS7T,EAAGO,GACvB,IAAIrH,EAAGsM,EAAI,IAAInN,WAAWkI,GAC1B,IAAKrH,EAAI,EAAGA,EAAIqH,EAAGrH,GAHT,MAIR+W,GAAO+D,gBAAgBxO,EAAEwD,SAAS9P,EAAGA,EAAIT,KAAKwb,IAAI1T,EAAIrH,EAJ9C,SAMV,IAAKA,EAAI,EAAGA,EAAIqH,EAAGrH,IAAK8G,EAAE9G,GAAKsM,EAAEtM,GACjCyX,GAAQnL,EACV,KAGAyK,GAAS,EAAQ,QACHA,GAAOyB,aACnBhU,EAAKmW,SAAQ,SAAS7T,EAAGO,GACvB,IAAIrH,EAAGsM,EAAIyK,GAAOyB,YAAYnR,GAC9B,IAAKrH,EAAI,EAAGA,EAAIqH,EAAGrH,IAAK8G,EAAE9G,GAAKsM,EAAEtM,GACjCyX,GAAQnL,EACV,GAKL,CAt1ED,CAs1EoChI,EAAOC,QAAUD,EAAOC,QAAWqW,KAAKpW,KAAOoW,KAAKpW,MAAQ,CAAC,E,iECp1EjG,IAIWxf,EAUAD,EAMAE,EASA+1B,EASAC,EAQAn2B,EA9CP0f,EAAO,EAAQ,KACf0W,EAAS,EAAQ,KAErB3W,EAAQvf,+BAA4B,GACzBA,EAQRuf,EAAQvf,4BAA8Buf,EAAQvf,0BAA4B,CAAC,IAPhDA,EAAyC,cAAI,GAAK,gBAC5EA,EAA0BA,EAA6C,kBAAI,GAAK,oBAChFA,EAA0BA,EAAoD,yBAAI,GAAK,2BACvFA,EAA0BA,EAAkD,uBAAI,GAAK,yBACrFA,EAA0BA,EAA6C,kBAAI,KAAO,oBAClFA,EAA0BA,EAA8C,mBAAI,KAAO,qBACnFA,EAA0BA,EAAgD,qBAAI,KAAO,uBAEzFuf,EAAQxf,8BAA2B,GACxBA,EAGRwf,EAAQxf,2BAA6Bwf,EAAQxf,yBAA2B,CAAC,IAF/CA,EAAwC,cAAI,GAAK,gBAC1EA,EAAyBA,EAA+C,qBAAI,KAAO,uBAGvFwf,EAAQtf,kCAA+B,GAC5BA,EAMRsf,EAAQtf,+BAAiCsf,EAAQtf,6BAA+B,CAAC,IALnDA,EAA4C,cAAI,GAAK,gBAClFA,EAA6BA,EAAgD,kBAAI,GAAK,oBACtFA,EAA6BA,EAAgD,kBAAI,KAAO,oBACxFA,EAA6BA,EAAiD,mBAAI,KAAO,qBACzFA,EAA6BA,EAAmD,qBAAI,KAAO,uBAG/Fsf,EAAQyW,2BAAwB,GACrBA,EAMRzW,EAAQyW,wBAA0BzW,EAAQyW,sBAAwB,CAAC,IAL5CA,EAAqC,cAAI,GAAK,gBACpEA,EAAsBA,EAAyC,kBAAI,GAAK,oBACxEA,EAAsBA,EAAyC,kBAAI,KAAO,oBAC1EA,EAAsBA,EAA0C,mBAAI,KAAO,qBAC3EA,EAAsBA,EAA4C,qBAAI,KAAO,uBAGjFzW,EAAQ0W,4BAAyB,GACtBA,EAKR1W,EAAQ0W,yBAA2B1W,EAAQ0W,uBAAyB,CAAC,IAJ7CA,EAAsC,cAAI,GAAK,gBACtEA,EAAuBA,EAA0C,kBAAI,GAAK,oBAC1EA,EAAuBA,EAA0C,kBAAI,KAAO,oBAC5EA,EAAuBA,EAA6C,qBAAI,KAAO,uBAGnF1W,EAAQzf,WAAQ,GACLA,EAGRyf,EAAQzf,QAAUyf,EAAQzf,MAAQ,CAAC,IAFnB,QAAI,OACnBA,EAAe,QAAI,KAgDvB,MAAM8G,EAAS,CACXC,OAjCJ,SAAgBnG,EAAOy1B,GAAU,GAC7B,IAAIC,EAUJ,OATI11B,aAAiByZ,WACjBic,EAAa11B,GAGQ,iBAAVA,IACPA,EAAQwB,KAAKC,UAAUzB,IAE3B01B,EAAa5W,EAAKK,WAAWnf,IAtBrC,SAA0BA,EAAOy1B,GAC7B,MAAME,EAAU7W,EAAKkB,aAAahgB,GAClC,OAAKy1B,EAGEnW,mBAAmBqW,GAFfA,CAGf,CAkBWC,CAAiBF,EAAYD,EACxC,EAsBIvpB,OArBJ,SAAgBlM,EAAOy1B,GAAU,GAC7B,MAAMI,EApBV,SAA4B71B,EAAOy1B,GAI/B,OAHIA,IACAz1B,EAAQ4f,mBAAmB5f,IAExB8e,EAAKmB,aAAajgB,EAC7B,CAe8B81B,CAAmB91B,EAAOy1B,GACpD,MAAO,CACHpxB,SAAQ,IACGya,EAAKW,WAAWoW,GAE3BE,WACI,IACI,OAAOv0B,KAAKgG,MAAMsX,EAAKW,WAAWoW,GACtC,CACA,MAAOnxB,GACH,OAAO,IACX,CACJ,EACAyH,aAAY,IACD0pB,EAGnB,GAMA,SAASG,EAAkBC,EAASC,GAChC,MAAMC,EAAc,IAAI1c,WAAWwc,EAAQxkB,OAASykB,EAAQzkB,QAG5D,OAFA0kB,EAAYrrB,IAAImrB,GAChBE,EAAYrrB,IAAIorB,EAASD,EAAQxkB,QAC1B0kB,CACX,CACA,SAASC,EAAmBC,EAAOzkB,GAC/B,GAAIA,GAASykB,EAAM5kB,OACf,MAAM,IAAI/T,MAAM,0BAIpB,MAAO,CAFW24B,EAAMna,MAAM,EAAGtK,GACfykB,EAAMna,MAAMtK,GAElC,CACA,SAAS0kB,EAAYC,GACjB,IAAIC,EAAY,GAIhB,OAHAD,EAAUvsB,SAAQ2P,IACd6c,IAAc,KAAc,IAAP7c,GAAatV,SAAS,KAAK6X,OAAO,EAAE,IAEtDsa,CACX,CACA,SAAS5rB,EAAe4rB,GACpB,GAAIA,EAAU/kB,OAAS,GAAM,EACzB,MAAM,IAAI/T,MAAM,kBAAkB84B,mBAEtC,MAAM10B,EAAS,IAAI2X,WAAW+c,EAAU/kB,OAAS,GACjD,IAAK,IAAI6I,EAAI,EAAGA,EAAIkc,EAAU/kB,OAAQ6I,GAAK,EACvCxY,EAAOwY,EAAI,GAAKpB,SAASsd,EAAUta,MAAM5B,EAAGA,EAAI,GAAI,IAExD,OAAOxY,CACX,CA8CA+c,EAAQ3Y,OAASA,EACjB2Y,EAAQ3V,cAzCR,MACIxM,YAAYq3B,GACRh3B,KAAKs2B,YAAc,GACnBt2B,KAAKg3B,QAAUA,EAAUh3B,KAAK05B,wBAAwB1C,GAAWh3B,KAAK25B,gBACtE35B,KAAKmF,UAAYo0B,EAAYv5B,KAAKg3B,QAAQ9d,UAC9C,CACAygB,gBACI,OAAOlB,EAAOrC,IAAIY,SACtB,CACA0C,wBAAwB1C,GACpB,MAAO,CACH9d,UAAWrL,EAAempB,EAAQ9d,WAClC0d,UAAW/oB,EAAempB,EAAQJ,WAE1C,CACAgD,cACI,OAAOnB,EAAO1C,YAAY/1B,KAAKs2B,YACnC,CACA1oB,QAAQhN,EAASi5B,GACb,MAAMC,GAAiB,IAAIC,aAAc3wB,OAAOxI,GAC1Cs1B,EAAQl2B,KAAK45B,cAEnB,OAAOX,EAAkB/C,EADPuC,EAAOrC,IAAI0D,EAAgB5D,EAAO2D,EAAmB75B,KAAKg3B,QAAQJ,WAExF,CACA1nB,QAAQtO,EAASo5B,GACb,MAAO9D,EAAO+D,GAAmBZ,EAAmBz4B,EAASZ,KAAKs2B,aAC5D4D,EAAYzB,EAAOrC,IAAID,KAAK8D,EAAiB/D,EAAO8D,EAAiBh6B,KAAKg3B,QAAQJ,WACxF,IAAKsD,EACD,MAAM,IAAIv5B,MAAM,iCAAiCC,EAAQ0G,gCAAgC0yB,EAAgB1yB,iCAAiCtH,KAAKg3B,QAAQ9d,UAAU5R,oCAAoCtH,KAAKg3B,QAAQJ,UAAUtvB,cAEhO,OAAO,IAAI6yB,aAAchrB,OAAO+qB,EACpC,CACArmB,mBACI,MAAO,CACHqF,UAAWqgB,EAAYv5B,KAAKg3B,QAAQ9d,WACpC0d,UAAW2C,EAAYv5B,KAAKg3B,QAAQJ,WAE5C,GAKJ9U,EAAQmX,kBAAoBA,EAC5BnX,EAAQjU,eAAiBA,EACzBiU,EAAQsY,OAhDR,WACI,MAA2B,oBAAZva,SAA+C,MAApBA,QAAQC,UAA6C,MAAzBD,QAAQC,SAASC,IAC3F,EA+CA+B,EAAQuX,mBAAqBA,EAC7BvX,EAAQyX,YAAcA,C,GCxLlBc,EAA2B,CAAC,ECE5BC,EDCJ,SAASC,EAAoBC,GAE5B,IAAIC,EAAeJ,EAAyBG,GAC5C,QAAqBhrB,IAAjBirB,EACH,OAAOA,EAAa3Y,QAGrB,IAAID,EAASwY,EAAyBG,GAAY,CAGjD1Y,QAAS,CAAC,GAOX,OAHA4Y,EAAoBF,GAAUrX,KAAKtB,EAAOC,QAASD,EAAQA,EAAOC,QAASyY,GAGpE1Y,EAAOC,OACf,CCnB0ByY,CAAoB,M", "sources": ["webpack://TonConnectSDK/./src/constants/version.ts", "webpack://TonConnectSDK/./src/errors/address/index.ts", "webpack://TonConnectSDK/./src/errors/address/wrong-address.error.ts", "webpack://TonConnectSDK/./src/errors/binary/index.ts", "webpack://TonConnectSDK/./src/errors/binary/parse-hex.error.ts", "webpack://TonConnectSDK/./src/errors/dapp/dapp-metadata.error.ts", "webpack://TonConnectSDK/./src/errors/index.ts", "webpack://TonConnectSDK/./src/errors/protocol/events/connect/index.ts", "webpack://TonConnectSDK/./src/errors/protocol/events/connect/manifest-content-error.error.ts", "webpack://TonConnectSDK/./src/errors/protocol/events/connect/manifest-not-found.error.ts", "webpack://TonConnectSDK/./src/errors/protocol/events/connect/user-rejects.error.ts", "webpack://TonConnectSDK/./src/errors/protocol/events/index.ts", "webpack://TonConnectSDK/./src/errors/protocol/index.ts", "webpack://TonConnectSDK/./src/errors/protocol/responses/bad-request.error.ts", "webpack://TonConnectSDK/./src/errors/protocol/responses/index.ts", "webpack://TonConnectSDK/./src/errors/protocol/responses/unknown-app.error.ts", "webpack://TonConnectSDK/./src/errors/storage/index.ts", "webpack://TonConnectSDK/./src/errors/storage/localstorage-not-found.error.ts", "webpack://TonConnectSDK/./src/errors/ton-connect.error.ts", "webpack://TonConnectSDK/./src/errors/unknown.error.ts", "webpack://TonConnectSDK/./src/errors/wallet/index.ts", "webpack://TonConnectSDK/./src/errors/wallet/wallet-already-connected.error.ts", "webpack://TonConnectSDK/./src/errors/wallet/wallet-not-connected.error.ts", "webpack://TonConnectSDK/./src/errors/wallet/wallet-not-injected.error.ts", "webpack://TonConnectSDK/./src/errors/wallet/wallet-not-support-feature.error.ts", "webpack://TonConnectSDK/./src/errors/wallets-manager/fetch-wallets.error.ts", "webpack://TonConnectSDK/./src/errors/wallets-manager/index.ts", "webpack://TonConnectSDK/./src/index.ts", "webpack://TonConnectSDK/./src/models/index.ts", "webpack://TonConnectSDK/./src/models/methods/index.ts", "webpack://TonConnectSDK/./src/models/wallet/index.ts", "webpack://TonConnectSDK/./src/models/wallet/wallet-connection-source.ts", "webpack://TonConnectSDK/./src/models/wallet/wallet-info.ts", "webpack://TonConnectSDK/./src/parsers/connect-errors-parser.ts", "webpack://TonConnectSDK/./src/parsers/rpc-parser.ts", "webpack://TonConnectSDK/./src/parsers/send-transaction-parser.ts", "webpack://TonConnectSDK/./src/provider/bridge/bridge-gateway.ts", "webpack://TonConnectSDK/./src/provider/bridge/bridge-provider.ts", "webpack://TonConnectSDK/./src/provider/bridge/models/bridge-connection.ts", "webpack://TonConnectSDK/./src/provider/injected/injected-provider.ts", "webpack://TonConnectSDK/./src/provider/injected/models/injected-wallet-api.ts", "webpack://TonConnectSDK/./src/resources/fallback-wallets-list.ts", "webpack://TonConnectSDK/./src/resources/protocol.ts", "webpack://TonConnectSDK/./src/storage/bridge-connection-storage.ts", "webpack://TonConnectSDK/./src/storage/default-storage.ts", "webpack://TonConnectSDK/./src/storage/http-bridge-gateway-storage.ts", "webpack://TonConnectSDK/./src/storage/models/in-memory-storage.ts", "webpack://TonConnectSDK/./src/ton-connect.ts", "webpack://TonConnectSDK/./src/tracker/browser-event-dispatcher.ts", "webpack://TonConnectSDK/./src/tracker/ton-connect-tracker.ts", "webpack://TonConnectSDK/./src/tracker/types.ts", "webpack://TonConnectSDK/./src/utils/address.ts", "webpack://TonConnectSDK/./src/utils/call-for-success.ts", "webpack://TonConnectSDK/./src/utils/create-abort-controller.ts", "webpack://TonConnectSDK/./src/utils/delay.ts", "webpack://TonConnectSDK/./src/utils/feature-support.ts", "webpack://TonConnectSDK/./src/utils/log.ts", "webpack://TonConnectSDK/./src/utils/resource.ts", "webpack://TonConnectSDK/./src/utils/timeout.ts", "webpack://TonConnectSDK/./src/utils/types.ts", "webpack://TonConnectSDK/./src/utils/url.ts", "webpack://TonConnectSDK/./src/utils/web-api.ts", "webpack://TonConnectSDK/./src/wallets-list-manager.ts", "webpack://TonConnectSDK/../../node_modules/tweetnacl-util/nacl-util.js", "webpack://TonConnectSDK/../../node_modules/tweetnacl/nacl-fast.js", "webpack://TonConnectSDK/../protocol/lib/cjs/index.cjs", "webpack://TonConnectSDK/webpack/bootstrap", "webpack://TonConnectSDK/webpack/startup"], "sourcesContent": ["declare const TON_CONNECT_SDK_VERSION: string;\n\nexport const tonConnectSdkVersion = TON_CONNECT_SDK_VERSION;\n", "export { WrongAddressError } from './wrong-address.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when passed address is in incorrect format.\n */\nexport class WrongAddressError extends TonConnectError {\n    protected get info(): string {\n        return 'Passed address is in incorrect format.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, WrongAddressError.prototype);\n    }\n}\n", "export { ParseHexError } from './parse-hex.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when passed hex is in incorrect format.\n */\nexport class ParseHexError extends TonConnectError {\n    protected get info(): string {\n        return 'Passed hex is in incorrect format.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, ParseHexError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when passed DappMetadata is in incorrect format.\n */\nexport class DappMetadataError extends TonConnectError {\n    protected get info(): string {\n        return 'Passed DappMetadata is in incorrect format.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, DappMetadataError.prototype);\n    }\n}\n", "export * from './protocol';\nexport * from './wallet';\nexport * from './storage';\nexport * from './wallets-manager';\nexport * from './address';\nexport * from './binary';\nexport { TonConnectError } from './ton-connect.error';\nexport { UnknownError } from './unknown.error';\n", "export { UserRejectsError } from 'src/errors/protocol/events/connect/user-rejects.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when passed manifest contains errors.\n */\nexport class ManifestContentErrorError extends TonConnectError {\n    protected get info(): string {\n        return 'Passed `tonconnect-manifest.json` contains errors. Check format of your manifest. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, ManifestContentErrorError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when wallet can't get manifest by passed manifestUrl.\n */\nexport class ManifestNotFoundError extends TonConnectError {\n    protected get info(): string {\n        return 'Manifest not found. Make sure you added `tonconnect-manifest.json` to the root of your app or passed correct manifestUrl. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, ManifestNotFoundError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when user rejects the action in the wallet.\n */\nexport class UserRejectsError extends TonConnectError {\n    protected get info(): string {\n        return 'User rejects the action in the wallet.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, UserRejectsError.prototype);\n    }\n}\n", "export * from './connect';\n", "export * from './events';\nexport * from './responses';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when request to the wallet contains errors.\n */\nexport class BadRequestError extends TonConnectError {\n    protected get info(): string {\n        return 'Request to the wallet contains errors.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, BadRequestError.prototype);\n    }\n}\n", "export { BadRequestError } from './bad-request.error';\nexport { UnknownAppError } from './unknown-app.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when app tries to send rpc request to the injected wallet while not connected.\n */\nexport class UnknownAppError extends TonConnectError {\n    protected get info(): string {\n        return 'App tries to send rpc request to the injected wallet while not connected.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, UnknownAppError.prototype);\n    }\n}\n", "export { LocalstorageNotFoundError } from './localstorage-not-found.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when `Storage` was not specified in the `DappMetadata` and default `localStorage` was not detected in the Node.js environment.\n */\nexport class LocalstorageNotFoundError extends TonConnectError {\n    protected get info(): string {\n        return 'Storage was not specified in the `DappMetadata` and default `localStorage` was not detected in the environment.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, LocalstorageNotFoundError.prototype);\n    }\n}\n", "/**\n * Base class for TonConnect errors. You can check if the error was triggered by the @tonconnect/sdk using `err instanceof TonConnectError`.\n */\nexport class TonConnectError extends Error {\n    private static prefix = '[TON_CONNECT_SDK_ERROR]';\n\n    protected get info(): string {\n        return '';\n    }\n\n    constructor(\n        message?: string,\n        options?: {\n            cause?: unknown;\n        }\n    ) {\n        super(message, options);\n\n        this.message = `${TonConnectError.prefix} ${this.constructor.name}${\n            this.info ? ': ' + this.info : ''\n        }${message ? '\\n' + message : ''}`;\n\n        Object.setPrototypeOf(this, TonConnectError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Unhanded unknown error.\n */\nexport class UnknownError extends TonConnectError {\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, UnknownError.prototype);\n    }\n}\n", "export { WalletAlreadyConnectedError } from './wallet-already-connected.error';\nexport { WalletNotConnectedError } from './wallet-not-connected.error';\nexport { WalletNotInjectedError } from './wallet-not-injected.error';\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when wallet connection called but wallet already connected. To avoid the error, disconnect the wallet before doing a new connection.\n */\nexport class WalletAlreadyConnectedError extends TonConnectError {\n    protected get info(): string {\n        return 'Wallet connection called but wallet already connected. To avoid the error, disconnect the wallet before doing a new connection.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, WalletAlreadyConnectedError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when send transaction or other protocol methods called while wallet is not connected.\n */\nexport class WalletNotConnectedError extends TonConnectError {\n    protected get info(): string {\n        return 'Send transaction or other protocol methods called while wallet is not connected.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, WalletNotConnectedError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when there is an attempt to connect to the injected wallet while it is not exists in the webpage.\n */\nexport class WalletNotInjectedError extends TonConnectError {\n    protected get info(): string {\n        return 'There is an attempt to connect to the injected wallet while it is not exists in the webpage.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, WalletNotInjectedError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when wallet doesn't support requested feature method.\n */\nexport class WalletNotSupportFeatureError extends TonConnectError {\n    protected get info(): string {\n        return \"Wallet doesn't support requested feature method.\";\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, WalletNotSupportFeatureError.prototype);\n    }\n}\n", "import { TonConnectError } from 'src/errors/ton-connect.error';\n\n/**\n * Thrown when an error occurred while fetching the wallets list.\n */\nexport class FetchWalletsError extends TonConnectError {\n    protected get info(): string {\n        return 'An error occurred while fetching the wallets list.';\n    }\n\n    constructor(...args: ConstructorParameters<typeof TonConnectError>) {\n        super(...args);\n\n        Object.setPrototypeOf(this, FetchWalletsError.prototype);\n    }\n}\n", "export { FetchWalletsError } from './fetch-wallets.error';\n", "export * from './ton-connect';\nexport * from './models';\nexport * from './errors';\nexport { IStorage } from './storage/models/storage.interface';\nexport { TonConnect as default } from './ton-connect';\nexport { WalletsListManager } from './wallets-list-manager';\nexport { ITonConnect } from './ton-connect.interface';\nexport type {\n    EventDispatcher,\n    RemoveTonConnectPrefix,\n    AddTonConnectPrefix\n} from './tracker/event-dispatcher';\nexport {\n    createConnectionStartedEvent,\n    createConnectionErrorEvent,\n    createConnectionCompletedEvent,\n    createConnectionRestoringStartedEvent,\n    createConnectionRestoringErrorEvent,\n    createConnectionRestoringCompletedEvent,\n    createDisconnectionEvent,\n    createTransactionSentForSignatureEvent,\n    createTransactionSigningFailedEvent,\n    createTransactionSignedEvent,\n    createRequestVersionEvent,\n    createResponseVersionEvent,\n    createVersionInfo\n} from './tracker/types';\nexport type {\n    AuthType,\n    ConnectionInfo,\n    ConnectionEvent,\n    ConnectionStartedEvent,\n    ConnectionCompletedEvent,\n    ConnectionErrorEvent,\n    ConnectionRestoringEvent,\n    ConnectionRestoringErrorEvent,\n    ConnectionRestoringStartedEvent,\n    ConnectionRestoringCompletedEvent,\n    DisconnectionEvent,\n    TransactionInfo,\n    TransactionMessage,\n    TransactionSigningEvent,\n    TransactionSignedEvent,\n    TransactionSentForSignatureEvent,\n    TransactionSigningFailedEvent,\n    SdkActionEvent,\n    RequestVersionEvent,\n    ResponseVersionEvent,\n    VersionEvent,\n    Version,\n    WithoutVersion\n} from './tracker/types';\nexport { BrowserEventDispatcher } from './tracker/browser-event-dispatcher';\nexport type { TonAddressItem, TonProofItem, ConnectItem } from '@tonconnect/protocol';\nexport {\n    CHAIN,\n    DeviceInfo,\n    Feature,\n    SendTransactionFeature,\n    SignDataFeature,\n    SendTransactionFeatureDeprecated,\n    TonProofItemReply,\n    TonProofItemReplySuccess,\n    TonProofItemReplyError,\n    ConnectItemReplyError,\n    CONNECT_ITEM_ERROR_CODES,\n    CONNECT_EVENT_ERROR_CODES,\n    SEND_TRANSACTION_ERROR_CODES\n} from '@tonconnect/protocol';\nexport { toUserFriendlyAddress } from './utils/address';\nexport { isTelegramUrl, encodeTelegramUrlParameters } from './utils/url';\n", "export * from './wallet';\nexport * from './methods';\nexport { DappMetadata } from './dapp/dapp-metadata';\nexport { TonConnectOptions } from './ton-connect-options';\n", "export * from './connect';\nexport * from './send-transaction';\n", "export type { Account } from './account';\nexport type { Wallet } from './wallet';\nexport type {\n    WalletConnectionSource,\n    WalletConnectionSourceHTTP,\n    WalletConnectionSourceJS\n} from './wallet-connection-source';\nexport {\n    WalletInfo,\n    WalletInfoBase,\n    WalletInfoInjectable,\n    WalletInfoCurrentlyInjected,\n    WalletInfoCurrentlyEmbedded,\n    WalletInfoRemote,\n    WalletInfoInjected,\n    isWalletInfoCurrentlyInjected,\n    isWalletInfoCurrentlyEmbedded,\n    isWalletInfoInjectable,\n    isWalletInfoRemote,\n    isWalletInfoInjected\n} from './wallet-info';\n", "export type WalletConnectionSource = WalletConnectionSourceHTTP | WalletConnectionSourceJS;\n\nexport interface WalletConnectionSourceHTTP {\n    /**\n     * Base part of the wallet universal url. The link should support [Ton Connect parameters]{@link https://github.com/ton-connect/docs/blob/main/bridge.md#universal-link}.\n     */\n    universalLink: string;\n\n    /**\n     * Url of the wallet's implementation of the [HTTP bridge]{@link https://github.com/ton-connect/docs/blob/main/bridge.md#http-bridge}.\n     */\n    bridgeUrl: string;\n}\n\nexport interface WalletConnectionSourceJS {\n    /**\n     * If the wallet handles JS Bridge connection, specifies the binding for the bridge object accessible through window. Example: the key \"tonkeeper\" means the bridge can be accessed as window.tonkeeper.\n     */\n    jsBridgeKey: string;\n}\n\nexport function isWalletConnectionSourceJS(\n    value: WalletConnectionSource\n): value is WalletConnectionSourceJS {\n    return 'jsBridgeKey' in value;\n}\n", "/**\n * Common information for injectable and http-compatible wallets.\n */\nexport interface WalletInfoBase {\n    /**\n     * Human-readable name of the wallet.\n     */\n    name: string;\n\n    /**\n     * ID of the wallet, equals to the `appName` property into {@link Wallet.device}.\n     */\n    appName: string;\n\n    /**\n     * Url to the icon of the wallet. Resolution 288×288px. On non-transparent background, without rounded corners. PNG format.\n     */\n    imageUrl: string;\n\n    /**\n     * Will be used in the protocol later.\n     */\n    tondns?: string;\n\n    /**\n     * Info or landing page of your wallet. May be useful for TON newcomers.\n     */\n    aboutUrl: string;\n\n    /**\n     * OS and browsers where the wallet could be installed\n     */\n    platforms: (\n        | 'ios'\n        | 'android'\n        | 'macos'\n        | 'windows'\n        | 'linux'\n        | 'chrome'\n        | 'firefox'\n        | 'safari'\n    )[];\n}\n\n/**\n * Http-compatible wallet information.\n */\nexport interface WalletInfoRemote extends WalletInfoBase {\n    /**\n     * Base part of the wallet universal url. The link should support [Ton Connect parameters]{@link https://github.com/ton-connect/docs/blob/main/bridge.md#universal-link}.\n     */\n    universalLink: string;\n\n    /**\n     * Native wallet app deepLink. The link should support [Ton Connect parameters]{@link https://github.com/ton-connect/docs/blob/main/bridge.md#universal-link}.\n     */\n    deepLink?: string;\n\n    /**\n     * Url of the wallet's implementation of the [HTTP bridge]{@link https://github.com/ton-connect/docs/blob/main/bridge.md#http-bridge}.\n     */\n    bridgeUrl: string;\n}\n\n/**\n * JS-injectable wallet information.\n */\nexport interface WalletInfoInjectable extends WalletInfoBase {\n    /**\n     * If the wallet handles JS Bridge connection, specifies the binding for the bridge object accessible through window. Example: the key \"tonkeeper\" means the bridge can be accessed as window.tonkeeper.\n     */\n    jsBridgeKey: string;\n\n    /**\n     * Indicates if the wallet currently is injected to the webpage.\n     */\n    injected: boolean;\n\n    /**\n     * Indicates if the dapp is opened inside this wallet's browser.\n     */\n    embedded: boolean;\n}\n\n/**\n * Information about the JS-injectable wallet that is injected to the current webpage.\n */\nexport interface WalletInfoCurrentlyInjected extends WalletInfoInjectable {\n    injected: true;\n}\n\n/**\n * Information about the JS-injectable wallet in the browser of which the dApp is opened.\n */\nexport interface WalletInfoCurrentlyEmbedded extends WalletInfoCurrentlyInjected {\n    injected: true;\n\n    embedded: true;\n}\n\n/**\n * @deprecated Use `WalletInfoInjectable` or `WalletInfoCurrentlyInjected` instead.\n */\nexport interface WalletInfoInjected extends WalletInfoBase {\n    jsBridgeKey: string;\n    injected: boolean;\n    embedded: boolean;\n}\n\nexport type WalletInfo =\n    | WalletInfoRemote\n    | WalletInfoInjectable\n    | (WalletInfoRemote & WalletInfoInjectable);\n\nexport interface WalletInfoDTO {\n    name: string;\n    app_name: string;\n    image: string;\n    tondns?: string;\n    about_url: string;\n    universal_url?: string;\n    platforms: (\n        | 'ios'\n        | 'android'\n        | 'macos'\n        | 'windows'\n        | 'linux'\n        | 'chrome'\n        | 'firefox'\n        | 'safari'\n    )[];\n\n    deepLink?: string;\n    bridge: (WalletInfoBridgeRemoteDTO | WalletInfoBridgeInjectedDTO)[];\n}\n\nexport interface WalletInfoBridgeRemoteDTO {\n    type: 'sse';\n    url: string;\n}\n\nexport interface WalletInfoBridgeInjectedDTO {\n    type: 'js';\n    key: string;\n}\n\n/**\n * Checks if `WalletInfo` is `WalletInfoInjectable` and `WalletInfo` is injected to the current webpage (`walletInfo.injected === true`).\n * @param value WalletInfo to check.\n */\nexport function isWalletInfoCurrentlyInjected(\n    value: WalletInfo\n): value is WalletInfoCurrentlyInjected {\n    return isWalletInfoInjectable(value) && value.injected;\n}\n\n/**\n * Checks if `WalletInfo` is `WalletInfoInjectable` and dApp is opened inside this wallet's browser.\n * @param value WalletInfo to check.\n */\nexport function isWalletInfoCurrentlyEmbedded(\n    value: WalletInfo\n): value is WalletInfoCurrentlyEmbedded {\n    return isWalletInfoCurrentlyInjected(value) && value.embedded;\n}\n\n/**\n * Checks if `WalletInfo` is `WalletInfoInjected`, but doesn't check if it is injected to the page or not.\n * @param value WalletInfo to check.\n */\nexport function isWalletInfoInjectable(value: WalletInfo): value is WalletInfoInjectable {\n    return 'jsBridgeKey' in value;\n}\n\n/**\n * Checks if `WalletInfo` is `WalletInfoRemote`.\n * @param value WalletInfo to check.\n */\nexport function isWalletInfoRemote(value: WalletInfo): value is WalletInfoRemote {\n    return 'bridgeUrl' in value;\n}\n\n/**\n * @deprecated use `isWalletInfoInjectable` or `isWalletInfoCurrentlyInjected` instead.\n * @param value WalletInfo to check.\n */\nexport function isWalletInfoInjected(value: WalletInfo): value is WalletInfoInjected {\n    return 'jsBridgeKey' in value;\n}\n", "import { BadRequestError, UnknownA<PERSON>Error, UserRejectsError } from 'src/errors';\nimport { ManifestContentErrorError } from 'src/errors/protocol/events/connect/manifest-content-error.error';\nimport { ManifestNotFoundError } from 'src/errors/protocol/events/connect/manifest-not-found.error';\nimport { TonConnectError } from 'src/errors/ton-connect.error';\nimport { UnknownError } from 'src/errors/unknown.error';\nimport { CONNECT_EVENT_ERROR_CODES, ConnectEventError } from '@tonconnect/protocol';\n\nconst connectEventErrorsCodes: Partial<Record<CONNECT_EVENT_ERROR_CODES, typeof TonConnectError>> =\n    {\n        [CONNECT_EVENT_ERROR_CODES.UNKNOWN_ERROR]: UnknownError,\n        [CONNECT_EVENT_ERROR_CODES.USER_REJECTS_ERROR]: UserRejectsError,\n        [CONNECT_EVENT_ERROR_CODES.BAD_REQUEST_ERROR]: BadRequestError,\n        [CONNECT_EVENT_ERROR_CODES.UNKNOWN_APP_ERROR]: UnknownAppError,\n        [CONNECT_EVENT_ERROR_CODES.MANIFEST_NOT_FOUND_ERROR]: ManifestNotFoundError,\n        [CONNECT_EVENT_ERROR_CODES.MANIFEST_CONTENT_ERROR]: ManifestContentErrorError\n    };\n\nclass ConnectErrorsParser {\n    parseError(error: ConnectEventError['payload']): TonConnectError {\n        let ErrorConstructor: typeof TonConnectError = UnknownError;\n\n        if (error.code in connectEventErrorsCodes) {\n            ErrorConstructor = connectEventErrorsCodes[error.code] || UnknownError;\n        }\n\n        return new ErrorConstructor(error.message);\n    }\n}\n\nexport const connectErrorsParser = new ConnectErrorsParser();\n", "import {\n    AppRequest,\n    RpcMethod,\n    WalletResponse,\n    WalletResponseError,\n    WalletResponseSuccess\n} from '@tonconnect/protocol';\nimport { WithoutId } from 'src/utils/types';\n\nexport abstract class RpcParser<T extends RpcMethod> {\n    abstract convertToRpcRequest(...args: unknown[]): WithoutId<AppRequest<T>>;\n\n    abstract convertFromRpcResponse(rpcResponse: WithoutId<WalletResponseSuccess<T>>): unknown;\n\n    abstract parseAndThrowError(response: WithoutId<WalletResponseError<T>>): never;\n\n    public isError(\n        response: WithoutId<WalletResponse<T>>\n    ): response is WithoutId<WalletResponseError<T>> {\n        return 'error' in response;\n    }\n}\n", "import {\n    CONNECT_EVENT_ERROR_CODES,\n    SEND_TRANSACTION_ERROR_CODES,\n    SendTransactionRpcRequest,\n    SendTransactionRpcResponseError,\n    SendTransactionRpcResponseSuccess\n} from '@tonconnect/protocol';\nimport { BadRequestError, TonConnectError, UnknownAppError, UserRejectsError } from 'src/errors';\nimport { UnknownError } from 'src/errors/unknown.error';\nimport { SendTransactionRequest, SendTransactionResponse } from 'src/models/methods';\nimport { RpcParser } from 'src/parsers/rpc-parser';\nimport { WithoutId } from 'src/utils/types';\n\nconst sendTransactionErrors: Partial<Record<CONNECT_EVENT_ERROR_CODES, typeof TonConnectError>> = {\n    [SEND_TRANSACTION_ERROR_CODES.UNKNOWN_ERROR]: UnknownError,\n    [SEND_TRANSACTION_ERROR_CODES.USER_REJECTS_ERROR]: UserRejectsError,\n    [SEND_TRANSACTION_ERROR_CODES.BAD_REQUEST_ERROR]: BadRequestError,\n    [SEND_TRANSACTION_ERROR_CODES.UNKNOWN_APP_ERROR]: UnknownAppError\n};\n\nclass SendTransactionParser extends RpcParser<'sendTransaction'> {\n    convertToRpcRequest(\n        request: Omit<SendTransactionRequest, 'validUntil'> & { valid_until: number }\n    ): WithoutId<SendTransactionRpcRequest> {\n        return {\n            method: 'sendTransaction',\n            params: [JSON.stringify(request)]\n        };\n    }\n\n    parseAndThrowError(response: WithoutId<SendTransactionRpcResponseError>): never {\n        let ErrorConstructor: typeof TonConnectError = UnknownError;\n\n        if (response.error.code in sendTransactionErrors) {\n            ErrorConstructor = sendTransactionErrors[response.error.code] || UnknownError;\n        }\n\n        throw new ErrorConstructor(response.error.message);\n    }\n\n    convertFromRpcResponse(\n        rpcResponse: WithoutId<SendTransactionRpcResponseSuccess>\n    ): SendTransactionResponse {\n        return {\n            boc: rpcResponse.result\n        };\n    }\n}\n\nexport const sendTransactionParser = new SendTransactionParser();\n", "import { Base64, RpcMethod } from '@tonconnect/protocol';\nimport { TonConnectError } from 'src/errors/ton-connect.error';\nimport { BridgeIncomingMessage } from 'src/provider/bridge/models/bridge-incomming-message';\nimport { HttpBridgeGatewayStorage } from 'src/storage/http-bridge-gateway-storage';\nimport { IStorage } from 'src/storage/models/storage.interface';\nimport { addPathToUrl } from 'src/utils/url';\nimport '@tonconnect/isomorphic-eventsource';\nimport '@tonconnect/isomorphic-fetch';\nimport { callForSuccess } from 'src/utils/call-for-success';\nimport { logDebug, logError } from 'src/utils/log';\nimport { createResource } from 'src/utils/resource';\nimport { timeout } from 'src/utils/timeout';\nimport { createAbortController } from 'src/utils/create-abort-controller';\n\nexport class BridgeGateway {\n    private readonly ssePath = 'events';\n\n    private readonly postPath = 'message';\n\n    private readonly heartbeatMessage = 'heartbeat';\n\n    private readonly defaultTtl = 300;\n\n    private readonly defaultReconnectDelay = 2000;\n\n    private readonly defaultResendDelay = 5000;\n\n    private eventSource = createResource(\n        async (signal?: AbortSignal, openingDeadlineMS?: number): Promise<EventSource> => {\n            const eventSourceConfig = {\n                bridgeUrl: this.bridgeUrl,\n                ssePath: this.ssePath,\n                sessionId: this.sessionId,\n                bridgeGatewayStorage: this.bridgeGatewayStorage,\n                errorHandler: this.errorsHandler.bind(this),\n                messageHandler: this.messagesHandler.bind(this),\n                signal: signal,\n                openingDeadlineMS: openingDeadlineMS\n            };\n            return await createEventSource(eventSourceConfig);\n        },\n        async (resource: EventSource) => {\n            resource.close();\n        }\n    );\n\n    private get isReady(): boolean {\n        const eventSource = this.eventSource.current();\n        return eventSource?.readyState === EventSource.OPEN;\n    }\n\n    private get isClosed(): boolean {\n        const eventSource = this.eventSource.current();\n        return eventSource?.readyState !== EventSource.OPEN;\n    }\n\n    private get isConnecting(): boolean {\n        const eventSource = this.eventSource.current();\n        return eventSource?.readyState === EventSource.CONNECTING;\n    }\n\n    private readonly bridgeGatewayStorage: HttpBridgeGatewayStorage;\n\n    constructor(\n        storage: IStorage,\n        public readonly bridgeUrl: string,\n        public readonly sessionId: string,\n        private listener: (msg: BridgeIncomingMessage) => void,\n        private errorsListener: (err: Event) => void\n    ) {\n        this.bridgeGatewayStorage = new HttpBridgeGatewayStorage(storage, bridgeUrl);\n    }\n\n    public async registerSession(options?: RegisterSessionOptions): Promise<void> {\n        await this.eventSource.create(options?.signal, options?.openingDeadlineMS);\n    }\n\n    public async send(\n        message: Uint8Array,\n        receiver: string,\n        topic: RpcMethod,\n        options?: {\n            ttl?: number;\n            signal?: AbortSignal;\n            attempts?: number;\n        }\n    ): Promise<void>;\n    /** @deprecated use send(message, receiver, topic, options) instead */\n    public async send(\n        message: Uint8Array,\n        receiver: string,\n        topic: RpcMethod,\n        ttl?: number\n    ): Promise<void>;\n    public async send(\n        message: Uint8Array,\n        receiver: string,\n        topic: RpcMethod,\n        ttlOrOptions?: number | { ttl?: number; signal?: AbortSignal; attempts?: number }\n    ): Promise<void> {\n        // TODO: remove deprecated method\n        const options: { ttl?: number; signal?: AbortSignal; attempts?: number } = {};\n        if (typeof ttlOrOptions === 'number') {\n            options.ttl = ttlOrOptions;\n        } else {\n            options.ttl = ttlOrOptions?.ttl;\n            options.signal = ttlOrOptions?.signal;\n            options.attempts = ttlOrOptions?.attempts;\n        }\n\n        const url = new URL(addPathToUrl(this.bridgeUrl, this.postPath));\n        url.searchParams.append('client_id', this.sessionId);\n        url.searchParams.append('to', receiver);\n        url.searchParams.append('ttl', (options?.ttl || this.defaultTtl).toString());\n        url.searchParams.append('topic', topic);\n        const body = Base64.encode(message);\n\n        await callForSuccess(\n            async options => {\n                const response = await this.post(url, body, options.signal);\n\n                if (!response.ok) {\n                    throw new TonConnectError(`Bridge send failed, status ${response.status}`);\n                }\n            },\n            {\n                attempts: options?.attempts ?? Number.MAX_SAFE_INTEGER,\n                delayMs: this.defaultResendDelay,\n                signal: options?.signal\n            }\n        );\n    }\n\n    public pause(): void {\n        this.eventSource.dispose().catch(e => logError(`Bridge pause failed, ${e}`));\n    }\n\n    public async unPause(): Promise<void> {\n        const RECREATE_WITHOUT_DELAY = 0;\n        await this.eventSource.recreate(RECREATE_WITHOUT_DELAY);\n    }\n\n    public async close(): Promise<void> {\n        await this.eventSource.dispose().catch(e => logError(`Bridge close failed, ${e}`));\n    }\n\n    public setListener(listener: (msg: BridgeIncomingMessage) => void): void {\n        this.listener = listener;\n    }\n\n    public setErrorsListener(errorsListener: (err: Event) => void): void {\n        this.errorsListener = errorsListener;\n    }\n\n    private async post(url: URL, body: string, signal?: AbortSignal): Promise<Response> {\n        const response = await fetch(url, {\n            method: 'post',\n            body: body,\n            signal: signal\n        });\n\n        if (!response.ok) {\n            throw new TonConnectError(`Bridge send failed, status ${response.status}`);\n        }\n\n        return response;\n    }\n\n    private async errorsHandler(eventSource: EventSource, e: Event): Promise<EventSource | void> {\n        if (this.isConnecting) {\n            eventSource.close();\n            throw new TonConnectError('Bridge error, failed to connect');\n        }\n\n        if (this.isReady) {\n            try {\n                this.errorsListener(e);\n            } catch (e) {}\n            return;\n        }\n\n        if (this.isClosed) {\n            eventSource.close();\n            logDebug(`Bridge reconnecting, ${this.defaultReconnectDelay}ms delay`);\n            return await this.eventSource.recreate(this.defaultReconnectDelay);\n        }\n\n        throw new TonConnectError('Bridge error, unknown state');\n    }\n\n    private async messagesHandler(e: MessageEvent<string>): Promise<void> {\n        if (e.data === this.heartbeatMessage) {\n            return;\n        }\n\n        await this.bridgeGatewayStorage.storeLastEventId(e.lastEventId);\n\n        if (this.isClosed) {\n            return;\n        }\n\n        let bridgeIncomingMessage: BridgeIncomingMessage;\n        try {\n            bridgeIncomingMessage = JSON.parse(e.data);\n        } catch (e) {\n            throw new TonConnectError(`Bridge message parse failed, message ${e.data}`);\n        }\n        this.listener(bridgeIncomingMessage);\n    }\n}\n\n/**\n * Represents options for creating an event source.\n */\nexport type RegisterSessionOptions = {\n    /**\n     * Deadline for opening the event source.\n     */\n    openingDeadlineMS?: number;\n\n    /**\n     * Signal to abort the operation.\n     */\n    signal?: AbortSignal;\n};\n\n/**\n * Configuration for creating an event source.\n */\nexport type CreateEventSourceConfig = {\n    /**\n     * URL of the bridge.\n     */\n    bridgeUrl: string;\n    /**\n     * Path to the SSE endpoint.\n     */\n    ssePath: string;\n    /**\n     * Session ID of the client.\n     */\n    sessionId: string;\n    /**\n     * Storage for the last event ID.\n     */\n    bridgeGatewayStorage: HttpBridgeGatewayStorage;\n    /**\n     * Error handler for the event source.\n     */\n    errorHandler: (eventSource: EventSource, e: Event) => Promise<EventSource | void>;\n    /**\n     * Message handler for the event source.\n     */\n    messageHandler: (e: MessageEvent<string>) => void;\n    /**\n     * Signal to abort opening the event source and destroy it.\n     */\n    signal?: AbortSignal;\n    /**\n     * Deadline for opening the event source.\n     */\n    openingDeadlineMS?: number;\n};\n\n/**\n * Creates an event source.\n * @param {CreateEventSourceConfig} config - Configuration for creating an event source.\n */\nasync function createEventSource(config: CreateEventSourceConfig): Promise<EventSource> {\n    return await timeout(\n        async (resolve, reject, deferOptions) => {\n            const abortController = createAbortController(deferOptions.signal);\n            const signal = abortController.signal;\n\n            if (signal.aborted) {\n                reject(new TonConnectError('Bridge connection aborted'));\n                return;\n            }\n\n            const url = new URL(addPathToUrl(config.bridgeUrl, config.ssePath));\n            url.searchParams.append('client_id', config.sessionId);\n\n            const lastEventId = await config.bridgeGatewayStorage.getLastEventId();\n            if (lastEventId) {\n                url.searchParams.append('last_event_id', lastEventId);\n            }\n\n            if (signal.aborted) {\n                reject(new TonConnectError('Bridge connection aborted'));\n                return;\n            }\n\n            const eventSource = new EventSource(url.toString());\n\n            eventSource.onerror = async (reason: Event): Promise<void> => {\n                if (signal.aborted) {\n                    eventSource.close();\n                    reject(new TonConnectError('Bridge connection aborted'));\n                    return;\n                }\n\n                try {\n                    const newInstance = await config.errorHandler(eventSource, reason);\n                    if (newInstance !== eventSource) {\n                        eventSource.close();\n                    }\n\n                    if (newInstance && newInstance !== eventSource) {\n                        resolve(newInstance);\n                    }\n                } catch (e) {\n                    eventSource.close();\n                    reject(e);\n                }\n            };\n            eventSource.onopen = (): void => {\n                if (signal.aborted) {\n                    eventSource.close();\n                    reject(new TonConnectError('Bridge connection aborted'));\n                    return;\n                }\n                resolve(eventSource);\n            };\n            eventSource.onmessage = (event: MessageEvent<string>): void => {\n                if (signal.aborted) {\n                    eventSource.close();\n                    reject(new TonConnectError('Bridge connection aborted'));\n                    return;\n                }\n                config.messageHandler(event);\n            };\n\n            config.signal?.addEventListener('abort', () => {\n                eventSource.close();\n                reject(new TonConnectError('Bridge connection aborted'));\n            });\n        },\n        { timeout: config.openingDeadlineMS, signal: config.signal }\n    );\n}\n", "import {\n    App<PERSON>e<PERSON>,\n    Base64,\n    ConnectEventSuc<PERSON>,\n    ConnectRequest,\n    hexToByteArray,\n    RpcMethod,\n    SessionCrypto,\n    TonAddressItemReply,\n    WalletEvent,\n    WalletMessage,\n    WalletResponse\n} from '@tonconnect/protocol';\nimport { TonConnectError } from 'src/errors/ton-connect.error';\nimport { WalletConnectionSourceHTTP } from 'src/models/wallet/wallet-connection-source';\nimport { BridgeGateway } from 'src/provider/bridge/bridge-gateway';\nimport {\n    BridgeConnectionHttp,\n    isPendingConnectionHttp\n} from 'src/provider/bridge/models/bridge-connection';\nimport { BridgeIncomingMessage } from 'src/provider/bridge/models/bridge-incomming-message';\nimport { BridgePartialSession, BridgeSession } from 'src/provider/bridge/models/bridge-session';\nimport { HTTPProvider } from 'src/provider/provider';\nimport { BridgeConnectionStorage } from 'src/storage/bridge-connection-storage';\nimport { IStorage } from 'src/storage/models/storage.interface';\nimport { Optional, WithoutId, WithoutIdDistributive } from 'src/utils/types';\nimport { PROTOCOL_VERSION } from 'src/resources/protocol';\nimport { logDebug, logError } from 'src/utils/log';\nimport { encodeTelegramUrlParameters, isTelegramUrl } from 'src/utils/url';\nimport { callForSuccess } from 'src/utils/call-for-success';\nimport { createAbortController } from 'src/utils/create-abort-controller';\n\nexport class BridgeProvider implements HTTPProvider {\n    public static async fromStorage(storage: IStorage): Promise<BridgeProvider> {\n        const bridgeConnectionStorage = new BridgeConnectionStorage(storage);\n        const connection = await bridgeConnectionStorage.getHttpConnection();\n\n        if (isPendingConnectionHttp(connection)) {\n            return new BridgeProvider(storage, connection.connectionSource);\n        }\n        return new BridgeProvider(storage, { bridgeUrl: connection.session.bridgeUrl });\n    }\n\n    public readonly type = 'http';\n\n    private readonly standardUniversalLink = 'tc://';\n\n    private readonly connectionStorage: BridgeConnectionStorage;\n\n    private readonly pendingRequests = new Map<\n        string,\n        (response: WithoutId<WalletResponse<RpcMethod>>) => void\n    >();\n\n    private session: BridgeSession | BridgePartialSession | null = null;\n\n    private gateway: BridgeGateway | null = null;\n\n    private pendingGateways: BridgeGateway[] = [];\n\n    private listeners: Array<(e: WithoutIdDistributive<WalletEvent>) => void> = [];\n\n    private readonly defaultOpeningDeadlineMS = 12000;\n\n    private readonly defaultRetryTimeoutMS = 2000;\n\n    private abortController?: AbortController;\n\n    constructor(\n        private readonly storage: IStorage,\n        private readonly walletConnectionSource:\n            | Optional<WalletConnectionSourceHTTP, 'universalLink'>\n            | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[]\n    ) {\n        this.connectionStorage = new BridgeConnectionStorage(storage);\n    }\n\n    public connect(\n        message: ConnectRequest,\n        options?: {\n            openingDeadlineMS?: number;\n            signal?: AbortSignal;\n        }\n    ): string {\n        const abortController = createAbortController(options?.signal);\n        this.abortController?.abort();\n        this.abortController = abortController;\n\n        this.closeGateways();\n\n        const sessionCrypto = new SessionCrypto();\n\n        this.session = {\n            sessionCrypto,\n            bridgeUrl:\n                'bridgeUrl' in this.walletConnectionSource\n                    ? this.walletConnectionSource.bridgeUrl\n                    : ''\n        };\n\n        this.connectionStorage\n            .storeConnection({\n                type: 'http',\n                connectionSource: this.walletConnectionSource,\n                sessionCrypto\n            })\n            .then(async () => {\n                if (abortController.signal.aborted) {\n                    return;\n                }\n\n                await callForSuccess(\n                    _options =>\n                        this.openGateways(sessionCrypto, {\n                            openingDeadlineMS:\n                                options?.openingDeadlineMS ?? this.defaultOpeningDeadlineMS,\n                            signal: _options?.signal\n                        }),\n                    {\n                        attempts: Number.MAX_SAFE_INTEGER,\n                        delayMs: this.defaultRetryTimeoutMS,\n                        signal: abortController.signal\n                    }\n                );\n            });\n\n        const universalLink =\n            'universalLink' in this.walletConnectionSource &&\n            this.walletConnectionSource.universalLink\n                ? this.walletConnectionSource.universalLink\n                : this.standardUniversalLink;\n\n        return this.generateUniversalLink(universalLink, message);\n    }\n\n    public async restoreConnection(options?: {\n        openingDeadlineMS?: number;\n        signal?: AbortSignal;\n    }): Promise<void> {\n        const abortController = createAbortController(options?.signal);\n        this.abortController?.abort();\n        this.abortController = abortController;\n\n        if (abortController.signal.aborted) {\n            return;\n        }\n\n        this.closeGateways();\n        const storedConnection = await this.connectionStorage.getHttpConnection();\n        if (!storedConnection) {\n            return;\n        }\n\n        if (abortController.signal.aborted) {\n            return;\n        }\n\n        const openingDeadlineMS = options?.openingDeadlineMS ?? this.defaultOpeningDeadlineMS;\n\n        if (isPendingConnectionHttp(storedConnection)) {\n            this.session = {\n                sessionCrypto: storedConnection.sessionCrypto,\n                bridgeUrl:\n                    'bridgeUrl' in this.walletConnectionSource\n                        ? this.walletConnectionSource.bridgeUrl\n                        : ''\n            };\n\n            return await this.openGateways(storedConnection.sessionCrypto, {\n                openingDeadlineMS: openingDeadlineMS,\n                signal: abortController?.signal\n            });\n        }\n\n        if (Array.isArray(this.walletConnectionSource)) {\n            throw new TonConnectError(\n                'Internal error. Connection source is array while WalletConnectionSourceHTTP was expected.'\n            );\n        }\n\n        this.session = storedConnection.session;\n\n        if (this.gateway) {\n            logDebug('Gateway is already opened, closing previous gateway');\n            await this.gateway.close();\n        }\n\n        this.gateway = new BridgeGateway(\n            this.storage,\n            this.walletConnectionSource.bridgeUrl,\n            storedConnection.session.sessionCrypto.sessionId,\n            this.gatewayListener.bind(this),\n            this.gatewayErrorsListener.bind(this)\n        );\n\n        if (abortController.signal.aborted) {\n            return;\n        }\n\n        // notify listeners about stored connection\n        this.listeners.forEach(listener => listener(storedConnection.connectEvent));\n\n        // wait for the connection to be opened\n        try {\n            await callForSuccess(\n                options =>\n                    this.gateway!.registerSession({\n                        openingDeadlineMS: openingDeadlineMS,\n                        signal: options.signal\n                    }),\n                {\n                    attempts: Number.MAX_SAFE_INTEGER,\n                    delayMs: this.defaultRetryTimeoutMS,\n                    signal: abortController.signal\n                }\n            );\n        } catch (e) {\n            await this.disconnect({ signal: abortController.signal });\n            return;\n        }\n    }\n\n    public sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        options?: {\n            attempts?: number;\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n        }\n    ): Promise<WithoutId<WalletResponse<T>>>;\n    /** @deprecated use sendRequest(transaction, options) instead */\n    public sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        onRequestSent?: () => void\n    ): Promise<WithoutId<WalletResponse<T>>>;\n    public sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        optionsOrOnRequestSent?:\n            | (() => void)\n            | { attempts?: number; onRequestSent?: () => void; signal?: AbortSignal }\n    ): Promise<WithoutId<WalletResponse<T>>> {\n        // TODO: remove deprecated method\n        const options: {\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n            attempts?: number;\n        } = {};\n        if (typeof optionsOrOnRequestSent === 'function') {\n            options.onRequestSent = optionsOrOnRequestSent;\n        } else {\n            options.onRequestSent = optionsOrOnRequestSent?.onRequestSent;\n            options.signal = optionsOrOnRequestSent?.signal;\n            options.attempts = optionsOrOnRequestSent?.attempts;\n        }\n\n        return new Promise(async (resolve, reject) => {\n            if (!this.gateway || !this.session || !('walletPublicKey' in this.session)) {\n                throw new TonConnectError('Trying to send bridge request without session');\n            }\n\n            const id = (await this.connectionStorage.getNextRpcRequestId()).toString();\n            await this.connectionStorage.increaseNextRpcRequestId();\n\n            logDebug('Send http-bridge request:', { ...request, id });\n\n            const encodedRequest = this.session!.sessionCrypto.encrypt(\n                JSON.stringify({ ...request, id }),\n                hexToByteArray(this.session.walletPublicKey)\n            );\n\n            try {\n                await this.gateway.send(\n                    encodedRequest,\n                    this.session.walletPublicKey,\n                    request.method,\n                    { attempts: options?.attempts, signal: options?.signal }\n                );\n                options?.onRequestSent?.();\n                this.pendingRequests.set(id.toString(), resolve);\n            } catch (e) {\n                reject(e);\n            }\n        });\n    }\n\n    public closeConnection(): void {\n        this.closeGateways();\n        this.listeners = [];\n        this.session = null;\n        this.gateway = null;\n    }\n\n    public async disconnect(options?: { signal?: AbortSignal }): Promise<void> {\n        return new Promise(async resolve => {\n            let called = false;\n            let timeoutId: ReturnType<typeof setTimeout> | null = null;\n            const onRequestSent = (): void => {\n                if (!called) {\n                    called = true;\n                    this.removeBridgeAndSession().then(resolve);\n                }\n            };\n\n            try {\n                this.closeGateways();\n\n                const abortController = createAbortController(options?.signal);\n                timeoutId = setTimeout(() => {\n                    abortController.abort();\n                }, this.defaultOpeningDeadlineMS);\n\n                await this.sendRequest(\n                    { method: 'disconnect', params: [] },\n                    {\n                        onRequestSent: onRequestSent,\n                        signal: abortController.signal,\n                        attempts: 1\n                    }\n                );\n            } catch (e) {\n                logDebug('Disconnect error:', e);\n\n                if (!called) {\n                    this.removeBridgeAndSession().then(resolve);\n                }\n            } finally {\n                if (timeoutId) {\n                    clearTimeout(timeoutId);\n                }\n\n                onRequestSent();\n            }\n        });\n    }\n\n    public listen(callback: (e: WithoutIdDistributive<WalletEvent>) => void): () => void {\n        this.listeners.push(callback);\n        return () => (this.listeners = this.listeners.filter(listener => listener !== callback));\n    }\n\n    public pause(): void {\n        this.gateway?.pause();\n        this.pendingGateways.forEach(bridge => bridge.pause());\n    }\n\n    public async unPause(): Promise<void> {\n        const promises = this.pendingGateways.map(bridge => bridge.unPause());\n        if (this.gateway) {\n            promises.push(this.gateway.unPause());\n        }\n        await Promise.all(promises);\n    }\n\n    private async pendingGatewaysListener(\n        gateway: BridgeGateway,\n        bridgeUrl: string,\n        bridgeIncomingMessage: BridgeIncomingMessage\n    ): Promise<void> {\n        if (!this.pendingGateways.includes(gateway)) {\n            await gateway.close();\n            return;\n        }\n\n        this.closeGateways({ except: gateway });\n\n        if (this.gateway) {\n            logDebug('Gateway is already opened, closing previous gateway');\n            await this.gateway.close();\n        }\n\n        this.session!.bridgeUrl = bridgeUrl;\n        this.gateway = gateway;\n        this.gateway.setErrorsListener(this.gatewayErrorsListener.bind(this));\n        this.gateway.setListener(this.gatewayListener.bind(this));\n        return this.gatewayListener(bridgeIncomingMessage);\n    }\n\n    private async gatewayListener(bridgeIncomingMessage: BridgeIncomingMessage): Promise<void> {\n        const walletMessage: WalletMessage = JSON.parse(\n            this.session!.sessionCrypto.decrypt(\n                Base64.decode(bridgeIncomingMessage.message).toUint8Array(),\n                hexToByteArray(bridgeIncomingMessage.from)\n            )\n        );\n\n        logDebug('Wallet message received:', walletMessage);\n\n        if (!('event' in walletMessage)) {\n            const id = walletMessage.id.toString();\n            const resolve = this.pendingRequests.get(id);\n            if (!resolve) {\n                logDebug(`Response id ${id} doesn't match any request's id`);\n                return;\n            }\n\n            resolve(walletMessage);\n            this.pendingRequests.delete(id);\n            return;\n        }\n\n        if (walletMessage.id !== undefined) {\n            const lastId = await this.connectionStorage.getLastWalletEventId();\n\n            if (lastId !== undefined && walletMessage.id <= lastId) {\n                logError(\n                    `Received event id (=${walletMessage.id}) must be greater than stored last wallet event id (=${lastId}) `\n                );\n                return;\n            }\n\n            if (walletMessage.event !== 'connect') {\n                await this.connectionStorage.storeLastWalletEventId(walletMessage.id);\n            }\n        }\n\n        // `this.listeners` might be modified in the event handler\n        const listeners = this.listeners;\n\n        if (walletMessage.event === 'connect') {\n            await this.updateSession(walletMessage, bridgeIncomingMessage.from);\n        }\n\n        if (walletMessage.event === 'disconnect') {\n            logDebug(`Removing bridge and session: received disconnect event`);\n            await this.removeBridgeAndSession();\n        }\n\n        listeners.forEach(listener => listener(walletMessage));\n    }\n\n    private async gatewayErrorsListener(e: Event): Promise<void> {\n        throw new TonConnectError(`Bridge error ${JSON.stringify(e)}`);\n    }\n\n    private async updateSession(\n        connectEvent: ConnectEventSuccess,\n        walletPublicKey: string\n    ): Promise<void> {\n        this.session = {\n            ...this.session!,\n            walletPublicKey\n        };\n\n        const tonAddrItem: TonAddressItemReply = connectEvent.payload.items.find(\n            item => item.name === 'ton_addr'\n        ) as TonAddressItemReply;\n\n        const connectEventToSave: BridgeConnectionHttp['connectEvent'] = {\n            ...connectEvent,\n            payload: {\n                ...connectEvent.payload,\n                items: [tonAddrItem]\n            }\n        };\n\n        await this.connectionStorage.storeConnection({\n            type: 'http',\n            session: this.session,\n            lastWalletEventId: connectEvent.id,\n            connectEvent: connectEventToSave,\n            nextRpcRequestId: 0\n        });\n    }\n\n    private async removeBridgeAndSession(): Promise<void> {\n        this.closeConnection();\n        await this.connectionStorage.removeConnection();\n    }\n\n    private generateUniversalLink(universalLink: string, message: ConnectRequest): string {\n        if (isTelegramUrl(universalLink)) {\n            return this.generateTGUniversalLink(universalLink, message);\n        }\n\n        return this.generateRegularUniversalLink(universalLink, message);\n    }\n\n    private generateRegularUniversalLink(universalLink: string, message: ConnectRequest): string {\n        const url = new URL(universalLink);\n        url.searchParams.append('v', PROTOCOL_VERSION.toString());\n        url.searchParams.append('id', this.session!.sessionCrypto.sessionId);\n        url.searchParams.append('r', JSON.stringify(message));\n        return url.toString();\n    }\n\n    private generateTGUniversalLink(universalLink: string, message: ConnectRequest): string {\n        const urlToWrap = this.generateRegularUniversalLink('about:blank', message);\n        const linkParams = urlToWrap.split('?')[1]!;\n\n        const startapp = 'tonconnect-' + encodeTelegramUrlParameters(linkParams);\n\n        // TODO: Remove this line after all dApps and the wallets-list.json have been updated\n        const updatedUniversalLink = this.convertToDirectLink(universalLink);\n\n        const url = new URL(updatedUniversalLink);\n        url.searchParams.append('startapp', startapp);\n        return url.toString();\n    }\n\n    // TODO: Remove this method after all dApps and the wallets-list.json have been updated\n    private convertToDirectLink(universalLink: string): string {\n        const url = new URL(universalLink);\n\n        if (url.searchParams.has('attach')) {\n            url.searchParams.delete('attach');\n            url.pathname += '/start';\n        }\n\n        return url.toString();\n    }\n\n    private async openGateways(\n        sessionCrypto: SessionCrypto,\n        options?: {\n            openingDeadlineMS?: number;\n            signal?: AbortSignal;\n        }\n    ): Promise<void> {\n        if (Array.isArray(this.walletConnectionSource)) {\n            // close all gateways before opening new ones\n            this.pendingGateways.map(bridge => bridge.close().catch());\n\n            // open new gateways\n            this.pendingGateways = this.walletConnectionSource.map(source => {\n                const gateway = new BridgeGateway(\n                    this.storage,\n                    source.bridgeUrl,\n                    sessionCrypto.sessionId,\n                    () => {},\n                    () => {}\n                );\n\n                gateway.setListener(message =>\n                    this.pendingGatewaysListener(gateway, source.bridgeUrl, message)\n                );\n\n                return gateway;\n            });\n\n            await Promise.allSettled(\n                this.pendingGateways.map(bridge =>\n                    callForSuccess(\n                        (_options): Promise<void> => {\n                            if (!this.pendingGateways.some(item => item === bridge)) {\n                                return bridge.close();\n                            }\n\n                            return bridge.registerSession({\n                                openingDeadlineMS:\n                                    options?.openingDeadlineMS ?? this.defaultOpeningDeadlineMS,\n                                signal: _options.signal\n                            });\n                        },\n                        {\n                            attempts: Number.MAX_SAFE_INTEGER,\n                            delayMs: this.defaultRetryTimeoutMS,\n                            signal: options?.signal\n                        }\n                    )\n                )\n            );\n\n            return;\n        } else {\n            if (this.gateway) {\n                logDebug(`Gateway is already opened, closing previous gateway`);\n                await this.gateway.close();\n            }\n\n            this.gateway = new BridgeGateway(\n                this.storage,\n                this.walletConnectionSource.bridgeUrl,\n                sessionCrypto.sessionId,\n                this.gatewayListener.bind(this),\n                this.gatewayErrorsListener.bind(this)\n            );\n            return await this.gateway.registerSession({\n                openingDeadlineMS: options?.openingDeadlineMS,\n                signal: options?.signal\n            });\n        }\n    }\n\n    private closeGateways(options?: { except: BridgeGateway }): void {\n        this.gateway?.close();\n        this.pendingGateways\n            .filter(item => item !== options?.except)\n            .forEach(bridge => bridge.close());\n        this.pendingGateways = [];\n    }\n}\n", "import { DeviceIn<PERSON>, KeyPair, SessionCrypto, TonAddressItemReply } from '@tonconnect/protocol';\nimport { BridgeSessionRaw } from 'src/provider/bridge/models/bridge-session-raw';\nimport { BridgeSession } from './bridge-session';\nimport { WalletConnectionSourceHTTP } from 'src/models';\nimport { Optional } from 'src/utils/types';\n\nexport type BridgeConnection =\n    | BridgeConnectionHttp\n    | BridgePendingConnectionHttp\n    | BridgeConnectionInjected;\n\nexport interface BridgeConnectionInjected {\n    type: 'injected';\n    jsBridgeKey: string;\n    nextRpcRequestId: number;\n}\n\nexport interface BridgeConnectionHttp {\n    type: 'http';\n    lastWalletEventId?: number;\n    nextRpcRequestId: number;\n    connectEvent: {\n        event: 'connect';\n        payload: {\n            items: [TonAddressItemReply];\n            device: DeviceInfo;\n        };\n    };\n    session: BridgeSession;\n}\n\nexport interface BridgePendingConnectionHttp {\n    type: 'http';\n    sessionCrypto: SessionCrypto;\n    connectionSource:\n        | Optional<WalletConnectionSourceHTTP, 'universalLink'>\n        | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[];\n}\n\nexport function isPendingConnectionHttp(\n    connection: BridgePendingConnectionHttp | BridgeConnectionHttp\n): connection is BridgePendingConnectionHttp {\n    return !('connectEvent' in connection);\n}\n\nexport type BridgeConnectionHttpRaw = Omit<BridgeConnectionHttp, 'session'> & {\n    session: BridgeSessionRaw;\n};\n\nexport type BridgePendingConnectionHttpRaw = Omit<BridgePendingConnectionHttp, 'sessionCrypto'> & {\n    sessionCrypto: KeyPair;\n};\n\nexport type BridgeConnectionRaw =\n    | BridgeConnectionHttpRaw\n    | BridgePendingConnectionHttpRaw\n    | BridgeConnectionInjected;\n", "import { WalletNotInjectedError } from 'src/errors/wallet/wallet-not-injected.error';\nimport {\n    AppRequest,\n    ConnectEventError,\n    ConnectRequest,\n    RpcMethod,\n    WalletEvent,\n    WalletResponse\n} from '@tonconnect/protocol';\nimport {\n    InjectedWalletApi,\n    isJSBridgeWithMetadata\n} from 'src/provider/injected/models/injected-wallet-api';\nimport { InternalProvider } from 'src/provider/provider';\nimport { BridgeConnectionStorage } from 'src/storage/bridge-connection-storage';\nimport { IStorage } from 'src/storage/models/storage.interface';\nimport { WithoutId, WithoutIdDistributive } from 'src/utils/types';\nimport { getWindow, tryGetWindowKeys } from 'src/utils/web-api';\nimport { PROTOCOL_VERSION } from 'src/resources/protocol';\nimport { WalletInfoCurrentlyInjected } from 'src/models';\nimport { logDebug } from 'src/utils/log';\n\ntype WindowWithTon<T extends string> = {\n    [key in T]: {\n        tonconnect: InjectedWalletApi;\n    };\n} & Window;\n\nexport class InjectedProvider<T extends string = string> implements InternalProvider {\n    private static window = getWindow();\n\n    public static async fromStorage(storage: IStorage): Promise<InjectedProvider> {\n        const bridgeConnectionStorage = new BridgeConnectionStorage(storage);\n        const connection = await bridgeConnectionStorage.getInjectedConnection();\n        return new InjectedProvider(storage, connection.jsBridgeKey);\n    }\n\n    public static isWalletInjected(injectedWalletKey: string): boolean {\n        return InjectedProvider.isWindowContainsWallet(this.window, injectedWalletKey);\n    }\n\n    public static isInsideWalletBrowser(injectedWalletKey: string): boolean {\n        if (InjectedProvider.isWindowContainsWallet(this.window, injectedWalletKey)) {\n            return this.window[injectedWalletKey]!.tonconnect.isWalletBrowser;\n        }\n\n        return false;\n    }\n\n    public static getCurrentlyInjectedWallets(): WalletInfoCurrentlyInjected[] {\n        if (!this.window) {\n            return [];\n        }\n\n        const windowKeys = tryGetWindowKeys();\n        const wallets = windowKeys.filter(([_, value]) =>\n            isJSBridgeWithMetadata(value)\n        ) as unknown as [string, { tonconnect: InjectedWalletApi }][];\n\n        return wallets.map(([jsBridgeKey, wallet]) => ({\n            name: wallet.tonconnect.walletInfo.name,\n            appName: wallet.tonconnect.walletInfo.app_name,\n            aboutUrl: wallet.tonconnect.walletInfo.about_url,\n            imageUrl: wallet.tonconnect.walletInfo.image,\n            tondns: wallet.tonconnect.walletInfo.tondns,\n            jsBridgeKey,\n            injected: true,\n            embedded: wallet.tonconnect.isWalletBrowser,\n            platforms: wallet.tonconnect.walletInfo.platforms\n        }));\n    }\n\n    private static isWindowContainsWallet<T extends string>(\n        window: Window | undefined,\n        injectedWalletKey: string\n    ): window is WindowWithTon<T> {\n        return (\n            !!window &&\n            injectedWalletKey in window &&\n            typeof window[injectedWalletKey as keyof Window] === 'object' &&\n            'tonconnect' in window[injectedWalletKey as keyof Window]\n        );\n    }\n\n    public readonly type = 'injected';\n\n    private unsubscribeCallback: (() => void) | null = null;\n\n    private injectedWallet: InjectedWalletApi;\n\n    private readonly connectionStorage: BridgeConnectionStorage;\n\n    private listenSubscriptions = false;\n\n    private listeners: Array<(e: WithoutIdDistributive<WalletEvent>) => void> = [];\n\n    constructor(storage: IStorage, private readonly injectedWalletKey: T) {\n        const window: Window | undefined | WindowWithTon<T> = InjectedProvider.window;\n        if (!InjectedProvider.isWindowContainsWallet(window, injectedWalletKey)) {\n            throw new WalletNotInjectedError();\n        }\n\n        this.connectionStorage = new BridgeConnectionStorage(storage);\n        this.injectedWallet = window[injectedWalletKey]!.tonconnect!;\n    }\n\n    public connect(message: ConnectRequest): void {\n        this._connect(PROTOCOL_VERSION, message);\n    }\n\n    public async restoreConnection(): Promise<void> {\n        try {\n            logDebug(`Injected Provider restoring connection...`);\n            const connectEvent = await this.injectedWallet.restoreConnection();\n            logDebug('Injected Provider restoring connection response', connectEvent);\n\n            if (connectEvent.event === 'connect') {\n                this.makeSubscriptions();\n                this.listeners.forEach(listener => listener(connectEvent));\n            } else {\n                await this.connectionStorage.removeConnection();\n            }\n        } catch (e) {\n            await this.connectionStorage.removeConnection();\n            console.error(e);\n        }\n    }\n\n    public closeConnection(): void {\n        if (this.listenSubscriptions) {\n            this.injectedWallet.disconnect();\n        }\n        this.closeAllListeners();\n    }\n\n    public async disconnect(): Promise<void> {\n        return new Promise(resolve => {\n            const onRequestSent = (): void => {\n                this.closeAllListeners();\n                this.connectionStorage.removeConnection().then(resolve);\n            };\n\n            try {\n                this.injectedWallet.disconnect();\n                onRequestSent();\n            } catch (e) {\n                logDebug(e);\n\n                this.sendRequest(\n                    {\n                        method: 'disconnect',\n                        params: []\n                    },\n                    onRequestSent\n                );\n            }\n        });\n    }\n\n    private closeAllListeners(): void {\n        this.listenSubscriptions = false;\n        this.listeners = [];\n        this.unsubscribeCallback?.();\n    }\n\n    public listen(eventsCallback: (e: WithoutIdDistributive<WalletEvent>) => void): () => void {\n        this.listeners.push(eventsCallback);\n        return () =>\n            (this.listeners = this.listeners.filter(listener => listener !== eventsCallback));\n    }\n\n    public sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        options?: {\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n            attempts?: number;\n        }\n    ): Promise<WithoutId<WalletResponse<T>>>;\n    /** @deprecated use sendRequest(transaction, options) instead */\n    public sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        onRequestSent?: () => void\n    ): Promise<WithoutId<WalletResponse<T>>>;\n    public async sendRequest<T extends RpcMethod>(\n        request: WithoutId<AppRequest<T>>,\n        optionsOrOnRequestSent?: (() => void) | { onRequestSent?: () => void; signal?: AbortSignal; attempts?: number }\n    ): Promise<WithoutId<WalletResponse<T>>> {\n        // TODO: remove deprecated method\n        const options: {\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n            attempts?: number;\n        } = {};\n        if (typeof optionsOrOnRequestSent === 'function') {\n            options.onRequestSent = optionsOrOnRequestSent;\n        } else {\n            options.onRequestSent = optionsOrOnRequestSent?.onRequestSent;\n            options.signal = optionsOrOnRequestSent?.signal;\n        }\n\n        const id = (await this.connectionStorage.getNextRpcRequestId()).toString();\n        await this.connectionStorage.increaseNextRpcRequestId();\n\n        logDebug('Send injected-bridge request:', { ...request, id });\n        const result = this.injectedWallet.send<T>({ ...request, id } as AppRequest<T>);\n        result.then(response => logDebug('Wallet message received:', response));\n        options?.onRequestSent?.();\n\n        return result;\n    }\n\n    private async _connect(protocolVersion: number, message: ConnectRequest): Promise<void> {\n        try {\n            logDebug(\n                `Injected Provider connect request: protocolVersion: ${protocolVersion}, message:`,\n                message\n            );\n            const connectEvent = await this.injectedWallet.connect(protocolVersion, message);\n\n            logDebug('Injected Provider connect response:', connectEvent);\n\n            if (connectEvent.event === 'connect') {\n                await this.updateSession();\n                this.makeSubscriptions();\n            }\n            this.listeners.forEach(listener => listener(connectEvent));\n        } catch (e) {\n            logDebug('Injected Provider connect error:', e);\n            const connectEventError: WithoutId<ConnectEventError> = {\n                event: 'connect_error',\n                payload: {\n                    code: 0,\n                    message: e?.toString()\n                }\n            };\n\n            this.listeners.forEach(listener => listener(connectEventError));\n        }\n    }\n\n    private makeSubscriptions(): void {\n        this.listenSubscriptions = true;\n        this.unsubscribeCallback = this.injectedWallet.listen(e => {\n            logDebug('Wallet message received:', e);\n\n            if (this.listenSubscriptions) {\n                this.listeners.forEach(listener => listener(e));\n            }\n\n            if (e.event === 'disconnect') {\n                this.disconnect();\n            }\n        });\n    }\n\n    private updateSession(): Promise<void> {\n        return this.connectionStorage.storeConnection({\n            type: 'injected',\n            jsBridgeKey: this.injectedWalletKey,\n            nextRpcRequestId: 0\n        });\n    }\n}\n", "import {\n    AppRequest,\n    ConnectEvent,\n    ConnectRequest,\n    DeviceInfo,\n    RpcMethod,\n    WalletEvent,\n    WalletResponse\n} from '@tonconnect/protocol';\nimport { WalletInfoDTO } from 'src/models/wallet/wallet-info';\nimport { hasProperties, hasProperty } from 'src/utils/types';\n\nexport interface InjectedWalletApi {\n    deviceInfo: DeviceInfo;\n    walletInfo: Pick<\n        WalletInfoDTO,\n        'name' | 'app_name' | 'tondns' | 'image' | 'about_url' | 'platforms'\n    >;\n    protocolVersion: number;\n    isWalletBrowser: boolean;\n    connect(protocolVersion: number, message: ConnectRequest): Promise<ConnectEvent>;\n    restoreConnection(): Promise<ConnectEvent>;\n    send<T extends RpcMethod>(message: AppRequest<T>): Promise<WalletResponse<T>>;\n    listen(callback: (event: WalletEvent) => void): () => void;\n\n    /**\n     * @deprecated\n     */\n    disconnect(): void;\n}\n\nexport function isJSBridgeWithMetadata(value: unknown): value is { tonconnect: InjectedWalletApi } {\n    try {\n        if (!hasProperty(value, 'tonconnect') || !hasProperty(value.tonconnect, 'walletInfo')) {\n            return false;\n        }\n\n        return hasProperties(value.tonconnect.walletInfo, [\n            'name',\n            'app_name',\n            'image',\n            'about_url',\n            'platforms'\n        ]);\n    } catch {\n        return false;\n    }\n}\n", "import { WalletInfoDTO } from 'src/models/wallet/wallet-info';\n\nexport const FALLBACK_WALLETS_LIST: WalletInfoDTO[] = [\n    {\n        app_name: 'telegram-wallet',\n        name: 'Wallet',\n        image: 'https://wallet.tg/images/logo-288.png',\n        about_url: 'https://wallet.tg/',\n        universal_url: 'https://t.me/wallet?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://walletbot.me/tonconnect-bridge/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'tonkeeper',\n        name: 'Tonkeeper',\n        image: 'https://tonkeeper.com/assets/tonconnect-icon.png',\n        tondns: 'tonkeeper.ton',\n        about_url: 'https://tonkeeper.com',\n        universal_url: 'https://app.tonkeeper.com/ton-connect',\n        deepLink: 'tonkeeper-tc://',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://bridge.tonapi.io/bridge'\n            },\n            {\n                type: 'js',\n                key: 'tonkeeper'\n            }\n        ],\n        platforms: ['ios', 'android', 'chrome', 'firefox', 'macos']\n    },\n    {\n        app_name: 'mytonwallet',\n        name: 'MyTonWallet',\n        image: 'https://static.mytonwallet.io/icon-256.png',\n        about_url: 'https://mytonwallet.io',\n        universal_url: 'https://connect.mytonwallet.org',\n        bridge: [\n            {\n                type: 'js',\n                key: 'mytonwallet'\n            },\n            {\n                type: 'sse',\n                url: 'https://tonconnectbridge.mytonwallet.org/bridge/'\n            }\n        ],\n        platforms: ['chrome', 'windows', 'macos', 'linux', 'ios', 'android', 'firefox']\n    },\n    {\n        app_name: 'tonhub',\n        name: 'Tonhub',\n        image: 'https://tonhub.com/tonconnect_logo.png',\n        about_url: 'https://tonhub.com',\n        universal_url: 'https://tonhub.com/ton-connect',\n        bridge: [\n            {\n                type: 'js',\n                key: 'tonhub'\n            },\n            {\n                type: 'sse',\n                url: 'https://connect.tonhubapi.com/tonconnect'\n            }\n        ],\n        platforms: ['ios', 'android']\n    },\n    {\n        app_name: 'bitgetTonWallet',\n        name: 'Bitget Wallet',\n        image: 'https://raw.githubusercontent.com/bitgetwallet/download/refs/heads/main/logo/png/bitget_wallet_logo_288_mini.png',\n        about_url: 'https://web3.bitget.com',\n        deepLink: 'bitkeep://',\n        bridge: [\n            {\n                type: 'js',\n                key: 'bitgetTonWallet'\n            },\n            {\n                type: 'sse',\n                url: 'https://ton-connect-bridge.bgwapi.io/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'chrome'],\n        universal_url: 'https://bkcode.vip/ton-connect'\n    },\n    {\n        app_name: 'okxMiniWallet',\n        name: 'OKX Mini Wallet',\n        image: 'https://static.okx.com/cdn/assets/imgs/2411/8BE1A4A434D8F58A.png',\n        about_url: 'https://www.okx.com/web3',\n        universal_url: 'https://t.me/OKX_WALLET_BOT?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://www.okx.com/tonbridge/discover/rpc/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'binanceWeb3TonWallet',\n        name: 'Binance Web3 Wallet',\n        image: 'https://public.bnbstatic.com/static/binance-w3w/ton-provider/binancew3w.png',\n        about_url: 'https://www.binance.com/en/web3wallet',\n        deepLink: 'bnc://app.binance.com/cedefi/ton-connect',\n        bridge: [\n            {\n                type: 'js',\n                key: 'binancew3w'\n            },\n            {\n                type: 'sse',\n                url: 'https://wallet.binance.com/tonbridge/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux'],\n        universal_url: 'https://app.binance.com/cedefi/ton-connect'\n    },\n    {\n        app_name: 'fintopio-tg',\n        name: 'Fintopio',\n        image: 'https://fintopio.com/tonconnect-icon.png',\n        about_url: 'https://fintopio.com',\n        universal_url: 'https://t.me/fintopio?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://wallet-bridge.fintopio.com/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'okxTonWallet',\n        name: 'OKX Wallet',\n        image: 'https://static.okx.com/cdn/assets/imgs/247/58E63FEA47A2B7D7.png',\n        about_url: 'https://www.okx.com/web3',\n        universal_url:\n            'https://www.okx.com/download?appendQuery=true&deeplink=okx://web3/wallet/tonconnect',\n        bridge: [\n            {\n                type: 'js',\n                key: 'okxTonWallet'\n            },\n            {\n                type: 'sse',\n                url: 'https://www.okx.com/tonbridge/discover/rpc/bridge'\n            }\n        ],\n        platforms: ['chrome', 'safari', 'firefox', 'ios', 'android']\n    },\n    {\n        app_name: 'hot',\n        name: 'HOT',\n        image: 'https://raw.githubusercontent.com/hot-dao/media/main/logo.png',\n        about_url: 'https://hot-labs.org/',\n        universal_url: 'https://t.me/herewalletbot?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://sse-bridge.hot-labs.org'\n            },\n            {\n                type: 'js',\n                key: 'hotWallet'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'bybitTonWallet',\n        name: 'Bybit Wallet',\n        image: 'https://raw.githubusercontent.com/bybit-web3/bybit-web3.github.io/main/docs/images/bybit-logo.png',\n        about_url: 'https://www.bybit.com/web3',\n        universal_url: 'https://app.bybit.com/ton-connect',\n        deepLink: 'bybitapp://',\n        bridge: [\n            {\n                type: 'js',\n                key: 'bybitTonWallet'\n            },\n            {\n                type: 'sse',\n                url: 'https://api-node.bybit.com/spot/api/web3/bridge/ton/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'chrome']\n    },\n    {\n        app_name: 'dewallet',\n        name: 'DeWallet',\n        image: 'https://raw.githubusercontent.com/delab-team/manifests-images/main/WalletAvatar.png',\n        about_url: 'https://delabwallet.com',\n        universal_url: 'https://t.me/dewallet?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://bridge.dewallet.pro/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'safepalwallet',\n        name: 'SafePal',\n        image: 'https://s.pvcliping.com/web/public_image/SafePal_x288.png',\n        tondns: '',\n        about_url: 'https://www.safepal.com',\n        universal_url: 'https://link.safepal.io/ton-connect',\n        deepLink: 'safepal-tc://',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://ton-bridge.safepal.com/tonbridge/v1/bridge'\n            },\n            {\n                type: 'js',\n                key: 'safepalwallet'\n            }\n        ],\n        platforms: ['ios', 'android', 'chrome', 'firefox']\n    },\n    {\n        app_name: 'GateWallet',\n        name: 'GateWallet',\n        image: 'https://img.gatedataimg.com/prd-ordinal-imgs/036f07bb8730716e/gateio-0925.png',\n        about_url: 'https://www.gate.io/',\n        bridge: [\n            {\n                type: 'js',\n                key: 'gatetonwallet'\n            },\n            {\n                type: 'sse',\n                url: 'https://dapp.gateio.services/tonbridge_api/bridge/v1'\n            }\n        ],\n        platforms: ['ios', 'android'],\n        universal_url: 'https://gateio.go.link/gateio/web3?adj_t=1ff8khdw_1fu4ccc7'\n    },\n    {\n        app_name: 'openmask',\n        name: 'OpenMask',\n        image: 'https://raw.githubusercontent.com/OpenProduct/openmask-extension/main/public/openmask-logo-288.png',\n        about_url: 'https://www.openmask.app/',\n        bridge: [\n            {\n                type: 'js',\n                key: 'openmask'\n            }\n        ],\n        platforms: ['chrome']\n    },\n    {\n        app_name: 'BitgetWeb3',\n        name: 'BitgetWeb3',\n        image: 'https://img.bitgetimg.com/image/third/1731638059795.png',\n        about_url: '​https://www.bitget.com',\n        universal_url: 'https://t.me/BitgetOfficialBot?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://ton-connect-bridge.bgwapi.io/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'windows', 'macos', 'linux']\n    },\n    {\n        app_name: 'tobi',\n        name: 'Tobi',\n        image: 'https://app.tobiwallet.app/icons/logo-288.png',\n        about_url: 'https://tobi.fun',\n        universal_url: 'https://t.me/TobiCopilotBot?attach=wallet',\n        bridge: [\n            {\n                type: 'sse',\n                url: 'https://ton-bridge.tobiwallet.app/bridge'\n            }\n        ],\n        platforms: ['ios', 'android', 'macos', 'windows', 'linux']\n    },\n    {\n        app_name: 'xtonwallet',\n        name: 'XTONWallet',\n        image: 'https://xtonwallet.com/assets/img/icon-256-back.png',\n        about_url: 'https://xtonwallet.com',\n        bridge: [\n            {\n                type: 'js',\n                key: 'xtonwallet'\n            }\n        ],\n        platforms: ['chrome', 'firefox']\n    },\n    {\n        app_name: 'tonwallet',\n        name: 'TON Wallet',\n        image: 'https://wallet.ton.org/assets/ui/qr-logo.png',\n        about_url:\n            'https://chrome.google.com/webstore/detail/ton-wallet/nphplpgoakhhjchkkhmiggakijnkhfnd',\n        bridge: [\n            {\n                type: 'js',\n                key: 'tonwallet'\n            }\n        ],\n        platforms: ['chrome']\n    }\n];\n", "export const PROTOCOL_VERSION = 2;\n", "import { SessionCrypto } from '@tonconnect/protocol';\nimport { TonConnectError } from 'src/errors';\nimport { BridgeSessionRaw } from 'src/provider/bridge/models/bridge-session-raw';\nimport {\n    BridgeConnection,\n    BridgeConnectionHttp,\n    BridgeConnectionHttpRaw,\n    BridgeConnectionInjected,\n    BridgeConnectionRaw,\n    BridgePendingConnectionHttp,\n    BridgePendingConnectionHttpRaw,\n    isPendingConnectionHttp\n} from 'src/provider/bridge/models/bridge-connection';\nimport { IStorage } from 'src/storage/models/storage.interface';\n\nexport class BridgeConnectionStorage {\n    private readonly storeKey = 'ton-connect-storage_bridge-connection';\n\n    constructor(private readonly storage: IStorage) {}\n\n    public async storeConnection(connection: BridgeConnection): Promise<void> {\n        if (connection.type === 'injected') {\n            return this.storage.setItem(this.storeKey, JSON.stringify(connection));\n        }\n\n        if (!isPendingConnectionHttp(connection)) {\n            const rawSession: BridgeSessionRaw = {\n                sessionKeyPair: connection.session.sessionCrypto.stringifyKeypair(),\n                walletPublicKey: connection.session.walletPublicKey,\n                bridgeUrl: connection.session.bridgeUrl\n            };\n\n            const rawConnection: BridgeConnectionHttpRaw = {\n                type: 'http',\n                connectEvent: connection.connectEvent,\n                session: rawSession,\n                lastWalletEventId: connection.lastWalletEventId,\n                nextRpcRequestId: connection.nextRpcRequestId\n            };\n            return this.storage.setItem(this.storeKey, JSON.stringify(rawConnection));\n        }\n\n        const rawConnection: BridgePendingConnectionHttpRaw = {\n            type: 'http',\n            connectionSource: connection.connectionSource,\n            sessionCrypto: connection.sessionCrypto.stringifyKeypair()\n        };\n\n        return this.storage.setItem(this.storeKey, JSON.stringify(rawConnection));\n    }\n\n    public async removeConnection(): Promise<void> {\n        return this.storage.removeItem(this.storeKey);\n    }\n\n    public async getConnection(): Promise<BridgeConnection | null> {\n        const stored = await this.storage.getItem(this.storeKey);\n        if (!stored) {\n            return null;\n        }\n\n        const connection: BridgeConnectionRaw = JSON.parse(stored);\n\n        if (connection.type === 'injected') {\n            return connection;\n        }\n\n        if ('connectEvent' in connection) {\n            const sessionCrypto = new SessionCrypto(connection.session.sessionKeyPair);\n            return {\n                type: 'http',\n                connectEvent: connection.connectEvent,\n                lastWalletEventId: connection.lastWalletEventId,\n                nextRpcRequestId: connection.nextRpcRequestId,\n                session: {\n                    sessionCrypto,\n                    bridgeUrl: connection.session.bridgeUrl,\n                    walletPublicKey: connection.session.walletPublicKey\n                }\n            };\n        }\n\n        return {\n            type: 'http',\n            sessionCrypto: new SessionCrypto(connection.sessionCrypto),\n            connectionSource: connection.connectionSource\n        };\n    }\n\n    public async getHttpConnection(): Promise<BridgeConnectionHttp | BridgePendingConnectionHttp> {\n        const connection = await this.getConnection();\n        if (!connection) {\n            throw new TonConnectError(\n                'Trying to read HTTP connection source while nothing is stored'\n            );\n        }\n\n        if (connection.type === 'injected') {\n            throw new TonConnectError(\n                'Trying to read HTTP connection source while injected connection is stored'\n            );\n        }\n\n        return connection;\n    }\n\n    public async getHttpPendingConnection(): Promise<BridgePendingConnectionHttp> {\n        const connection = await this.getConnection();\n        if (!connection) {\n            throw new TonConnectError(\n                'Trying to read HTTP connection source while nothing is stored'\n            );\n        }\n\n        if (connection.type === 'injected') {\n            throw new TonConnectError(\n                'Trying to read HTTP connection source while injected connection is stored'\n            );\n        }\n\n        if (!isPendingConnectionHttp(connection)) {\n            throw new TonConnectError(\n                'Trying to read HTTP-pending connection while http connection is stored'\n            );\n        }\n\n        return connection;\n    }\n\n    public async getInjectedConnection(): Promise<BridgeConnectionInjected> {\n        const connection = await this.getConnection();\n\n        if (!connection) {\n            throw new TonConnectError(\n                'Trying to read Injected bridge connection source while nothing is stored'\n            );\n        }\n\n        if (connection?.type === 'http') {\n            throw new TonConnectError(\n                'Trying to read Injected bridge connection source while HTTP connection is stored'\n            );\n        }\n\n        return connection;\n    }\n\n    public async storedConnectionType(): Promise<BridgeConnection['type'] | null> {\n        const stored = await this.storage.getItem(this.storeKey);\n        if (!stored) {\n            return null;\n        }\n        const connection: BridgeConnection = JSON.parse(stored);\n        return connection.type;\n    }\n\n    public async storeLastWalletEventId(id: number): Promise<void> {\n        const connection = await this.getConnection();\n        if (connection && connection.type === 'http' && !isPendingConnectionHttp(connection)) {\n            connection.lastWalletEventId = id;\n            return this.storeConnection(connection);\n        }\n    }\n\n    public async getLastWalletEventId(): Promise<number | undefined> {\n        const connection = await this.getConnection();\n        if (connection && 'lastWalletEventId' in connection) {\n            return connection.lastWalletEventId;\n        }\n\n        return undefined;\n    }\n\n    public async increaseNextRpcRequestId(): Promise<void> {\n        const connection = await this.getConnection();\n        if (connection && 'nextRpcRequestId' in connection) {\n            const lastId = connection.nextRpcRequestId || 0;\n            connection.nextRpcRequestId = lastId + 1;\n            return this.storeConnection(connection);\n        }\n    }\n\n    public async getNextRpcRequestId(): Promise<number> {\n        const connection = await this.getConnection();\n        if (connection && 'nextRpcRequestId' in connection) {\n            return connection.nextRpcRequestId || 0;\n        }\n\n        return 0;\n    }\n}\n", "import { IStorage } from 'src/storage/models/storage.interface';\nimport { tryGetLocalStorage } from 'src/utils/web-api';\n\n/**\n * Default storage to save protocol data, uses `localStorage` if it is available. In Safari's private mode, it uses `InMemoryStorage`. In Node.js, it throws an error.\n */\nexport class DefaultStorage implements IStorage {\n    private readonly localStorage: Storage;\n\n    constructor() {\n        this.localStorage = tryGetLocalStorage();\n    }\n\n    public async getItem(key: string): Promise<string | null> {\n        return this.localStorage.getItem(key);\n    }\n\n    public async removeItem(key: string): Promise<void> {\n        this.localStorage.removeItem(key);\n    }\n\n    public async setItem(key: string, value: string): Promise<void> {\n        this.localStorage.setItem(key, value);\n    }\n}\n", "import { IStorage } from 'src/storage/models/storage.interface';\n\nexport class HttpBridgeGatewayStorage {\n    private readonly storeKey: string;\n\n    constructor(private readonly storage: IStorage, bridgeUrl: string) {\n        this.storeKey = 'ton-connect-storage_http-bridge-gateway::' + bridgeUrl;\n    }\n\n    public async storeLastEventId(lastEventId: string): Promise<void> {\n        return this.storage.setItem(this.storeKey, lastEventId);\n    }\n\n    public async removeLastEventId(): Promise<void> {\n        return this.storage.removeItem(this.storeKey);\n    }\n\n    public async getLastEventId(): Promise<string | null> {\n        const stored = await this.storage.getItem(this.storeKey);\n        if (!stored) {\n            return null;\n        }\n\n        return stored;\n    }\n}\n", "/**\n * In memory storage like localStorage, but without persistence.\n * Uses as a fallback for localStorage in Safari's private mode.\n */\nexport class InMemoryStorage implements Storage {\n    private static instance: InMemoryStorage;\n\n    private storage: Record<string, string> = {};\n\n    public static getInstance(): InMemoryStorage {\n        if (!InMemoryStorage.instance) {\n            InMemoryStorage.instance = new InMemoryStorage();\n        }\n\n        return InMemoryStorage.instance;\n    }\n\n    private constructor() {}\n\n    public get length(): number {\n        return Object.keys(this.storage).length;\n    }\n\n    public clear(): void {\n        this.storage = {};\n    }\n\n    public getItem(key: string): string | null {\n        return this.storage[key] ?? null;\n    }\n\n    public key(index: number): string | null {\n        const keys = Object.keys(this.storage);\n        if (index < 0 || index >= keys.length) {\n            return null;\n        }\n\n        return keys[index] ?? null;\n    }\n\n    public removeItem(key: string): void {\n        delete this.storage[key];\n    }\n\n    public setItem(key: string, value: string): void {\n        this.storage[key] = value;\n    }\n}\n", "import {\n    ConnectEventError,\n    ConnectEventSuccess,\n    ConnectItem,\n    ConnectRequest,\n    SendTransactionRpcResponseSuccess,\n    TonAddressItemReply,\n    TonProofItemReply,\n    WalletEvent\n} from '@tonconnect/protocol';\nimport { DappMetadataError } from 'src/errors/dapp/dapp-metadata.error';\nimport { ManifestContentErrorError } from 'src/errors/protocol/events/connect/manifest-content-error.error';\nimport { ManifestNotFoundError } from 'src/errors/protocol/events/connect/manifest-not-found.error';\nimport { TonConnectError } from 'src/errors/ton-connect.error';\nimport { WalletAlreadyConnectedError } from 'src/errors/wallet/wallet-already-connected.error';\nimport { WalletNotConnectedError } from 'src/errors/wallet/wallet-not-connected.error';\nimport {\n    Account,\n    Wallet,\n    WalletConnectionSource,\n    WalletConnectionSourceHTTP,\n    WalletInfo\n} from 'src/models';\nimport { SendTransactionRequest, SendTransactionResponse } from 'src/models/methods';\nimport { ConnectAdditionalRequest } from 'src/models/methods/connect/connect-additional-request';\nimport { TonConnectOptions } from 'src/models/ton-connect-options';\nimport {\n    isWalletConnectionSourceJS,\n    WalletConnectionSourceJS\n} from 'src/models/wallet/wallet-connection-source';\nimport { connectErrorsParser } from 'src/parsers/connect-errors-parser';\nimport { sendTransactionParser } from 'src/parsers/send-transaction-parser';\nimport { BridgeProvider } from 'src/provider/bridge/bridge-provider';\nimport { InjectedProvider } from 'src/provider/injected/injected-provider';\nimport { Provider } from 'src/provider/provider';\nimport { BridgeConnectionStorage } from 'src/storage/bridge-connection-storage';\nimport { DefaultStorage } from 'src/storage/default-storage';\nimport { ITonConnect } from 'src/ton-connect.interface';\nimport { getDocument, getWebPageManifest } from 'src/utils/web-api';\nimport { WalletsListManager } from 'src/wallets-list-manager';\nimport { WithoutIdDistributive } from 'src/utils/types';\nimport { checkSendTransactionSupport } from 'src/utils/feature-support';\nimport { callForSuccess } from 'src/utils/call-for-success';\nimport { logDebug, logError } from 'src/utils/log';\nimport { createAbortController } from 'src/utils/create-abort-controller';\nimport { TonConnectTracker } from 'src/tracker/ton-connect-tracker';\nimport { tonConnectSdkVersion } from 'src/constants/version';\n\nexport class TonConnect implements ITonConnect {\n    private static readonly walletsList = new WalletsListManager();\n\n    /**\n     * Check if specified wallet is injected and available to use with the app.\n     * @param walletJSKey target wallet's js bridge key.\n     */\n    public static isWalletInjected = (walletJSKey: string): boolean =>\n        InjectedProvider.isWalletInjected(walletJSKey);\n\n    /**\n     * Check if the app is opened inside specified wallet's browser.\n     * @param walletJSKey target wallet's js bridge key.\n     */\n    public static isInsideWalletBrowser = (walletJSKey: string): boolean =>\n        InjectedProvider.isInsideWalletBrowser(walletJSKey);\n\n    /**\n     * Returns available wallets list.\n     */\n    public static getWallets(): Promise<WalletInfo[]> {\n        return this.walletsList.getWallets();\n    }\n\n    /**\n     * Emits user action event to the EventDispatcher. By default, it uses `window.dispatchEvent` for browser environment.\n     * @private\n     */\n    private readonly tracker: TonConnectTracker;\n\n    private readonly walletsList = new WalletsListManager();\n\n    private readonly dappSettings: Pick<Required<TonConnectOptions>, 'manifestUrl' | 'storage'>;\n\n    private readonly bridgeConnectionStorage: BridgeConnectionStorage;\n\n    private _wallet: Wallet | null = null;\n\n    private provider: Provider | null = null;\n\n    private statusChangeSubscriptions: ((walletInfo: Wallet | null) => void)[] = [];\n\n    private statusChangeErrorSubscriptions: ((err: TonConnectError) => void)[] = [];\n\n    private abortController?: AbortController;\n\n    /**\n     * Shows if the wallet is connected right now.\n     */\n    public get connected(): boolean {\n        return this._wallet !== null;\n    }\n\n    /**\n     * Current connected account or null if no account is connected.\n     */\n    public get account(): Account | null {\n        return this._wallet?.account || null;\n    }\n\n    /**\n     * Current connected wallet or null if no account is connected.\n     */\n    public get wallet(): Wallet | null {\n        return this._wallet;\n    }\n\n    private set wallet(value: Wallet | null) {\n        this._wallet = value;\n        this.statusChangeSubscriptions.forEach(callback => callback(this._wallet));\n    }\n\n    constructor(options?: TonConnectOptions) {\n        this.dappSettings = {\n            manifestUrl: options?.manifestUrl || getWebPageManifest(),\n            storage: options?.storage || new DefaultStorage()\n        };\n\n        this.walletsList = new WalletsListManager({\n            walletsListSource: options?.walletsListSource,\n            cacheTTLMs: options?.walletsListCacheTTLMs\n        });\n\n        this.tracker = new TonConnectTracker({\n            eventDispatcher: options?.eventDispatcher,\n            tonConnectSdkVersion: tonConnectSdkVersion\n        });\n\n        if (!this.dappSettings.manifestUrl) {\n            throw new DappMetadataError(\n                'Dapp tonconnect-manifest.json must be specified if window.location.origin is undefined. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest'\n            );\n        }\n\n        this.bridgeConnectionStorage = new BridgeConnectionStorage(this.dappSettings.storage);\n\n        if (!options?.disableAutoPauseConnection) {\n            this.addWindowFocusAndBlurSubscriptions();\n        }\n    }\n\n    /**\n     * Returns available wallets list.\n     */\n    public getWallets(): Promise<WalletInfo[]> {\n        return this.walletsList.getWallets();\n    }\n\n    /**\n     * Allows to subscribe to connection status changes and handle connection errors.\n     * @param callback will be called after connections status changes with actual wallet or null.\n     * @param errorsHandler (optional) will be called with some instance of TonConnectError when connect error is received.\n     * @returns unsubscribe callback.\n     */\n    public onStatusChange(\n        callback: (wallet: Wallet | null) => void,\n        errorsHandler?: (err: TonConnectError) => void\n    ): () => void {\n        this.statusChangeSubscriptions.push(callback);\n        if (errorsHandler) {\n            this.statusChangeErrorSubscriptions.push(errorsHandler);\n        }\n\n        return () => {\n            this.statusChangeSubscriptions = this.statusChangeSubscriptions.filter(\n                item => item !== callback\n            );\n            if (errorsHandler) {\n                this.statusChangeErrorSubscriptions = this.statusChangeErrorSubscriptions.filter(\n                    item => item !== errorsHandler\n                );\n            }\n        };\n    }\n\n    /**\n     * Generates universal link for an external wallet and subscribes to the wallet's bridge, or sends connect request to the injected wallet.\n     * @param wallet wallet's bridge url and universal link for an external wallet or jsBridge key for the injected wallet.\n     * @param request (optional) additional request to pass to the wallet while connect (currently only ton_proof is available).\n     * @param options (optional) openingDeadlineMS for the connection opening deadline and signal for the connection abort.\n     * @returns universal link if external wallet was passed or void for the injected wallet.\n     */\n\n    public connect<\n        T extends WalletConnectionSource | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[]\n    >(\n        wallet: T,\n        options?: {\n            request?: ConnectAdditionalRequest;\n            openingDeadlineMS?: number;\n            signal?: AbortSignal;\n        }\n    ): T extends WalletConnectionSourceJS ? void : string;\n    /** @deprecated use connect(wallet, options) instead */\n    public connect<\n        T extends WalletConnectionSource | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[]\n    >(\n        wallet: T,\n        request?: ConnectAdditionalRequest,\n        options?: {\n            openingDeadlineMS?: number;\n            signal?: AbortSignal;\n        }\n    ): T extends WalletConnectionSourceJS ? void : string;\n    public connect(\n        wallet: WalletConnectionSource | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[],\n        requestOrOptions?:\n            | ConnectAdditionalRequest\n            | {\n                  request?: ConnectAdditionalRequest;\n                  openingDeadlineMS?: number;\n                  signal?: AbortSignal;\n              }\n    ): void | string {\n        // TODO: remove deprecated method\n        const options: {\n            request?: ConnectAdditionalRequest;\n            openingDeadlineMS?: number;\n            signal?: AbortSignal;\n        } = {};\n        if (typeof requestOrOptions === 'object' && 'tonProof' in requestOrOptions) {\n            options.request = requestOrOptions;\n        }\n        if (\n            typeof requestOrOptions === 'object' &&\n            ('openingDeadlineMS' in requestOrOptions ||\n                'signal' in requestOrOptions ||\n                'request' in requestOrOptions)\n        ) {\n            options.request = requestOrOptions?.request;\n            options.openingDeadlineMS = requestOrOptions?.openingDeadlineMS;\n            options.signal = requestOrOptions?.signal;\n        }\n\n        if (this.connected) {\n            throw new WalletAlreadyConnectedError();\n        }\n\n        const abortController = createAbortController(options?.signal);\n        this.abortController?.abort();\n        this.abortController = abortController;\n\n        if (abortController.signal.aborted) {\n            throw new TonConnectError('Connection was aborted');\n        }\n\n        this.provider?.closeConnection();\n        this.provider = this.createProvider(wallet);\n\n        abortController.signal.addEventListener('abort', () => {\n            this.provider?.closeConnection();\n            this.provider = null;\n        });\n\n        this.tracker.trackConnectionStarted();\n\n        return this.provider.connect(this.createConnectRequest(options?.request), {\n            openingDeadlineMS: options?.openingDeadlineMS,\n            signal: abortController.signal\n        });\n    }\n\n    /**\n     * Try to restore existing session and reconnect to the corresponding wallet. Call it immediately when your app is loaded.\n     */\n    public async restoreConnection(options?: {\n        openingDeadlineMS?: number;\n        signal?: AbortSignal;\n    }): Promise<void> {\n        this.tracker.trackConnectionRestoringStarted();\n\n        const abortController = createAbortController(options?.signal);\n        this.abortController?.abort();\n        this.abortController = abortController;\n\n        if (abortController.signal.aborted) {\n            this.tracker.trackConnectionRestoringError('Connection restoring was aborted');\n            return;\n        }\n\n        // TODO: potentially race condition here\n        const [bridgeConnectionType, embeddedWallet] = await Promise.all([\n            this.bridgeConnectionStorage.storedConnectionType(),\n            this.walletsList.getEmbeddedWallet()\n        ]);\n\n        if (abortController.signal.aborted) {\n            this.tracker.trackConnectionRestoringError('Connection restoring was aborted');\n            return;\n        }\n\n        let provider: Provider | null = null;\n        try {\n            switch (bridgeConnectionType) {\n                case 'http':\n                    provider = await BridgeProvider.fromStorage(this.dappSettings.storage);\n                    break;\n                case 'injected':\n                    provider = await InjectedProvider.fromStorage(this.dappSettings.storage);\n                    break;\n                default:\n                    if (embeddedWallet) {\n                        provider = this.createProvider(embeddedWallet);\n                    } else {\n                        return;\n                    }\n            }\n        } catch {\n            this.tracker.trackConnectionRestoringError('Provider is not restored');\n            await this.bridgeConnectionStorage.removeConnection();\n            provider?.closeConnection();\n            provider = null;\n            return;\n        }\n\n        if (abortController.signal.aborted) {\n            provider?.closeConnection();\n            this.tracker.trackConnectionRestoringError('Connection restoring was aborted');\n            return;\n        }\n\n        if (!provider) {\n            logError('Provider is not restored');\n            this.tracker.trackConnectionRestoringError('Provider is not restored');\n            return;\n        }\n\n        this.provider?.closeConnection();\n        this.provider = provider;\n        provider.listen(this.walletEventsListener.bind(this));\n\n        const onAbortRestore = (): void => {\n            this.tracker.trackConnectionRestoringError('Connection restoring was aborted');\n            provider?.closeConnection();\n            provider = null;\n        };\n        abortController.signal.addEventListener('abort', onAbortRestore);\n\n        const restoreConnectionTask = callForSuccess(\n            async _options => {\n                await provider?.restoreConnection({\n                    openingDeadlineMS: options?.openingDeadlineMS,\n                    signal: _options.signal\n                });\n\n                abortController.signal.removeEventListener('abort', onAbortRestore);\n                if (this.connected) {\n                    this.tracker.trackConnectionRestoringCompleted(this.wallet);\n                } else {\n                    this.tracker.trackConnectionRestoringError('Connection restoring failed');\n                }\n            },\n            {\n                attempts: Number.MAX_SAFE_INTEGER,\n                delayMs: 2_000,\n                signal: options?.signal\n            }\n        );\n        const restoreConnectionTimeout = new Promise<void>(\n            resolve => setTimeout(() => resolve(), 12_000) // connection deadline\n        );\n        return Promise.race([restoreConnectionTask, restoreConnectionTimeout]);\n    }\n\n    /**\n     * Asks connected wallet to sign and send the transaction.\n     * @param transaction transaction to send.\n     * @param options (optional) onRequestSent will be called after the request was sent to the wallet and signal for the transaction abort.\n     * @returns signed transaction boc that allows you to find the transaction in the blockchain.\n     * If user rejects transaction, method will throw the corresponding error.\n     */\n    public sendTransaction(\n        transaction: SendTransactionRequest,\n        options?: {\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n        }\n    ): Promise<SendTransactionResponse>;\n    /** @deprecated use sendTransaction(transaction, options) instead */\n    public sendTransaction(\n        transaction: SendTransactionRequest,\n        onRequestSent?: () => void\n    ): Promise<SendTransactionResponse>;\n    public async sendTransaction(\n        transaction: SendTransactionRequest,\n        optionsOrOnRequestSent?:\n            | {\n                  onRequestSent?: () => void;\n                  signal?: AbortSignal;\n              }\n            | (() => void)\n    ): Promise<SendTransactionResponse> {\n        // TODO: remove deprecated method\n        const options: {\n            onRequestSent?: () => void;\n            signal?: AbortSignal;\n        } = {};\n        if (typeof optionsOrOnRequestSent === 'function') {\n            options.onRequestSent = optionsOrOnRequestSent;\n        } else {\n            options.onRequestSent = optionsOrOnRequestSent?.onRequestSent;\n            options.signal = optionsOrOnRequestSent?.signal;\n        }\n\n        const abortController = createAbortController(options?.signal);\n        if (abortController.signal.aborted) {\n            throw new TonConnectError('Transaction sending was aborted');\n        }\n\n        this.checkConnection();\n        checkSendTransactionSupport(this.wallet!.device.features, {\n            requiredMessagesNumber: transaction.messages.length\n        });\n\n        this.tracker.trackTransactionSentForSignature(this.wallet, transaction);\n\n        const { validUntil, ...tx } = transaction;\n        const from = transaction.from || this.account!.address;\n        const network = transaction.network || this.account!.chain;\n\n        const response = await this.provider!.sendRequest(\n            sendTransactionParser.convertToRpcRequest({\n                ...tx,\n                valid_until: validUntil,\n                from,\n                network\n            }),\n            { onRequestSent: options.onRequestSent, signal: abortController.signal }\n        );\n\n        if (sendTransactionParser.isError(response)) {\n            this.tracker.trackTransactionSigningFailed(\n                this.wallet,\n                transaction,\n                response.error.message,\n                response.error.code\n            );\n            return sendTransactionParser.parseAndThrowError(response);\n        }\n\n        const result = sendTransactionParser.convertFromRpcResponse(\n            response as SendTransactionRpcResponseSuccess\n        );\n        this.tracker.trackTransactionSigned(this.wallet, transaction, result);\n        return result;\n    }\n\n    /**\n     * Disconnect form thw connected wallet and drop current session.\n     */\n    public async disconnect(options?: { signal?: AbortSignal }): Promise<void> {\n        if (!this.connected) {\n            throw new WalletNotConnectedError();\n        }\n        const abortController = createAbortController(options?.signal);\n        const prevAbortController = this.abortController;\n        this.abortController = abortController;\n\n        if (abortController.signal.aborted) {\n            throw new TonConnectError('Disconnect was aborted');\n        }\n\n        this.onWalletDisconnected('dapp');\n        await this.provider?.disconnect({\n            signal: abortController.signal\n        });\n        prevAbortController?.abort();\n    }\n\n    /**\n     * Pause bridge HTTP connection. Might be helpful, if you want to pause connections while browser tab is unfocused,\n     * or if you use SDK with NodeJS and want to save server resources.\n     */\n    public pauseConnection(): void {\n        if (this.provider?.type !== 'http') {\n            return;\n        }\n\n        this.provider.pause();\n    }\n\n    /**\n     * Unpause bridge HTTP connection if it is paused.\n     */\n    public unPauseConnection(): Promise<void> {\n        if (this.provider?.type !== 'http') {\n            return Promise.resolve();\n        }\n\n        return this.provider.unPause();\n    }\n\n    private addWindowFocusAndBlurSubscriptions(): void {\n        const document = getDocument();\n        if (!document) {\n            return;\n        }\n\n        try {\n            document.addEventListener('visibilitychange', () => {\n                if (document.hidden) {\n                    this.pauseConnection();\n                } else {\n                    this.unPauseConnection().catch();\n                }\n            });\n        } catch (e) {\n            logError('Cannot subscribe to the document.visibilitychange: ', e);\n        }\n    }\n\n    private createProvider(\n        wallet: WalletConnectionSource | Pick<WalletConnectionSourceHTTP, 'bridgeUrl'>[]\n    ): Provider {\n        let provider: Provider;\n\n        if (!Array.isArray(wallet) && isWalletConnectionSourceJS(wallet)) {\n            provider = new InjectedProvider(this.dappSettings.storage, wallet.jsBridgeKey);\n        } else {\n            provider = new BridgeProvider(this.dappSettings.storage, wallet);\n        }\n\n        provider.listen(this.walletEventsListener.bind(this));\n        return provider;\n    }\n\n    private walletEventsListener(e: WithoutIdDistributive<WalletEvent>): void {\n        switch (e.event) {\n            case 'connect':\n                this.onWalletConnected(e.payload);\n                break;\n            case 'connect_error':\n                this.onWalletConnectError(e.payload);\n                break;\n            case 'disconnect':\n                this.onWalletDisconnected('wallet');\n        }\n    }\n\n    private onWalletConnected(connectEvent: ConnectEventSuccess['payload']): void {\n        const tonAccountItem: TonAddressItemReply | undefined = connectEvent.items.find(\n            item => item.name === 'ton_addr'\n        ) as TonAddressItemReply | undefined;\n\n        const tonProofItem: TonProofItemReply | undefined = connectEvent.items.find(\n            item => item.name === 'ton_proof'\n        ) as TonProofItemReply | undefined;\n\n        if (!tonAccountItem) {\n            throw new TonConnectError('ton_addr connection item was not found');\n        }\n\n        const wallet: Wallet = {\n            device: connectEvent.device,\n            provider: this.provider!.type,\n            account: {\n                address: tonAccountItem.address,\n                chain: tonAccountItem.network,\n                walletStateInit: tonAccountItem.walletStateInit,\n                publicKey: tonAccountItem.publicKey\n            }\n        };\n\n        if (tonProofItem) {\n            wallet.connectItems = {\n                tonProof: tonProofItem\n            };\n        }\n\n        this.wallet = wallet;\n\n        this.tracker.trackConnectionCompleted(wallet);\n    }\n\n    private onWalletConnectError(connectEventError: ConnectEventError['payload']): void {\n        const error = connectErrorsParser.parseError(connectEventError);\n        this.statusChangeErrorSubscriptions.forEach(errorsHandler => errorsHandler(error));\n\n        logDebug(error);\n        this.tracker.trackConnectionError(connectEventError.message, connectEventError.code);\n\n        if (error instanceof ManifestNotFoundError || error instanceof ManifestContentErrorError) {\n            logError(error);\n            throw error;\n        }\n    }\n\n    private onWalletDisconnected(scope: 'wallet' | 'dapp'): void {\n        this.tracker.trackDisconnection(this.wallet, scope);\n        this.wallet = null;\n    }\n\n    private checkConnection(): void | never {\n        if (!this.connected) {\n            throw new WalletNotConnectedError();\n        }\n    }\n\n    private createConnectRequest(request?: ConnectAdditionalRequest): ConnectRequest {\n        const items: ConnectItem[] = [\n            {\n                name: 'ton_addr'\n            }\n        ];\n\n        if (request?.tonProof) {\n            items.push({\n                name: 'ton_proof',\n                payload: request.tonProof\n            });\n        }\n\n        return {\n            manifestUrl: this.dappSettings.manifestUrl,\n            items\n        };\n    }\n}\n", "import { getWindow } from 'src/utils/web-api';\nimport { AddTonConnectPrefix, EventDispatcher, RemoveTonConnectPrefix } from './event-dispatcher';\n\n/**\n * A concrete implementation of EventDispatcher that dispatches events to the browser window.\n */\nexport class BrowserEventDispatcher<T extends { type: string }> implements EventDispatcher<T> {\n    /**\n     * The window object, possibly undefined in a server environment.\n     * @private\n     */\n    private readonly window: Window | undefined = getWindow();\n\n    /**\n     * Dispatches an event with the given name and details to the browser window.\n     * @param eventName - The name of the event to dispatch.\n     * @param eventDetails - The details of the event to dispatch.\n     * @returns A promise that resolves when the event has been dispatched.\n     */\n    public async dispatchEvent<P extends AddTonConnectPrefix<T['type']>>(\n        eventName: P,\n        eventDetails: T & { type: RemoveTonConnectPrefix<P> }\n    ): Promise<void> {\n        const event = new CustomEvent<T>(eventName, { detail: eventDetails });\n        this.window?.dispatchEvent(event);\n    }\n\n    /**\n     * Adds an event listener to the browser window.\n     * @param eventName - The name of the event to listen for.\n     * @param listener - The listener to add.\n     * @param options - The options for the listener.\n     * @returns A function that removes the listener.\n     */\n    public async addEventListener<P extends AddTonConnectPrefix<T['type']>>(\n        eventName: P,\n        listener: (event: CustomEvent<T & { type: RemoveTonConnectPrefix<P> }>) => void,\n        options?: AddEventListenerOptions\n    ): Promise<() => void> {\n        this.window?.addEventListener(\n            eventName,\n            listener as EventListener | EventListenerObject,\n            options\n        );\n        return () =>\n            this.window?.removeEventListener(\n                eventName,\n                listener as EventListener | EventListenerObject\n            );\n    }\n}\n", "import {\n    createConnectionCompletedEvent,\n    createConnectionErrorEvent,\n    createConnectionRestoringCompletedEvent,\n    createConnectionRestoringErrorEvent,\n    createConnectionRestoringStartedEvent,\n    createConnectionStartedEvent,\n    createDisconnectionEvent,\n    createRequestVersionEvent,\n    createResponseVersionEvent,\n    createTransactionSentForSignatureEvent,\n    createTransactionSignedEvent,\n    createTransactionSigningFailedEvent,\n    createVersionInfo,\n    ResponseVersionEvent,\n    SdkActionEvent,\n    Version,\n    WithoutVersion\n} from './types';\nimport { EventDispatcher } from 'src/tracker/event-dispatcher';\nimport { BrowserEventDispatcher } from 'src/tracker/browser-event-dispatcher';\n\n/**\n * Options for the TonConnect tracker.\n */\nexport type TonConnectTrackerOptions = {\n    /**\n     * Event dispatcher to track user actions.\n     * @default new BrowserEventDispatcher()\n     */\n    eventDispatcher?: EventDispatcher<SdkActionEvent> | null;\n    /**\n     * TonConnect SDK version.\n     */\n    tonConnectSdkVersion: string;\n};\n\n/**\n * Tracker for TonConnect user actions, such as transaction signing, connection, etc.\n *\n * List of events:\n *  * `connection-started`: when a user starts connecting a wallet.\n *  * `connection-completed`: when a user successfully connected a wallet.\n *  * `connection-error`: when a user cancels a connection or there is an error during the connection process.\n *  * `connection-restoring-started`: when the dApp starts restoring a connection.\n *  * `connection-restoring-completed`: when the dApp successfully restores a connection.\n *  * `connection-restoring-error`: when the dApp fails to restore a connection.\n *  * `disconnection`: when a user starts disconnecting a wallet.\n *  * `transaction-sent-for-signature`: when a user sends a transaction for signature.\n *  * `transaction-signed`: when a user successfully signs a transaction.\n *  * `transaction-signing-failed`: when a user cancels transaction signing or there is an error during the signing process.\n *\n * If you want to track user actions, you can subscribe to the window events with prefix `ton-connect-`:\n *\n * @example\n * window.addEventListener('ton-connect-transaction-sent-for-signature', (event) => {\n *    console.log('Transaction init', event.detail);\n * });\n *\n * @internal\n */\nexport class TonConnectTracker {\n    /**\n     * Event prefix for user actions.\n     * @private\n     */\n    private readonly eventPrefix = 'ton-connect-';\n\n    /**\n     * TonConnect SDK version.\n     */\n    private readonly tonConnectSdkVersion: string;\n\n    /**\n     * TonConnect UI version.\n     */\n    private tonConnectUiVersion: string | null = null;\n\n    /**\n     * Version of the library.\n     */\n    get version(): Version {\n        return createVersionInfo({\n            ton_connect_sdk_lib: this.tonConnectSdkVersion,\n            ton_connect_ui_lib: this.tonConnectUiVersion\n        });\n    }\n\n    /**\n     * Event dispatcher to track user actions. By default, it uses `window.dispatchEvent` for browser environment.\n     * @private\n     */\n    private readonly eventDispatcher: EventDispatcher<SdkActionEvent>;\n\n    constructor(options: TonConnectTrackerOptions) {\n        this.eventDispatcher = options?.eventDispatcher ?? new BrowserEventDispatcher();\n        this.tonConnectSdkVersion = options.tonConnectSdkVersion;\n\n        this.init().catch();\n    }\n\n    /**\n     * Called once when the tracker is created and request version other libraries.\n     */\n    private async init(): Promise<void> {\n        try {\n            await this.setRequestVersionHandler();\n            this.tonConnectUiVersion = await this.requestTonConnectUiVersion();\n        } catch (e) {}\n    }\n\n    /**\n     * Set request version handler.\n     * @private\n     */\n    private async setRequestVersionHandler(): Promise<void> {\n        await this.eventDispatcher.addEventListener('ton-connect-request-version', async () => {\n            await this.eventDispatcher.dispatchEvent(\n                'ton-connect-response-version',\n                createResponseVersionEvent(this.tonConnectSdkVersion)\n            );\n        });\n    }\n\n    /**\n     * Request TonConnect UI version.\n     * @private\n     */\n    private async requestTonConnectUiVersion(): Promise<string> {\n        return new Promise(async (resolve, reject) => {\n            try {\n                await this.eventDispatcher.addEventListener(\n                    'ton-connect-ui-response-version',\n                    (event: CustomEvent<ResponseVersionEvent>) => {\n                        resolve(event.detail.version);\n                    },\n                    { once: true }\n                );\n\n                await this.eventDispatcher.dispatchEvent(\n                    'ton-connect-ui-request-version',\n                    createRequestVersionEvent()\n                );\n            } catch (e) {\n                reject(e);\n            }\n        });\n    }\n\n    /**\n     * Emit user action event to the window.\n     * @param eventDetails\n     * @private\n     */\n    private dispatchUserActionEvent(eventDetails: SdkActionEvent): void {\n        try {\n            this.eventDispatcher\n                .dispatchEvent(`${this.eventPrefix}${eventDetails.type}`, eventDetails)\n                .catch();\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection init event.\n     * @param args\n     */\n    public trackConnectionStarted(\n        ...args: WithoutVersion<Parameters<typeof createConnectionStartedEvent>>\n    ): void {\n        try {\n            const event = createConnectionStartedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection success event.\n     * @param args\n     */\n    public trackConnectionCompleted(\n        ...args: WithoutVersion<Parameters<typeof createConnectionCompletedEvent>>\n    ): void {\n        try {\n            const event = createConnectionCompletedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection error event.\n     * @param args\n     */\n    public trackConnectionError(\n        ...args: WithoutVersion<Parameters<typeof createConnectionErrorEvent>>\n    ): void {\n        try {\n            const event = createConnectionErrorEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection restoring init event.\n     * @param args\n     */\n    public trackConnectionRestoringStarted(\n        ...args: WithoutVersion<Parameters<typeof createConnectionRestoringStartedEvent>>\n    ): void {\n        try {\n            const event = createConnectionRestoringStartedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection restoring success event.\n     * @param args\n     */\n    public trackConnectionRestoringCompleted(\n        ...args: WithoutVersion<Parameters<typeof createConnectionRestoringCompletedEvent>>\n    ): void {\n        try {\n            const event = createConnectionRestoringCompletedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track connection restoring error event.\n     * @param args\n     */\n    public trackConnectionRestoringError(\n        ...args: WithoutVersion<Parameters<typeof createConnectionRestoringErrorEvent>>\n    ): void {\n        try {\n            const event = createConnectionRestoringErrorEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track disconnect event.\n     * @param args\n     */\n    public trackDisconnection(\n        ...args: WithoutVersion<Parameters<typeof createDisconnectionEvent>>\n    ): void {\n        try {\n            const event = createDisconnectionEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track transaction init event.\n     * @param args\n     */\n    public trackTransactionSentForSignature(\n        ...args: WithoutVersion<Parameters<typeof createTransactionSentForSignatureEvent>>\n    ): void {\n        try {\n            const event = createTransactionSentForSignatureEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track transaction signed event.\n     * @param args\n     */\n    public trackTransactionSigned(\n        ...args: WithoutVersion<Parameters<typeof createTransactionSignedEvent>>\n    ): void {\n        try {\n            const event = createTransactionSignedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n\n    /**\n     * Track transaction error event.\n     * @param args\n     */\n    public trackTransactionSigningFailed(\n        ...args: WithoutVersion<Parameters<typeof createTransactionSigningFailedEvent>>\n    ): void {\n        try {\n            const event = createTransactionSigningFailedEvent(this.version, ...args);\n            this.dispatchUserActionEvent(event);\n        } catch (e) {}\n    }\n}\n", "import { CONNECT_EVENT_ERROR_CODES, ConnectItem, SEND_TRANSACTION_ERROR_CODES } from '@tonconnect/protocol';\nimport { SendTransactionRequest, SendTransactionResponse, Wallet } from 'src/models';\n\n/**\n * Request TON Connect UI version.\n */\nexport type RequestVersionEvent = {\n    /**\n     * Event type.\n     */\n    type: 'request-version';\n};\n\n/**\n * Create a request version event.\n */\nexport function createRequestVersionEvent(): RequestVersionEvent {\n    return {\n        type: 'request-version'\n    };\n}\n\n/**\n * Response TON Connect UI version.\n */\nexport type ResponseVersionEvent = {\n    /**\n     * Event type.\n     */\n    type: 'response-version';\n    /**\n     * TON Connect UI version.\n     */\n    version: string;\n};\n\n/**\n * Create a response version event.\n * @param version\n */\nexport function createResponseVersionEvent(version: string): ResponseVersionEvent {\n    return {\n        type: 'response-version',\n        version: version\n    };\n}\n\n/**\n * Version events.\n */\nexport type VersionEvent = RequestVersionEvent | ResponseVersionEvent;\n\n/**\n * Version of the TON Connect SDK and TON Connect UI.\n */\nexport type Version = {\n    /**\n     * TON Connect SDK version.\n     */\n    ton_connect_sdk_lib: string | null;\n    /**\n     * TON Connect UI version.\n     */\n    ton_connect_ui_lib: string | null;\n};\n\n/**\n * Create a version info.\n * @param version\n */\nexport function createVersionInfo(version: Version): Version {\n    return {\n        ton_connect_sdk_lib: version.ton_connect_sdk_lib,\n        ton_connect_ui_lib: version.ton_connect_ui_lib\n    };\n}\n\n/**\n * Requested authentication type: 'ton_addr' or 'ton_proof'.\n */\nexport type AuthType = ConnectItem['name'];\n\n/**\n * Information about a connected wallet.\n */\nexport type ConnectionInfo = {\n    /**\n     * Connected wallet address.\n     */\n    wallet_address: string | null;\n    /**\n     * Wallet type: 'tonkeeper', 'tonhub', etc.\n     */\n    wallet_type: string | null;\n    /**\n     * Wallet version.\n     */\n    wallet_version: string | null;\n    /**\n     * Requested authentication types.\n     */\n    auth_type: AuthType;\n    /**\n     * Custom data for the connection.\n     */\n    custom_data: {\n        /**\n         * Connected chain ID.\n         */\n        chain_id: string | null;\n        /**\n         * Wallet provider.\n         */\n        provider: 'http' | 'injected' | null;\n    } & Version;\n};\n\nfunction createConnectionInfo(version: Version, wallet: Wallet | null): ConnectionInfo {\n    const isTonProof = wallet?.connectItems?.tonProof && 'proof' in wallet.connectItems.tonProof;\n    const authType: AuthType = isTonProof ? 'ton_proof' : 'ton_addr';\n\n    return {\n        wallet_address: wallet?.account?.address ?? null,\n        wallet_type: wallet?.device.appName ?? null,\n        wallet_version: wallet?.device.appVersion ?? null,\n        auth_type: authType,\n        custom_data: {\n            chain_id: wallet?.account?.chain ?? null,\n            provider: wallet?.provider ?? null,\n            ...createVersionInfo(version)\n        }\n    };\n}\n\n/**\n * Initial connection event when a user initiates a connection.\n */\nexport type ConnectionStartedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-started';\n    /**\n     * Custom data for the connection.\n     */\n    custom_data: Version;\n};\n\n/**\n * Create a connection init event.\n */\nexport function createConnectionStartedEvent(version: Version): ConnectionStartedEvent {\n    return {\n        type: 'connection-started',\n        custom_data: createVersionInfo(version)\n    };\n}\n\n/**\n * Successful connection event when a user successfully connected a wallet.\n */\nexport type ConnectionCompletedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-completed';\n    /**\n     * Connection success flag.\n     */\n    is_success: true;\n} & ConnectionInfo;\n\n/**\n * Create a connection completed event.\n * @param version\n * @param wallet\n */\nexport function createConnectionCompletedEvent(\n    version: Version,\n    wallet: Wallet | null\n): ConnectionCompletedEvent {\n    return {\n        type: 'connection-completed',\n        is_success: true,\n        ...createConnectionInfo(version, wallet)\n    };\n}\n\n/**\n * Connection error event when a user cancels a connection or there is an error during the connection process.\n */\nexport type ConnectionErrorEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-error';\n    /**\n     * Connection success flag.\n     */\n    is_success: false;\n    /**\n     * Reason for the error.\n     */\n    error_message: string;\n    /**\n     * Error code.\n     */\n    error_code: CONNECT_EVENT_ERROR_CODES | null;\n    /**\n     * Custom data for the connection.\n     */\n    custom_data: Version;\n};\n\n/**\n * Create a connection error event.\n * @param version\n * @param error_message\n * @param errorCode\n */\nexport function createConnectionErrorEvent(\n    version: Version,\n    error_message: string,\n    errorCode: CONNECT_EVENT_ERROR_CODES | void\n): ConnectionErrorEvent {\n    return {\n        type: 'connection-error',\n        is_success: false,\n        error_message: error_message,\n        error_code: errorCode ?? null,\n        custom_data: createVersionInfo(version)\n    };\n}\n\n/**\n * Connection events.\n */\nexport type ConnectionEvent =\n    | ConnectionStartedEvent\n    | ConnectionCompletedEvent\n    | ConnectionErrorEvent;\n\n/**\n * Connection restoring started event when initiates a connection restoring process.\n */\nexport type ConnectionRestoringStartedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-restoring-started';\n    /**\n     * Custom data for the connection.\n     */\n    custom_data: Version;\n};\n\n/**\n * Create a connection restoring started event.\n */\nexport function createConnectionRestoringStartedEvent(\n    version: Version\n): ConnectionRestoringStartedEvent {\n    return {\n        type: 'connection-restoring-started',\n        custom_data: createVersionInfo(version)\n    };\n}\n\n/**\n * Connection restoring completed event when successfully restored a connection.\n */\nexport type ConnectionRestoringCompletedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-restoring-completed';\n    /**\n     * Connection success flag.\n     */\n    is_success: true;\n} & ConnectionInfo;\n\n/**\n * Create a connection restoring completed event.\n * @param version\n * @param wallet\n */\nexport function createConnectionRestoringCompletedEvent(\n    version: Version,\n    wallet: Wallet | null\n): ConnectionRestoringCompletedEvent {\n    return {\n        type: 'connection-restoring-completed',\n        is_success: true,\n        ...createConnectionInfo(version, wallet)\n    };\n}\n\n/**\n * Connection restoring error event when there is an error during the connection restoring process.\n */\nexport type ConnectionRestoringErrorEvent = {\n    /**\n     * Event type.\n     */\n    type: 'connection-restoring-error';\n    /**\n     * Connection success flag.\n     */\n    is_success: false;\n    /**\n     * Reason for the error.\n     */\n    error_message: string;\n    /**\n     * Custom data for the connection.\n     */\n    custom_data: Version;\n};\n\n/**\n * Create a connection restoring error event.\n * @param version\n * @param errorMessage\n */\nexport function createConnectionRestoringErrorEvent(\n    version: Version,\n    errorMessage: string\n): ConnectionRestoringErrorEvent {\n    return {\n        type: 'connection-restoring-error',\n        is_success: false,\n        error_message: errorMessage,\n        custom_data: createVersionInfo(version)\n    };\n}\n\n/**\n * Connection restoring events.\n */\nexport type ConnectionRestoringEvent =\n    | ConnectionRestoringStartedEvent\n    | ConnectionRestoringCompletedEvent\n    | ConnectionRestoringErrorEvent;\n\n/**\n * Transaction message.\n */\nexport type TransactionMessage = {\n    /**\n     * Recipient address.\n     */\n    address: string | null;\n    /**\n     * Transfer amount.\n     */\n    amount: string | null;\n};\n\n/**\n * Transaction information.\n */\nexport type TransactionInfo = {\n    /**\n     * Transaction validity time in unix timestamp.\n     */\n    valid_until: string | null;\n    /**\n     * Sender address.\n     */\n    from: string | null;\n    /**\n     * Transaction messages.\n     */\n    messages: TransactionMessage[];\n};\n\nfunction createTransactionInfo(\n    wallet: Wallet | null,\n    transaction: SendTransactionRequest\n): TransactionInfo {\n    return {\n        valid_until: String(transaction.validUntil) ?? null,\n        from: transaction.from ?? wallet?.account?.address ?? null,\n        messages: transaction.messages.map(message => ({\n            address: message.address ?? null,\n            amount: message.amount ?? null\n        }))\n    };\n}\n\n/**\n * Initial transaction event when a user initiates a transaction.\n */\nexport type TransactionSentForSignatureEvent = {\n    /**\n     * Event type.\n     */\n    type: 'transaction-sent-for-signature';\n} & ConnectionInfo &\n    TransactionInfo;\n\n/**\n * Create a transaction init event.\n * @param version\n * @param wallet\n * @param transaction\n */\nexport function createTransactionSentForSignatureEvent(\n    version: Version,\n    wallet: Wallet | null,\n    transaction: SendTransactionRequest\n): TransactionSentForSignatureEvent {\n    return {\n        type: 'transaction-sent-for-signature',\n        ...createConnectionInfo(version, wallet),\n        ...createTransactionInfo(wallet, transaction)\n    };\n}\n\n/**\n * Transaction signed event when a user successfully signed a transaction.\n */\nexport type TransactionSignedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'transaction-signed';\n    /**\n     * Connection success flag.\n     */\n    is_success: true;\n    /**\n     * Signed transaction.\n     */\n    signed_transaction: string;\n} & ConnectionInfo &\n    TransactionInfo;\n\n/**\n * Create a transaction signed event.\n * @param version\n * @param wallet\n * @param transaction\n * @param signedTransaction\n */\nexport function createTransactionSignedEvent(\n    version: Version,\n    wallet: Wallet | null,\n    transaction: SendTransactionRequest,\n    signedTransaction: SendTransactionResponse\n): TransactionSignedEvent {\n    return {\n        type: 'transaction-signed',\n        is_success: true,\n        signed_transaction: signedTransaction.boc,\n        ...createConnectionInfo(version, wallet),\n        ...createTransactionInfo(wallet, transaction)\n    };\n}\n\n/**\n * Transaction error event when a user cancels a transaction or there is an error during the transaction process.\n */\nexport type TransactionSigningFailedEvent = {\n    /**\n     * Event type.\n     */\n    type: 'transaction-signing-failed';\n    /**\n     * Connection success flag.\n     */\n    is_success: false;\n    /**\n     * Reason for the error.\n     */\n    error_message: string;\n    /**\n     * Error code.\n     */\n    error_code: SEND_TRANSACTION_ERROR_CODES | null;\n} & ConnectionInfo &\n    TransactionInfo;\n\n/**\n * Create a transaction error event.\n * @param version\n * @param wallet\n * @param transaction\n * @param errorMessage\n * @param errorCode\n */\nexport function createTransactionSigningFailedEvent(\n    version: Version,\n    wallet: Wallet | null,\n    transaction: SendTransactionRequest,\n    errorMessage: string,\n    errorCode: SEND_TRANSACTION_ERROR_CODES | void\n): TransactionSigningFailedEvent {\n    return {\n        type: 'transaction-signing-failed',\n        is_success: false,\n        error_message: errorMessage,\n        error_code: errorCode ?? null,\n        ...createConnectionInfo(version, wallet),\n        ...createTransactionInfo(wallet, transaction)\n    };\n}\n\n/**\n * Transaction events.\n */\nexport type TransactionSigningEvent =\n    | TransactionSentForSignatureEvent\n    | TransactionSignedEvent\n    | TransactionSigningFailedEvent;\n\n/**\n * Disconnect event when a user initiates a disconnection.\n */\nexport type DisconnectionEvent = {\n    /**\n     * Event type.\n     */\n    type: 'disconnection';\n    /**\n     * Disconnect scope: 'dapp' or 'wallet'.\n     */\n    scope: 'dapp' | 'wallet';\n} & ConnectionInfo;\n\n/**\n * Create a disconnect event.\n * @param version\n * @param wallet\n * @param scope\n * @returns\n */\nexport function createDisconnectionEvent(\n    version: Version,\n    wallet: Wallet | null,\n    scope: 'dapp' | 'wallet'\n): DisconnectionEvent {\n    return {\n        type: 'disconnection',\n        scope: scope,\n        ...createConnectionInfo(version, wallet)\n    };\n}\n\n/**\n * User action events.\n */\nexport type SdkActionEvent =\n    | VersionEvent\n    | ConnectionEvent\n    | ConnectionRestoringEvent\n    | DisconnectionEvent\n    | TransactionSigningEvent;\n\n/**\n * Parameters without version field.\n */\nexport type WithoutVersion<T> = T extends [Version, ...infer Rest] ? [...Rest] : never;\n", "import { WrongAddressError, ParseHexError } from 'src/errors';\nimport { Base64 } from '@tonconnect/protocol';\n\nconst noBounceableTag = 0x51;\nconst testOnlyTag = 0x80;\n\n/**\n * Converts raw TON address to no-bounceable user-friendly format. [See details]{@link https://ton.org/docs/learn/overviews/addresses#user-friendly-address}\n * @param hexAddress raw TON address formatted as \"0:<hex string without 0x>\".\n * @param [testOnly=false] convert address to test-only form. [See details]{@link https://ton.org/docs/learn/overviews/addresses#user-friendly-address}\n */\nexport function toUserFriendlyAddress(hexAddress: string, testOnly = false): string {\n    const { wc, hex } = parseHexAddress(hexAddress);\n\n    let tag = noBounceableTag;\n    if (testOnly) {\n        tag |= testOnlyTag;\n    }\n\n    const addr = new Int8Array(34);\n    addr[0] = tag;\n    addr[1] = wc;\n    addr.set(hex, 2);\n\n    const addressWithChecksum = new Uint8Array(36);\n    addressWithChecksum.set(addr);\n    addressWithChecksum.set(crc16(addr), 34);\n\n    let addressBase64 = Base64.encode(addressWithChecksum);\n\n    return addressBase64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nfunction parseHexAddress(hexAddress: string): { wc: 0 | -1; hex: Uint8Array } {\n    if (!hexAddress.includes(':')) {\n        throw new WrongAddressError(`Wrong address ${hexAddress}. Address must include \":\".`);\n    }\n\n    const parts = hexAddress.split(':');\n    if (parts.length !== 2) {\n        throw new WrongAddressError(\n            `Wrong address ${hexAddress}. Address must include \":\" only once.`\n        );\n    }\n\n    const wc = parseInt(parts[0]!);\n    if (wc !== 0 && wc !== -1) {\n        throw new WrongAddressError(\n            `Wrong address ${hexAddress}. WC must be eq 0 or -1, but ${wc} received.`\n        );\n    }\n\n    const hex = parts[1];\n    if (hex?.length !== 64) {\n        throw new WrongAddressError(\n            `Wrong address ${hexAddress}. Hex part must be 64bytes length, but ${hex?.length} received.`\n        );\n    }\n\n    return {\n        wc,\n        hex: hexToBytes(hex)\n    };\n}\n\nfunction crc16(data: ArrayLike<number>): Uint8Array {\n    const poly = 0x1021;\n    let reg = 0;\n    const message = new Uint8Array(data.length + 2);\n    message.set(data);\n    for (let byte of message) {\n        let mask = 0x80;\n        while (mask > 0) {\n            reg <<= 1;\n            if (byte & mask) {\n                reg += 1;\n            }\n            mask >>= 1;\n            if (reg > 0xffff) {\n                reg &= 0xffff;\n                reg ^= poly;\n            }\n        }\n    }\n    return new Uint8Array([Math.floor(reg / 256), reg % 256]);\n}\n\nconst toByteMap: Record<string, number> = {};\nfor (let ord = 0; ord <= 0xff; ord++) {\n    let s = ord.toString(16);\n    if (s.length < 2) {\n        s = '0' + s;\n    }\n    toByteMap[s] = ord;\n}\n\nfunction hexToBytes(hex: string): Uint8Array {\n    hex = hex.toLowerCase();\n    const length2 = hex.length;\n    if (length2 % 2 !== 0) {\n        throw new ParseHexError('Hex string must have length a multiple of 2: ' + hex);\n    }\n    const length = length2 / 2;\n    const result = new Uint8Array(length);\n    for (let i = 0; i < length; i++) {\n        const doubled = i * 2;\n        const hexSubstring = hex.substring(doubled, doubled + 2);\n        if (!toByteMap.hasOwnProperty(hexSubstring)) {\n            throw new ParseHexError('Invalid hex character: ' + hexSubstring);\n        }\n        result[i] = toByteMap[hexSubstring]!;\n    }\n    return result;\n}\n", "import { delay } from 'src/utils/delay';\nimport { TonConnectError } from 'src/errors';\nimport { createAbortController } from 'src/utils/create-abort-controller';\n\n/**\n * Configuration options for the callForSuccess function.\n */\nexport type CallForSuccessOptions = {\n    /**\n     * An 'AbortSignal' object that can be used to abort the function.\n     */\n    signal?: AbortSignal;\n\n    /**\n     * The number of attempts to make before giving up. Default is 20.\n     */\n    attempts?: number;\n\n    /**\n     * The delay in milliseconds between each attempt. Default is 100ms.\n     */\n    delayMs?: number;\n};\n\n/**\n * Function to call ton api until we get response.\n * Because ton network is pretty unstable we need to make sure response is final.\n * @param {T} fn - function to call\n * @param {CallForSuccessOptions} [options] - optional configuration options\n */\nexport async function callForSuccess<T extends (options: { signal?: AbortSignal }) => Promise<any>>(\n    fn: T,\n    options?: CallForSuccessOptions\n): Promise<Awaited<ReturnType<T>>> {\n    const attempts = options?.attempts ?? 10;\n    const delayMs = options?.delayMs ?? 200;\n    const abortController = createAbortController(options?.signal);\n\n    if (typeof fn !== 'function') {\n        throw new TonConnectError(`Expected a function, got ${typeof fn}`);\n    }\n\n    let i = 0;\n    let lastError: unknown;\n\n    while (i < attempts) {\n        if (abortController.signal.aborted) {\n            throw new TonConnectError(`Aborted after attempts ${i}`);\n        }\n\n        try {\n            return await fn({ signal: abortController.signal });\n        } catch (err) {\n            lastError = err;\n            i++;\n\n            if (i < attempts) {\n                await delay(delayMs);\n            }\n        }\n    }\n\n    throw lastError;\n}\n", "/**\n * Creates an AbortController instance with an optional AbortSignal.\n *\n * @param {AbortSignal} [signal] - An optional AbortSignal to use for aborting the controller.\n * @returns {AbortController} - An instance of AbortController.\n */\nexport function createAbortController(signal?: AbortSignal): AbortController {\n    const abortController = new AbortController();\n    if (signal?.aborted) {\n        abortController.abort();\n    } else {\n        signal?.addEventListener('abort', () => abortController.abort(), { once: true });\n    }\n    return abortController;\n}\n", "import { TonConnectError } from 'src/errors';\n\n/**\n * Configuration options for the delay function.\n */\nexport type DelayFnOptions = {\n    /**\n     * An 'AbortSignal' object that can be used to abort the delay.\n     */\n    signal?: AbortSignal;\n};\n\n/**\n * Delays the execution of code for a specified number of milliseconds.\n * @param {number} timeout - The number of milliseconds to delay the execution.\n * @param {DelayOptions} [options] - Optional configuration options for the delay.\n * @return {Promise<void>} - A promise that resolves after the specified delay, or rejects if the delay is aborted.\n */\nexport async function delay(timeout: number, options?: DelayFnOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n        if (options?.signal?.aborted) {\n            reject(new TonConnectError('Delay aborted'));\n            return;\n        }\n\n        const timeoutId = setTimeout(() => resolve(), timeout);\n        options?.signal?.addEventListener('abort', () => {\n            clearTimeout(timeoutId);\n            reject(new TonConnectError('Delay aborted'));\n        });\n    });\n}\n", "import { Feature, SendTransactionFeature } from '@tonconnect/protocol';\nimport { logWarning } from 'src/utils/log';\nimport { WalletNotSupportFeatureError } from 'src/errors/wallet/wallet-not-support-feature.error';\n\nexport function checkSendTransactionSupport(\n    features: Feature[],\n    options: { requiredMessagesNumber: number }\n): never | void {\n    const supportsDeprecatedSendTransactionFeature = features.includes('SendTransaction');\n    const sendTransactionFeature = features.find(\n        feature => feature && typeof feature === 'object' && feature.name === 'SendTransaction'\n    ) as SendTransactionFeature;\n\n    if (!supportsDeprecatedSendTransactionFeature && !sendTransactionFeature) {\n        throw new WalletNotSupportFeatureError(\"Wallet doesn't support SendTransaction feature.\");\n    }\n\n    if (sendTransactionFeature && sendTransactionFeature.maxMessages !== undefined) {\n        if (sendTransactionFeature.maxMessages < options.requiredMessagesNumber) {\n            throw new WalletNotSupportFeatureError(\n                `Wall<PERSON> is not able to handle such SendTransaction request. Max support messages number is ${sendTransactionFeature.maxMessages}, but ${options.requiredMessagesNumber} is required.`\n            );\n        }\n        return;\n    }\n\n    logWarning(\n        \"Connected wallet didn't provide information about max allowed messages in the SendTransaction request. Request may be rejected by the wallet.\"\n    );\n}\n", "export function logDebug(...args: Parameters<Console['debug']>): void {\n    if (typeof 'console' !== undefined) {\n        try {\n            console.debug('[TON_CONNECT_SDK]', ...args);\n        } catch {}\n    }\n}\n\nexport function logError(...args: Parameters<Console['error']>): void {\n    if (typeof 'console' !== undefined) {\n        try {\n            console.error('[TON_CONNECT_SDK]', ...args);\n        } catch {}\n    }\n}\n\nexport function logWarning(...args: Parameters<Console['warn']>): void {\n    if (typeof 'console' !== undefined) {\n        try {\n            console.warn('[TON_CONNECT_SDK]', ...args);\n        } catch {}\n    }\n}\n\n\n", "import { TonConnectError } from 'src/errors';\nimport { delay } from 'src/utils/delay';\nimport { createAbortController } from 'src/utils/create-abort-controller';\n\n/**\n * The resource interface.\n */\nexport type Resource<T, Args extends any[]> = {\n    /**\n     * Create a new resource.\n     */\n    create: (abortSignal?: AbortSignal, ...args: Args) => Promise<T>;\n\n    /**\n     * Get the current resource.\n     */\n    current: () => T | null;\n\n    /**\n     * Dispose the current resource.\n     */\n    dispose: () => Promise<void>;\n\n    /**\n     * Recreate the current resource.\n     */\n    recreate: (delayMs: number) => Promise<T>;\n};\n\n/**\n * Create a resource.\n *\n * @template T - The type of the resource.\n * @template Args - The type of the arguments for creating the resource.\n *\n * @param {(...args: Args) => Promise<T>} createFn - A function that creates the resource.\n * @param {(resource: T) => Promise<void>} [disposeFn] - An optional function that disposes the resource.\n */\nexport function createResource<T extends EventSource, <PERSON><PERSON><PERSON> extends any[]>(\n    createFn: (signal?: AbortSignal, ...args: Args) => Promise<T>,\n    disposeFn: (resource: T) => Promise<void>\n): Resource<T, Args> {\n    let currentResource: T | null = null;\n    let currentArgs: Args | null = null;\n    let currentPromise: Promise<T> | null = null;\n    let currentSignal: AbortSignal | null = null;\n    let abortController: AbortController | null = null;\n\n    // create a new resource\n    const create = async (signal?: AbortSignal, ...args: Args): Promise<T> => {\n        currentSignal = signal ?? null;\n\n        abortController?.abort();\n        abortController = createAbortController(signal);\n\n        if (abortController.signal.aborted) {\n            throw new TonConnectError('Resource creation was aborted');\n        }\n\n        currentArgs = args ?? null;\n\n        const promise = createFn(abortController.signal, ...args);\n        currentPromise = promise;\n        const resource = await promise;\n\n        if (currentPromise !== promise && resource !== currentResource) {\n            await disposeFn(resource);\n            throw new TonConnectError('Resource creation was aborted by a new resource creation');\n        }\n\n        currentResource = resource;\n        return currentResource;\n    };\n\n    // get the current resource\n    const current = (): T | null => {\n        return currentResource ?? null;\n    };\n\n    // dispose the current resource\n    const dispose = async (): Promise<void> => {\n        try {\n            const resource = currentResource;\n            currentResource = null;\n\n            const promise = currentPromise;\n            currentPromise = null;\n\n            try {\n                abortController?.abort();\n            } catch (e) {}\n\n            await Promise.allSettled([\n                resource ? disposeFn(resource) : Promise.resolve(),\n                promise ? disposeFn(await promise) : Promise.resolve()\n            ]);\n        } catch (e) {}\n    };\n\n    // recreate the current resource\n    const recreate = async (delayMs: number): Promise<T> => {\n        const resource = currentResource;\n        const promise = currentPromise;\n        const args = currentArgs;\n        const signal = currentSignal;\n\n        await delay(delayMs);\n\n        if (\n            resource === currentResource &&\n            promise === currentPromise &&\n            args === currentArgs &&\n            signal === currentSignal\n        ) {\n            return await create(currentSignal!, ...((args ?? []) as Args));\n        }\n\n        throw new TonConnectError('Resource recreation was aborted by a new resource creation');\n    };\n\n    return {\n        create,\n        current,\n        dispose,\n        recreate\n    };\n}\n", "import { TonConnectError } from 'src/errors';\nimport { createAbortController } from 'src/utils/create-abort-controller';\n\n/**\n * Represents the options for deferring a task.\n */\nexport type DeferOptions = {\n    /**\n     * The timeout in milliseconds after which the task should be aborted.\n     */\n    timeout?: number;\n\n    /**\n     * An optional AbortSignal to use for aborting the task.\n     */\n    signal?: AbortSignal;\n};\n\n/**\n * Represents a deferrable action that can be executed asynchronously.\n *\n * @template T The type of the value returned by the deferrable action.\n * @param {DeferOptions} [options] The options to configure the deferrable action.\n * @returns {Promise<T>} A promise that resolves with the result of the deferrable action.\n */\nexport type Deferrable<T> = (\n    resolve: (value: T) => void,\n    reject: (reason?: any) => void,\n    options: DeferOptions\n) => Promise<void>;\n\n/**\n * Executes a function and provides deferred behavior, allowing for a timeout and abort functionality.\n *\n * @param {Deferrable<T>} fn - The function to execute. It should return a promise that resolves with the desired result.\n * @param {DeferOptions} options - Optional configuration options for the defer behavior.\n * @returns {Promise<T>} - A promise that resolves with the result of the executed function, or rejects with an error if it times out or is aborted.\n */\nexport function timeout<T>(fn: Deferrable<T>, options?: DeferOptions): Promise<T> {\n    const timeout = options?.timeout;\n    const signal = options?.signal;\n\n    const abortController = createAbortController(signal);\n\n    return new Promise(async (resolve, reject) => {\n        if (abortController.signal.aborted) {\n            reject(new TonConnectError('Operation aborted'));\n            return;\n        }\n\n        let timeoutId: ReturnType<typeof setTimeout> | undefined;\n        if (typeof timeout !== 'undefined') {\n            timeoutId = setTimeout(() => {\n                abortController.abort();\n                reject(new TonConnectError(`Timeout after ${timeout}ms`));\n            }, timeout);\n        }\n\n        abortController.signal.addEventListener(\n            'abort',\n            () => {\n                clearTimeout(timeoutId);\n                reject(new TonConnectError('Operation aborted'));\n            },\n            { once: true }\n        );\n\n        const deferOptions = { timeout, abort: abortController.signal };\n        await fn(\n            (...args) => {\n                clearTimeout(timeoutId);\n                resolve(...args);\n            },\n            () => {\n                clearTimeout(timeoutId);\n                reject();\n            },\n            deferOptions\n        );\n    });\n}\n", "export type AsStruct<T> = { [P in keyof T as T[P] extends Function ? never : P]: T[P] };\n\nexport type WithoutId<T extends { id: unknown }> = Omit<T, 'id'>;\nexport type WithoutIdDistributive<T extends { id: unknown }> = DistributiveOmit<T, 'id'>;\n\nexport type DistributiveOmit<T, K extends keyof T> = T extends unknown ? Omit<T, K> : never;\n\nexport type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport function hasProperty<T extends string>(\n    value: unknown,\n    propertyKey: T\n): value is Record<T, unknown> {\n    return hasProperties(value, [propertyKey]);\n}\n\nexport function hasProperties<T extends string>(\n    value: unknown,\n    propertyKeys: T[]\n): value is Record<T, unknown> {\n    if (!value || typeof value !== 'object') {\n        return false;\n    }\n\n    return propertyKeys.every(propertyKey => propertyKey in value);\n}\n", "export function removeUrlLastSlash(url: string): string {\n    if (url.slice(-1) === '/') {\n        return url.slice(0, -1);\n    }\n\n    return url;\n}\n\nexport function addPathToUrl(url: string, path: string): string {\n    return removeUrlLastSlash(url) + '/' + path;\n}\n\nexport function isTelegramUrl(link: string | undefined): link is string {\n    if (!link) {\n        return false;\n    }\n\n    const url = new URL(link);\n    return url.protocol === 'tg:' || url.hostname === 't.me';\n}\n\nexport function encodeTelegramUrlParameters(parameters: string): string {\n    return parameters\n        .replaceAll('.', '%2E')\n        .replaceAll('-', '%2D')\n        .replaceAll('_', '%5F')\n        .replaceAll('&', '-')\n        .replaceAll('=', '__')\n        .replaceAll('%', '--');\n}\n", "import { InMemoryStorage } from 'src/storage/models/in-memory-storage';\nimport { TonConnectError } from 'src/errors';\n\nexport function getWindow(): Window | undefined {\n    if (typeof window === 'undefined') {\n        return undefined;\n    }\n\n    return window;\n}\n\n/**\n * The function try to get window keys, if it is not available it returns empty array.\n * As an example, for Safari's private mode it returns empty array, because the browser does not allow to get window keys.\n */\nexport function tryGetWindowKeys(): string[] {\n    const window = getWindow();\n    if (!window) {\n        return [];\n    }\n\n    try {\n        return Object.keys(window);\n    } catch {\n        return [];\n    }\n}\n\nexport function getDocument(): Document | undefined {\n    if (typeof document === 'undefined') {\n        return undefined;\n    }\n\n    return document;\n}\n\nexport function getWebPageManifest(): string {\n    const origin = getWindow()?.location.origin;\n    if (origin) {\n        return origin + '/tonconnect-manifest.json';\n    }\n\n    return '';\n}\n\n/**\n * Returns `localStorage` if it is available. In <PERSON>fari's private mode, it returns `InMemoryStorage`. In Node.js, it throws an error.\n */\nexport function tryGetLocalStorage(): Storage {\n    if (isLocalStorageAvailable()) {\n        return localStorage;\n    }\n\n    if (isNodeJs()) {\n        throw new TonConnectError(\n            '`localStorage` is unavailable, but it is required for TonConnect. For more details, see https://github.com/ton-connect/sdk/tree/main/packages/sdk#init-connector'\n        );\n    }\n\n    return InMemoryStorage.getInstance();\n}\n\n/**\n * Checks if `localStorage` is available.\n */\nfunction isLocalStorageAvailable(): boolean {\n    // We use a try/catch block because Safari's private mode throws an error when attempting to access localStorage.\n    try {\n        return typeof localStorage !== 'undefined';\n    } catch {\n        return false;\n    }\n}\n\n/**\n * Checks if the environment is Node.js.\n */\nfunction isNodeJs(): boolean {\n    return (\n        typeof process !== 'undefined' && process.versions != null && process.versions.node != null\n    );\n}\n", "import { FetchWalletsError } from 'src/errors/wallets-manager/fetch-wallets.error';\nimport {\n    WalletInfoRemote,\n    WalletInfoInjectable,\n    WalletInfo,\n    WalletInfoDTO,\n    isWalletInfoCurrentlyEmbedded,\n    WalletInfoCurrentlyEmbedded,\n    WalletInfoCurrentlyInjected,\n    WalletInfoBase\n} from 'src/models/wallet/wallet-info';\nimport { InjectedProvider } from 'src/provider/injected/injected-provider';\nimport { logError } from 'src/utils/log';\nimport { FALLBACK_WALLETS_LIST } from 'src/resources/fallback-wallets-list';\n\nexport class WalletsListManager {\n    private walletsListCache: Promise<WalletInfo[]> | null = null;\n\n    private walletsListCacheCreationTimestamp: number | null = null;\n\n    private readonly cacheTTLMs: number | undefined;\n\n    private readonly walletsListSource: string =\n        'https://raw.githubusercontent.com/ton-blockchain/wallets-list/main/wallets-v2.json';\n\n    constructor(options?: { walletsListSource?: string; cacheTTLMs?: number }) {\n        if (options?.walletsListSource) {\n            this.walletsListSource = options.walletsListSource;\n        }\n\n        if (options?.cacheTTLMs) {\n            this.cacheTTLMs = options.cacheTTLMs;\n        }\n    }\n\n    public async getWallets(): Promise<WalletInfo[]> {\n        if (\n            this.cacheTTLMs &&\n            this.walletsListCacheCreationTimestamp &&\n            Date.now() > this.walletsListCacheCreationTimestamp + this.cacheTTLMs\n        ) {\n            this.walletsListCache = null;\n        }\n\n        if (!this.walletsListCache) {\n            this.walletsListCache = this.fetchWalletsList();\n            this.walletsListCache\n                .then(() => {\n                    this.walletsListCacheCreationTimestamp = Date.now();\n                })\n                .catch(() => {\n                    this.walletsListCache = null;\n                    this.walletsListCacheCreationTimestamp = null;\n                });\n        }\n\n        return this.walletsListCache;\n    }\n\n    public async getEmbeddedWallet(): Promise<WalletInfoCurrentlyEmbedded | null> {\n        const walletsList = await this.getWallets();\n        const embeddedWallets = walletsList.filter(isWalletInfoCurrentlyEmbedded);\n\n        if (embeddedWallets.length !== 1) {\n            return null;\n        }\n\n        return embeddedWallets[0]!;\n    }\n\n    private async fetchWalletsList(): Promise<WalletInfo[]> {\n        let walletsList: WalletInfoDTO[] = [];\n\n        try {\n            const walletsResponse = await fetch(this.walletsListSource);\n            walletsList = await walletsResponse.json();\n\n            if (!Array.isArray(walletsList)) {\n                throw new FetchWalletsError(\n                    'Wrong wallets list format, wallets list must be an array.'\n                );\n            }\n\n            const wrongFormatWallets = walletsList.filter(\n                wallet => !this.isCorrectWalletConfigDTO(wallet)\n            );\n            if (wrongFormatWallets.length) {\n                logError(\n                    `Wallet(s) ${wrongFormatWallets\n                        .map(wallet => wallet.name)\n                        .join(\n                            ', '\n                        )} config format is wrong. They were removed from the wallets list.`\n                );\n\n                walletsList = walletsList.filter(wallet => this.isCorrectWalletConfigDTO(wallet));\n            }\n        } catch (e) {\n            logError(e);\n            walletsList = FALLBACK_WALLETS_LIST;\n        }\n\n        let currentlyInjectedWallets: WalletInfoCurrentlyInjected[] = [];\n        try {\n            currentlyInjectedWallets = InjectedProvider.getCurrentlyInjectedWallets();\n        } catch (e) {\n            logError(e);\n        }\n\n        return this.mergeWalletsLists(\n            this.walletConfigDTOListToWalletConfigList(walletsList),\n            currentlyInjectedWallets\n        );\n    }\n\n    private walletConfigDTOListToWalletConfigList(walletConfigDTO: WalletInfoDTO[]): WalletInfo[] {\n        return walletConfigDTO.map(walletConfigDTO => {\n            const walletConfigBase: WalletInfoBase = {\n                name: walletConfigDTO.name,\n                appName: walletConfigDTO.app_name,\n                imageUrl: walletConfigDTO.image,\n                aboutUrl: walletConfigDTO.about_url,\n                tondns: walletConfigDTO.tondns,\n                platforms: walletConfigDTO.platforms\n            };\n\n            const walletConfig: WalletInfo = walletConfigBase as WalletInfo;\n\n            walletConfigDTO.bridge.forEach(bridge => {\n                if (bridge.type === 'sse') {\n                    (walletConfig as WalletInfoRemote).bridgeUrl = bridge.url;\n                    (walletConfig as WalletInfoRemote).universalLink =\n                        walletConfigDTO.universal_url!;\n                    (walletConfig as WalletInfoRemote).deepLink = walletConfigDTO.deepLink;\n                }\n\n                if (bridge.type === 'js') {\n                    const jsBridgeKey = bridge.key;\n                    (walletConfig as WalletInfoInjectable).jsBridgeKey = jsBridgeKey;\n                    (walletConfig as WalletInfoInjectable).injected =\n                        InjectedProvider.isWalletInjected(jsBridgeKey);\n                    (walletConfig as WalletInfoInjectable).embedded =\n                        InjectedProvider.isInsideWalletBrowser(jsBridgeKey);\n                }\n            });\n\n            return walletConfig;\n        });\n    }\n\n    private mergeWalletsLists(list1: WalletInfo[], list2: WalletInfo[]): WalletInfo[] {\n        const names = new Set(list1.concat(list2).map(item => item.name));\n\n        return [...names.values()].map(name => {\n            const list1Item = list1.find(item => item.name === name);\n            const list2Item = list2.find(item => item.name === name);\n\n            return {\n                ...(list1Item && { ...list1Item }),\n                ...(list2Item && { ...list2Item })\n            } as WalletInfo;\n        });\n    }\n\n    // eslint-disable-next-line complexity\n    private isCorrectWalletConfigDTO(value: unknown): value is WalletInfoDTO {\n        if (!value || !(typeof value === 'object')) {\n            return false;\n        }\n\n        const containsName = 'name' in value;\n        const containsAppName = 'app_name' in value;\n        const containsImage = 'image' in value;\n        const containsAbout = 'about_url' in value;\n        const containsPlatforms = 'platforms' in value;\n\n        if (\n            !containsName ||\n            !containsImage ||\n            !containsAbout ||\n            !containsPlatforms ||\n            !containsAppName\n        ) {\n            return false;\n        }\n\n        if (\n            !(value as { platforms: unknown }).platforms ||\n            !Array.isArray((value as { platforms: unknown }).platforms) ||\n            !(value as { platforms: string[] }).platforms.length\n        ) {\n            return false;\n        }\n        if (\n            !('bridge' in value) ||\n            !Array.isArray((value as { bridge: unknown }).bridge) ||\n            !(value as { bridge: unknown[] }).bridge.length\n        ) {\n            return false;\n        }\n\n        const bridge = (value as { bridge: unknown[] }).bridge;\n\n        if (bridge.some(item => !item || typeof item !== 'object' || !('type' in item))) {\n            return false;\n        }\n\n        const sseBridge = bridge.find(item => (item as { type: string }).type === 'sse');\n\n        if (sseBridge) {\n            if (\n                !('url' in sseBridge) ||\n                !(sseBridge as { url: string }).url ||\n                !(value as { universal_url: string }).universal_url\n            ) {\n                return false;\n            }\n        }\n\n        const jsBridge = bridge.find(item => (item as { type: string }).type === 'js');\n\n        if (jsBridge) {\n            if (!('key' in jsBridge) || !(jsBridge as { key: string }).key) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n}\n", "// Written in 2014-2016 by <PERSON> and <PERSON>.\n// Public domain.\n(function(root, f) {\n  'use strict';\n  if (typeof module !== 'undefined' && module.exports) module.exports = f();\n  else if (root.nacl) root.nacl.util = f();\n  else {\n    root.nacl = {};\n    root.nacl.util = f();\n  }\n}(this, function() {\n  'use strict';\n\n  var util = {};\n\n  function validateBase64(s) {\n    if (!(/^(?:[A-Za-z0-9+\\/]{2}[A-Za-z0-9+\\/]{2})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+\\/]{3}=)?$/.test(s))) {\n      throw new TypeError('invalid encoding');\n    }\n  }\n\n  util.decodeUTF8 = function(s) {\n    if (typeof s !== 'string') throw new TypeError('expected string');\n    var i, d = unescape(encodeURIComponent(s)), b = new Uint8Array(d.length);\n    for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n    return b;\n  };\n\n  util.encodeUTF8 = function(arr) {\n    var i, s = [];\n    for (i = 0; i < arr.length; i++) s.push(String.fromCharCode(arr[i]));\n    return decodeURIComponent(escape(s.join('')));\n  };\n\n  if (typeof atob === 'undefined') {\n    // Node.js\n\n    if (typeof Buffer.from !== 'undefined') {\n       // Node v6 and later\n      util.encodeBase64 = function (arr) { // v6 and later\n          return Buffer.from(arr).toString('base64');\n      };\n\n      util.decodeBase64 = function (s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(Buffer.from(s, 'base64'), 0));\n      };\n\n    } else {\n      // Node earlier than v6\n      util.encodeBase64 = function (arr) { // v6 and later\n        return (new Buffer(arr)).toString('base64');\n      };\n\n      util.decodeBase64 = function(s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(new Buffer(s, 'base64'), 0));\n      };\n    }\n\n  } else {\n    // Browsers\n\n    util.encodeBase64 = function(arr) {\n      var i, s = [], len = arr.length;\n      for (i = 0; i < len; i++) s.push(String.fromCharCode(arr[i]));\n      return btoa(s.join(''));\n    };\n\n    util.decodeBase64 = function(s) {\n      validateBase64(s);\n      var i, d = atob(s), b = new Uint8Array(d.length);\n      for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n      return b;\n    };\n\n  }\n\n  return util;\n\n}));\n", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n", "'use strict';\n\nvar nacl = require('tweetnacl-util');\nvar nacl$1 = require('tweetnacl');\n\nexports.CONNECT_EVENT_ERROR_CODES = void 0;\n(function (CONNECT_EVENT_ERROR_CODES) {\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"UNKNOWN_ERROR\"] = 0] = \"UNKNOWN_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"BAD_REQUEST_ERROR\"] = 1] = \"BAD_REQUEST_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"MANIFEST_NOT_FOUND_ERROR\"] = 2] = \"MANIFEST_NOT_FOUND_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"MANIFEST_CONTENT_ERROR\"] = 3] = \"MANIFEST_CONTENT_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"UNKNOWN_APP_ERROR\"] = 100] = \"UNKNOWN_APP_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"USER_REJECTS_ERROR\"] = 300] = \"USER_REJECTS_ERROR\";\n    CONNECT_EVENT_ERROR_CODES[CONNECT_EVENT_ERROR_CODES[\"METHOD_NOT_SUPPORTED\"] = 400] = \"METHOD_NOT_SUPPORTED\";\n})(exports.CONNECT_EVENT_ERROR_CODES || (exports.CONNECT_EVENT_ERROR_CODES = {}));\nexports.CONNECT_ITEM_ERROR_CODES = void 0;\n(function (CONNECT_ITEM_ERROR_CODES) {\n    CONNECT_ITEM_ERROR_CODES[CONNECT_ITEM_ERROR_CODES[\"UNKNOWN_ERROR\"] = 0] = \"UNKNOWN_ERROR\";\n    CONNECT_ITEM_ERROR_CODES[CONNECT_ITEM_ERROR_CODES[\"METHOD_NOT_SUPPORTED\"] = 400] = \"METHOD_NOT_SUPPORTED\";\n})(exports.CONNECT_ITEM_ERROR_CODES || (exports.CONNECT_ITEM_ERROR_CODES = {}));\n\nexports.SEND_TRANSACTION_ERROR_CODES = void 0;\n(function (SEND_TRANSACTION_ERROR_CODES) {\n    SEND_TRANSACTION_ERROR_CODES[SEND_TRANSACTION_ERROR_CODES[\"UNKNOWN_ERROR\"] = 0] = \"UNKNOWN_ERROR\";\n    SEND_TRANSACTION_ERROR_CODES[SEND_TRANSACTION_ERROR_CODES[\"BAD_REQUEST_ERROR\"] = 1] = \"BAD_REQUEST_ERROR\";\n    SEND_TRANSACTION_ERROR_CODES[SEND_TRANSACTION_ERROR_CODES[\"UNKNOWN_APP_ERROR\"] = 100] = \"UNKNOWN_APP_ERROR\";\n    SEND_TRANSACTION_ERROR_CODES[SEND_TRANSACTION_ERROR_CODES[\"USER_REJECTS_ERROR\"] = 300] = \"USER_REJECTS_ERROR\";\n    SEND_TRANSACTION_ERROR_CODES[SEND_TRANSACTION_ERROR_CODES[\"METHOD_NOT_SUPPORTED\"] = 400] = \"METHOD_NOT_SUPPORTED\";\n})(exports.SEND_TRANSACTION_ERROR_CODES || (exports.SEND_TRANSACTION_ERROR_CODES = {}));\n\nexports.SIGN_DATA_ERROR_CODES = void 0;\n(function (SIGN_DATA_ERROR_CODES) {\n    SIGN_DATA_ERROR_CODES[SIGN_DATA_ERROR_CODES[\"UNKNOWN_ERROR\"] = 0] = \"UNKNOWN_ERROR\";\n    SIGN_DATA_ERROR_CODES[SIGN_DATA_ERROR_CODES[\"BAD_REQUEST_ERROR\"] = 1] = \"BAD_REQUEST_ERROR\";\n    SIGN_DATA_ERROR_CODES[SIGN_DATA_ERROR_CODES[\"UNKNOWN_APP_ERROR\"] = 100] = \"UNKNOWN_APP_ERROR\";\n    SIGN_DATA_ERROR_CODES[SIGN_DATA_ERROR_CODES[\"USER_REJECTS_ERROR\"] = 300] = \"USER_REJECTS_ERROR\";\n    SIGN_DATA_ERROR_CODES[SIGN_DATA_ERROR_CODES[\"METHOD_NOT_SUPPORTED\"] = 400] = \"METHOD_NOT_SUPPORTED\";\n})(exports.SIGN_DATA_ERROR_CODES || (exports.SIGN_DATA_ERROR_CODES = {}));\n\nexports.DISCONNECT_ERROR_CODES = void 0;\n(function (DISCONNECT_ERROR_CODES) {\n    DISCONNECT_ERROR_CODES[DISCONNECT_ERROR_CODES[\"UNKNOWN_ERROR\"] = 0] = \"UNKNOWN_ERROR\";\n    DISCONNECT_ERROR_CODES[DISCONNECT_ERROR_CODES[\"BAD_REQUEST_ERROR\"] = 1] = \"BAD_REQUEST_ERROR\";\n    DISCONNECT_ERROR_CODES[DISCONNECT_ERROR_CODES[\"UNKNOWN_APP_ERROR\"] = 100] = \"UNKNOWN_APP_ERROR\";\n    DISCONNECT_ERROR_CODES[DISCONNECT_ERROR_CODES[\"METHOD_NOT_SUPPORTED\"] = 400] = \"METHOD_NOT_SUPPORTED\";\n})(exports.DISCONNECT_ERROR_CODES || (exports.DISCONNECT_ERROR_CODES = {}));\n\nexports.CHAIN = void 0;\n(function (CHAIN) {\n    CHAIN[\"MAINNET\"] = \"-239\";\n    CHAIN[\"TESTNET\"] = \"-3\";\n})(exports.CHAIN || (exports.CHAIN = {}));\n\nfunction encodeUint8Array(value, urlSafe) {\n    const encoded = nacl.encodeBase64(value);\n    if (!urlSafe) {\n        return encoded;\n    }\n    return encodeURIComponent(encoded);\n}\nfunction decodeToUint8Array(value, urlSafe) {\n    if (urlSafe) {\n        value = decodeURIComponent(value);\n    }\n    return nacl.decodeBase64(value);\n}\nfunction encode(value, urlSafe = false) {\n    let uint8Array;\n    if (value instanceof Uint8Array) {\n        uint8Array = value;\n    }\n    else {\n        if (typeof value !== 'string') {\n            value = JSON.stringify(value);\n        }\n        uint8Array = nacl.decodeUTF8(value);\n    }\n    return encodeUint8Array(uint8Array, urlSafe);\n}\nfunction decode(value, urlSafe = false) {\n    const decodedUint8Array = decodeToUint8Array(value, urlSafe);\n    return {\n        toString() {\n            return nacl.encodeUTF8(decodedUint8Array);\n        },\n        toObject() {\n            try {\n                return JSON.parse(nacl.encodeUTF8(decodedUint8Array));\n            }\n            catch (e) {\n                return null;\n            }\n        },\n        toUint8Array() {\n            return decodedUint8Array;\n        }\n    };\n}\nconst Base64 = {\n    encode,\n    decode\n};\n\nfunction concatUint8Arrays(buffer1, buffer2) {\n    const mergedArray = new Uint8Array(buffer1.length + buffer2.length);\n    mergedArray.set(buffer1);\n    mergedArray.set(buffer2, buffer1.length);\n    return mergedArray;\n}\nfunction splitToUint8Arrays(array, index) {\n    if (index >= array.length) {\n        throw new Error('Index is out of buffer');\n    }\n    const subArray1 = array.slice(0, index);\n    const subArray2 = array.slice(index);\n    return [subArray1, subArray2];\n}\nfunction toHexString(byteArray) {\n    let hexString = '';\n    byteArray.forEach(byte => {\n        hexString += ('0' + (byte & 0xff).toString(16)).slice(-2);\n    });\n    return hexString;\n}\nfunction hexToByteArray(hexString) {\n    if (hexString.length % 2 !== 0) {\n        throw new Error(`Cannot convert ${hexString} to bytesArray`);\n    }\n    const result = new Uint8Array(hexString.length / 2);\n    for (let i = 0; i < hexString.length; i += 2) {\n        result[i / 2] = parseInt(hexString.slice(i, i + 2), 16);\n    }\n    return result;\n}\n\nfunction isNode() {\n    return (typeof process !== 'undefined' && process.versions != null && process.versions.node != null);\n}\n\nclass SessionCrypto {\n    constructor(keyPair) {\n        this.nonceLength = 24;\n        this.keyPair = keyPair ? this.createKeypairFromString(keyPair) : this.createKeypair();\n        this.sessionId = toHexString(this.keyPair.publicKey);\n    }\n    createKeypair() {\n        return nacl$1.box.keyPair();\n    }\n    createKeypairFromString(keyPair) {\n        return {\n            publicKey: hexToByteArray(keyPair.publicKey),\n            secretKey: hexToByteArray(keyPair.secretKey)\n        };\n    }\n    createNonce() {\n        return nacl$1.randomBytes(this.nonceLength);\n    }\n    encrypt(message, receiverPublicKey) {\n        const encodedMessage = new TextEncoder().encode(message);\n        const nonce = this.createNonce();\n        const encrypted = nacl$1.box(encodedMessage, nonce, receiverPublicKey, this.keyPair.secretKey);\n        return concatUint8Arrays(nonce, encrypted);\n    }\n    decrypt(message, senderPublicKey) {\n        const [nonce, internalMessage] = splitToUint8Arrays(message, this.nonceLength);\n        const decrypted = nacl$1.box.open(internalMessage, nonce, senderPublicKey, this.keyPair.secretKey);\n        if (!decrypted) {\n            throw new Error(`Decryption error: \\n message: ${message.toString()} \\n sender pubkey: ${senderPublicKey.toString()} \\n keypair pubkey: ${this.keyPair.publicKey.toString()} \\n keypair secretkey: ${this.keyPair.secretKey.toString()}`);\n        }\n        return new TextDecoder().decode(decrypted);\n    }\n    stringifyKeypair() {\n        return {\n            publicKey: toHexString(this.keyPair.publicKey),\n            secretKey: toHexString(this.keyPair.secretKey)\n        };\n    }\n}\n\nexports.Base64 = Base64;\nexports.SessionCrypto = SessionCrypto;\nexports.concatUint8Arrays = concatUint8Arrays;\nexports.hexToByteArray = hexToByteArray;\nexports.isNode = isNode;\nexports.splitToUint8Arrays = splitToUint8Arrays;\nexports.toHexString = toHexString;\n//# sourceMappingURL=index.cjs.map\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(1920);\n"], "names": ["tonConnectSdkVersion", "WrongAddressError", "TonConnectError", "info", "constructor", "args", "super", "Object", "setPrototypeOf", "this", "prototype", "ParseHexError", "DappMetadataError", "UnknownE<PERSON>r", "UserRejectsError", "ManifestContentErrorError", "ManifestNotFoundError", "BadRequestError", "UnknownAppError", "LocalstorageNotFoundError", "Error", "message", "options", "prefix", "name", "WalletAlreadyConnectedError", "WalletNotConnectedError", "WalletNotInjectedError", "WalletNotSupportFeatureError", "FetchWalletsError", "TonConnect", "WalletsListManager", "createConnectionStartedEvent", "createConnectionErrorEvent", "createConnectionCompletedEvent", "createConnectionRestoringStartedEvent", "createConnectionRestoringErrorEvent", "createConnectionRestoringCompletedEvent", "createDisconnectionEvent", "createTransactionSentForSignatureEvent", "createTransactionSigningFailedEvent", "createTransactionSignedEvent", "createRequestVersionEvent", "createResponseVersionEvent", "createVersionInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CHAIN", "CONNECT_ITEM_ERROR_CODES", "CONNECT_EVENT_ERROR_CODES", "SEND_TRANSACTION_ERROR_CODES", "toUser<PERSON>rien<PERSON><PERSON><PERSON><PERSON>", "isTelegramUrl", "encodeTelegramUrlParameters", "isWalletInfoCurrentlyInjected", "isWalletInfoCurrentlyEmbedded", "isWalletInfoInjectable", "isWalletInfoRemote", "isWalletInfoInjected", "value", "injected", "embedded", "connectEventErrorsCodes", "UNKNOWN_ERROR", "USER_REJECTS_ERROR", "BAD_REQUEST_ERROR", "UNKNOWN_APP_ERROR", "MANIFEST_NOT_FOUND_ERROR", "MANIFEST_CONTENT_ERROR", "connectErrorsParser", "parseError", "error", "ErrorConstructor", "code", "isError", "response", "sendTransactionErrors", "SendTransactionParser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "convertToRpcRequest", "request", "method", "params", "JSON", "stringify", "parseAndThrowError", "convertFromRpcResponse", "rpcResponse", "boc", "result", "sendTransactionParser", "storage", "bridgeUrl", "sessionId", "listener", "errorsListener", "ssePath", "postPath", "heartbeatMessage", "defaultTtl", "defaultReconnectDelay", "defaultResendDelay", "eventSource", "createResource", "signal", "openingDeadlineMS", "eventSourceConfig", "bridgeGatewayStorage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "bind", "messageHandler", "messages<PERSON><PERSON><PERSON>", "config", "timeout", "resolve", "reject", "deferOptions", "createAbortController", "aborted", "url", "URL", "addPathToUrl", "searchParams", "append", "lastEventId", "getLastEventId", "EventSource", "toString", "onerror", "reason", "close", "newInstance", "e", "onopen", "onmessage", "event", "addEventListener", "createEventSource", "resource", "HttpBridgeGatewayStorage", "isReady", "current", "readyState", "OPEN", "isClosed", "isConnecting", "CONNECTING", "registerSession", "create", "send", "receiver", "topic", "ttlOrOptions", "ttl", "attempts", "body", "Base64", "encode", "callForSuccess", "post", "ok", "status", "Number", "MAX_SAFE_INTEGER", "delayMs", "pause", "dispose", "catch", "logError", "unPause", "recreate", "setListener", "setErrorsListener", "fetch", "logDebug", "data", "storeLastEventId", "bridgeIncomingMessage", "parse", "BridgeProvider", "walletConnectionSource", "type", "standardUniversalLink", "pendingRequests", "Map", "session", "gateway", "pendingGateways", "listeners", "defaultOpeningDeadlineMS", "defaultRetryTimeoutMS", "connectionStorage", "BridgeConnectionStorage", "static", "bridgeConnectionStorage", "connection", "getHttpConnection", "isPendingConnectionHttp", "connectionSource", "connect", "abortController", "abort", "closeGateways", "sessionCrypto", "SessionCrypto", "storeConnection", "then", "_options", "openGateways", "universalLink", "generateUniversalLink", "restoreConnection", "storedConnection", "Array", "isArray", "BridgeGateway", "gatewayListener", "gatewayErrorsListener", "for<PERSON>ach", "connectEvent", "disconnect", "sendRequest", "optionsOrOnRequestSent", "onRequestSent", "Promise", "id", "getNextRpcRequestId", "increaseNextRpcRequestId", "encodedRequest", "encrypt", "hexToByteArray", "walletPublicKey", "set", "closeConnection", "called", "timeoutId", "removeBridgeAndSession", "setTimeout", "clearTimeout", "listen", "callback", "push", "filter", "bridge", "promises", "map", "all", "pendingGatewaysListener", "includes", "except", "walletMessage", "decrypt", "decode", "toUint8Array", "from", "get", "delete", "undefined", "lastId", "getLastWalletEventId", "storeLastWalletEventId", "updateSession", "tonAddrItem", "payload", "items", "find", "item", "connectEventToSave", "lastWalletEventId", "nextRpcRequestId", "removeConnection", "generateTGUniversalLink", "generateRegularUniversalLink", "PROTOCOL_VERSION", "linkParams", "split", "startapp", "updatedUniversalLink", "convertToDirectLink", "has", "pathname", "source", "allSettled", "some", "In<PERSON><PERSON><PERSON><PERSON>", "injectedWalletKey", "unsubscribeCallback", "listenSubscriptions", "window", "isWindowContainsWallet", "injectedWallet", "tonconnect", "getInjectedConnection", "jsBridgeKey", "isWalletBrowser", "tryGetWindowKeys", "_", "isJSBridgeWithMetadata", "wallet", "walletInfo", "appName", "app_name", "aboutUrl", "about_url", "imageUrl", "image", "tondns", "platforms", "_connect", "makeSubscriptions", "console", "closeAllListeners", "eventsCallback", "protocolVersion", "connectEventError", "getWindow", "hasProperty", "hasProperties", "FALLBACK_WALLETS_LIST", "universal_url", "deepLink", "key", "storeKey", "setItem", "rawSession", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stringifyKeypair", "rawConnection", "removeItem", "getConnection", "stored", "getItem", "getHttpPendingConnection", "storedConnectionType", "localStorage", "tryGetLocalStorage", "removeLastEventId", "InMemoryStorage", "instance", "length", "keys", "clear", "index", "walletsList", "_wallet", "provider", "statusChangeSubscriptions", "statusChangeErrorSubscriptions", "dappSettings", "manifestUrl", "getWebPageManifest", "DefaultStorage", "walletsListSource", "cacheTTLMs", "walletsListCacheTTLMs", "tracker", "TonConnectTracker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableAutoPauseConnection", "addWindowFocusAndBlurSubscriptions", "getWallets", "connected", "account", "onStatusChange", "requestOrOptions", "createProvider", "trackConnectionStarted", "createConnectRequest", "trackConnectionRestoringStarted", "trackConnectionRestoringError", "bridgeConnectionType", "embeddedWallet", "getEmbeddedWallet", "fromStorage", "walletEventsListener", "onAbortRestore", "restoreConnectionTask", "removeEventListener", "trackConnectionRestoringCompleted", "restoreConnectionTimeout", "race", "sendTransaction", "transaction", "checkConnection", "checkSendTransactionSupport", "device", "features", "requiredMessagesNumber", "messages", "trackTransactionSentForSignature", "validUntil", "tx", "address", "network", "chain", "valid_until", "trackTransactionSigningFailed", "trackTransactionSigned", "prevAbortController", "onWalletDisconnected", "pauseConnection", "unPauseConnection", "document", "getDocument", "hidden", "isWalletConnectionSourceJS", "onWalletConnected", "onWalletConnectError", "tonAccountItem", "tonProofItem", "walletStateInit", "public<PERSON>ey", "connectItems", "tonProof", "trackConnectionCompleted", "trackConnectionError", "scope", "trackDisconnection", "isWalletInjected", "walletJSKey", "isInsideWalletBrowser", "dispatchEvent", "eventName", "eventDetails", "CustomEvent", "detail", "eventPrefix", "tonConnectUiVersion", "init", "version", "ton_connect_sdk_lib", "ton_connect_ui_lib", "setRequestVersionHandler", "requestTonConnectUiVersion", "once", "dispatchUserActionEvent", "createConnectionInfo", "authType", "wallet_address", "wallet_type", "wallet_version", "appVersion", "auth_type", "custom_data", "chain_id", "createTransactionInfo", "String", "amount", "is_success", "error_message", "errorCode", "error_code", "errorMessage", "signedTransaction", "signed_transaction", "hex<PERSON><PERSON><PERSON>", "testOnly", "wc", "hex", "parts", "parseInt", "hexToBytes", "parseHexAddress", "tag", "addr", "Int8Array", "addressWithChecksum", "Uint8Array", "reg", "byte", "mask", "Math", "floor", "crc16", "replace", "toByteMap", "ord", "s", "length2", "toLowerCase", "i", "doubled", "hexSubstring", "substring", "hasOwnProperty", "fn", "lastError", "err", "delay", "AbortController", "supportsDeprecatedSendTransactionFeature", "sendTransactionFeature", "feature", "maxMessages", "logWarning", "debug", "warn", "createFn", "disposeFn", "currentResource", "currentArgs", "currentPromise", "currentSignal", "promise", "propertyKeys", "every", "propertyKey", "removeUrlLastSlash", "slice", "path", "link", "protocol", "hostname", "parameters", "replaceAll", "origin", "location", "isLocalStorageAvailable", "process", "versions", "node", "getInstance", "walletsListCache", "walletsListCacheCreationTimestamp", "Date", "now", "fetchWalletsList", "embeddedWallets", "walletsResponse", "json", "wrongFormatWallets", "isCorrectWalletConfigDTO", "join", "currentlyInjectedWallets", "getCurrentlyInjectedWallets", "mergeWalletsLists", "walletConfigDTOListToWalletConfigList", "walletConfigDTO", "walletConfig", "list1", "list2", "Set", "concat", "values", "list1Item", "list2Item", "sseBridge", "jsBridge", "root", "f", "module", "exports", "nacl", "util", "validateBase64", "test", "TypeError", "decodeUTF8", "d", "unescape", "encodeURIComponent", "b", "charCodeAt", "encodeUTF8", "arr", "fromCharCode", "decodeURIComponent", "escape", "atob", "<PERSON><PERSON><PERSON>", "encodeBase64", "decodeBase64", "call", "len", "btoa", "gf", "r", "Float64Array", "randombytes", "_0", "_9", "gf0", "gf1", "_121665", "D", "D2", "X", "Y", "I", "ts64", "x", "h", "l", "vn", "xi", "y", "yi", "n", "crypto_verify_16", "crypto_verify_32", "crypto_core_salsa20", "out", "inp", "k", "c", "o", "p", "u", "j0", "j1", "j2", "j3", "j4", "j5", "j6", "j7", "j8", "j9", "j10", "j11", "j12", "j13", "j14", "j15", "x0", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "core_salsa20", "crypto_core_hsalsa20", "core_hsalsa20", "sigma", "crypto_stream_salsa20_xor", "cpos", "m", "mpos", "z", "crypto_stream_salsa20", "crypto_stream", "sn", "crypto_stream_xor", "poly1305", "t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "buffer", "Uint16Array", "pad", "leftover", "fin", "crypto_onetimeauth", "outpos", "update", "finish", "crypto_onetimeauth_verify", "hpos", "crypto_secretbox", "crypto_secretbox_open", "set25519", "a", "car25519", "v", "sel25519", "q", "t", "pack25519", "j", "neq25519", "par25519", "unpack25519", "A", "Z", "M", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "b11", "b12", "b13", "b14", "b15", "S", "inv25519", "pow2523", "crypto_scalarmult", "x32", "subarray", "x16", "crypto_scalarmult_base", "crypto_box_keypair", "crypto_box_beforenm", "blocks", "bytes", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "hibit", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "h8", "h9", "r0", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "mac", "macpos", "g", "want", "crypto_box_afternm", "crypto_box_open_afternm", "K", "crypto_hashblocks_hl", "hh", "hl", "bh0", "bh1", "bh2", "bh3", "bh4", "bh5", "bh6", "bh7", "bl0", "bl1", "bl2", "bl3", "bl4", "bl5", "bl6", "bl7", "th", "tl", "wh", "Int32Array", "wl", "ah0", "ah1", "ah2", "ah3", "ah4", "ah5", "ah6", "ah7", "al0", "al1", "al2", "al3", "al4", "al5", "al6", "al7", "pos", "crypto_hash", "add", "cswap", "pack", "ty", "zi", "scalarmult", "scalarbase", "crypto_sign_keypair", "pk", "sk", "seeded", "L", "modL", "carry", "reduce", "crypto_sign", "sm", "smlen", "crypto_sign_open", "chk", "num", "den", "den2", "den4", "den6", "unpackneg", "crypto", "crypto_secretbox_KEYBYTES", "crypto_secretbox_NONCEBYTES", "crypto_box_NONCEBYTES", "crypto_sign_BYTES", "crypto_sign_PUBLICKEYBYTES", "crypto_sign_SECRETKEYBYTES", "checkLengths", "checkArrayTypes", "arguments", "cleanup", "lowlevel", "crypto_box", "crypto_box_open", "crypto_secretbox_ZEROBYTES", "crypto_secretbox_BOXZEROBYTES", "crypto_scalarmult_BYTES", "crypto_scalarmult_SCALARBYTES", "crypto_box_PUBLICKEYBYTES", "crypto_box_SECRETKEYBYTES", "crypto_box_BEFORENMBYTES", "crypto_box_ZEROBYTES", "crypto_box_BOXZEROBYTES", "crypto_sign_SEEDBYTES", "crypto_hash_BYTES", "randomBytes", "secretbox", "msg", "nonce", "open", "box", "<PERSON><PERSON><PERSON><PERSON>", "non<PERSON><PERSON><PERSON><PERSON>", "overheadLength", "scalarMult", "base", "scalar<PERSON>ength", "groupElementLength", "secret<PERSON>ey", "before", "checkBoxLengths", "after", "keyPair", "fromSecretKey", "publicKeyLength", "secretKeyLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "signed<PERSON>g", "tmp", "mlen", "detached", "sig", "verify", "fromSeed", "seed", "seedLength", "<PERSON><PERSON><PERSON><PERSON>", "hash", "hash<PERSON><PERSON><PERSON>", "setPRNG", "self", "msCrypto", "getRandomValues", "min", "SIGN_DATA_ERROR_CODES", "DISCONNECT_ERROR_CODES", "nacl$1", "urlSafe", "uint8Array", "encoded", "encodeUint8Array", "decodedUint8Array", "decodeToUint8Array", "toObject", "concatUint8Arrays", "buffer1", "buffer2", "mergedArray", "splitToUint8Arrays", "array", "toHexString", "byteArray", "hexString", "createKeypairFromString", "createKeypair", "createNonce", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodedMessage", "TextEncoder", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "internalMessage", "decrypted", "TextDecoder", "isNode", "__webpack_module_cache__", "__webpack_exports__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}