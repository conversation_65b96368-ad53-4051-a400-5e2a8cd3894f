{"version": 3, "sources": ["../../../../src/start/interface/interactiveActions.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { BLT, printHelp, printItem, printQRCode, printUsage, StartOptions } from './commandsTable';\nimport * as Log from '../../log';\nimport { env } from '../../utils/env';\nimport { learnMore } from '../../utils/link';\nimport { openBrowserAsync } from '../../utils/open';\nimport { ExpoChoice, selectAsync } from '../../utils/prompts';\nimport { DevServerManager } from '../server/DevServerManager';\nimport {\n  openJsInspector,\n  queryAllInspectorAppsAsync,\n  promptInspectorAppAsync,\n} from '../server/middleware/inspector/JsInspector';\n\nconst debug = require('debug')('expo:start:interface:interactiveActions') as typeof console.log;\n\ninterface MoreToolMenuItem extends ExpoChoice<string> {\n  action?: () => unknown;\n}\n\n/** Wraps the DevServerManager and adds an interface for user actions. */\nexport class DevServerManagerActions {\n  constructor(\n    private devServerManager: DevServerManager,\n    private options: Pick<StartOptions, 'devClient' | 'platforms'>\n  ) {}\n\n  printDevServerInfo(\n    options: Pick<StartOptions, 'devClient' | 'isWebSocketsEnabled' | 'platforms'>\n  ) {\n    // If native dev server is running, print its URL.\n    if (this.devServerManager.getNativeDevServerPort()) {\n      const devServer = this.devServerManager.getDefaultDevServer();\n      try {\n        const nativeRuntimeUrl = devServer.getNativeRuntimeUrl()!;\n        const interstitialPageUrl = devServer.getRedirectUrl();\n\n        printQRCode(interstitialPageUrl ?? nativeRuntimeUrl);\n\n        if (interstitialPageUrl) {\n          Log.log(\n            printItem(\n              chalk`Choose an app to open your project at {underline ${interstitialPageUrl}}`\n            )\n          );\n        }\n\n        if (env.__EXPO_E2E_TEST) {\n          // Print the URL to stdout for tests\n          console.info(\n            `[__EXPO_E2E_TEST:server] ${JSON.stringify({ url: devServer.getDevServerUrl() })}`\n          );\n        }\n\n        Log.log(printItem(chalk`Metro waiting on {underline ${nativeRuntimeUrl}}`));\n        if (options.devClient === false) {\n          // TODO: if development build, change this message!\n          Log.log(\n            printItem('Scan the QR code above with Expo Go (Android) or the Camera app (iOS)')\n          );\n        } else {\n          Log.log(\n            printItem(\n              'Scan the QR code above to open the project in a development build. ' +\n                learnMore('https://expo.fyi/start')\n            )\n          );\n        }\n      } catch (error) {\n        console.log('err', error);\n        // @ts-ignore: If there is no development build scheme, then skip the QR code.\n        if (error.code !== 'NO_DEV_CLIENT_SCHEME') {\n          throw error;\n        } else {\n          const serverUrl = devServer.getDevServerUrl();\n          Log.log(printItem(chalk`Metro waiting on {underline ${serverUrl}}`));\n          Log.log(printItem(`Linking is disabled because the client scheme cannot be resolved.`));\n        }\n      }\n    }\n\n    if (this.options.platforms?.includes('web')) {\n      const webDevServer = this.devServerManager.getWebDevServer();\n      const webUrl = webDevServer?.getDevServerUrl({ hostType: 'localhost' });\n      if (webUrl) {\n        Log.log();\n        Log.log(printItem(chalk`Web is waiting on {underline ${webUrl}}`));\n      }\n    }\n\n    printUsage(options, { verbose: false });\n    printHelp();\n    Log.log();\n  }\n\n  async openJsInspectorAsync() {\n    try {\n      const metroServerOrigin = this.devServerManager.getDefaultDevServer().getJsInspectorBaseUrl();\n      const apps = await queryAllInspectorAppsAsync(metroServerOrigin);\n      if (!apps.length) {\n        return Log.warn(\n          chalk`{bold Debug:} No compatible apps connected, React Native DevTools can only be used with Hermes. ${learnMore(\n            'https://docs.expo.dev/guides/using-hermes/'\n          )}`\n        );\n      }\n\n      const app = await promptInspectorAppAsync(apps);\n      if (!app) {\n        return Log.error(chalk`{bold Debug:} No inspectable device selected`);\n      }\n\n      if (!(await openJsInspector(metroServerOrigin, app))) {\n        Log.warn(\n          chalk`{bold Debug:} Failed to open the React Native DevTools, see debug logs for more info.`\n        );\n      }\n    } catch (error: any) {\n      // Handle aborting prompt\n      if (error.code === 'ABORTED') return;\n\n      Log.error('Failed to open the React Native DevTools.');\n      Log.exception(error);\n    }\n  }\n\n  reloadApp() {\n    Log.log(`${BLT} Reloading apps`);\n    // Send reload requests over the dev servers\n    this.devServerManager.broadcastMessage('reload');\n  }\n\n  async openMoreToolsAsync() {\n    // Options match: Chrome > View > Developer\n    try {\n      const defaultMenuItems: MoreToolMenuItem[] = [\n        { title: 'Inspect elements', value: 'toggleElementInspector' },\n        { title: 'Toggle performance monitor', value: 'togglePerformanceMonitor' },\n        { title: 'Toggle developer menu', value: 'toggleDevMenu' },\n        { title: 'Reload app', value: 'reload' },\n        // TODO: Maybe a \"View Source\" option to open code.\n      ];\n      const pluginMenuItems = (\n        await this.devServerManager.devtoolsPluginManager.queryPluginsAsync()\n      ).map((plugin) => ({\n        title: chalk`Open {bold ${plugin.packageName}}`,\n        value: `devtoolsPlugin:${plugin.packageName}`,\n        action: async () => {\n          const url = new URL(\n            plugin.webpageEndpoint,\n            this.devServerManager\n              .getDefaultDevServer()\n              .getUrlCreator()\n              .constructUrl({ scheme: 'http' })\n          );\n          await openBrowserAsync(url.toString());\n        },\n      }));\n      const menuItems = [...defaultMenuItems, ...pluginMenuItems];\n      const value = await selectAsync(chalk`Dev tools {dim (native only)}`, menuItems);\n      const menuItem = menuItems.find((item) => item.value === value);\n      if (menuItem?.action) {\n        menuItem.action();\n      } else if (menuItem?.value) {\n        this.devServerManager.broadcastMessage('sendDevCommand', { name: menuItem.value });\n      }\n    } catch (error: any) {\n      debug(error);\n      // do nothing\n    } finally {\n      printHelp();\n    }\n  }\n\n  toggleDevMenu() {\n    Log.log(`${BLT} Toggling dev menu`);\n    this.devServerManager.broadcastMessage('devMenu');\n  }\n}\n"], "names": ["DevServerManagerActions", "debug", "require", "constructor", "devServerManager", "options", "printDevServerInfo", "getNativeDevServerPort", "devServer", "getDefaultDevServer", "nativeRuntimeUrl", "getNativeRuntimeUrl", "interstitialPageUrl", "getRedirectUrl", "printQRCode", "Log", "log", "printItem", "chalk", "env", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "url", "getDevServerUrl", "devClient", "learnMore", "error", "code", "serverUrl", "platforms", "includes", "webDevServer", "getWebDevServer", "webUrl", "hostType", "printUsage", "verbose", "printHelp", "openJsInspectorAsync", "metroServerOrigin", "getJsInspectorBaseUrl", "apps", "queryAllInspectorAppsAsync", "length", "warn", "app", "promptInspectorAppAsync", "openJsInspector", "exception", "reloadApp", "BLT", "broadcastMessage", "openMoreToolsAsync", "defaultMenuItems", "title", "value", "pluginMenuItems", "devtoolsPluginManager", "queryPluginsAsync", "map", "plugin", "packageName", "action", "URL", "webpageEndpoint", "getUrlCreator", "constructUrl", "scheme", "openBrowserAsync", "toString", "menuItems", "selectAsync", "menuItem", "find", "item", "name", "toggleDevMenu"], "mappings": ";;;;+BAsBaA;;;eAAAA;;;;gEAtBK;;;;;;+BAE+D;6DAC5D;qBACD;sBACM;sBACO;yBACO;6BAMjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAOxB,MAAMF;IACXG,YACE,AAAQC,gBAAkC,EAC1C,AAAQC,OAAsD,CAC9D;aAFQD,mBAAAA;aACAC,UAAAA;IACP;IAEHC,mBACED,OAA8E,EAC9E;YAoDI;QAnDJ,kDAAkD;QAClD,IAAI,IAAI,CAACD,gBAAgB,CAACG,sBAAsB,IAAI;YAClD,MAAMC,YAAY,IAAI,CAACJ,gBAAgB,CAACK,mBAAmB;YAC3D,IAAI;gBACF,MAAMC,mBAAmBF,UAAUG,mBAAmB;gBACtD,MAAMC,sBAAsBJ,UAAUK,cAAc;gBAEpDC,IAAAA,0BAAW,EAACF,uBAAuBF;gBAEnC,IAAIE,qBAAqB;oBACvBG,KAAIC,GAAG,CACLC,IAAAA,wBAAS,EACPC,IAAAA,gBAAK,CAAA,CAAC,iDAAiD,EAAEN,oBAAoB,CAAC,CAAC;gBAGrF;gBAEA,IAAIO,QAAG,CAACC,eAAe,EAAE;oBACvB,oCAAoC;oBACpCC,QAAQC,IAAI,CACV,CAAC,yBAAyB,EAAEC,KAAKC,SAAS,CAAC;wBAAEC,KAAKjB,UAAUkB,eAAe;oBAAG,IAAI;gBAEtF;gBAEAX,KAAIC,GAAG,CAACC,IAAAA,wBAAS,EAACC,IAAAA,gBAAK,CAAA,CAAC,4BAA4B,EAAER,iBAAiB,CAAC,CAAC;gBACzE,IAAIL,QAAQsB,SAAS,KAAK,OAAO;oBAC/B,mDAAmD;oBACnDZ,KAAIC,GAAG,CACLC,IAAAA,wBAAS,EAAC;gBAEd,OAAO;oBACLF,KAAIC,GAAG,CACLC,IAAAA,wBAAS,EACP,wEACEW,IAAAA,eAAS,EAAC;gBAGlB;YACF,EAAE,OAAOC,OAAO;gBACdR,QAAQL,GAAG,CAAC,OAAOa;gBACnB,8EAA8E;gBAC9E,IAAIA,MAAMC,IAAI,KAAK,wBAAwB;oBACzC,MAAMD;gBACR,OAAO;oBACL,MAAME,YAAYvB,UAAUkB,eAAe;oBAC3CX,KAAIC,GAAG,CAACC,IAAAA,wBAAS,EAACC,IAAAA,gBAAK,CAAA,CAAC,4BAA4B,EAAEa,UAAU,CAAC,CAAC;oBAClEhB,KAAIC,GAAG,CAACC,IAAAA,wBAAS,EAAC,CAAC,iEAAiE,CAAC;gBACvF;YACF;QACF;QAEA,KAAI,0BAAA,IAAI,CAACZ,OAAO,CAAC2B,SAAS,qBAAtB,wBAAwBC,QAAQ,CAAC,QAAQ;YAC3C,MAAMC,eAAe,IAAI,CAAC9B,gBAAgB,CAAC+B,eAAe;YAC1D,MAAMC,SAASF,gCAAAA,aAAcR,eAAe,CAAC;gBAAEW,UAAU;YAAY;YACrE,IAAID,QAAQ;gBACVrB,KAAIC,GAAG;gBACPD,KAAIC,GAAG,CAACC,IAAAA,wBAAS,EAACC,IAAAA,gBAAK,CAAA,CAAC,6BAA6B,EAAEkB,OAAO,CAAC,CAAC;YAClE;QACF;QAEAE,IAAAA,yBAAU,EAACjC,SAAS;YAAEkC,SAAS;QAAM;QACrCC,IAAAA,wBAAS;QACTzB,KAAIC,GAAG;IACT;IAEA,MAAMyB,uBAAuB;QAC3B,IAAI;YACF,MAAMC,oBAAoB,IAAI,CAACtC,gBAAgB,CAACK,mBAAmB,GAAGkC,qBAAqB;YAC3F,MAAMC,OAAO,MAAMC,IAAAA,uCAA0B,EAACH;YAC9C,IAAI,CAACE,KAAKE,MAAM,EAAE;gBAChB,OAAO/B,KAAIgC,IAAI,CACb7B,IAAAA,gBAAK,CAAA,CAAC,gGAAgG,EAAEU,IAAAA,eAAS,EAC/G,8CACA,CAAC;YAEP;YAEA,MAAMoB,MAAM,MAAMC,IAAAA,oCAAuB,EAACL;YAC1C,IAAI,CAACI,KAAK;gBACR,OAAOjC,KAAIc,KAAK,CAACX,IAAAA,gBAAK,CAAA,CAAC,4CAA4C,CAAC;YACtE;YAEA,IAAI,CAAE,MAAMgC,IAAAA,4BAAe,EAACR,mBAAmBM,MAAO;gBACpDjC,KAAIgC,IAAI,CACN7B,IAAAA,gBAAK,CAAA,CAAC,qFAAqF,CAAC;YAEhG;QACF,EAAE,OAAOW,OAAY;YACnB,yBAAyB;YACzB,IAAIA,MAAMC,IAAI,KAAK,WAAW;YAE9Bf,KAAIc,KAAK,CAAC;YACVd,KAAIoC,SAAS,CAACtB;QAChB;IACF;IAEAuB,YAAY;QACVrC,KAAIC,GAAG,CAAC,GAAGqC,kBAAG,CAAC,eAAe,CAAC;QAC/B,4CAA4C;QAC5C,IAAI,CAACjD,gBAAgB,CAACkD,gBAAgB,CAAC;IACzC;IAEA,MAAMC,qBAAqB;QACzB,2CAA2C;QAC3C,IAAI;YACF,MAAMC,mBAAuC;gBAC3C;oBAAEC,OAAO;oBAAoBC,OAAO;gBAAyB;gBAC7D;oBAAED,OAAO;oBAA8BC,OAAO;gBAA2B;gBACzE;oBAAED,OAAO;oBAAyBC,OAAO;gBAAgB;gBACzD;oBAAED,OAAO;oBAAcC,OAAO;gBAAS;aAExC;YACD,MAAMC,kBAAkB,AACtB,CAAA,MAAM,IAAI,CAACvD,gBAAgB,CAACwD,qBAAqB,CAACC,iBAAiB,EAAC,EACpEC,GAAG,CAAC,CAACC,SAAY,CAAA;oBACjBN,OAAOvC,IAAAA,gBAAK,CAAA,CAAC,WAAW,EAAE6C,OAAOC,WAAW,CAAC,CAAC,CAAC;oBAC/CN,OAAO,CAAC,eAAe,EAAEK,OAAOC,WAAW,EAAE;oBAC7CC,QAAQ;wBACN,MAAMxC,MAAM,IAAIyC,IACdH,OAAOI,eAAe,EACtB,IAAI,CAAC/D,gBAAgB,CAClBK,mBAAmB,GACnB2D,aAAa,GACbC,YAAY,CAAC;4BAAEC,QAAQ;wBAAO;wBAEnC,MAAMC,IAAAA,sBAAgB,EAAC9C,IAAI+C,QAAQ;oBACrC;gBACF,CAAA;YACA,MAAMC,YAAY;mBAAIjB;mBAAqBG;aAAgB;YAC3D,MAAMD,QAAQ,MAAMgB,IAAAA,oBAAW,EAACxD,IAAAA,gBAAK,CAAA,CAAC,6BAA6B,CAAC,EAAEuD;YACtE,MAAME,WAAWF,UAAUG,IAAI,CAAC,CAACC,OAASA,KAAKnB,KAAK,KAAKA;YACzD,IAAIiB,4BAAAA,SAAUV,MAAM,EAAE;gBACpBU,SAASV,MAAM;YACjB,OAAO,IAAIU,4BAAAA,SAAUjB,KAAK,EAAE;gBAC1B,IAAI,CAACtD,gBAAgB,CAACkD,gBAAgB,CAAC,kBAAkB;oBAAEwB,MAAMH,SAASjB,KAAK;gBAAC;YAClF;QACF,EAAE,OAAO7B,OAAY;YACnB5B,MAAM4B;QACN,aAAa;QACf,SAAU;YACRW,IAAAA,wBAAS;QACX;IACF;IAEAuC,gBAAgB;QACdhE,KAAIC,GAAG,CAAC,GAAGqC,kBAAG,CAAC,kBAAkB,CAAC;QAClC,IAAI,CAACjD,gBAAgB,CAACkD,gBAAgB,CAAC;IACzC;AACF"}