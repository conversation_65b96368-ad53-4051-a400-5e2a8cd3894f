{"name": "@tonconnect/sdk", "version": "3.0.6", "scripts": {"build": "npx rimraf types-dist && npx rimraf lib && npx rollup -c rollup.config.mjs && ttsc --project tsconfig.declarations.json && api-extractor run && npx rimraf types-dist && npx rimraf dist && npx webpack --config webpack.config.js", "test": "vitest run"}, "repository": {"type": "git", "url": "git+https://github.com/ton-connect/sdk.git"}, "homepage": "https://github.com/ton-connect/sdk/tree/main/packages/sdk", "bugs": {"url": "https://github.com/ton-connect/sdk/issues"}, "keywords": ["TON", "Wallet", "ton-connect", "Connect", "<PERSON><PERSON><PERSON><PERSON>", "sdk"], "author": "tonconnect", "license": "Apache-2.0", "dependencies": {"@tonconnect/isomorphic-eventsource": "^0.0.2", "@tonconnect/isomorphic-fetch": "^0.0.3", "@tonconnect/protocol": "^2.2.6"}, "files": ["lib", "dist"], "main": "./lib/cjs/index.cjs", "module": "./lib/esm/index.mjs", "types": "./lib/types/index.d.ts", "exports": {".": {"types": "./lib/types/index.d.ts", "require": "./lib/cjs/index.cjs", "import": "./lib/esm/index.mjs", "default": "./lib/cjs/index.cjs"}}, "nx": {"tags": ["scope:sdk"]}, "devDependencies": {"ts-loader": "^9.4.1", "ttypescript": "^1.5.13", "typescript-transform-paths": "^3.3.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.0", "rollup": "^3.18.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-replace": "5.0.5", "vite": "^4.2.1", "vite-tsconfig-paths": "^4.0.5", "vitest": "^0.29.2", "vitest-fetch-mock": "^0.2.2"}, "typedoc": {"entryPoint": "./src/index.ts"}}