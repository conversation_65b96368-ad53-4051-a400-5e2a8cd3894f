{"version": 3, "sources": ["../../../../src/utils/tsconfig/loadTsConfigPaths.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport path from 'path';\n\nimport { evaluateTsConfig, importTypeScriptFromProjectOptionally } from './evaluateTsConfig';\nimport { fileExistsAsync } from '../dir';\n\nexport type TsConfigPaths = {\n  paths?: Record<string, string[]>;\n  baseUrl?: string;\n};\n\ntype ConfigReadResults = [\n  string,\n  {\n    compilerOptions?: {\n      baseUrl?: string;\n      paths?: Record<string, string[]>;\n    };\n  },\n];\n\nconst debug = require('debug')('expo:utils:tsconfig:load') as typeof console.log;\n\nexport async function loadTsConfigPathsAsync(dir: string): Promise<TsConfigPaths | null> {\n  const options = (await readTsconfigAsync(dir)) ?? (await readJsconfigAsync(dir));\n  if (options) {\n    const [, config] = options;\n    return {\n      paths: config.compilerOptions?.paths,\n      baseUrl: config.compilerOptions?.baseUrl\n        ? path.resolve(dir, config.compilerOptions.baseUrl)\n        : undefined,\n    };\n  }\n  return null;\n}\n\nasync function readJsconfigAsync(projectRoot: string): Promise<null | ConfigReadResults> {\n  const configPath = path.join(projectRoot, 'jsconfig.json');\n  if (await fileExistsAsync(configPath)) {\n    const config = await JsonFile.readAsync(configPath, { json5: true });\n    if (config) {\n      return [configPath, config];\n    }\n  }\n  return null;\n}\n\n// TODO: Refactor for speed\nexport async function readTsconfigAsync(projectRoot: string): Promise<null | ConfigReadResults> {\n  const configPath = path.join(projectRoot, 'tsconfig.json');\n  if (await fileExistsAsync(configPath)) {\n    // We need to fully evaluate the tsconfig to get the baseUrl and paths in case they were applied in `extends`.\n    const ts = importTypeScriptFromProjectOptionally(projectRoot);\n    if (ts) {\n      return [configPath, evaluateTsConfig(ts, configPath)];\n    }\n    debug(`typescript module not found in: ${projectRoot}`);\n  }\n  return null;\n}\n"], "names": ["loadTsConfigPathsAsync", "readTsconfigAsync", "debug", "require", "dir", "options", "readJsconfigAsync", "config", "paths", "compilerOptions", "baseUrl", "path", "resolve", "undefined", "projectRoot", "config<PERSON><PERSON>", "join", "fileExistsAsync", "JsonFile", "readAsync", "json5", "ts", "importTypeScriptFromProjectOptionally", "evaluateTsConfig"], "mappings": ";;;;;;;;;;;IAuBsBA,sBAAsB;eAAtBA;;IA0BAC,iBAAiB;eAAjBA;;;;gEAjDD;;;;;;;gEACJ;;;;;;kCAEuD;qBACxC;;;;;;AAiBhC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeH,uBAAuBI,GAAW;IACtD,MAAMC,UAAU,AAAC,MAAMJ,kBAAkBG,QAAU,MAAME,kBAAkBF;IAC3E,IAAIC,SAAS;YAGFE,yBACEA;QAHX,MAAM,GAAGA,OAAO,GAAGF;QACnB,OAAO;YACLG,KAAK,GAAED,0BAAAA,OAAOE,eAAe,qBAAtBF,wBAAwBC,KAAK;YACpCE,SAASH,EAAAA,2BAAAA,OAAOE,eAAe,qBAAtBF,yBAAwBG,OAAO,IACpCC,eAAI,CAACC,OAAO,CAACR,KAAKG,OAAOE,eAAe,CAACC,OAAO,IAChDG;QACN;IACF;IACA,OAAO;AACT;AAEA,eAAeP,kBAAkBQ,WAAmB;IAClD,MAAMC,aAAaJ,eAAI,CAACK,IAAI,CAACF,aAAa;IAC1C,IAAI,MAAMG,IAAAA,oBAAe,EAACF,aAAa;QACrC,MAAMR,SAAS,MAAMW,mBAAQ,CAACC,SAAS,CAACJ,YAAY;YAAEK,OAAO;QAAK;QAClE,IAAIb,QAAQ;YACV,OAAO;gBAACQ;gBAAYR;aAAO;QAC7B;IACF;IACA,OAAO;AACT;AAGO,eAAeN,kBAAkBa,WAAmB;IACzD,MAAMC,aAAaJ,eAAI,CAACK,IAAI,CAACF,aAAa;IAC1C,IAAI,MAAMG,IAAAA,oBAAe,EAACF,aAAa;QACrC,8GAA8G;QAC9G,MAAMM,KAAKC,IAAAA,uDAAqC,EAACR;QACjD,IAAIO,IAAI;YACN,OAAO;gBAACN;gBAAYQ,IAAAA,kCAAgB,EAACF,IAAIN;aAAY;QACvD;QACAb,MAAM,CAAC,gCAAgC,EAAEY,aAAa;IACxD;IACA,OAAO;AACT"}