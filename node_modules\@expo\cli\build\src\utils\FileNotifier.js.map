{"version": 3, "sources": ["../../../src/utils/FileNotifier.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { watchFile } from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { memoize } from './fn';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:fileNotifier') as typeof console.log;\n\n/** Observes and reports file changes. */\nexport class FileNotifier {\n  static instances: FileNotifier[] = [];\n\n  static stopAll() {\n    for (const instance of FileNotifier.instances) {\n      instance.stopObserving();\n    }\n  }\n\n  private unsubscribe: (() => void) | null = null;\n\n  constructor(\n    /** Project root to resolve the module IDs relative to. */\n    private projectRoot: string,\n    /** List of module IDs sorted by priority. Only the first file that exists will be observed. */\n    private moduleIds: string[],\n    private settings: {\n      /** An additional warning message to add to the notice. */\n      additionalWarning?: string;\n    } = {}\n  ) {\n    FileNotifier.instances.push(this);\n  }\n\n  /** Get the file in the project. */\n  private resolveFilePath(): string | null {\n    for (const moduleId of this.moduleIds) {\n      const filePath = resolveFrom.silent(this.projectRoot, moduleId);\n      if (filePath) {\n        return filePath;\n      }\n    }\n    return null;\n  }\n\n  public startObserving(callback?: (cur: any, prev: any) => void) {\n    const configPath = this.resolveFilePath();\n    if (configPath) {\n      debug(`Observing ${configPath}`);\n      return this.watchFile(configPath, callback);\n    }\n    return configPath;\n  }\n\n  public stopObserving() {\n    this.unsubscribe?.();\n  }\n\n  /** Watch the file and warn to reload the CLI if it changes. */\n  public watchFile = memoize(this.startWatchingFile.bind(this));\n\n  private startWatchingFile(filePath: string, callback?: (cur: any, prev: any) => void): string {\n    const configName = path.relative(this.projectRoot, filePath);\n    const listener = (cur: any, prev: any) => {\n      if (prev.size || cur.size) {\n        Log.log(\n          `\\u203A Detected a change in ${chalk.bold(\n            configName\n          )}. Restart the server to see the new results.` + (this.settings.additionalWarning || '')\n        );\n      }\n    };\n\n    const watcher = watchFile(filePath, callback ?? listener);\n\n    this.unsubscribe = () => {\n      watcher.unref();\n    };\n\n    return filePath;\n  }\n}\n"], "names": ["FileNotifier", "debug", "require", "instances", "stopAll", "instance", "stopObserving", "constructor", "projectRoot", "moduleIds", "settings", "unsubscribe", "watchFile", "memoize", "startWatchingFile", "bind", "push", "resolve<PERSON><PERSON><PERSON><PERSON>", "moduleId", "filePath", "resolveFrom", "silent", "startObserving", "callback", "config<PERSON><PERSON>", "config<PERSON><PERSON>", "path", "relative", "listener", "cur", "prev", "size", "Log", "log", "chalk", "bold", "additionalWarning", "watcher", "unref"], "mappings": ";;;;+BAWaA;;;eAAAA;;;;gEAXK;;;;;;;yBACQ;;;;;;;gEACT;;;;;;;gEACO;;;;;;oBAEA;6DACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMF;qBACJG,YAA4B,EAAE;IAErC,OAAOC,UAAU;QACf,KAAK,MAAMC,YAAYL,aAAaG,SAAS,CAAE;YAC7CE,SAASC,aAAa;QACxB;IACF;IAIAC,YACE,wDAAwD,GACxD,AAAQC,WAAmB,EAC3B,6FAA6F,GAC7F,AAAQC,SAAmB,EAC3B,AAAQC,WAGJ,CAAC,CAAC,CACN;aAPQF,cAAAA;aAEAC,YAAAA;aACAC,WAAAA;aAPFC,cAAmC;aAwCpCC,YAAYC,IAAAA,WAAO,EAAC,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI;QA5BzDf,aAAaG,SAAS,CAACa,IAAI,CAAC,IAAI;IAClC;IAEA,iCAAiC,GACjC,AAAQC,kBAAiC;QACvC,KAAK,MAAMC,YAAY,IAAI,CAACT,SAAS,CAAE;YACrC,MAAMU,WAAWC,sBAAW,CAACC,MAAM,CAAC,IAAI,CAACb,WAAW,EAAEU;YACtD,IAAIC,UAAU;gBACZ,OAAOA;YACT;QACF;QACA,OAAO;IACT;IAEOG,eAAeC,QAAwC,EAAE;QAC9D,MAAMC,aAAa,IAAI,CAACP,eAAe;QACvC,IAAIO,YAAY;YACdvB,MAAM,CAAC,UAAU,EAAEuB,YAAY;YAC/B,OAAO,IAAI,CAACZ,SAAS,CAACY,YAAYD;QACpC;QACA,OAAOC;IACT;IAEOlB,gBAAgB;QACrB,IAAI,CAACK,WAAW,oBAAhB,IAAI,CAACA,WAAW,MAAhB,IAAI;IACN;IAKQG,kBAAkBK,QAAgB,EAAEI,QAAwC,EAAU;QAC5F,MAAME,aAAaC,eAAI,CAACC,QAAQ,CAAC,IAAI,CAACnB,WAAW,EAAEW;QACnD,MAAMS,WAAW,CAACC,KAAUC;YAC1B,IAAIA,KAAKC,IAAI,IAAIF,IAAIE,IAAI,EAAE;gBACzBC,KAAIC,GAAG,CACL,CAAC,4BAA4B,EAAEC,gBAAK,CAACC,IAAI,CACvCV,YACA,4CAA4C,CAAC,GAAI,CAAA,IAAI,CAACf,QAAQ,CAAC0B,iBAAiB,IAAI,EAAC;YAE3F;QACF;QAEA,MAAMC,UAAUzB,IAAAA,eAAS,EAACO,UAAUI,YAAYK;QAEhD,IAAI,CAACjB,WAAW,GAAG;YACjB0B,QAAQC,KAAK;QACf;QAEA,OAAOnB;IACT;AACF"}