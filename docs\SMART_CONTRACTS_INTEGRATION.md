# 🚀 Интеграция смарт-контрактов для управления устройствами

## 📋 Обзор

Этот проект интегрирует смарт-контракты TON для безопасной оплаты времени управления устройствами. Каждое устройство имеет свой собственный смарт-контракт, который обрабатывает платежи и управляет доступом.

## 🏗️ Архитектура

### Компоненты системы:

1. **Смарт-контракт устройства** (`device_control.fc`)
   - Обрабатывает покупку времени управления
   - Управляет доступом к устройству
   - Накапливает средства для стримера и комиссии платформы

2. **TonService** - основной сервис для работы с TON
3. **ContractDeployService** - сервис для создания и деплоя контрактов
4. **TransactionMonitorService** - мониторинг транзакций
5. **Админ-панель** - управление платформой и выводом комиссий

## 🔧 Настройка

### 1. Установка зависимостей

```bash
npm install @ton/ton @ton/core @ton/crypto @ton/blueprint
```

### 2. Настройка переменных окружения

Скопируйте `.env.example` в `.env` и заполните:

```bash
# Основные настройки TON
TON_NETWORK=testnet  # или mainnet для продакшена
TON_API_KEY=your_api_key_here

# Кошелек платформы
PLATFORM_ADDRESS=EQD...
PLATFORM_MNEMONIC="word1 word2 ... word24"
PLATFORM_FEE_PERCENT=500  # 5%
```

### 3. Компиляция смарт-контракта

```bash
cd contracts
node compile.js
```

### 4. Обновление базы данных

Выполните SQL миграции для добавления таблиц смарт-контрактов:

```sql
-- Таблица контрактов устройств
CREATE TABLE device_contracts (
    device_id VARCHAR(255) PRIMARY KEY,
    contract_address VARCHAR(255) UNIQUE,
    streamer_wallet VARCHAR(255) NOT NULL,
    price_per_minute DECIMAL(18,9) NOT NULL,
    platform_fee_percent INTEGER DEFAULT 500,
    accumulated_earnings DECIMAL(18,9) DEFAULT 0,
    accumulated_platform_fee DECIMAL(18,9) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    last_sync_time TIMESTAMP
);

-- Таблица заработков стримеров
CREATE TABLE streamer_earnings (
    id SERIAL PRIMARY KEY,
    streamer_wallet VARCHAR(255) NOT NULL,
    device_id VARCHAR(255),
    amount_ton DECIMAL(18,9) NOT NULL,
    transaction_hash VARCHAR(255),
    withdrawal_time TIMESTAMP DEFAULT NOW()
);
```

## 🎯 Использование

### Создание контракта для устройства

```javascript
// API endpoint: POST /api/contracts/create-device-contract
{
  "device_id": "AA:BB:CC:DD:EE:FF",
  "owner_address": "EQD...",
  "price_per_minute": "1.0",
  "platform_fee_percent": 500
}
```

### Покупка времени управления

```javascript
// API endpoint: POST /api/contracts/buy-time
{
  "device_id": "AA:BB:CC:DD:EE:FF",
  "duration_minutes": 5,
  "buyer_wallet": "EQD...",
  "streamer_nickname": "streamer123"
}
```

### Вывод средств стримера

```javascript
// API endpoint: POST /api/contracts/withdraw-earnings
{
  "streamer_wallet": "EQD..."
}
```

## 🔄 Процесс работы

### 1. Создание контракта
1. Стример привязывает устройство к своему кошельку
2. Система автоматически создает смарт-контракт для устройства
3. Контракт сохраняется в базе данных

### 2. Покупка времени
1. Пользователь выбирает время управления
2. Система создает транзакцию через TON Connect
3. Смарт-контракт проверяет платеж и предоставляет доступ
4. Средства распределяются между стримером и платформой

### 3. Управление устройством
1. Система проверяет права доступа через смарт-контракт
2. Пользователь может управлять устройством до истечения времени
3. Доступ автоматически отзывается по истечении времени

### 4. Вывод средств
1. Стример может вывести накопленные средства
2. Администратор может вывести комиссии платформы
3. Транзакции выполняются через смарт-контракты

## 🛡️ Безопасность

### Проверки в смарт-контракте:
- Валидация платежей
- Проверка времени доступа
- Защита от повторных атак
- Контроль прав доступа

### Проверки на сервере:
- Валидация адресов кошельков
- Проверка лимитов времени и цены
- Мониторинг транзакций
- Логирование всех операций

## 📊 Мониторинг

### Админ-панель предоставляет:
- Статистику платформы
- Список устройств с накопленными комиссиями
- Возможность вывода комиссий
- Мониторинг транзакций

### Доступ к админ-панели:
```
http://localhost:4000/admin-panel
```

## 🔧 API Endpoints

### Контракты
- `POST /api/contracts/create-device-contract` - создание контракта
- `POST /api/contracts/buy-time` - покупка времени
- `POST /api/contracts/withdraw-earnings` - вывод средств стримера
- `GET /api/contracts/device/:device_id` - информация о контракте

### Транзакции
- `GET /api/transactions/platform/stats` - статистика платформы
- `GET /api/transactions/platform/devices-with-fees` - устройства с комиссиями
- `POST /api/transactions/platform/withdraw-all-fees` - вывод всех комиссий
- `POST /api/transactions/platform/withdraw-device-fee` - вывод комиссии устройства

## 🚨 Устранение неполадок

### Частые проблемы:

1. **Ошибка компиляции контракта**
   ```bash
   # Убедитесь, что установлен @ton/blueprint
   npm install @ton/blueprint
   ```

2. **Ошибка подключения к TON**
   ```bash
   # Проверьте API ключ и сеть
   TON_NETWORK=testnet
   TON_API_KEY=your_valid_api_key
   ```

3. **Ошибка деплоя контракта**
   ```bash
   # Проверьте баланс кошелька платформы
   # Убедитесь, что мнемоника корректна
   ```

### Логи:
- Все операции логируются в `service.log`
- Используйте `LOG_LEVEL=debug` для детального логирования

## 📈 Масштабирование

### Для высоких нагрузок:
1. Используйте пул соединений к TON
2. Настройте кэширование информации о контрактах
3. Используйте очереди для обработки транзакций
4. Настройте мониторинг производительности

### Оптимизация:
- Батчинг транзакций
- Кэширование балансов
- Асинхронная обработка
- Горизонтальное масштабирование

## 🤝 Поддержка

Для получения помощи:
1. Проверьте логи в `service.log`
2. Убедитесь в корректности конфигурации
3. Проверьте статус сети TON
4. Обратитесь к документации TON

## 📚 Дополнительные ресурсы

- [TON Documentation](https://ton.org/docs)
- [TON Connect](https://docs.ton.org/develop/dapps/ton-connect)
- [FunC Language](https://docs.ton.org/develop/func)
- [TON Center API](https://toncenter.com/api/v2/)
