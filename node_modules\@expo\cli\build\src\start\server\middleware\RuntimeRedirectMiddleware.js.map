{"version": 3, "sources": ["../../../../../src/start/server/middleware/RuntimeRedirectMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:runtimeRedirect'\n) as typeof console.log;\n\n/** Runtime to target: expo = Expo Go, custom = Dev Client. */\ntype RuntimeTarget = 'expo' | 'custom';\n\nexport type DeepLinkHandler = (props: {\n  runtime: RuntimeTarget;\n  platform: RuntimePlatform;\n}) => void | Promise<void>;\n\nexport class RuntimeRedirectMiddleware extends ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected options: {\n      onDeepLink?: DeepLinkHandler;\n      getLocation: (props: { runtime: RuntimeTarget }) => string | null | undefined;\n    }\n  ) {\n    super(projectRoot, ['/_expo/link']);\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    const { query } = parse(req.url!, true);\n    const isDevClient = query['choice'] === 'expo-dev-client';\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n    const runtime = isDevClient ? 'custom' : 'expo';\n\n    debug(`props:`, { platform, runtime });\n\n    this.options.onDeepLink?.({ runtime, platform });\n\n    const redirect = this.options.getLocation({ runtime });\n    if (!redirect) {\n      Log.warn(\n        `[redirect middleware]: Unable to determine redirect location for runtime '${runtime}' and platform '${platform}'`\n      );\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n    debug('Redirect ->', redirect);\n    res.setHeader('Location', redirect);\n\n    // Disable caching\n    disableResponseCache(res);\n\n    // 'Temporary Redirect'\n    res.statusCode = 307;\n    res.end();\n  }\n}\n"], "names": ["RuntimeRedirectMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "options", "handleRequestAsync", "req", "res", "query", "parse", "url", "isDevClient", "platform", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "assertRuntimePlatform", "runtime", "onDeepLink", "redirect", "getLocation", "Log", "warn", "statusCode", "end", "<PERSON><PERSON><PERSON><PERSON>", "disableResponseCache"], "mappings": ";;;;+BAyBaA;;;eAAAA;;;;yBAzBS;;;;;;gCAE+B;iCAO9C;6DAEc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SACpB;AAWK,MAAMF,kCAAkCG,8BAAc;IAC3DC,YACE,AAAUC,WAAmB,EAC7B,AAAUC,OAGT,CACD;QACA,KAAK,CAACD,aAAa;YAAC;SAAc,QANxBA,cAAAA,kBACAC,UAAAA;IAMZ;IAEA,MAAMC,mBAAmBC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/E,MAAM,EAAEC,KAAK,EAAE,GAAGC,IAAAA,YAAK,EAACH,IAAII,GAAG,EAAG;QAClC,MAAMC,cAAcH,KAAK,CAAC,SAAS,KAAK;QACxC,MAAMI,WAAWC,IAAAA,oCAAmB,EAACP,QAAQQ,IAAAA,mDAAkC,EAACR;QAChFS,IAAAA,6CAA4B,EAACH;QAC7BI,IAAAA,sCAAqB,EAACJ;QACtB,MAAMK,UAAUN,cAAc,WAAW;QAEzCZ,MAAM,CAAC,MAAM,CAAC,EAAE;YAAEa;YAAUK;QAAQ;QAEpC,IAAI,CAACb,OAAO,CAACc,UAAU,oBAAvB,IAAI,CAACd,OAAO,CAACc,UAAU,MAAvB,IAAI,CAACd,OAAO,EAAc;YAAEa;YAASL;QAAS;QAE9C,MAAMO,WAAW,IAAI,CAACf,OAAO,CAACgB,WAAW,CAAC;YAAEH;QAAQ;QACpD,IAAI,CAACE,UAAU;YACbE,KAAIC,IAAI,CACN,CAAC,0EAA0E,EAAEL,QAAQ,gBAAgB,EAAEL,SAAS,CAAC,CAAC;YAEpHL,IAAIgB,UAAU,GAAG;YACjBhB,IAAIiB,GAAG;YACP;QACF;QACAzB,MAAM,eAAeoB;QACrBZ,IAAIkB,SAAS,CAAC,YAAYN;QAE1B,kBAAkB;QAClBO,IAAAA,oCAAoB,EAACnB;QAErB,uBAAuB;QACvBA,IAAIgB,UAAU,GAAG;QACjBhB,IAAIiB,GAAG;IACT;AACF"}