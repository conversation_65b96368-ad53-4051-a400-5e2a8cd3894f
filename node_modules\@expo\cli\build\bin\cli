#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _arg() {
    const data = /*#__PURE__*/ _interop_require_default(require("arg"));
    _arg = function() {
        return data;
    };
    return data;
}
function _chalk() {
    const data = /*#__PURE__*/ _interop_require_default(require("chalk"));
    _chalk = function() {
        return data;
    };
    return data;
}
function _debug() {
    const data = /*#__PURE__*/ _interop_require_default(require("debug"));
    _debug = function() {
        return data;
    };
    return data;
}
function _getenv() {
    const data = require("getenv");
    _getenv = function() {
        return data;
    };
    return data;
}
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
// Setup before requiring `debug`.
if ((0, _getenv().boolish)('EXPO_DEBUG', false)) {
    _debug().default.enable('expo:*');
} else if (_debug().default.enabled('expo:')) {
    process.env.EXPO_DEBUG = '1';
}
const defaultCmd = 'start';
const commands = {
    // Add a new command here
    // NOTE(EvanBacon): Ensure every bundler-related command sets `NODE_ENV` as expected for the command.
    run: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/run/index.js"))).then((i)=>i.expoRun),
    'run:ios': ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/run/ios/index.js"))).then((i)=>i.expoRunIos),
    'run:android': ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/run/android/index.js"))).then((i)=>i.expoRunAndroid),
    start: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/start/index.js"))).then((i)=>i.expoStart),
    prebuild: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/prebuild/index.js"))).then((i)=>i.expoPrebuild),
    config: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/config/index.js"))).then((i)=>i.expoConfig),
    export: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/export/index.js"))).then((i)=>i.expoExport),
    'export:web': ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/export/web/index.js"))).then((i)=>i.expoExportWeb),
    'export:embed': ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/export/embed/index.js"))).then((i)=>i.expoExportEmbed),
    serve: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/serve/index.js"))).then((i)=>i.expoServe),
    // Auxiliary commands
    install: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/install/index.js"))).then((i)=>i.expoInstall),
    add: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/install/index.js"))).then((i)=>i.expoInstall),
    customize: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/customize/index.js"))).then((i)=>i.expoCustomize),
    lint: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/lint/index.js"))).then((i)=>i.expoLint),
    // Auth
    login: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/login/index.js"))).then((i)=>i.expoLogin),
    logout: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/logout/index.js"))).then((i)=>i.expoLogout),
    register: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/register/index.js"))).then((i)=>i.expoRegister),
    whoami: ()=>Promise.resolve().then(()=>/*#__PURE__*/ _interop_require_wildcard(require("../src/whoami/index.js"))).then((i)=>i.expoWhoami)
};
const args = (0, _arg().default)({
    // Types
    '--version': Boolean,
    '--help': Boolean,
    // NOTE(EvanBacon): This is here to silence warnings from processes that
    // expect the global expo-cli.
    '--non-interactive': Boolean,
    // Aliases
    '-v': '--version',
    '-h': '--help'
}, {
    permissive: true
});
if (args['--version']) {
    // Version is added in the build script.
    console.log("0.24.13");
    process.exit(0);
}
if (args['--non-interactive']) {
    console.warn(_chalk().default.yellow`  {bold --non-interactive} is not supported, use {bold $CI=1} instead`);
}
// Check if we are running `npx expo <subcommand>` or `npx expo`
const isSubcommand = Boolean(commands[args._[0]]);
// Handle `--help` flag
if (!isSubcommand && args['--help']) {
    const { login, logout, whoami, register, start, install, add, export: _export, config, customize, prebuild, 'run:ios': runIos, 'run:android': runAndroid, // NOTE(EvanBacon): Don't document this command as it's a temporary
    // workaround until we can use `expo export` for all production bundling.
    // https://github.com/expo/expo/pull/21396/files#r1121025873
    'export:embed': exportEmbed_unused, // The export:web command is deprecated. Hide it from the help prompt.
    'export:web': exportWeb_unused, // Other ignored commands, these are intentially not listed in the `--help` output
    run: _run, // NOTE(cedric): Still pending the migration to ESLint's flat config
    lint: _lint, serve, // All other commands
    ...others } = commands;
    console.log((0, _chalk().default)`
  {bold Usage}
    {dim $} npx expo <command>

  {bold Commands}
    ${Object.keys({
        start,
        export: _export,
        ...others
    }).join(', ')}
    ${Object.keys({
        'run:ios': runIos,
        'run:android': runAndroid,
        prebuild
    }).join(', ')}
    ${Object.keys({
        install,
        customize,
        config,
        serve
    }).join(', ')}
    {dim ${Object.keys({
        login,
        logout,
        whoami,
        register
    }).join(', ')}}

  {bold Options}
    --version, -v   Version number
    --help, -h      Usage info

  For more info run a command with the {bold --help} flag
    {dim $} npx expo start --help
`);
    process.exit(0);
}
// NOTE(EvanBacon): Squat some directory names to help with migration,
// users can still use folders named "send" or "eject" by using the fully qualified `npx expo start ./send`.
if (!isSubcommand) {
    const migrationMap = {
        init: 'npx create-expo-app',
        eject: 'npx expo prebuild',
        web: 'npx expo start --web',
        'start:web': 'npx expo start --web',
        'build:ios': 'eas build -p ios',
        'build:android': 'eas build -p android',
        'client:install:ios': 'npx expo start --ios',
        'client:install:android': 'npx expo start --android',
        doctor: 'npx expo-doctor',
        upgrade: 'https://docs.expo.dev/workflow/upgrading-expo-sdk-walkthrough/',
        'customize:web': 'npx expo customize',
        publish: 'eas update',
        'publish:set': 'eas update',
        'publish:rollback': 'eas update',
        'publish:history': 'eas update',
        'publish:details': 'eas update',
        'build:web': 'npx expo export:web',
        'credentials:manager': `eas credentials`,
        'fetch:ios:certs': `eas credentials`,
        'fetch:android:keystore': `eas credentials`,
        'fetch:android:hashes': `eas credentials`,
        'fetch:android:upload-cert': `eas credentials`,
        'push:android:upload': `eas credentials`,
        'push:android:show': `eas credentials`,
        'push:android:clear': `eas credentials`,
        url: `eas build:list`,
        'url:ipa': `eas build:list`,
        'url:apk': `eas build:list`,
        webhooks: `eas webhook`,
        'webhooks:add': `eas webhook:create`,
        'webhooks:remove': `eas webhook:delete`,
        'webhooks:update': `eas webhook:update`,
        'build:status': `eas build:list`,
        'upload:android': `eas submit -p android`,
        'upload:ios': `eas submit -p ios`
    };
    // TODO: Log telemetry about invalid command used.
    const subcommand = args._[0];
    if (subcommand in migrationMap) {
        const replacement = migrationMap[subcommand];
        console.log();
        const instruction = subcommand === 'upgrade' ? 'follow this guide' : 'use';
        console.log(_chalk().default.yellow`  {gray $} {bold expo ${subcommand}} is not supported in the local CLI, please ${instruction} {bold ${replacement}} instead`);
        console.log();
        process.exit(1);
    }
    const deprecated = [
        'send',
        'client:ios'
    ];
    if (deprecated.includes(subcommand)) {
        console.log();
        console.log(_chalk().default.yellow`  {gray $} {bold expo ${subcommand}} is deprecated`);
        console.log();
        process.exit(1);
    }
}
const command = isSubcommand ? args._[0] : defaultCmd;
const commandArgs = isSubcommand ? args._.slice(1) : args._;
// Push the help flag to the subcommand args.
if (args['--help']) {
    commandArgs.push('--help');
}
// Install exit hooks
process.on('SIGINT', ()=>process.exit(0));
process.on('SIGTERM', ()=>process.exit(0));
commands[command]().then((exec)=>{
    exec(commandArgs);
    // NOTE(EvanBacon): Track some basic telemetry events indicating the command
    // that was run. This can be disabled with the $EXPO_NO_TELEMETRY environment variable.
    // We do this to determine how well deprecations are going before removing a command.
    if (!(0, _getenv().boolish)('EXPO_NO_TELEMETRY', false)) {
        const { recordCommand } = require('../src/utils/telemetry');
        recordCommand(command);
    }
});

//# sourceMappingURL=cli.map