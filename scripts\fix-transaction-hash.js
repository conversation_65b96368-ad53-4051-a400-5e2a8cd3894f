const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
});

async function fixTransactionHashConstraint() {
  try {
    console.log('🔧 Исправление ограничения NOT NULL для transaction_hash...');
    
    // Делаем поле transaction_hash необязательным
    await pool.query(`
      ALTER TABLE control_transactions 
      ALTER COLUMN transaction_hash DROP NOT NULL;
    `);
    
    console.log('✅ Ограничение NOT NULL для transaction_hash удалено');
    
    // Проверяем структуру таблицы
    const result = await pool.query(`
      SELECT column_name, is_nullable, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'control_transactions' 
      AND column_name = 'transaction_hash';
    `);
    
    console.log('📋 Текущая структура поля transaction_hash:', result.rows[0]);
    
    await pool.end();
    console.log('🎉 Исправление завершено!');
  } catch (error) {
    console.error('❌ Ошибка:', error);
    process.exit(1);
  }
}

fixTransactionHashConstraint();
