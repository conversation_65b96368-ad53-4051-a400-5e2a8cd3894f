-- Миграция для универсального смарт-контракта
-- Обновляет схему базы данных для работы с единым контрактом

-- 1. Создание таблицы для универсального контракта
CREATE TABLE IF NOT EXISTS universal_contract (
    id SERIAL PRIMARY KEY,
    contract_address VARCHAR(255) UNIQUE NOT NULL,
    platform_address VARCHAR(255) NOT NULL,
    platform_fee_percent INTEGER DEFAULT 500,
    total_platform_fees DECIMAL(18,9) DEFAULT 0,
    total_volume DECIMAL(18,9) DEFAULT 0,
    is_deployed BOOLEAN DEFAULT false,
    deployed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. Обновление таблицы стримеров для работы с универсальным контрактом
ALTER TABLE streamers 
ADD COLUMN IF NOT EXISTS accumulated_balance DECIMAL(18,9) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_balance_sync TIMESTAMP;

-- 3. Обновление таблицы транзакций для поддержки memo
ALTER TABLE control_transactions 
ADD COLUMN IF NOT EXISTS memo VARCHAR(500),
ADD COLUMN IF NOT EXISTS contract_address VARCHAR(255),
ADD COLUMN IF NOT EXISTS streamer_address VARCHAR(255);

-- 4. Создание индексов для оптимизации
CREATE INDEX IF NOT EXISTS idx_control_transactions_memo ON control_transactions(memo);
CREATE INDEX IF NOT EXISTS idx_control_transactions_streamer ON control_transactions(streamer_address);
CREATE INDEX IF NOT EXISTS idx_streamers_wallet ON streamers(wallet_address);

-- 5. Обновление таблицы заработков стримеров
ALTER TABLE streamer_earnings 
ADD COLUMN IF NOT EXISTS contract_type VARCHAR(50) DEFAULT 'universal',
ADD COLUMN IF NOT EXISTS memo VARCHAR(500);

-- 6. Создание таблицы для отслеживания активных сессий
CREATE TABLE IF NOT EXISTS active_sessions (
    id SERIAL PRIMARY KEY,
    streamer_address VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    controller_address VARCHAR(255) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    duration_minutes INTEGER NOT NULL,
    amount_paid DECIMAL(18,9) NOT NULL,
    memo VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(streamer_address, device_id, start_time)
);

-- 7. Создание индексов для активных сессий
CREATE INDEX IF NOT EXISTS idx_active_sessions_streamer_device ON active_sessions(streamer_address, device_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_controller ON active_sessions(controller_address);
CREATE INDEX IF NOT EXISTS idx_active_sessions_active ON active_sessions(is_active, end_time);

-- 8. Создание таблицы для настроек устройств в универсальном контракте
CREATE TABLE IF NOT EXISTS device_settings (
    id SERIAL PRIMARY KEY,
    streamer_address VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    price_per_minute DECIMAL(18,9) NOT NULL DEFAULT 1.0,
    is_active BOOLEAN DEFAULT true,
    memo VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(streamer_address, device_id)
);

-- 9. Создание индексов для настроек устройств
CREATE INDEX IF NOT EXISTS idx_device_settings_streamer ON device_settings(streamer_address);
CREATE INDEX IF NOT EXISTS idx_device_settings_memo ON device_settings(memo);

-- 10. Создание таблицы для мониторинга транзакций универсального контракта
CREATE TABLE IF NOT EXISTS universal_contract_transactions (
    id SERIAL PRIMARY KEY,
    transaction_hash VARCHAR(255) UNIQUE NOT NULL,
    transaction_type VARCHAR(50) NOT NULL, -- 'buy_time', 'withdraw_earnings', 'withdraw_platform_fee'
    from_address VARCHAR(255),
    to_address VARCHAR(255),
    amount DECIMAL(18,9),
    memo VARCHAR(500),
    block_number BIGINT,
    transaction_time TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'confirmed', 'failed'
    processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 11. Создание индексов для мониторинга транзакций
CREATE INDEX IF NOT EXISTS idx_universal_transactions_hash ON universal_contract_transactions(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_universal_transactions_type ON universal_contract_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_universal_transactions_status ON universal_contract_transactions(status);
CREATE INDEX IF NOT EXISTS idx_universal_transactions_memo ON universal_contract_transactions(memo);
CREATE INDEX IF NOT EXISTS idx_universal_transactions_processed ON universal_contract_transactions(processed);

-- 12. Создание таблицы для логов платформы
CREATE TABLE IF NOT EXISTS platform_logs (
    id SERIAL PRIMARY KEY,
    log_type VARCHAR(50) NOT NULL, -- 'contract_deploy', 'earnings_withdrawal', 'fee_withdrawal', 'error'
    message TEXT NOT NULL,
    data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 13. Создание индексов для логов
CREATE INDEX IF NOT EXISTS idx_platform_logs_type ON platform_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_platform_logs_created ON platform_logs(created_at);

-- 14. Создание функции для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 15. Создание триггеров для автоматического обновления updated_at
CREATE TRIGGER update_universal_contract_updated_at 
    BEFORE UPDATE ON universal_contract 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_device_settings_updated_at 
    BEFORE UPDATE ON device_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_universal_contract_transactions_updated_at 
    BEFORE UPDATE ON universal_contract_transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 16. Вставка начальной записи для универсального контракта (если не существует)
INSERT INTO universal_contract (
    contract_address, 
    platform_address, 
    platform_fee_percent,
    is_deployed
) 
SELECT 
    'pending_deployment',
    'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t', -- Замените на ваш адрес
    500,
    false
WHERE NOT EXISTS (SELECT 1 FROM universal_contract);

-- 17. Создание представления для статистики платформы
CREATE OR REPLACE VIEW platform_statistics AS
SELECT 
    uc.contract_address,
    uc.platform_fee_percent,
    uc.total_platform_fees,
    uc.total_volume,
    COUNT(DISTINCT ds.streamer_address) as total_streamers,
    COUNT(DISTINCT ds.device_id) as total_devices,
    COUNT(DISTINCT CASE WHEN as_table.is_active THEN as_table.id END) as active_sessions,
    SUM(CASE WHEN uct.transaction_type = 'buy_time' AND uct.status = 'confirmed' THEN uct.amount ELSE 0 END) as confirmed_volume,
    COUNT(CASE WHEN uct.transaction_type = 'buy_time' AND uct.status = 'confirmed' THEN 1 END) as confirmed_transactions
FROM universal_contract uc
LEFT JOIN device_settings ds ON ds.is_active = true
LEFT JOIN active_sessions as_table ON as_table.is_active = true AND as_table.end_time > NOW()
LEFT JOIN universal_contract_transactions uct ON uct.status = 'confirmed'
GROUP BY uc.id, uc.contract_address, uc.platform_fee_percent, uc.total_platform_fees, uc.total_volume;

-- 18. Создание представления для активных устройств
CREATE OR REPLACE VIEW active_devices_view AS
SELECT 
    ds.streamer_address,
    s.nickname as streamer_nickname,
    ds.device_id,
    ds.price_per_minute,
    ds.memo,
    CASE 
        WHEN as_table.end_time > NOW() THEN true 
        ELSE false 
    END as is_currently_controlled,
    as_table.controller_address as current_controller,
    as_table.end_time as control_end_time,
    ds.created_at as device_added_at
FROM device_settings ds
LEFT JOIN streamers s ON s.wallet_address = ds.streamer_address
LEFT JOIN active_sessions as_table ON as_table.streamer_address = ds.streamer_address 
    AND as_table.device_id = ds.device_id 
    AND as_table.is_active = true 
    AND as_table.end_time > NOW()
WHERE ds.is_active = true
ORDER BY ds.created_at DESC;

-- 19. Комментарии к таблицам
COMMENT ON TABLE universal_contract IS 'Информация об универсальном смарт-контракте';
COMMENT ON TABLE active_sessions IS 'Активные сессии управления устройствами';
COMMENT ON TABLE device_settings IS 'Настройки устройств в универсальном контракте';
COMMENT ON TABLE universal_contract_transactions IS 'Транзакции универсального контракта';
COMMENT ON TABLE platform_logs IS 'Логи платформы';

-- 20. Успешное завершение миграции
INSERT INTO platform_logs (log_type, message, data) 
VALUES ('migration', 'Universal contract migration completed successfully', 
        jsonb_build_object('timestamp', NOW(), 'version', '1.0'));

-- Вывод информации о завершении
SELECT 'Universal contract migration completed successfully!' as status;
