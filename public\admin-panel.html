<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Админ-панель платформы</title>
    <script src="https://unpkg.com/@tonconnect/ui@latest/dist/tonconnect-ui.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-card h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .wallet-info {
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .wallet-address {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
            margin: 10px 0;
        }

        .earnings-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 15px 0;
        }

        .earnings-amount {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }

        .device-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .device-item {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-item:last-child {
            border-bottom: none;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .device-earnings {
            color: #28a745;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ Админ-панель платформы</h1>
            <p>Управление комиссиями и статистикой платформы</p>
        </div>

        <div class="content">
            <!-- Подключение кошелька -->
            <div class="section">
                <h2>🔗 Подключение кошелька администратора</h2>
                <div id="wallet-connection">
                    <div id="ton-connect"></div>
                    <div id="wallet-info" class="wallet-info" style="display: none;">
                        <h3>Подключенный кошелек:</h3>
                        <div id="wallet-address" class="wallet-address"></div>
                        <p><strong>Статус:</strong> <span id="wallet-status">Подключен</span></p>
                    </div>
                </div>
            </div>

            <!-- Статистика платформы -->
            <div class="section">
                <h2>📊 Статистика платформы</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Общая комиссия</h3>
                        <div class="stat-value" id="total-platform-fee">0</div>
                        <div class="stat-label">TON</div>
                    </div>
                    <div class="stat-card">
                        <h3>Активных устройств</h3>
                        <div class="stat-value" id="active-devices">0</div>
                        <div class="stat-label">устройств</div>
                    </div>
                    <div class="stat-card">
                        <h3>Всего стримеров</h3>
                        <div class="stat-value" id="total-streamers">0</div>
                        <div class="stat-label">пользователей</div>
                    </div>
                    <div class="stat-card">
                        <h3>Транзакций сегодня</h3>
                        <div class="stat-value" id="transactions-today">0</div>
                        <div class="stat-label">операций</div>
                    </div>
                </div>
            </div>

            <!-- Вывод комиссии -->
            <div class="section">
                <h2>💰 Управление комиссиями</h2>
                <div id="platform-earnings">
                    <div class="loading">Загрузка данных о комиссиях...</div>
                </div>
                <button class="btn btn-success" onclick="withdrawAllPlatformFees()">
                    💸 Вывести все накопленные комиссии
                </button>
                <button class="btn btn-warning" onclick="refreshPlatformStats()">
                    🔄 Обновить статистику
                </button>
            </div>

            <!-- Список устройств с комиссиями -->
            <div class="section">
                <h2>🎮 Устройства с накопленными комиссиями</h2>
                <div id="devices-with-fees" class="device-list">
                    <div class="loading">Загрузка списка устройств...</div>
                </div>
            </div>

            <!-- Управление платформой -->
            <div class="section">
                <h2>⚙️ Управление платформой</h2>
                <button class="btn btn-warning" onclick="updatePlatformSettings()">
                    🔧 Настройки платформы
                </button>
                <button class="btn" onclick="exportStatistics()">
                    📊 Экспорт статистики
                </button>
                <button class="btn btn-danger" onclick="emergencyStop()">
                    🚨 Экстренная остановка
                </button>
            </div>

            <!-- Сообщения -->
            <div id="messages"></div>
        </div>
    </div>

    <script src="/js/smart-contract-manager.js"></script>
    <script>
        let tonConnectUI;
        let connectedWallet = null;
        let platformStats = {};

        // Инициализация TON Connect
        async function initTonConnect() {
            tonConnectUI = new TON_CONNECT_UI.TonConnectUI({
                manifestUrl: '/tonconnect-manifest.json',
                buttonRootId: 'ton-connect'
            });

            tonConnectUI.onStatusChange(wallet => {
                if (wallet) {
                    connectedWallet = wallet;
                    showWalletInfo(wallet);
                    loadPlatformData();
                } else {
                    connectedWallet = null;
                    hideWalletInfo();
                }
            });
        }

        function showWalletInfo(wallet) {
            document.getElementById('wallet-address').textContent = wallet.account.address;
            document.getElementById('wallet-info').style.display = 'block';
        }

        function hideWalletInfo() {
            document.getElementById('wallet-info').style.display = 'none';
        }

        // Загрузка данных платформы
        async function loadPlatformData() {
            if (!connectedWallet) {
                showMessage('Подключите кошелек администратора', 'error');
                return;
            }

            try {
                // Загрузка статистики платформы
                const statsResponse = await fetch('/api/transactions/platform/stats');
                const statsData = await statsResponse.json();
                
                if (statsData.success) {
                    updatePlatformStats(statsData.data);
                }

                // Загрузка устройств с комиссиями
                await loadDevicesWithFees();
                
            } catch (error) {
                console.error('Ошибка загрузки данных:', error);
                showMessage('Ошибка загрузки данных платформы', 'error');
            }
        }

        function updatePlatformStats(data) {
            platformStats = data;
            
            document.getElementById('total-platform-fee').textContent = 
                (data.totalStats?.total_platform_fees || 0).toFixed(4);
            document.getElementById('active-devices').textContent = 
                data.totalStats?.active_devices || 0;
            document.getElementById('total-streamers').textContent = 
                data.totalStats?.total_streamers || 0;
            document.getElementById('transactions-today').textContent = 
                data.dailyStats?.transactions_count || 0;
        }

        async function loadDevicesWithFees() {
            try {
                const response = await fetch('/api/transactions/platform/devices-with-fees');
                const data = await response.json();
                
                if (data.success) {
                    displayDevicesWithFees(data.data.devices);
                }
            } catch (error) {
                console.error('Ошибка загрузки устройств:', error);
            }
        }

        function displayDevicesWithFees(devices) {
            const container = document.getElementById('devices-with-fees');
            
            if (devices.length === 0) {
                container.innerHTML = '<div class="loading">Нет устройств с накопленными комиссиями</div>';
                return;
            }

            let html = '';
            let totalFees = 0;

            devices.forEach(device => {
                const fees = parseFloat(device.accumulated_platform_fee || 0);
                totalFees += fees;
                
                html += `
                    <div class="device-item">
                        <div class="device-info">
                            <div class="device-name">${device.device_name || device.device_id}</div>
                            <div>Стример: ${device.streamer_nickname || 'Неизвестен'}</div>
                        </div>
                        <div class="device-earnings">${fees.toFixed(4)} TON</div>
                        <button class="btn btn-success" onclick="withdrawDeviceFee('${device.device_id}')">
                            Вывести
                        </button>
                    </div>
                `;
            });

            container.innerHTML = html;

            // Обновляем отображение общих комиссий
            document.getElementById('platform-earnings').innerHTML = `
                <div class="earnings-display">
                    <div class="earnings-amount">${totalFees.toFixed(4)} TON</div>
                    <div>Общая накопленная комиссия</div>
                </div>
            `;
        }

        // Вывод всех комиссий платформы
        async function withdrawAllPlatformFees() {
            if (!connectedWallet) {
                showMessage('Подключите кошелек администратора', 'error');
                return;
            }

            try {
                showMessage('Инициация вывода всех комиссий...', 'info');
                
                const response = await fetch('/api/transactions/platform/withdraw-all-fees', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        admin_wallet: connectedWallet.account.address
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage('Вывод комиссий инициирован успешно!', 'success');
                    setTimeout(() => {
                        loadPlatformData();
                    }, 2000);
                } else {
                    showMessage(`Ошибка: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Ошибка вывода комиссий:', error);
                showMessage('Ошибка при выводе комиссий', 'error');
            }
        }

        // Вывод комиссии конкретного устройства
        async function withdrawDeviceFee(deviceId) {
            if (!connectedWallet) {
                showMessage('Подключите кошелек администратора', 'error');
                return;
            }

            try {
                const response = await fetch('/api/transactions/platform/withdraw-device-fee', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_id: deviceId,
                        admin_wallet: connectedWallet.account.address
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage(`Комиссия с устройства ${deviceId} выведена!`, 'success');
                    setTimeout(() => {
                        loadPlatformData();
                    }, 2000);
                } else {
                    showMessage(`Ошибка: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('Ошибка вывода комиссии:', error);
                showMessage('Ошибка при выводе комиссии', 'error');
            }
        }

        function refreshPlatformStats() {
            loadPlatformData();
            showMessage('Статистика обновлена', 'success');
        }

        function updatePlatformSettings() {
            showMessage('Функция настроек в разработке', 'info');
        }

        function exportStatistics() {
            const data = JSON.stringify(platformStats, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `platform-stats-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            showMessage('Статистика экспортирована', 'success');
        }

        function emergencyStop() {
            if (confirm('Вы уверены, что хотите остановить все операции платформы?')) {
                showMessage('Функция экстренной остановки в разработке', 'warning');
            }
        }

        function showMessage(message, type = 'info') {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messagesContainer.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', () => {
            initTonConnect();
        });
    </script>
</body>
</html>
