{"version": 3, "sources": ["../../../../../src/start/platforms/ios/ensureSimulatorAppRunning.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport spawnAsync from '@expo/spawn-async';\n\nimport { Device } from './simctl';\nimport * as Log from '../../../log';\nimport { waitForActionAsync } from '../../../utils/delay';\nimport { CommandError } from '../../../utils/errors';\n\n/** Open the Simulator.app and return when the system registers it as 'open'. */\nexport async function ensureSimulatorAppRunningAsync(\n  device: Partial<Pick<Device, 'udid'>>,\n  {\n    maxWaitTime,\n  }: {\n    maxWaitTime?: number;\n  } = {}\n): Promise<void> {\n  if (await isSimulatorAppRunningAsync()) {\n    return;\n  }\n\n  Log.log(`\\u203A Opening the iOS simulator, this might take a moment.`);\n\n  // In theory this would ensure the correct simulator is booted as well.\n  // This isn't theory though, this is Xcode.\n  await openSimulatorAppAsync(device);\n\n  if (!(await waitForSimulatorAppToStart({ maxWaitTime }))) {\n    throw new CommandError(\n      'SIMULATOR_TIMEOUT',\n      `Simulator app did not open fast enough. Try opening Simulator first, then running your app.`\n    );\n  }\n}\n\nasync function waitForSimulatorAppToStart({\n  maxWaitTime,\n}: { maxWaitTime?: number } = {}): Promise<boolean> {\n  return waitForActionAsync<boolean>({\n    interval: 50,\n    maxWaitTime,\n    action: isSimulatorAppRunningAsync,\n  });\n}\n\n// I think the app can be open while no simulators are booted.\nasync function isSimulatorAppRunningAsync(): Promise<boolean> {\n  try {\n    const zeroMeansNo = (\n      await osascript.execAsync(\n        'tell app \"System Events\" to count processes whose name is \"Simulator\"'\n      )\n    ).trim();\n    if (zeroMeansNo === '0') {\n      return false;\n    }\n  } catch (error: any) {\n    if (error.message.includes('Application isn’t running')) {\n      return false;\n    }\n    throw error;\n  }\n\n  return true;\n}\n\nasync function openSimulatorAppAsync(device: { udid?: string }) {\n  const args = ['-a', 'Simulator'];\n  if (device.udid) {\n    // This has no effect if the app is already running.\n    args.push('--args', '-CurrentDeviceUDID', device.udid);\n  }\n  await spawnAsync('open', args);\n}\n"], "names": ["ensureSimulatorAppRunningAsync", "device", "maxWaitTime", "isSimulatorAppRunningAsync", "Log", "log", "openSimulatorAppAsync", "waitForSimulatorAppToStart", "CommandError", "waitForActionAsync", "interval", "action", "zeroMeansNo", "osascript", "execAsync", "trim", "error", "message", "includes", "args", "udid", "push", "spawnAsync"], "mappings": ";;;;+BASsBA;;;eAAAA;;;;iEATK;;;;;;;gEACJ;;;;;;6DAGF;uBACc;wBACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtB,eAAeA,+BACpBC,MAAqC,EACrC,EACEC,WAAW,EAGZ,GAAG,CAAC,CAAC;IAEN,IAAI,MAAMC,8BAA8B;QACtC;IACF;IAEAC,KAAIC,GAAG,CAAC,CAAC,2DAA2D,CAAC;IAErE,uEAAuE;IACvE,2CAA2C;IAC3C,MAAMC,sBAAsBL;IAE5B,IAAI,CAAE,MAAMM,2BAA2B;QAAEL;IAAY,IAAK;QACxD,MAAM,IAAIM,oBAAY,CACpB,qBACA,CAAC,2FAA2F,CAAC;IAEjG;AACF;AAEA,eAAeD,2BAA2B,EACxCL,WAAW,EACc,GAAG,CAAC,CAAC;IAC9B,OAAOO,IAAAA,yBAAkB,EAAU;QACjCC,UAAU;QACVR;QACAS,QAAQR;IACV;AACF;AAEA,8DAA8D;AAC9D,eAAeA;IACb,IAAI;QACF,MAAMS,cAAc,AAClB,CAAA,MAAMC,aAAUC,SAAS,CACvB,wEACF,EACAC,IAAI;QACN,IAAIH,gBAAgB,KAAK;YACvB,OAAO;QACT;IACF,EAAE,OAAOI,OAAY;QACnB,IAAIA,MAAMC,OAAO,CAACC,QAAQ,CAAC,8BAA8B;YACvD,OAAO;QACT;QACA,MAAMF;IACR;IAEA,OAAO;AACT;AAEA,eAAeV,sBAAsBL,MAAyB;IAC5D,MAAMkB,OAAO;QAAC;QAAM;KAAY;IAChC,IAAIlB,OAAOmB,IAAI,EAAE;QACf,oDAAoD;QACpDD,KAAKE,IAAI,CAAC,UAAU,sBAAsBpB,OAAOmB,IAAI;IACvD;IACA,MAAME,IAAAA,qBAAU,EAAC,QAAQH;AAC3B"}