{"version": 3, "sources": ["../../../../src/run/android/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { Command } from '../../../bin/cli';\nimport * as Log from '../../log';\nimport { assertWithOptionsArgs } from '../../utils/args';\nimport { logCmdError } from '../../utils/errors';\n\nexport const expoRunAndroid: Command = async (argv) => {\n  const rawArgsMap: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--no-build-cache': Boolean,\n    '--no-install': Boolean,\n    '--no-bundler': Boolean,\n    '--variant': String,\n    '--binary': String,\n    '--app-id': String,\n    // Unstable, temporary fallback to disable active archs only behavior\n    // TODO: replace with better fallback option, like free-form passing gradle props\n    '--all-arch': Boolean,\n\n    '--port': Number,\n    // Aliases\n    '-p': '--port',\n\n    '-h': '--help',\n  };\n  const args = assertWithOptionsArgs(rawArgsMap, {\n    argv,\n\n    permissive: true,\n  });\n\n  // '-d' -> '--device': Bo<PERSON>an,\n\n  if (args['--help']) {\n    Log.exit(\n      chalk`\n  {bold Description}\n    Run the native Android app locally\n\n  {bold Usage}\n    $ npx expo run:android <dir>\n\n  {bold Options} \n    --no-build-cache       Clear the native build cache\n    --no-install           Skip installing dependencies\n    --no-bundler           Skip starting the bundler\n    --app-id <appId>       Custom Android application ID to launch.\n    --variant <name>       Build variant or product flavor and build variant. {dim Default: debug}\n    --binary <path>        Path to existing .apk or .aab to install.\n    -d, --device [device]  Device name to run the app on\n    -p, --port <port>      Port to start the dev server on. {dim Default: 8081}\n    -h, --help             Output usage information\n`,\n      0\n    );\n  }\n\n  const { resolveStringOrBooleanArgsAsync } = await import('../../utils/resolveArgs.js');\n  const parsed = await resolveStringOrBooleanArgsAsync(argv ?? [], rawArgsMap, {\n    '--device': Boolean,\n    '-d': '--device',\n  }).catch(logCmdError);\n\n  const { runAndroidAsync } = await import('./runAndroidAsync.js');\n\n  return runAndroidAsync(path.resolve(parsed.projectRoot), {\n    // Parsed options\n    buildCache: !args['--no-build-cache'],\n    install: !args['--no-install'],\n    bundler: !args['--no-bundler'],\n    port: args['--port'],\n    variant: args['--variant'],\n    allArch: args['--all-arch'],\n    binary: args['--binary'],\n    appId: args['--app-id'],\n    // Custom parsed args\n    device: parsed.args['--device'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoRunAndroid", "argv", "rawArgsMap", "Boolean", "String", "Number", "args", "assertWithOptionsArgs", "permissive", "Log", "exit", "chalk", "resolveStringOrBooleanArgsAsync", "parsed", "catch", "logCmdError", "runAndroidAsync", "path", "resolve", "projectRoot", "buildCache", "install", "bundler", "port", "variant", "allArch", "binary", "appId", "device"], "mappings": ";;;;;+BAUaA;;;eAAAA;;;;gEARK;;;;;;;gEACD;;;;;;6DAGI;sBACiB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,iBAA0B,OAAOC;IAC5C,MAAMC,aAAuB;QAC3B,QAAQ;QACR,UAAUC;QACV,oBAAoBA;QACpB,gBAAgBA;QAChB,gBAAgBA;QAChB,aAAaC;QACb,YAAYA;QACZ,YAAYA;QACZ,qEAAqE;QACrE,iFAAiF;QACjF,cAAcD;QAEd,UAAUE;QACV,UAAU;QACV,MAAM;QAEN,MAAM;IACR;IACA,MAAMC,OAAOC,IAAAA,2BAAqB,EAACL,YAAY;QAC7CD;QAEAO,YAAY;IACd;IAEA,+BAA+B;IAE/B,IAAIF,IAAI,CAAC,SAAS,EAAE;QAClBG,KAAIC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC;;;;;;;;;;;;;;;;;AAiBZ,CAAC,EACK;IAEJ;IAEA,MAAM,EAAEC,+BAA+B,EAAE,GAAG,MAAM,mEAAA,QAAO;IACzD,MAAMC,SAAS,MAAMD,gCAAgCX,QAAQ,EAAE,EAAEC,YAAY;QAC3E,YAAYC;QACZ,MAAM;IACR,GAAGW,KAAK,CAACC,mBAAW;IAEpB,MAAM,EAAEC,eAAe,EAAE,GAAG,MAAM,mEAAA,QAAO;IAEzC,OAAOA,gBAAgBC,eAAI,CAACC,OAAO,CAACL,OAAOM,WAAW,GAAG;QACvD,iBAAiB;QACjBC,YAAY,CAACd,IAAI,CAAC,mBAAmB;QACrCe,SAAS,CAACf,IAAI,CAAC,eAAe;QAC9BgB,SAAS,CAAChB,IAAI,CAAC,eAAe;QAC9BiB,MAAMjB,IAAI,CAAC,SAAS;QACpBkB,SAASlB,IAAI,CAAC,YAAY;QAC1BmB,SAASnB,IAAI,CAAC,aAAa;QAC3BoB,QAAQpB,IAAI,CAAC,WAAW;QACxBqB,OAAOrB,IAAI,CAAC,WAAW;QACvB,qBAAqB;QACrBsB,QAAQf,OAAOP,IAAI,CAAC,WAAW;IACjC,GAAGQ,KAAK,CAACC,mBAAW;AACtB"}