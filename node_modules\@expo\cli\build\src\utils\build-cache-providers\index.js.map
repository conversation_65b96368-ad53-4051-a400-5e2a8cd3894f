{"version": 3, "sources": ["../../../../src/utils/build-cache-providers/index.ts"], "sourcesContent": ["import { ExpoConfig, BuildCacheProviderPlugin, BuildCacheProvider, RunOptions } from '@expo/config';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { moduleNameIsDirectFileReference, moduleNameIsPackageReference } from './helpers';\nimport * as Log from '../../log';\nimport { ensureDependenciesAsync } from '../../start/doctor/dependencies/ensureDependenciesAsync';\nimport { CommandError } from '../errors';\n\nconst debug = require('debug')('expo:run:build-cache-provider') as typeof console.log;\n\nexport const resolveBuildCacheProvider = async (\n  provider: Required<Required<ExpoConfig>['experiments']>['buildCacheProvider'] | undefined,\n  projectRoot: string\n): Promise<BuildCacheProvider | undefined> => {\n  if (!provider) {\n    return;\n  }\n\n  if (provider === 'eas') {\n    try {\n      await ensureDependenciesAsync(projectRoot, {\n        isProjectMutable: true,\n        installMessage:\n          'eas-build-cache-provider package is required to use the EAS build cache.\\n',\n        warningMessage: 'Unable to to use the EAS remote build cache.',\n        requiredPackages: [\n          {\n            pkg: 'eas-build-cache-provider',\n            file: 'eas-build-cache-provider/package.json',\n            dev: true,\n          },\n        ],\n      });\n\n      // We need to manually load dependencies installed on the fly\n      const plugin = await manuallyLoadDependency(projectRoot, 'eas-build-cache-provider');\n\n      return {\n        plugin: plugin.default ?? plugin,\n        options: {},\n      };\n    } catch (error: any) {\n      if (error instanceof CommandError) {\n        Log.warn(error.message);\n      } else {\n        throw error;\n      }\n      return undefined;\n    }\n  }\n\n  if (typeof provider === 'object' && typeof provider.plugin === 'string') {\n    const plugin = resolvePluginFunction(projectRoot, provider.plugin);\n\n    return { plugin, options: provider.options };\n  }\n\n  throw new Error('Invalid build cache provider');\n};\n\nexport async function resolveBuildCache({\n  projectRoot,\n  platform,\n  provider,\n  runOptions,\n}: {\n  projectRoot: string;\n  platform: 'android' | 'ios';\n  provider: BuildCacheProvider;\n  runOptions: RunOptions;\n}): Promise<string | null> {\n  const fingerprintHash = await calculateFingerprintHashAsync({\n    projectRoot,\n    platform,\n    provider,\n    runOptions,\n  });\n  if (!fingerprintHash) {\n    return null;\n  }\n\n  if ('resolveRemoteBuildCache' in provider.plugin) {\n    Log.warn('The resolveRemoteBuildCache function is deprecated. Use resolveBuildCache instead.');\n    return await provider.plugin.resolveRemoteBuildCache(\n      { fingerprintHash, platform, runOptions, projectRoot },\n      provider.options\n    );\n  }\n  return await provider.plugin.resolveBuildCache(\n    { fingerprintHash, platform, runOptions, projectRoot },\n    provider.options\n  );\n}\n\nexport async function uploadBuildCache({\n  projectRoot,\n  platform,\n  provider,\n  buildPath,\n  runOptions,\n}: {\n  projectRoot: string;\n  platform: 'android' | 'ios';\n  provider: BuildCacheProvider;\n  buildPath: string;\n  runOptions: RunOptions;\n}): Promise<void> {\n  const fingerprintHash = await calculateFingerprintHashAsync({\n    projectRoot,\n    platform,\n    provider,\n    runOptions,\n  });\n  if (!fingerprintHash) {\n    debug('No fingerprint hash found, skipping upload');\n    return;\n  }\n\n  if ('uploadRemoteBuildCache' in provider.plugin) {\n    Log.warn('The uploadRemoteBuildCache function is deprecated. Use uploadBuildCache instead.');\n    await provider.plugin.uploadRemoteBuildCache(\n      {\n        projectRoot,\n        platform,\n        fingerprintHash,\n        buildPath,\n        runOptions,\n      },\n      provider.options\n    );\n  } else {\n    await provider.plugin.uploadBuildCache(\n      {\n        projectRoot,\n        platform,\n        fingerprintHash,\n        buildPath,\n        runOptions,\n      },\n      provider.options\n    );\n  }\n}\n\nasync function calculateFingerprintHashAsync({\n  projectRoot,\n  platform,\n  provider,\n  runOptions,\n}: {\n  projectRoot: string;\n  platform: 'android' | 'ios';\n  provider: BuildCacheProvider;\n  runOptions: RunOptions;\n}): Promise<string | null> {\n  if (provider.plugin.calculateFingerprintHash) {\n    return await provider.plugin.calculateFingerprintHash(\n      { projectRoot, platform, runOptions },\n      provider.options\n    );\n  }\n\n  const Fingerprint = importFingerprintForDev(projectRoot);\n  if (!Fingerprint) {\n    debug('@expo/fingerprint is not installed in the project, unable to calculate fingerprint');\n    return null;\n  }\n  const fingerprint = await Fingerprint.createFingerprintAsync(projectRoot);\n\n  return fingerprint.hash;\n}\n\nfunction importFingerprintForDev(projectRoot: string): null | typeof import('@expo/fingerprint') {\n  try {\n    return require(require.resolve('@expo/fingerprint', { paths: [projectRoot] }));\n  } catch (error: any) {\n    if ('code' in error && error.code === 'MODULE_NOT_FOUND') {\n      return null;\n    }\n    throw error;\n  }\n}\n\n/**\n * Resolve the provider plugin from a node module or package.\n * If the module or package does not include a provider plugin, this function throws.\n * The resolution is done in following order:\n *   1. Is the reference a relative file path or an import specifier with file path? e.g. `./file.js`, `pkg/file.js` or `@org/pkg/file.js`?\n *     - Resolve the provider plugin as-is\n *   2. Does the module have a valid provider plugin in the `main` field?\n *     - Resolve the `main` entry point as provider plugin\n */\nfunction resolvePluginFilePathForModule(projectRoot: string, pluginReference: string) {\n  if (moduleNameIsDirectFileReference(pluginReference)) {\n    // Only resolve `./file.js`, `package/file.js`, `@org/package/file.js`\n    const pluginScriptFile = resolveFrom.silent(projectRoot, pluginReference);\n    if (pluginScriptFile) {\n      return pluginScriptFile;\n    }\n  } else if (moduleNameIsPackageReference(pluginReference)) {\n    // Try to resole the `main` entry as config plugin\n    return resolveFrom(projectRoot, pluginReference);\n  }\n\n  throw new Error(\n    `Failed to resolve provider plugin for module \"${pluginReference}\" relative to \"${projectRoot}\". Do you have node modules installed?`\n  );\n}\n\n// Resolve the module function and assert type\nexport function resolvePluginFunction(\n  projectRoot: string,\n  pluginReference: string\n): BuildCacheProviderPlugin {\n  const pluginFile = resolvePluginFilePathForModule(projectRoot, pluginReference);\n\n  try {\n    let plugin = require(pluginFile);\n    if (plugin?.default != null) {\n      plugin = plugin.default;\n    }\n\n    if (\n      typeof plugin !== 'object' ||\n      (typeof plugin.resolveRemoteBuildCache !== 'function' &&\n        typeof plugin.resolveBuildCache !== 'function') ||\n      (typeof plugin.uploadRemoteBuildCache !== 'function' &&\n        typeof plugin.uploadBuildCache !== 'function')\n    ) {\n      throw new Error(`\n        The provider plugin \"${pluginReference}\" must export an object containing\n        the resolveBuildCache and uploadBuildCache functions.\n      `);\n    }\n    return plugin;\n  } catch (error) {\n    if (error instanceof SyntaxError) {\n      // Add error linking to the docs of how create a valid provider plugin\n    }\n    throw error;\n  }\n}\n\nasync function manuallyLoadDependency(projectRoot: string, packageName: string) {\n  const possiblePaths = [\n    path.join(projectRoot, 'node_modules'),\n    ...(require.resolve.paths(packageName) ?? []),\n  ];\n  const nodeModulesFolder = possiblePaths?.find((p) => {\n    const packagePath = path.join(p, packageName);\n    return fs.existsSync(packagePath);\n  });\n  if (!nodeModulesFolder) {\n    throw new Error(`Package ${packageName} not found in ${possiblePaths}`);\n  }\n\n  const { main } = await import(path.join(nodeModulesFolder, packageName, 'package.json'));\n  return import(path.join(nodeModulesFolder, packageName, main));\n}\n"], "names": ["resolveBuildCache", "resolveBuildCacheProvider", "resolvePluginFunction", "uploadBuildCache", "debug", "require", "provider", "projectRoot", "ensureDependenciesAsync", "isProjectMutable", "installMessage", "warningMessage", "requiredPackages", "pkg", "file", "dev", "plugin", "manuallyLoadDependency", "default", "options", "error", "CommandError", "Log", "warn", "message", "undefined", "Error", "platform", "runOptions", "fingerprintHash", "calculateFingerprintHashAsync", "resolveRemoteBuildCache", "buildPath", "uploadRemoteBuildCache", "calculateFingerprintHash", "Fingerprint", "importFingerprintForDev", "fingerprint", "createFingerprintAsync", "hash", "resolve", "paths", "code", "resolvePluginFilePathForModule", "pluginReference", "moduleNameIsDirectFileReference", "pluginScriptFile", "resolveFrom", "silent", "moduleNameIsPackageReference", "pluginFile", "SyntaxError", "packageName", "possiblePaths", "path", "join", "nodeModulesFolder", "find", "p", "packagePath", "fs", "existsSync", "main"], "mappings": ";;;;;;;;;;;IA8DsBA,iBAAiB;eAAjBA;;IAlDTC,yBAAyB;eAAzBA;;IAwMGC,qBAAqB;eAArBA;;IApHMC,gBAAgB;eAAhBA;;;;gEA/FP;;;;;;;gEACE;;;;;;;gEACO;;;;;;yBAEsD;6DACzD;yCACmB;wBACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAExB,MAAMJ,4BAA4B,OACvCK,UACAC;IAEA,IAAI,CAACD,UAAU;QACb;IACF;IAEA,IAAIA,aAAa,OAAO;QACtB,IAAI;YACF,MAAME,IAAAA,gDAAuB,EAACD,aAAa;gBACzCE,kBAAkB;gBAClBC,gBACE;gBACFC,gBAAgB;gBAChBC,kBAAkB;oBAChB;wBACEC,KAAK;wBACLC,MAAM;wBACNC,KAAK;oBACP;iBACD;YACH;YAEA,6DAA6D;YAC7D,MAAMC,SAAS,MAAMC,uBAAuBV,aAAa;YAEzD,OAAO;gBACLS,QAAQA,OAAOE,OAAO,IAAIF;gBAC1BG,SAAS,CAAC;YACZ;QACF,EAAE,OAAOC,OAAY;YACnB,IAAIA,iBAAiBC,oBAAY,EAAE;gBACjCC,KAAIC,IAAI,CAACH,MAAMI,OAAO;YACxB,OAAO;gBACL,MAAMJ;YACR;YACA,OAAOK;QACT;IACF;IAEA,IAAI,OAAOnB,aAAa,YAAY,OAAOA,SAASU,MAAM,KAAK,UAAU;QACvE,MAAMA,SAASd,sBAAsBK,aAAaD,SAASU,MAAM;QAEjE,OAAO;YAAEA;YAAQG,SAASb,SAASa,OAAO;QAAC;IAC7C;IAEA,MAAM,IAAIO,MAAM;AAClB;AAEO,eAAe1B,kBAAkB,EACtCO,WAAW,EACXoB,QAAQ,EACRrB,QAAQ,EACRsB,UAAU,EAMX;IACC,MAAMC,kBAAkB,MAAMC,8BAA8B;QAC1DvB;QACAoB;QACArB;QACAsB;IACF;IACA,IAAI,CAACC,iBAAiB;QACpB,OAAO;IACT;IAEA,IAAI,6BAA6BvB,SAASU,MAAM,EAAE;QAChDM,KAAIC,IAAI,CAAC;QACT,OAAO,MAAMjB,SAASU,MAAM,CAACe,uBAAuB,CAClD;YAAEF;YAAiBF;YAAUC;YAAYrB;QAAY,GACrDD,SAASa,OAAO;IAEpB;IACA,OAAO,MAAMb,SAASU,MAAM,CAAChB,iBAAiB,CAC5C;QAAE6B;QAAiBF;QAAUC;QAAYrB;IAAY,GACrDD,SAASa,OAAO;AAEpB;AAEO,eAAehB,iBAAiB,EACrCI,WAAW,EACXoB,QAAQ,EACRrB,QAAQ,EACR0B,SAAS,EACTJ,UAAU,EAOX;IACC,MAAMC,kBAAkB,MAAMC,8BAA8B;QAC1DvB;QACAoB;QACArB;QACAsB;IACF;IACA,IAAI,CAACC,iBAAiB;QACpBzB,MAAM;QACN;IACF;IAEA,IAAI,4BAA4BE,SAASU,MAAM,EAAE;QAC/CM,KAAIC,IAAI,CAAC;QACT,MAAMjB,SAASU,MAAM,CAACiB,sBAAsB,CAC1C;YACE1B;YACAoB;YACAE;YACAG;YACAJ;QACF,GACAtB,SAASa,OAAO;IAEpB,OAAO;QACL,MAAMb,SAASU,MAAM,CAACb,gBAAgB,CACpC;YACEI;YACAoB;YACAE;YACAG;YACAJ;QACF,GACAtB,SAASa,OAAO;IAEpB;AACF;AAEA,eAAeW,8BAA8B,EAC3CvB,WAAW,EACXoB,QAAQ,EACRrB,QAAQ,EACRsB,UAAU,EAMX;IACC,IAAItB,SAASU,MAAM,CAACkB,wBAAwB,EAAE;QAC5C,OAAO,MAAM5B,SAASU,MAAM,CAACkB,wBAAwB,CACnD;YAAE3B;YAAaoB;YAAUC;QAAW,GACpCtB,SAASa,OAAO;IAEpB;IAEA,MAAMgB,cAAcC,wBAAwB7B;IAC5C,IAAI,CAAC4B,aAAa;QAChB/B,MAAM;QACN,OAAO;IACT;IACA,MAAMiC,cAAc,MAAMF,YAAYG,sBAAsB,CAAC/B;IAE7D,OAAO8B,YAAYE,IAAI;AACzB;AAEA,SAASH,wBAAwB7B,WAAmB;IAClD,IAAI;QACF,OAAOF,QAAQA,QAAQmC,OAAO,CAAC,qBAAqB;YAAEC,OAAO;gBAAClC;aAAY;QAAC;IAC7E,EAAE,OAAOa,OAAY;QACnB,IAAI,UAAUA,SAASA,MAAMsB,IAAI,KAAK,oBAAoB;YACxD,OAAO;QACT;QACA,MAAMtB;IACR;AACF;AAEA;;;;;;;;CAQC,GACD,SAASuB,+BAA+BpC,WAAmB,EAAEqC,eAAuB;IAClF,IAAIC,IAAAA,wCAA+B,EAACD,kBAAkB;QACpD,sEAAsE;QACtE,MAAME,mBAAmBC,sBAAW,CAACC,MAAM,CAACzC,aAAaqC;QACzD,IAAIE,kBAAkB;YACpB,OAAOA;QACT;IACF,OAAO,IAAIG,IAAAA,qCAA4B,EAACL,kBAAkB;QACxD,kDAAkD;QAClD,OAAOG,IAAAA,sBAAW,EAACxC,aAAaqC;IAClC;IAEA,MAAM,IAAIlB,MACR,CAAC,8CAA8C,EAAEkB,gBAAgB,eAAe,EAAErC,YAAY,sCAAsC,CAAC;AAEzI;AAGO,SAASL,sBACdK,WAAmB,EACnBqC,eAAuB;IAEvB,MAAMM,aAAaP,+BAA+BpC,aAAaqC;IAE/D,IAAI;QACF,IAAI5B,SAASX,QAAQ6C;QACrB,IAAIlC,CAAAA,0BAAAA,OAAQE,OAAO,KAAI,MAAM;YAC3BF,SAASA,OAAOE,OAAO;QACzB;QAEA,IACE,OAAOF,WAAW,YACjB,OAAOA,OAAOe,uBAAuB,KAAK,cACzC,OAAOf,OAAOhB,iBAAiB,KAAK,cACrC,OAAOgB,OAAOiB,sBAAsB,KAAK,cACxC,OAAOjB,OAAOb,gBAAgB,KAAK,YACrC;YACA,MAAM,IAAIuB,MAAM,CAAC;6BACM,EAAEkB,gBAAgB;;MAEzC,CAAC;QACH;QACA,OAAO5B;IACT,EAAE,OAAOI,OAAO;QACd,IAAIA,iBAAiB+B,aAAa;QAChC,sEAAsE;QACxE;QACA,MAAM/B;IACR;AACF;AAEA,eAAeH,uBAAuBV,WAAmB,EAAE6C,WAAmB;IAC5E,MAAMC,gBAAgB;QACpBC,eAAI,CAACC,IAAI,CAAChD,aAAa;WACnBF,QAAQmC,OAAO,CAACC,KAAK,CAACW,gBAAgB,EAAE;KAC7C;IACD,MAAMI,oBAAoBH,iCAAAA,cAAeI,IAAI,CAAC,CAACC;QAC7C,MAAMC,cAAcL,eAAI,CAACC,IAAI,CAACG,GAAGN;QACjC,OAAOQ,aAAE,CAACC,UAAU,CAACF;IACvB;IACA,IAAI,CAACH,mBAAmB;QACtB,MAAM,IAAI9B,MAAM,CAAC,QAAQ,EAAE0B,YAAY,cAAc,EAAEC,eAAe;IACxE;IAEA,MAAM,EAAES,IAAI,EAAE,GAAG,MAAM,gBAAOR,eAAI,CAACC,IAAI,CAACC,mBAAmBJ,aAAa,mEAAjD;IACvB,OAAO,gBAAOE,eAAI,CAACC,IAAI,CAACC,mBAAmBJ,aAAaU,yDAAjD;AACT"}