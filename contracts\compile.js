const { Cell, beginCell, Address, toNano } = require('@ton/core');
const { compile } = require('@ton/blueprint');
const fs = require('fs');
const path = require('path');

/**
 * Компиляция смарт-контракта
 */
async function compileContract() {
    console.log('🔨 Компиляция смарт-контракта...');
    
    try {
        // Компилируем контракт
        const contractCode = await compile('device_control');
        
        console.log('✅ Контракт успешно скомпилирован');
        console.log('📦 Размер кода:', contractCode.refs.length, 'ячеек');
        
        // Сохраняем скомпилированный код
        const compiledPath = path.join(__dirname, 'compiled');
        if (!fs.existsSync(compiledPath)) {
            fs.mkdirSync(compiledPath);
        }
        
        fs.writeFileSync(
            path.join(compiledPath, 'device_control.boc'),
            contractCode.toBoc()
        );
        
        console.log('💾 Код сохранен в contracts/compiled/device_control.boc');
        
        return contractCode;
    } catch (error) {
        console.error('❌ Ошибка компиляции:', error);
        throw error;
    }
}

/**
 * Создание начальных данных для контракта
 */
function createInitialData(params) {
    const {
        ownerAddress,
        deviceId,
        pricePerMinute,
        platformFeePercent = 500, // 5%
        platformAddress
    } = params;
    
    // Создаем пустой адрес для current_controller
    const emptyAddress = beginCell()
        .storeUint(0, 2) // addr_none
        .endCell()
        .beginParse();
    
    return beginCell()
        .storeAddress(Address.parse(ownerAddress))     // owner_address
        .storeUint(deviceId, 256)                      // device_id (хеш от MAC)
        .storeCoins(toNano(pricePerMinute))           // price_per_minute
        .storeSlice(emptyAddress)                      // current_controller (пустой)
        .storeUint(0, 32)                             // control_end_time (0)
        .storeUint(platformFeePercent, 16)            // platform_fee_percent
        .storeAddress(Address.parse(platformAddress))  // platform_address
        .storeCoins(0)                                // accumulated_earnings (0)
        .storeCoins(0)                                // accumulated_platform_fee (0)
        .storeUint(1, 1)                              // is_active (true)
        .endCell();
}

/**
 * Вычисление адреса контракта
 */
function calculateContractAddress(code, data, workchain = 0) {
    const stateInit = beginCell()
        .storeBit(0) // split_depth
        .storeBit(0) // special
        .storeBit(1) // code
        .storeRef(code)
        .storeBit(1) // data
        .storeRef(data)
        .storeBit(0) // library
        .endCell();
    
    return new Address(workchain, stateInit.hash());
}

/**
 * Создание сообщения для деплоя контракта
 */
function createDeployMessage(code, data, amount = '0.1') {
    const stateInit = beginCell()
        .storeBit(0) // split_depth
        .storeBit(0) // special
        .storeBit(1) // code
        .storeRef(code)
        .storeBit(1) // data
        .storeRef(data)
        .storeBit(0) // library
        .endCell();
    
    const contractAddress = new Address(0, stateInit.hash());
    
    return {
        address: contractAddress.toString(),
        amount: toNano(amount),
        stateInit: stateInit,
        body: beginCell().endCell() // Пустое тело для деплоя
    };
}

/**
 * Хеширование device_id
 */
function hashDeviceId(deviceId) {
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(deviceId).digest();
    return BigInt('0x' + hash.toString('hex'));
}

module.exports = {
    compileContract,
    createInitialData,
    calculateContractAddress,
    createDeployMessage,
    hashDeviceId
};

// Если файл запущен напрямую
if (require.main === module) {
    compileContract()
        .then(() => {
            console.log('🎉 Компиляция завершена успешно!');
            
            // Пример создания контракта
            console.log('\n📋 Пример создания контракта:');
            console.log('const { compileContract, createInitialData, calculateContractAddress } = require("./compile");');
            console.log('');
            console.log('const code = await compileContract();');
            console.log('const data = createInitialData({');
            console.log('  ownerAddress: "EQD...", // Адрес стримера');
            console.log('  deviceId: hashDeviceId("AA:BB:CC:DD:EE:FF"), // MAC устройства');
            console.log('  pricePerMinute: "1.0", // Цена в TON');
            console.log('  platformAddress: "EQD..." // Адрес платформы');
            console.log('});');
            console.log('const address = calculateContractAddress(code, data);');
        })
        .catch(error => {
            console.error('💥 Ошибка:', error);
            process.exit(1);
        });
}
