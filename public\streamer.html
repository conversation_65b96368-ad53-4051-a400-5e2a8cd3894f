<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>🔥 Битва донатов – Стример</title>
  <!-- Подключаем TON Connect UI -->
  <script src="https://unpkg.com/@tonconnect/ui@2.0.11/dist/tonconnect-ui.min.js"></script>
  <style>
    :root {
      --primary: #2196f3;
      --primary-dark: #1976d2;
      --success: #4caf50;
      --success-light: rgba(76, 175, 80, 0.1);
      --error: #f44336;
      --error-light: rgba(244, 67, 54, 0.1);
      --warning: #ff9800;
      --warning-light: rgba(255, 152, 0, 0.1);
      --crown: #ffc107;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-600: #6c757d;
      --radius: 8px;
      --transition: all 0.3s ease;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, sans-serif;
      background: linear-gradient(135deg, #ece9e6 0%, #ffffff 100%);
    }
    #ton-connect {
      position: fixed;
      top: 15px;
      right: 15px;
      z-index: 1000;
    }
    .container {
      max-width: 1400px;
      margin: 20px auto;
      padding: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 15px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
      backdrop-filter: blur(10px);
      display: flex;
      gap: 20px;
      min-height: calc(100vh - 40px);
    }
    .stream-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    #streamer-name {
      font-size: 28px;
      margin: 0;
      color: #333;
    }
    .stream-container {
      position: relative;
      width: 100%;
      aspect-ratio: 16/9;
      display: none;
      border: 4px solid #333;
      border-radius: 10px;
      background-color: black;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
    .stream-container iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 10px;
    }
    #stream-status {
      color: #cc0000;
      font-weight: bold;
    }
    .right-column {
      width: 300px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .box {
      background: white;
      border-radius: var(--radius);
      padding: 15px;
      border: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    }
    .box h2 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: var(--primary-dark);
      padding-bottom: 10px;
      border-bottom: 1px solid var(--gray-200);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    button:disabled {
      background-color: #ccc !important;
      cursor: not-allowed !important;
    }
    .online {
      color: var(--success);
      background: var(--success-light);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    .offline {
      color: var(--error);
      background: var(--error-light);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    .emergency {
      color: var(--warning);
      background: var(--warning-light);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    .control-buttons {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    .control-buttons button {
      flex: 1;
      background: var(--primary);
      color: white;
      border: none;
      padding: 10px;
      border-radius: var(--radius);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .control-buttons button:hover {
      background: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .control-buttons button:active {
      transform: translateY(1px);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Добавляем эффект риппла (волны) при нажатии */
    .control-buttons button::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%, -50%);
      transform-origin: 50% 50%;
    }

    .control-buttons button:focus:not(:active)::after {
      animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
      0% {
        transform: scale(0, 0);
        opacity: 0.5;
      }
      20% {
        transform: scale(25, 25);
        opacity: 0.5;
      }
      100% {
        opacity: 0;
        transform: scale(40, 40);
      }
    }

    /* Анимация нажатия на кнопку */
    .button-pressed {
      transform: scale(0.95) !important;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
      transition: transform 0.1s ease, box-shadow 0.1s ease !important;
    }

    .control-buttons button:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    .control-buttons button:disabled {
      background: var(--gray-300);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .donation-box input {
      width: 91%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid var(--gray-300);
      border-radius: var(--radius);
      font-size: 14px;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .donation-box input:hover {
      border-color: var(--primary);
    }

    .donation-box input:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.25);
    }
    .donation-box button {
      width: 100%;
      background: var(--success);
      color: white;
      border: none;
      padding: 12px;
      border-radius: var(--radius);
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .donation-box button:hover:not(:disabled) {
      background: #43a047;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .donation-box button:active {
      transform: translateY(1px);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Добавляем эффект риппла (волны) при нажатии */
    .donation-box button::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%, -50%);
      transform-origin: 50% 50%;
    }

    .donation-box button:focus:not(:active)::after {
      animation: ripple 0.6s ease-out;
    }

    .donation-box button {
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
    #leader {
      margin: 15px 0 8px 0;
      font-weight: 500;
      font-size: 14px;
    }
    .bet-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: var(--gray-100);
      border-radius: var(--radius);
      margin-bottom: 6px;
      font-size: 13px;
    }
    .bet-item.leader {
      background: rgba(255, 193, 7, 0.1);
      border: 1px solid rgba(255, 193, 7, 0.2);
    }
    .bet-item .crown {
      color: var(--crown);
      margin-right: 8px;
    }
    .bet-item .name {
      flex: 1;
    }
    .bet-item .amount {
      color: var(--primary-dark);
      font-weight: 500;
    }
    .balance-info {
      font-size: 13px;
      color: var(--gray-600);
      background: var(--gray-100);
      padding: 4px 8px;
      border-radius: var(--radius);
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
    #device-select {
      width: 100%;
      padding: 8px;
      margin-bottom: 12px;
      border: 1px solid var(--gray-300);
      border-radius: var(--radius);
      font-size: 14px;
      transition: all 0.2s ease;
      cursor: pointer;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      appearance: none;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
      background-repeat: no-repeat;
      background-position: right 10px center;
      padding-right: 30px;
    }

    #device-select:hover {
      border-color: var(--primary);
      box-shadow: 0 2px 5px rgba(33, 150, 243, 0.2);
    }

    #device-select:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.25);
    }
    .toast-container {
      position: fixed;
      bottom: 20px;
      right: 80px;
      z-index: 1000;
      display: flex;
      flex-direction: column-reverse;
    }
    .toast {
      padding: 12px 20px;
      margin-bottom: 10px;
      border-radius: var(--radius);
      background: white;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      gap: 10px;
      animation: slideInFromBottom 0.3s ease;
    }
    .toast.success {
      border-left: 4px solid var(--success);
    }
    .toast.error {
      border-left: 4px solid var(--error);
    }
    .rpm-info {
      margin-top: 5px;
      font-size: 13px;
      color: var(--success);
      text-align: center;
      background: var(--success-light);
      padding: 4px 8px;
      border-radius: var(--radius);
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .tooltip-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      background: var(--primary);
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      font-size: 11px;
      margin-left: 5px;
      cursor: help;
      position: relative;
    }
    .tooltip-text {
      visibility: hidden;
      width: 220px;
      background-color: #333;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 5px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      transform: translateX(-50%);
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 11px;
      line-height: 1.3;
    }
    .tooltip-icon:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }
    .button-container {
      display: flex;
      flex-direction: column;
      flex: 1;
    }
    @keyframes slideInFromBottom {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    @keyframes slideOutToBottom {
      to { transform: translateY(100%); opacity: 0; }
    }

    /* Стили для режима фиксированного времени */
    .time-price-info {
      background-color: var(--gray-100);
      padding: 10px;
      border-radius: var(--radius);
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      margin: 10px 0;
      color: var(--primary-dark);
    }

    .time-control-status {
      background-color: var(--gray-100);
      padding: 15px;
      border-radius: var(--radius);
      margin: 15px 0;
    }

    .time-remaining {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    .timer {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-dark);
      background-color: white;
      padding: 5px 15px;
      border-radius: var(--radius);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    /* Добавляем анимацию при добавлении времени */
    @keyframes timeAdded {
      0% { color: var(--primary-dark); }
      50% { color: var(--success); transform: scale(1.1); }
      100% { color: var(--primary-dark); }
    }

    .time-added {
      animation: timeAdded 1s ease;
    }

    /* Добавляем анимацию для завершения времени */
    @keyframes timeEnding {
      0% { color: var(--error); }
      50% { color: var(--error); transform: scale(1.2); }
      100% { color: var(--error); }
    }

    .time-ending {
      animation: timeEnding 0.8s ease infinite;
      color: var(--error);
      font-weight: bold;
    }

    #time-control-buyer {
      font-weight: bold;
      color: var(--primary-dark);
    }

    #buy-time-btn {
      background-color: var(--primary);
    }

    #buy-time-btn:hover:not(:disabled) {
      background-color: var(--primary-dark);
    }
  </style>
</head>
<body>
  <div class="toast-container"></div>
  <div id="ton-connect"></div>

  <div class="container">
    <!-- Левая колонка: Стрим -->
    <div class="stream-column">
      <h1 id="streamer-name">Загрузка...</h1>
      <div class="stream-container" id="stream-container">
        <iframe id="stream-frame" allowfullscreen></iframe>
      </div>
      <p id="stream-status">Загрузка трансляции...</p>
    </div>

    <!-- Правая колонка: Управление устройством + Битва донатов -->
    <div class="right-column">
      <div class="box device-box">
        <h2>Управление устройством: <span id="device-type"></span></h2>
        <p id="device-status" class="offline">—</p>
        <label for="device-select">Выберите устройство:</label>
        <select id="device-select">
          <option value="">--Нет подключённых устройств--</option>
        </select>
        <!-- По умолчанию пусть будет пусто (или "Загрузка...") -->
        <div id="control-commands">
          <p>Загрузка команд...</p>
        </div>
      </div>

      <!-- Блок управления донатами -->
      <div class="box donation-box" id="donation-battle-box" style="display: none;">
        <h2>
          Битва донатов
          <span class="balance-info">
            Баланс: <span id="wallet-balance">—</span> TON
          </span>
        </h2>
        <p>Кто больше задонатит, тот управляет устройством!</p>

        <input type="text" id="sponsor-name" placeholder="Ваш ник (или аноним)" />
        <div id="nickname-error"></div>

        <input type="hidden" id="user" readonly />
        <input type="number" id="amount" placeholder="Сумма TON" step="0.1" />
        <button onclick="sendDonation('donation-battle')">💸 Отправить донат</button>

        <h3>Текущий лидер:</h3>
        <p id="leader"></p>
        <div id="bet-history"></div>
      </div>

      <!-- Блок покупки времени -->
      <div class="box donation-box" id="fixed-time-box" style="display: none;">
        <h2>
          Покупка времени управления
          <span class="balance-info">
            Баланс: <span id="wallet-balance-fixed">—</span> TON
          </span>
        </h2>
        <div id="time-control-info">
          <p>Купите время управления устройством!</p>
          <div class="time-price-info">
            <span id="time-minutes">5</span> минут за <span id="time-price">1.0</span> TON
          </div>
        </div>

        <div id="time-control-active" style="display: none;">
          <div class="time-control-status">
            <p>Управляет: <span id="time-control-buyer">—</span></p>
            <div class="time-remaining">
              <span>Осталось времени:</span>
              <div class="timer" id="time-remaining-display">00:00</div>
            </div>
          </div>
        </div>

        <input type="text" id="sponsor-name-fixed" placeholder="Ваш ник (или аноним)" />
        <div id="nickname-error-fixed"></div>

        <input type="hidden" id="user-fixed" readonly />
        <button id="buy-time-btn" onclick="sendDonation('fixed-time')">⏰ Купить время управления</button>
      </div>
    </div>
  </div>

  <script>
    /****************************************
     * Глобальные переменные
     ****************************************/
    let walletAddress = null;
    let currentLeaderWallet = null;
    let lastDonationsLength = 0;
    let streamerNickname = null;
    let controlMode = "donation-battle"; // Режим управления по умолчанию
    let timeControlSettings = {
      timeMinutes: 5,
      priceTon: 1.0,
      timeLeft: 0,
      endTime: 0,
      buyer: null
    };
    let timeUpdateInterval = null;

    // Флаг для отслеживания покупки времени
    let justPurchasedTime = false;

    // Храним ID последней покупки для предотвращения дублирования уведомлений
    let lastPurchaseId = null;

    // Список устройств (заполняется при updateDeviceStatus)
    let globalDeviceData = null;

    // Команды из config/commands.json
    let commandsConfig = null;

    // Текущее состояние
    window.currentDeviceStatus = {
      online: false,
      mac: null
    };

    // Хранилище значений RPM для каждого устройства
    window.deviceRpmValues = {};

    // Хранилище статусов emergency для каждого устройства
    // Загружаем сохраненные статусы из localStorage, если они есть
    try {
      const savedEmergencyStatus = localStorage.getItem('deviceEmergencyStatus');
      window.deviceEmergencyStatus = savedEmergencyStatus ? JSON.parse(savedEmergencyStatus) : {};
    } catch (e) {
      console.error('Ошибка при загрузке статусов emergency:', e);
      window.deviceEmergencyStatus = {};
    }

    /****************************************
     * Инициализация TON Connect
     ****************************************/
    const tonConnectUI = new TON_CONNECT_UI.TonConnectUI({
      manifestUrl: "http://localhost:4000/tonconnect-manifest.json"
    });
    setTimeout(() => {
      tonConnectUI.uiOptions = { buttonRootId: "ton-connect" };
    }, 100);

    tonConnectUI.onStatusChange(async () => {
      if (tonConnectUI.wallet) {
        walletAddress = tonConnectUI.wallet.account.address;
        document.getElementById("user").value = walletAddress;
        document.getElementById("user-fixed").value = walletAddress;
        updateBalance(walletAddress);
        loadSponsorName();
      } else {
        walletAddress = null;
        document.getElementById("user").value = "";
        document.getElementById("user-fixed").value = "";
        document.getElementById("wallet-balance").innerText = "—";
        document.getElementById("wallet-balance-fixed").innerText = "—";
      }
      updateControlsState();
    });

    /****************************************
     * Загрузка страницы
     ****************************************/
    document.addEventListener("DOMContentLoaded", async () => {
      try {
        // Загружаем никнейм пользователя, если он авторизован
        if (tonConnectUI.wallet) {
          walletAddress = tonConnectUI.wallet.account.address;
          document.getElementById("user").value = walletAddress;
          document.getElementById("user-fixed").value = walletAddress;
          updateBalance(walletAddress);
          loadSponsorName();
        }

        // Получаем никнейм стримера из URL
        streamerNickname = getNicknameFromURL();
        if (!streamerNickname) {
          showToast("Не удалось получить никнейм стримера", "error");
          return;
        }

        console.log(`Загрузка страницы для стримера: ${streamerNickname}`);

        // 1) Сначала подгружаем конфиг команд
        await loadCommandsConfig();

        // 2) Загружаем режим управления
        console.log('Загрузка режима управления...');
        await loadControlMode();
        console.log(`Режим управления загружен: ${controlMode}`);

        // Показываем соответствующий блок
        document.getElementById("donation-battle-box").style.display = controlMode === "donation-battle" ? "block" : "none";
        document.getElementById("fixed-time-box").style.display = controlMode === "fixed-time" ? "block" : "none";

        // 3) Загружаем стрим, лидера и устройства
        loadStream();
        if (controlMode === "donation-battle") {
          updateLeader();
        }
        await updateDeviceStatus();
      } catch (error) {
        console.error('Ошибка при инициализации страницы:', error);
        showToast('Ошибка при загрузке страницы', 'error');
      }

      // Вызываем обработчик изменения устройства сразу после загрузки
      onDeviceSelectChange();

      // Дополнительно проверяем статус emergency из localStorage
      const selectedDeviceMAC = document.getElementById('device-select').value;
      if (selectedDeviceMAC && window.deviceEmergencyStatus && window.deviceEmergencyStatus[selectedDeviceMAC] === true) {
        const statusEl = document.getElementById("device-status");
        if (statusEl) {
          statusEl.textContent = "Пауза";
          statusEl.className = "emergency";

          // Обновляем текущее состояние устройства
          if (window.currentDeviceStatus) {
            window.currentDeviceStatus.emergency = true;
          }

          // Обновляем состояние кнопок
          updateControlsState();
        }
      }

      // Обновляем периодически
      if (controlMode === "donation-battle") {
        setInterval(updateLeader, 3000);
      }
      setInterval(updateDeviceStatus, 5000);

      // При смене устройства в <select>
      document.getElementById("device-select").addEventListener("change", onDeviceSelectChange);

      // Добавляем эффект нажатия на кнопки
      document.addEventListener('click', function(e) {
        // Проверяем, что клик был по кнопке
        if (e.target.tagName === 'BUTTON') {
          // Создаем эффект нажатия
          const button = e.target;
          button.classList.add('button-pressed');

          // Удаляем класс после анимации
          setTimeout(() => {
            button.classList.remove('button-pressed');
          }, 200);
        }
      });

      // Синхронизируем поля никнейма для обоих режимов
      document.getElementById("sponsor-name").addEventListener("input", function() {
        document.getElementById("sponsor-name-fixed").value = this.value;
      });

      document.getElementById("sponsor-name-fixed").addEventListener("input", function() {
        document.getElementById("sponsor-name").value = this.value;
      });
    });

    /****************************************
     * Загрузка режима управления
     ****************************************/
    async function loadControlMode() {
      if (!streamerNickname) {
        console.error('Не удалось загрузить режим управления: нет никнейма стримера');
        return;
      }

      try {
        console.log(`Запрос режима управления для стримера: ${streamerNickname}`);
        const response = await fetch(`http://localhost:4000/streamer-control-mode/${streamerNickname}`);
        const data = await response.json();
        console.log('Ответ от сервера:', data);

        if (data.success && data.controlMode) {
          // Проверяем, есть ли активный покупатель времени
          const hasActiveTimeControl = data.controlMode.timeControl && data.controlMode.timeControl.buyer;

          // Если есть активный покупатель времени, устанавливаем режим fixed-time независимо от настроек
          if (hasActiveTimeControl) {
            controlMode = "fixed-time";
            console.log(`Обнаружен активный покупатель времени, установлен режим: fixed-time`);
          } else {
            // Иначе устанавливаем режим из настроек
            controlMode = data.controlMode.mode || "donation-battle";
            console.log(`Установлен режим управления из настроек: ${controlMode}`);
          }

          // Обновляем настройки фиксированного времени
          if (controlMode === "fixed-time") {
            timeControlSettings.timeMinutes = data.controlMode.timeMinutes || 5;
            timeControlSettings.priceTon = data.controlMode.priceTon || 1.0;

            // Обновляем отображение цены и времени
            const timeMinutesEl = document.getElementById("time-minutes");
            const timePriceEl = document.getElementById("time-price");

            if (timeMinutesEl) timeMinutesEl.textContent = timeControlSettings.timeMinutes;
            if (timePriceEl) timePriceEl.textContent = timeControlSettings.priceTon;

            // Проверяем, есть ли активный покупатель времени
            if (data.controlMode.timeControl) {
              timeControlSettings.buyer = data.controlMode.timeControl.buyer;
              timeControlSettings.timeLeft = data.controlMode.timeControl.timeLeft;
              timeControlSettings.endTime = data.controlMode.timeControl.endTime;

              console.log(`Активный покупатель времени: ${timeControlSettings.buyer}, осталось: ${timeControlSettings.timeLeft} сек.`);

              // Обновляем отображение активного покупателя
              updateTimeControlDisplay();

              // Запускаем таймер обратного отсчета
              startTimeCountdown();
            }
          }

          // Показываем соответствующий блок
          const donationBattleBox = document.getElementById("donation-battle-box");
          const fixedTimeBox = document.getElementById("fixed-time-box");

          if (donationBattleBox) donationBattleBox.style.display = controlMode === "donation-battle" ? "block" : "none";
          if (fixedTimeBox) fixedTimeBox.style.display = controlMode === "fixed-time" ? "block" : "none";

          console.log(`Блоки отображения обновлены: donation-battle=${controlMode === "donation-battle"}, fixed-time=${controlMode === "fixed-time"}`);
        } else {
          console.error('Нет данных о режиме управления в ответе сервера');
        }
      } catch (error) {
        console.error("Ошибка загрузки режима управления:", error);
        showToast("Ошибка загрузки режима управления", "error");
      }
    }

    /****************************************
     * Управление фиксированным временем
     ****************************************/
    function updateTimeControlDisplay() {
      const timeControlInfo = document.getElementById("time-control-info");
      const timeControlActive = document.getElementById("time-control-active");
      const buyerElement = document.getElementById("time-control-buyer");
      const buyTimeBtn = document.getElementById("buy-time-btn");

      if (timeControlSettings.buyer) {
        // Есть активный покупатель
        timeControlInfo.style.display = "none";
        timeControlActive.style.display = "block";
        buyTimeBtn.disabled = true;

        // Получаем никнейм покупателя
        fetch(`http://localhost:4000/get-sponsor/${timeControlSettings.buyer}`)
          .then(response => response.json())
          .then(data => {
            const nickname = data.success && data.nickname ? data.nickname : "anonim";
            buyerElement.textContent = nickname;
            // Не перезаписываем поля ввода никнейма, чтобы каждый пользователь видел свой никнейм
          })
          .catch(error => {
            console.error("Ошибка получения никнейма покупателя:", error);
            buyerElement.textContent = "anonim";
          });

        // Обновляем время
        updateTimeDisplay();
      } else {
        // Нет активного покупателя
        timeControlInfo.style.display = "block";
        timeControlActive.style.display = "none";
        buyTimeBtn.disabled = false;
      }
    }

    function updateTimeDisplay() {
      const timeDisplay = document.getElementById("time-remaining-display");
      if (!timeDisplay) return;

      const minutes = Math.floor(timeControlSettings.timeLeft / 60);
      const seconds = timeControlSettings.timeLeft % 60;

      timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Глобальные переменные для плавного отображения таймера
    let lastUpdateTime = 0;
    let displayTimeLeft = 0; // Для плавного отображения
    let targetTimeLeft = 0;  // Целевое значение времени

    // Функция обновления отображения времени (вызывается из smoothUpdateTimer)
    function updateTimeDisplay() {
      smoothUpdateTimer();
    }

    // Функция плавного обновления таймера
    function smoothUpdateTimer() {
      // Вычисляем текущее фактическое время
      const now = Date.now();
      const elapsedSeconds = (now - lastUpdateTime) / 1000; // В секундах (с дробной частью)

      // Обновляем целевое значение времени
      targetTimeLeft = Math.max(0, timeControlSettings.timeLeft - Math.floor(elapsedSeconds));

      // Плавно приближаем отображаемое время к целевому
      if (Math.abs(displayTimeLeft - targetTimeLeft) > 0.1) {
        // Если разница большая, делаем более быстрое приближение
        displayTimeLeft = displayTimeLeft + (targetTimeLeft - displayTimeLeft) * 0.2;
      } else {
        // Если разница маленькая, просто устанавливаем точное значение
        displayTimeLeft = targetTimeLeft;
      }

      // Получаем элемент таймера
      const timerElement = document.getElementById("time-remaining-display");
      if (!timerElement) return;

      // Проверяем, не подходит ли время к концу (10 секунд или меньше)
      if (targetTimeLeft <= 10 && targetTimeLeft > 0) {
        // Добавляем класс для анимации завершения
        if (!timerElement.classList.contains('time-ending')) {
          timerElement.classList.add('time-ending');
          // Показываем уведомление о завершении времени
          showToast('Внимание! Время управления заканчивается!', 'warning');
        }
      } else {
        // Удаляем класс анимации, если время больше 10 секунд или равно 0
        timerElement.classList.remove('time-ending');
      }

      // Обновляем отображение
      const minutes = Math.floor(displayTimeLeft / 60);
      const seconds = Math.floor(displayTimeLeft % 60);
      timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      // Проверяем, не закончилось ли время
      if (targetTimeLeft <= 0) {
        // Время истекло
        timeControlSettings.buyer = null;
        updateTimeControlDisplay();
        updateControlsState();
      }
    }

    function startTimeCountdown() {
      // Очищаем предыдущий интервал, если он был
      if (timeUpdateInterval) {
        clearInterval(timeUpdateInterval);
      }

      // Запоминаем время последнего обновления
      lastUpdateTime = Date.now();

      // Устанавливаем начальные значения для плавного отображения
      displayTimeLeft = timeControlSettings.timeLeft;
      targetTimeLeft = timeControlSettings.timeLeft;

      // Обновляем отображение времени сразу
      updateTimeDisplay();

      // Запускаем новый интервал с более частым обновлением
      timeUpdateInterval = setInterval(() => {
        // Обновляем фактическое время каждую секунду
        if (Date.now() - lastUpdateTime >= 1000) {
          const now = Date.now();
          const elapsedSeconds = Math.floor((now - lastUpdateTime) / 1000);
          lastUpdateTime = now;

          // Уменьшаем время на прошедшее количество секунд
          timeControlSettings.timeLeft = Math.max(0, timeControlSettings.timeLeft - elapsedSeconds);

          if (timeControlSettings.timeLeft <= 0) {
            // Время истекло
            clearInterval(timeUpdateInterval);
            timeControlSettings.buyer = null;
            updateTimeControlDisplay();
            updateControlsState();
            return;
          }
        }

        // Плавно обновляем отображение таймера
        smoothUpdateTimer();
      }, 100); // Обновляем каждые 100 мс для плавности
    }

    async function loadCommandsConfig() {
      try {
        const resp = await fetch('/config/commands.json');
        commandsConfig = await resp.json();
      } catch (err) {
        console.error("Ошибка загрузки commands.json:", err);
        commandsConfig = {};
      }
    }

    /****************************************
     * Смена устройства (селект)
     ****************************************/
    function onDeviceSelectChange() {
      if (!globalDeviceData || globalDeviceData.length === 0) return;

      // Получаем выбранное устройство
      const selectedDeviceMAC = document.getElementById('device-select').value;
      const selectedDevice = globalDeviceData.find(d => d.mac === selectedDeviceMAC);

      if (selectedDevice) {
        // Немедленно обновляем статус устройства
        const statusEl = document.getElementById("device-status");

        // Проверяем статус emergency из разных источников
        let isEmergency = false;

        // Если с сервера пришло явное указание о статусе emergency
        if (selectedDevice.emergency !== undefined) {
          // Сохраняем актуальный статус в хранилище
          if (!window.deviceEmergencyStatus) {
            window.deviceEmergencyStatus = {};
          }

          // Сохраняем статус из данных сервера
          isEmergency = selectedDevice.emergency === true;
          window.deviceEmergencyStatus[selectedDeviceMAC] = isEmergency;

          // Сохраняем в localStorage
          try {
            localStorage.setItem('deviceEmergencyStatus', JSON.stringify(window.deviceEmergencyStatus));
            console.log(`Обновлен статус emergency для устройства ${selectedDeviceMAC}: ${isEmergency} (при выборе устройства)`);
          } catch (e) {
            console.error('Ошибка при сохранении статусов emergency:', e);
          }
        }
        // Если с сервера не пришло данных о статусе emergency, используем сохраненный статус
        else if (window.deviceEmergencyStatus && window.deviceEmergencyStatus[selectedDeviceMAC] !== undefined) {
          isEmergency = window.deviceEmergencyStatus[selectedDeviceMAC];
          console.log(`Используем сохраненный статус emergency для устройства ${selectedDeviceMAC}: ${isEmergency} (при выборе устройства)`);
        }

        if (!selectedDevice.online) {
          // Если устройство оффлайн
          statusEl.textContent = "Оффлайн";
          statusEl.className = "offline";
        } else if (isEmergency) {
          // Если устройство в аварийном режиме
          statusEl.textContent = "Пауза";
          statusEl.className = "emergency";
        } else {
          // Если устройство онлайн и не в аварийном режиме
          statusEl.textContent = "Онлайн";
          statusEl.className = "online";
        }

        // Обновляем текущее состояние устройства
        window.currentDeviceStatus = {
          online: selectedDevice.online,
          mac: selectedDevice.mac,
          emergency: isEmergency
        };
      }

      // Загружаем контролы для устройства
      loadDeviceControlFromGlobal();

      // Обновляем значение RPM только для устройств типа "motor"
      if (selectedDevice && selectedDevice.device_type === "motor" &&
          selectedDeviceMAC && window.deviceRpmValues && window.deviceRpmValues[selectedDeviceMAC]) {
        // Обновляем значение RPM для выбранного устройства
        const rpmDisplay = document.getElementById('rpm-display');
        if (rpmDisplay) {
          rpmDisplay.textContent = `RPM: ${window.deviceRpmValues[selectedDeviceMAC]}`;
        }
      }

      // Немедленно обновляем состояние кнопок управления
      updateControlsState();
    }

    /****************************************
     * Загрузка стрима
     ****************************************/
    function getNicknameFromURL() {
      return window.location.pathname.split("/").pop();
    }
    async function loadStream() {
      const nickname = getNicknameFromURL();
      if (!nickname) return;
      try {
        const response = await fetch(`http://localhost:4000/api/stream/${nickname}`);
        const data = await response.json();
        if (!data.success || !data.stream) {
          document.getElementById("stream-status").textContent = "Стример не найден или трансляция отсутствует!";
          return;
        }
        document.getElementById("streamer-name").textContent = `Стрим: ${nickname}`;
        const { stream_platform, stream_id } = data.stream;
        let embedUrl = "";
        if (stream_platform === "youtube") {
          embedUrl = `https://www.youtube.com/embed/${stream_id}?autoplay=1&mute=1&rel=0`;
        } else if (stream_platform === "twitch") {
          embedUrl = `https://player.twitch.tv/?channel=${stream_id}&parent=${window.location.hostname}`;
        } else if (stream_platform === "vk") {
          embedUrl = `https://live.vkvideo.ru/app/embed/${stream_id}`;
        } else if (stream_platform === "kick") {
          embedUrl = `https://player.kick.com/${stream_id}`;
        }
        if (embedUrl) {
          document.getElementById("stream-frame").src = embedUrl;
          document.getElementById("stream-container").style.display = "block";
          document.getElementById("stream-status").style.display = "none";
        } else {
          document.getElementById("stream-status").textContent = "Ошибка: Платформа не поддерживается.";
        }
      } catch (error) {
        console.error("Ошибка loadStream:", error);
        document.getElementById("stream-status").textContent = "Ошибка при загрузке стрима.";
      }
    }

    /****************************************
     * Спонсорский ник
     ****************************************/
    async function loadSponsorName() {
      if (!walletAddress) return;
      try {
        const resp = await fetch(`http://localhost:4000/get-sponsor/${walletAddress}`);
        const data = await resp.json();
        if (data.success) {
          // Устанавливаем никнейм в оба поля ввода
          document.getElementById("sponsor-name").value = data.nickname;
          document.getElementById("sponsor-name-fixed").value = data.nickname;
        }
      } catch (error) {
        console.error("Ошибка загрузки ника спонсора:", error);
      }
    }
    async function updateSponsor(nickname) {
      if (!walletAddress) return;
      try {
        const resp = await fetch("http://localhost:4000/update-sponsor", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ walletAddress, nickname })
        });
        const data = await resp.json();
        if (data.success) {
          // Обновляем оба поля ввода
          document.getElementById("sponsor-name").value = nickname;
          document.getElementById("sponsor-name-fixed").value = nickname;
        } else {
          alert("Ошибка сохранения ника: " + data.message);
        }
      } catch (error) {
        console.error("Ошибка сохранения ника:", error);
      }
    }
    document.getElementById("sponsor-name").addEventListener("change", async (e) => {
      const nickname = e.target.value.trim() || "anonim";
      await updateSponsor(nickname);
    });

    document.getElementById("sponsor-name-fixed").addEventListener("change", async (e) => {
      const nickname = e.target.value.trim() || "anonim";
      await updateSponsor(nickname);
    });

    /****************************************
     * Донаты
     ****************************************/
    async function updateBalance(address) {
      if (!address) return;
      try {
        const response = await fetch(`https://tonapi.io/v2/accounts/${address}`);
        const data = await response.json();
        if (data.balance) {
          let balance = parseFloat(data.balance) / 1e9;
          document.getElementById("wallet-balance").innerText = balance.toFixed(2);
          document.getElementById("wallet-balance-fixed").innerText = balance.toFixed(2);
        } else {
          document.getElementById("wallet-balance").innerText = "0";
          document.getElementById("wallet-balance-fixed").innerText = "0";
        }
      } catch (error) {
        console.error("Ошибка updateBalance:", error);
        document.getElementById("wallet-balance").innerText = "Ошибка";
        document.getElementById("wallet-balance-fixed").innerText = "Ошибка";
      }
    }
    async function sendDonation(mode) {
      if (!walletAddress) {
        tonConnectUI.connectWallet();
        return;
      }

      // Проверяем, что режим совпадает с текущим
      if (mode !== controlMode) {
        showToast(`Режим ${mode} не совпадает с текущим режимом ${controlMode}`, "error");
        return;
      }

      const user = walletAddress;
      let amount;

      if (mode === "donation-battle") {
        // Режим битвы донатов
        amount = document.getElementById("amount").value;
        if (!amount) {
          showToast("Введите сумму TON!", "error");
          return;
        }
      } else if (mode === "fixed-time") {
        // Режим фиксированного времени - интеграция со смарт-контрактом
        amount = timeControlSettings.priceTon;

        // Сохраняем никнейм спонсора
        const sponsorName = document.getElementById("sponsor-name-fixed").value.trim() || "anonim";
        await updateSponsor(sponsorName);

        // Инициируем покупку времени через смарт-контракт
        try {
          showToast("Инициация покупки времени через смарт-контракт...", "info");

          const response = await fetch("/api/contracts/buy-time", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              device_id: globalDeviceData && globalDeviceData.length > 0 ? globalDeviceData[0].mac : null,
              duration_minutes: timeControlSettings.timeMinutes,
              buyer_wallet: walletAddress,
              streamer_nickname: streamerNickname
            })
          });

          const contractData = await response.json();

          if (contractData.success) {
            // Создаем транзакцию TON Connect
            const transaction = {
              validUntil: Math.floor(Date.now() / 1000) + 300, // 5 минут
              messages: [
                {
                  address: contractData.contractAddress,
                  amount: contractData.estimatedCost,
                  payload: contractData.payload || ""
                }
              ]
            };

            // Отправляем транзакцию через TON Connect
            const result = await tonConnectUI.sendTransaction(transaction);

            if (result) {
              showToast("Транзакция отправлена! Ожидание подтверждения...", "success");

              // Подтверждаем транзакцию на сервере
              const confirmResponse = await fetch("/api/contracts/confirm-transaction", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  transaction_id: contractData.transactionId,
                  transaction_hash: result.boc
                })
              });

              const confirmData = await confirmResponse.json();

              if (confirmData.success) {
                showToast("Время управления успешно приобретено!", "success");

                // Обновляем данные о времени управления
                if (confirmData.timeControl) {
                  timeControlSettings.buyer = user;
                  timeControlSettings.timeLeft = confirmData.timeControl.timeLeft;
                  timeControlSettings.endTime = confirmData.timeControl.endTime;

                  updateTimeControlDisplay();
                  startTimeCountdown();
                  updateControlsState();
                }
              } else {
                showToast(`Ошибка подтверждения: ${confirmData.message}`, "error");
              }
            }
          } else {
            showToast(`Ошибка инициации покупки: ${contractData.message}`, "error");
          }

          return; // Выходим из функции, так как обработали смарт-контракт
        } catch (error) {
          console.error("Ошибка покупки времени через смарт-контракт:", error);
          showToast("Ошибка при покупке времени. Попробуйте снова.", "error");
          return;
        }
      }

      try {
        const response = await fetch("http://localhost:4000/donate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ user, amount, streamerNickname }),
        });
        const data = await response.json();

        // Показываем уведомление только в случае ошибки
        if (!data.success) {
          showToast(data.message, "error");
        }

        if (data.success) {
          if (data.mode === "donation-battle") {
            // Обновляем данные для режима битвы донатов
            updateLeader();
            if (user) updateBalance(user);
            const nextMin = parseFloat(data.leader.amount) + 0.1;
            const input = document.getElementById("amount");
            if ((parseFloat(input.value) || 0) < nextMin) {
              input.value = nextMin.toFixed(1);
            }
          } else if (data.mode === "fixed-time") {
            // Обновляем данные для режима фиксированного времени
            if (user) updateBalance(user);

            // Сохраняем текущее время для проверки добавления времени
            const currentTimeLeft = timeControlSettings.timeLeft;
            const isAddingTime = timeControlSettings.buyer === user && data.timeControl.timeLeft > currentTimeLeft;

            // Сбрасываем время последнего обновления
            lastUpdateTime = Date.now();

            // Обновляем данные о времени
            timeControlSettings.buyer = user;
            timeControlSettings.timeLeft = data.timeControl.timeLeft;
            timeControlSettings.endTime = data.timeControl.endTime;

            // Получаем никнейм из поля ввода
            const nickname = document.getElementById("sponsor-name-fixed").value.trim() || "anonim";
            document.getElementById("time-control-buyer").textContent = nickname;

            // Обновляем отображение
            updateTimeControlDisplay();

            // Если это добавление времени, делаем плавный переход
            if (isAddingTime) {
              // Устанавливаем целевое значение времени
              targetTimeLeft = data.timeControl.timeLeft;
              // Не меняем displayTimeLeft, чтобы оно плавно дошло до нового значения

              // Добавляем анимацию к таймеру
              const timerElement = document.getElementById("time-remaining-display");
              timerElement.classList.add("time-added");
              // Удаляем класс после завершения анимации
              setTimeout(() => {
                timerElement.classList.remove("time-added");
              }, 1000);
            } else {
              // Если это первая покупка или сброс времени, перезапускаем таймер
              if (timeUpdateInterval) {
                clearInterval(timeUpdateInterval);
              }
              // Устанавливаем начальные значения для плавного отображения
              displayTimeLeft = data.timeControl.timeLeft;
              targetTimeLeft = data.timeControl.timeLeft;
              startTimeCountdown();
            }

            // Обновляем состояние кнопок
            updateControlsState();

            // Сохраняем ID этой покупки, чтобы избежать дублирования уведомлений
            if (data.purchaseId) {
              lastPurchaseId = data.purchaseId;
            }

            // Устанавливаем флаг, что только что купили время
            justPurchasedTime = true;

            // Показываем уведомление о добавлении времени
            const minutes = Math.floor(data.timeControl.timeLeft / 60);
            showToast(`Вы ${isAddingTime ? 'добавили еще' : 'приобрели'} ${timeControlSettings.timeMinutes} минут управления. Общее время: ${minutes} мин.`, "success");

            // Сбрасываем флаг через некоторое время
            setTimeout(() => {
              justPurchasedTime = false;
            }, 5000); // Сбрасываем флаг через 5 секунд
          }
        }
      } catch (error) {
        console.error("Ошибка при донате:", error);
        showToast("Ошибка при отправке доната", "error");
      }
    }
    async function updateLeader() {
      try {
        const response = await fetch("http://localhost:4000/donations");
        const data = await response.json();

        if (data.leader) {
          currentLeaderWallet = data.leader.user;
          const leaderAmount = parseFloat(data.leader.amount);

          const sponsorResp = await fetch(`http://localhost:4000/get-sponsor/${currentLeaderWallet}`);
          const sponsorData = await sponsorResp.json();
          let leaderNick = sponsorData.success && sponsorData.nickname ? sponsorData.nickname : "anonim";

          const betHistory = data.donations || [];
          if (betHistory.length !== lastDonationsLength) {
            lastDonationsLength = betHistory.length;
            betHistory.sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
            const lastBets = betHistory.slice(1, 3);

            document.getElementById("leader").innerHTML = `
              <div class="bet-item leader">
                <span class="crown">👑</span>
                <span class="name">${leaderNick}</span>
                <span class="amount">${leaderAmount} TON</span>
              </div>
            `;
            const historyHTML = await Promise.all(lastBets.map(async (bet) => {
              const sResp = await fetch(`http://localhost:4000/get-sponsor/${bet.user}`);
              const sData = await sResp.json();
              let betNick = sData.success && sData.nickname ? sData.nickname : "anonim";
              let isMyBet = (bet.user === walletAddress) ? " (my bet)" : "";
              return `
                <div class="bet-item">
                  <span class="name">${betNick}${isMyBet}</span>
                  <span class="amount">${bet.amount} TON</span>
                </div>
              `;
            }));
            document.getElementById("bet-history").innerHTML = historyHTML.join('');
          }
          const input = document.getElementById("amount");
          const currentVal = parseFloat(input.value) || 0;
          const nextMin = leaderAmount + 0.1;
          if (currentVal < nextMin) {
            input.value = nextMin.toFixed(1);
          }
        } else {
          currentLeaderWallet = null;
          document.getElementById("leader").innerHTML = "";
          document.getElementById("bet-history").innerHTML = "";
        }
        updateControlsState();
      } catch (error) {
        console.error("Ошибка updateLeader:", error);
      }
    }

    /****************************************
     * Управление устройствами
     ****************************************/
    async function updateDeviceStatus() {
      try {
        const nickname = getNicknameFromURL();
        if (!nickname) return;

        const resp = await fetch(`http://localhost:4000/devices-by-nickname/${nickname}`);
        const data = await resp.json();

        const select = document.getElementById("device-select");
        const statusEl = document.getElementById("device-status");

        if (!data.success || !data.devices || data.devices.length === 0) {
          select.innerHTML = '<option value="">--Нет устройств--</option>';
          statusEl.textContent = "Нет устройства";
          statusEl.className = "offline";
          document.getElementById("control-commands").innerHTML = "<p>Нет привязанных устройств.</p>";
          document.getElementById("device-type").textContent = "";
          globalDeviceData = null;
          return;
        }

        // Сортируем устройства: сначала онлайн или на паузе, затем оффлайн
        data.devices.sort((a, b) => {
          // Проверяем статус emergency для устройств
          const aEmergency = a.emergency === true || (window.deviceEmergencyStatus && window.deviceEmergencyStatus[a.mac] === true);
          const bEmergency = b.emergency === true || (window.deviceEmergencyStatus && window.deviceEmergencyStatus[b.mac] === true);

          // Определяем, активно ли устройство (онлайн или на паузе)
          const aActive = a.online;
          const bActive = b.online;

          // Если одно устройство активно (онлайн или на паузе), а другое оффлайн
          if (aActive !== bActive) {
            return aActive ? -1 : 1; // Активные устройства в начале
          }

          // Если оба устройства активны, приоритет устройствам на паузе
          if (aActive && bActive && aEmergency !== bEmergency) {
            return aEmergency ? -1 : 1; // Устройства на паузе перед обычными онлайн
          }

          // В остальных случаях сохраняем исходный порядок
          return 0;
        });

        globalDeviceData = data.devices;

        const previouslySelected = select.value;

        select.innerHTML = "";
        data.devices.forEach(dev => {
          // Показываем в списке только тип (или MAC, если device_type пустой)
          let devType = dev.device_type || dev.mac;
          const opt = document.createElement("option");
          opt.value = dev.mac;

          // Проверяем статус emergency для этого устройства
          let isEmergency = dev.emergency === true;

          // Также проверяем сохраненный статус emergency в нашем хранилище
          if (window.deviceEmergencyStatus && window.deviceEmergencyStatus[dev.mac] === true) {
            isEmergency = true;
          }

          // Устанавливаем текст опции в зависимости от статуса
          if (!dev.online) {
            opt.text = `${devType} (Оффлайн)`;
            opt.disabled = true;
          } else if (isEmergency) {
            opt.text = `${devType} (Пауза)`;
          } else {
            opt.text = `${devType} (Онлайн)`;
          }

          select.appendChild(opt);
        });

        // Восстанавливаем выбор
        if (previouslySelected && data.devices.some(d => d.mac === previouslySelected)) {
          select.value = previouslySelected;
        } else {
          // Если нет ранее выбранного устройства – выбираем первое
          select.value = data.devices[0].mac;
        }

        // Обновляем статус
        const selectedMac = select.value;
        const device = data.devices.find(d => d.mac === selectedMac);
        if (!device) {
          statusEl.textContent = "Нет устройства";
          statusEl.className = "offline";
        } else {
          // Проверяем статус emergency
          let isEmergency = false;

          // Если с сервера пришло явное указание о статусе emergency
          if (device.emergency !== undefined) {
            // Сохраняем актуальный статус в хранилище
            if (!window.deviceEmergencyStatus) {
              window.deviceEmergencyStatus = {};
            }

            // Сохраняем статус из данных сервера
            isEmergency = device.emergency === true;
            window.deviceEmergencyStatus[device.mac] = isEmergency;

            // Сохраняем в localStorage
            try {
              localStorage.setItem('deviceEmergencyStatus', JSON.stringify(window.deviceEmergencyStatus));
              console.log(`Обновлен статус emergency для устройства ${device.mac}: ${isEmergency} (из данных сервера)`);
            } catch (e) {
              console.error('Ошибка при сохранении статусов emergency:', e);
            }
          }
          // Если с сервера не пришло данных о статусе emergency, используем сохраненный статус
          else if (window.deviceEmergencyStatus && window.deviceEmergencyStatus[device.mac] !== undefined) {
            isEmergency = window.deviceEmergencyStatus[device.mac];
            console.log(`Используем сохраненный статус emergency для устройства ${device.mac}: ${isEmergency}`);
          }

          if (!device.online) {
            // Если устройство оффлайн
            statusEl.textContent = "Оффлайн";
            statusEl.className = "offline";
          } else if (isEmergency) {
            // Если устройство в аварийном режиме
            statusEl.textContent = "Пауза";
            statusEl.className = "emergency";
          } else {
            // Если устройство онлайн и не в аварийном режиме
            statusEl.textContent = "Онлайн";
            statusEl.className = "online";
          }

          // Сохраняем статус устройства с учетом emergency
          window.currentDeviceStatus = {
            online: device.online,
            mac: device.mac,
            emergency: isEmergency
          };
        }

        // Отображаем кнопки и обновлённое название устройства (только тип)
        loadDeviceControlFromGlobal();

        // Обновляем состояние кнопок управления
        updateControlsState();

      } catch (error) {
        console.error("Ошибка updateDeviceStatus:", error);
      }
    }

    function loadDeviceControlFromGlobal() {
      const container = document.getElementById("control-commands");
      const deviceTypeEl = document.getElementById("device-type");
      const select = document.getElementById("device-select");

      if (!globalDeviceData || globalDeviceData.length === 0) {
        container.innerHTML = "<p>Нет привязанных устройств.</p>";
        deviceTypeEl.textContent = "";
        return;
      }

      const selectedMac = select.value;
      if (!selectedMac) {
        container.innerHTML = "<p>Нет выбранного устройства.</p>";
        deviceTypeEl.textContent = "";
        return;
      }

      const device = globalDeviceData.find(d => d.mac === selectedMac);
      if (!device) {
        container.innerHTML = "<p>Устройство не найдено.</p>";
        deviceTypeEl.textContent = "";
        return;
      }

      // Берём только тип (или по умолчанию 'buzzer')
      let deviceType = device.device_type || "buzzer";
      deviceTypeEl.textContent = deviceType;

      // Генерируем кнопки на основе commandsConfig
      if (!commandsConfig || !commandsConfig[deviceType]) {
        container.innerHTML = "<p>Нет команд для такого типа устройства</p>";
        return;
      }

      const cmdList = commandsConfig[deviceType].commands || [];
      if (!cmdList.length) {
        container.innerHTML = "<p>Пустой список команд</p>";
        return;
      }

      // Генерируем HTML-кнопки
      const btnsHTML = cmdList.map(cmd => {
        // cmd.name => 'BUZZER_ON', cmd.label => 'ON'
        // Добавляем див для RPM под кнопкой ON только для устройств типа "motor"
        let infoDiv = '';
        // Проверяем, что это устройство типа "motor" и кнопка ON
        if (deviceType === "motor" && cmd.label === 'ON') {
          // Получаем значение RPM для выбранного устройства
          let rpmValue = "0/0/0.00/0";
          if (window.deviceRpmValues && window.deviceRpmValues[device.mac]) {
            rpmValue = window.deviceRpmValues[device.mac];
          }

          infoDiv = `
            <div class="rpm-info">
              <span id="rpm-display">RPM: ${rpmValue}</span>
              <span class="tooltip-icon">?
                <span class="tooltip-text">Формат: скорость/max. скорость/об.мин./шаг изменения скорости</span>
              </span>
            </div>
          `;
        }
        return `
          <div class="button-container">
            <button onclick="sendDeviceCommand('${cmd.name}', undefined, '${device.mac}')">
              ${cmd.label}
            </button>
            ${infoDiv}
          </div>
        `;
      }).join('');

      container.innerHTML = `<div class="control-buttons">${btnsHTML}</div>`;
    }

    /****************************************
     * Отправка команды
     ****************************************/
    async function sendDeviceCommand(cmdName, value, deviceMac) {
      if (!deviceMac) {
        showToast("Устройство не выбрано!", "error");
        return;
      }

      if (!walletAddress) {
        showToast("Сначала подключите кошелек!", "error");
        tonConnectUI.connectWallet();
        return;
      }

      let commandToSend = cmdName;
      if (value !== undefined) {
        commandToSend = `${cmdName}:${value}`;
      }
      try {
        const res = await fetch("http://localhost:4000/control", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            user: walletAddress,
            device_id: deviceMac,
            command: commandToSend,
            streamerNickname
          })
        });
        const data = await res.json();
        showToast(data.message, data.success ? "success" : "error");
      } catch (error) {
        console.error("Ошибка sendDeviceCommand:", error);
        showToast("Ошибка отправки команды устройству.", "error");
      }
    }

    /****************************************
     * Управление кнопками
     ****************************************/
    function updateControlsState() {
      const isAuthorized = !!walletAddress;
      const deviceStatus = window.currentDeviceStatus || { online: false };

      // Обновляем состояние полей ввода
      document.getElementById("sponsor-name").disabled = !isAuthorized;
      document.getElementById("sponsor-name-fixed").disabled = !isAuthorized;
      document.getElementById("amount").disabled = false;

      // Обновляем состояние кнопок донатов
      const donateButtons = document.querySelectorAll(".donation-box button");
      donateButtons.forEach(btn => {
        btn.disabled = !isAuthorized;
      });

      // Если устройство оффлайн – отключаем кнопки управления
      const deviceButtons = document.querySelectorAll("#control-commands button");

      // Проверяем, онлайн ли устройство и не в аварийном ли режиме
      const isDeviceOnline = deviceStatus.online;
      const isDeviceEmergency = deviceStatus.emergency === true;

      // Устройство доступно для управления, если оно онлайн и не в аварийном режиме
      const isDeviceAvailable = isDeviceOnline && !isDeviceEmergency;

      // Проверяем права доступа в зависимости от режима
      let hasControlAccess = false;

      if (controlMode === "donation-battle") {
        // В режиме битвы донатов проверяем, является ли пользователь лидером
        hasControlAccess = walletAddress === currentLeaderWallet;
      } else if (controlMode === "fixed-time") {
        // В режиме фиксированного времени проверяем, является ли пользователь текущим покупателем времени
        hasControlAccess = timeControlSettings.buyer === walletAddress;
      }

      // Обновляем состояние кнопок
      deviceButtons.forEach(btn => {
        // Кнопка активна, если пользователь авторизован, имеет права доступа и устройство доступно
        btn.disabled = !isAuthorized || !hasControlAccess || !isDeviceAvailable;
      });
    }

    /****************************************
     * Realtime WebSocket
     ****************************************/
    const realtimeSocket = new WebSocket('ws://localhost:4001');
    realtimeSocket.addEventListener('open', () => {
      console.log('Realtime WebSocket: подключено');

      // При подключении к WebSocket запрашиваем актуальные данные с сервера
      setTimeout(() => {
        // Запрашиваем актуальные данные с сервера
        updateDeviceStatus();
      }, 500); // Небольшая задержка, чтобы убедиться, что соединение установлено
    });
    realtimeSocket.addEventListener('message', async (event) => {
      const data = JSON.parse(event.data);

      if (data.type === 'donation_update') {
        if (controlMode === "donation-battle") {
          await updateLeader();
        }
        await updateDeviceStatus();
      }

      if (data.type === 'device_status_update') {
        await updateDeviceStatus();
      }

      if (data.type === 'time_control_update') {
        // Обновление данных о времени управления
        if (controlMode === "fixed-time" && data.timeControl) {
          // Сохраняем текущего покупателя и текущее время
          const currentBuyer = timeControlSettings.buyer;
          const currentTimeLeft = timeControlSettings.timeLeft;
          const wasSignificantChange = !currentBuyer ||
                                      currentBuyer !== data.timeControl.buyer ||
                                      Math.abs(currentTimeLeft - data.timeControl.timeLeft) > 5;

          // Обновляем данные о времени
          timeControlSettings.buyer = data.timeControl.buyer;
          timeControlSettings.timeLeft = data.timeControl.timeLeft;
          timeControlSettings.endTime = data.timeControl.endTime;

          // Если есть никнейм стримера в ответе, сохраняем его
          if (data.timeControl.streamerNickname) {
            streamerNickname = data.timeControl.streamerNickname;
          }

          // Обновляем отображение
          updateTimeControlDisplay();

          if (wasSignificantChange) {
            // Если это значительное изменение (новый покупатель или большое изменение времени)
            // Сбрасываем время последнего обновления
            lastUpdateTime = Date.now();

            // Устанавливаем новые значения для плавного отображения
            // Если это добавление времени, делаем плавный переход
            if (currentBuyer === data.timeControl.buyer && data.timeControl.timeLeft > currentTimeLeft) {
              // Сохраняем текущее отображаемое время
              targetTimeLeft = data.timeControl.timeLeft;
              // Не меняем displayTimeLeft, чтобы оно плавно дошло до нового значения

              // Полностью отключаем уведомления от WebSocket, чтобы избежать дублирования
              // Сохраняем только обновление таймера без показа уведомлений
              console.log('Получено WebSocket-сообщение об обновлении времени:', data.timeControl);

              // Сохраняем ID покупки, если он есть
              if (data.timeControl.purchaseId) {
                lastPurchaseId = data.timeControl.purchaseId;
              }
            } else {
              // Если это новый покупатель или сброс времени, перезапускаем таймер
              if (timeUpdateInterval) {
                clearInterval(timeUpdateInterval);
              }
              // Устанавливаем начальные значения для плавного отображения
              displayTimeLeft = data.timeControl.timeLeft;
              targetTimeLeft = data.timeControl.timeLeft;
              startTimeCountdown();
            }
          } else {
            // Если это небольшое обновление, просто обновляем целевое значение
            targetTimeLeft = data.timeControl.timeLeft;
            // Не меняем lastUpdateTime, чтобы не сбить плавный отсчет
          }

          // Обновляем состояние кнопок
          updateControlsState();
        }
      }

      if (data.type === 'time_control_ended') {
        // Время управления закончилось
        if (controlMode === "fixed-time") {
          timeControlSettings.buyer = null;
          timeControlSettings.timeLeft = 0;

          // Обновляем отображение
          updateTimeControlDisplay();

          // Обновляем состояние кнопок
          updateControlsState();

          // Показываем уведомление
          showToast("Время управления закончилось", "info");
        }
      }
      // Обработка данных телеметрии устройства
      if (data.type === 'device_telemetry' && data.telemetry && data.deviceID && data.deviceType) {
        // Обрабатываем только данные от устройств типа "motor"
        if (data.deviceType === "motor") {
          // Получаем текущее выбранное устройство
          const selectedDeviceMAC = document.getElementById('device-select').value;

          // Сохраняем значения RPM для всех устройств типа "motor"
          if (data.telemetry.rpm && data.telemetry.rpm !== '0/0/0/0') {
            // Сохраняем значения для каждого устройства отдельно
            if (!window.deviceRpmValues) {
              window.deviceRpmValues = {};
            }
            window.deviceRpmValues[data.deviceID] = data.telemetry.rpm;

            // Обновляем отображение только если это выбранное устройство
            if (data.deviceID === selectedDeviceMAC) {
              const rpmDisplay = document.getElementById('rpm-display');
              if (rpmDisplay) {
                // Обновляем только текст, а не весь элемент
                rpmDisplay.textContent = `RPM: ${data.telemetry.rpm}`;
              }
            }
          }

          // Обрабатываем статус emergency для всех устройств
          if (data.telemetry.emergency !== undefined) {
            // Создаем хранилище для статусов emergency, если его еще нет
            if (!window.deviceEmergencyStatus) {
              window.deviceEmergencyStatus = {};
            }

            // Сохраняем статус emergency для этого устройства
            const isEmergency = data.telemetry.emergency === true;

            // Всегда сохраняем актуальный статус emergency
            window.deviceEmergencyStatus[data.deviceID] = isEmergency;
            console.log(`Обновлен статус emergency для устройства ${data.deviceID}: ${isEmergency}`);

            // Сохраняем в localStorage для персистентности между обновлениями страницы
            try {
              localStorage.setItem('deviceEmergencyStatus', JSON.stringify(window.deviceEmergencyStatus));
            } catch (e) {
              console.error('Ошибка при сохранении статусов emergency:', e);
            }

            // Обновляем статус устройства, если это выбранное устройство
            if (data.deviceID === selectedDeviceMAC) {
              // Обновляем текущее состояние устройства
              if (window.currentDeviceStatus) {
                window.currentDeviceStatus.emergency = isEmergency;
              }

              // Обновляем отображение статуса
              const statusEl = document.getElementById("device-status");
              if (statusEl && window.currentDeviceStatus.online) {
                if (isEmergency) {
                  statusEl.textContent = "Пауза";
                  statusEl.className = "emergency";
                } else {
                  statusEl.textContent = "Онлайн";
                  statusEl.className = "online";
                }

                // Обновляем состояние кнопок
                updateControlsState();
              }
            }
          }
        }
      }
    });

    /****************************************
     * Toast helper
     ****************************************/
    function showToast(message, type = 'success') {
      const container = document.querySelector('.toast-container') || (() => {
        const div = document.createElement('div');
        div.className = 'toast-container';
        document.body.appendChild(div);
        return div;
      })();

      const existingToasts = container.querySelectorAll('.toast');
      if (existingToasts.length >= 3) {
        const oldestToast = existingToasts[0];
        oldestToast.style.animation = 'slideOutToBottom 0.3s ease forwards';
        setTimeout(() => oldestToast.remove(), 300);
      }

      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.innerHTML = `
        ${type === 'success' ? '✅' : '❌'}
        <span>${message}</span>
      `;
      container.appendChild(toast);

      setTimeout(() => {
        toast.style.animation = 'slideOutToBottom 0.3s ease forwards';
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }
  </script>
</body>
</html>
