{"version": 3, "sources": ["../../../../../src/start/server/middleware/ExpoGoManifestHandlerMiddleware.ts"], "sourcesContent": ["import { ExpoUpdatesManifest } from '@expo/config';\nimport { Updates } from '@expo/config-plugins';\nimport accepts from 'accepts';\nimport crypto from 'crypto';\nimport { serializeDictionary, Dictionary } from 'structured-headers';\n\nimport { ManifestMiddleware, ManifestRequestInfo } from './ManifestMiddleware';\nimport { assertRuntimePlatform, parsePlatformHeader } from './resolvePlatform';\nimport { resolveRuntimeVersionWithExpoUpdatesAsync } from './resolveRuntimeVersionWithExpoUpdatesAsync';\nimport { ServerHeaders, ServerRequest } from './server.types';\nimport { getAnonymousIdAsync } from '../../../api/user/UserSettings';\nimport { ANONYMOUS_USERNAME } from '../../../api/user/user';\nimport {\n  CodeSigningInfo,\n  getCodeSigningInfoAsync,\n  signManifestString,\n} from '../../../utils/codesigning';\nimport { CommandError } from '../../../utils/errors';\nimport {\n  encodeMultipartMixed,\n  FormDataField,\n  EncodedFormData,\n} from '../../../utils/multipartMixed';\nimport { stripPort } from '../../../utils/url';\n\nconst debug = require('debug')('expo:start:server:middleware:ExpoGoManifestHandlerMiddleware');\n\nexport enum ResponseContentType {\n  TEXT_PLAIN,\n  APPLICATION_JSON,\n  APPLICATION_EXPO_JSON,\n  MULTIPART_MIXED,\n}\n\ninterface ExpoGoManifestRequestInfo extends ManifestRequestInfo {\n  responseContentType: ResponseContentType;\n  expectSignature: string | null;\n}\n\nexport class ExpoGoManifestHandlerMiddleware extends ManifestMiddleware<ExpoGoManifestRequestInfo> {\n  public getParsedHeaders(req: ServerRequest): ExpoGoManifestRequestInfo {\n    let platform = parsePlatformHeader(req);\n\n    if (!platform) {\n      debug(\n        `No \"expo-platform\" header or \"platform\" query parameter specified. Falling back to \"ios\".`\n      );\n      platform = 'ios';\n    }\n\n    assertRuntimePlatform(platform);\n\n    // Expo Updates clients explicitly accept \"multipart/mixed\" responses while browsers implicitly\n    // accept them with \"accept: */*\". To make it easier to debug manifest responses by visiting their\n    // URLs in a browser, we denote the response as \"text/plain\" if the user agent appears not to be\n    // an Expo Updates client.\n    const accept = accepts(req);\n    const acceptedType = accept.types([\n      'unknown/unknown',\n      'multipart/mixed',\n      'application/json',\n      'application/expo+json',\n      'text/plain',\n    ]);\n\n    let responseContentType;\n    switch (acceptedType) {\n      case 'multipart/mixed':\n        responseContentType = ResponseContentType.MULTIPART_MIXED;\n        break;\n      case 'application/json':\n        responseContentType = ResponseContentType.APPLICATION_JSON;\n        break;\n      case 'application/expo+json':\n        responseContentType = ResponseContentType.APPLICATION_EXPO_JSON;\n        break;\n      default:\n        responseContentType = ResponseContentType.TEXT_PLAIN;\n        break;\n    }\n\n    const expectSignature = req.headers['expo-expect-signature'];\n\n    return {\n      responseContentType,\n      platform,\n      expectSignature: expectSignature ? String(expectSignature) : null,\n      hostname: stripPort(req.headers['host']),\n      protocol: req.headers['x-forwarded-proto'] as 'http' | 'https' | undefined,\n    };\n  }\n\n  private getDefaultResponseHeaders(): ServerHeaders {\n    const headers = new Map<string, number | string | readonly string[]>();\n    // set required headers for Expo Updates manifest specification\n    headers.set('expo-protocol-version', 0);\n    headers.set('expo-sfv-version', 0);\n    headers.set('cache-control', 'private, max-age=0');\n    return headers;\n  }\n\n  public async _getManifestResponseAsync(requestOptions: ExpoGoManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }> {\n    const { exp, hostUri, expoGoConfig, bundleUrl } =\n      await this._resolveProjectSettingsAsync(requestOptions);\n\n    const runtimeVersion =\n      (await resolveRuntimeVersionWithExpoUpdatesAsync({\n        projectRoot: this.projectRoot,\n        platform: requestOptions.platform,\n      })) ??\n      // if expo-updates can't determine runtime version, fall back to calculation from config-plugin.\n      // this happens when expo-updates is installed but runtimeVersion hasn't yet been configured or when\n      // expo-updates is not installed.\n      (await Updates.getRuntimeVersionAsync(\n        this.projectRoot,\n        { ...exp, runtimeVersion: exp.runtimeVersion ?? { policy: 'sdkVersion' } },\n        requestOptions.platform\n      ));\n    if (!runtimeVersion) {\n      throw new CommandError(\n        'MANIFEST_MIDDLEWARE',\n        `Unable to determine runtime version for platform '${requestOptions.platform}'`\n      );\n    }\n\n    const codeSigningInfo = await getCodeSigningInfoAsync(\n      exp,\n      requestOptions.expectSignature,\n      this.options.privateKeyPath\n    );\n\n    const easProjectId = exp.extra?.eas?.projectId as string | undefined | null;\n    const scopeKey = await ExpoGoManifestHandlerMiddleware.getScopeKeyAsync({\n      slug: exp.slug,\n      codeSigningInfo,\n    });\n\n    const expoUpdatesManifest: ExpoUpdatesManifest = {\n      id: crypto.randomUUID(),\n      createdAt: new Date().toISOString(),\n      runtimeVersion,\n      launchAsset: {\n        key: 'bundle',\n        contentType: 'application/javascript',\n        url: bundleUrl,\n      },\n      assets: [], // assets are not used in development\n      metadata: {}, // required for the client to detect that this is an expo-updates manifest\n      extra: {\n        eas: {\n          projectId: easProjectId ?? undefined,\n        },\n        expoClient: {\n          ...exp,\n          hostUri,\n        },\n        expoGo: expoGoConfig,\n        scopeKey,\n      },\n    };\n\n    const stringifiedManifest = JSON.stringify(expoUpdatesManifest);\n\n    let manifestPartHeaders: { 'expo-signature': string } | null = null;\n    let certificateChainBody: string | null = null;\n    if (codeSigningInfo) {\n      const signature = signManifestString(stringifiedManifest, codeSigningInfo);\n      manifestPartHeaders = {\n        'expo-signature': serializeDictionary(\n          convertToDictionaryItemsRepresentation({\n            keyid: codeSigningInfo.keyId,\n            sig: signature,\n            alg: 'rsa-v1_5-sha256',\n          })\n        ),\n      };\n      certificateChainBody = codeSigningInfo.certificateChainForResponse.join('\\n');\n    }\n\n    const headers = this.getDefaultResponseHeaders();\n\n    switch (requestOptions.responseContentType) {\n      case ResponseContentType.MULTIPART_MIXED: {\n        const encoded = await this.encodeFormDataAsync({\n          stringifiedManifest,\n          manifestPartHeaders,\n          certificateChainBody,\n        });\n        headers.set('content-type', `multipart/mixed; boundary=${encoded.boundary}`);\n        return {\n          body: encoded.body,\n          version: runtimeVersion,\n          headers,\n        };\n      }\n      case ResponseContentType.APPLICATION_EXPO_JSON:\n      case ResponseContentType.APPLICATION_JSON:\n      case ResponseContentType.TEXT_PLAIN: {\n        headers.set(\n          'content-type',\n          ExpoGoManifestHandlerMiddleware.getContentTypeForResponseContentType(\n            requestOptions.responseContentType\n          )\n        );\n        if (manifestPartHeaders) {\n          Object.entries(manifestPartHeaders).forEach(([key, value]) => {\n            headers.set(key, value);\n          });\n        }\n\n        return {\n          body: stringifiedManifest,\n          version: runtimeVersion,\n          headers,\n        };\n      }\n    }\n  }\n\n  private static getContentTypeForResponseContentType(\n    responseContentType: ResponseContentType\n  ): string {\n    switch (responseContentType) {\n      case ResponseContentType.MULTIPART_MIXED:\n        return 'multipart/mixed';\n      case ResponseContentType.APPLICATION_EXPO_JSON:\n        return 'application/expo+json';\n      case ResponseContentType.APPLICATION_JSON:\n        return 'application/json';\n      case ResponseContentType.TEXT_PLAIN:\n        return 'text/plain';\n    }\n  }\n\n  private encodeFormDataAsync({\n    stringifiedManifest,\n    manifestPartHeaders,\n    certificateChainBody,\n  }: {\n    stringifiedManifest: string;\n    manifestPartHeaders: { 'expo-signature': string } | null;\n    certificateChainBody: string | null;\n  }): Promise<EncodedFormData> {\n    const fields: FormDataField[] = [\n      {\n        name: 'manifest',\n        value: stringifiedManifest,\n        contentType: 'application/json',\n        partHeaders: manifestPartHeaders,\n      },\n    ];\n    if (certificateChainBody && certificateChainBody.length > 0) {\n      fields.push({\n        name: 'certificate_chain',\n        value: certificateChainBody,\n        contentType: 'application/x-pem-file',\n      });\n    }\n    return encodeMultipartMixed(fields);\n  }\n\n  private static async getScopeKeyAsync({\n    slug,\n    codeSigningInfo,\n  }: {\n    slug: string;\n    codeSigningInfo: CodeSigningInfo | null;\n  }): Promise<string> {\n    const scopeKeyFromCodeSigningInfo = codeSigningInfo?.scopeKey;\n    if (scopeKeyFromCodeSigningInfo) {\n      return scopeKeyFromCodeSigningInfo;\n    }\n\n    // Log.warn(\n    //   env.EXPO_OFFLINE\n    //     ? 'Using anonymous scope key in manifest for offline mode.'\n    //     : 'Using anonymous scope key in manifest.'\n    // );\n    return await getAnonymousScopeKeyAsync(slug);\n  }\n}\n\nasync function getAnonymousScopeKeyAsync(slug: string): Promise<string> {\n  const userAnonymousIdentifier = await getAnonymousIdAsync();\n  return `@${ANONYMOUS_USERNAME}/${slug}-${userAnonymousIdentifier}`;\n}\n\nfunction convertToDictionaryItemsRepresentation(obj: { [key: string]: string }): Dictionary {\n  return new Map(\n    Object.entries(obj).map(([k, v]) => {\n      return [k, [v, new Map()]];\n    })\n  );\n}\n"], "names": ["ExpoGoManifestHandlerMiddleware", "ResponseContentType", "debug", "require", "ManifestMiddleware", "getParsedHeaders", "req", "platform", "parsePlatformHeader", "assertRuntimePlatform", "accept", "accepts", "acceptedType", "types", "responseContentType", "expectSignature", "headers", "String", "hostname", "stripPort", "protocol", "getDefaultResponseHeaders", "Map", "set", "_getManifestResponseAsync", "requestOptions", "exp", "hostUri", "expoGoConfig", "bundleUrl", "_resolveProjectSettingsAsync", "runtimeVersion", "resolveRuntimeVersionWithExpoUpdatesAsync", "projectRoot", "Updates", "getRuntimeVersionAsync", "policy", "CommandError", "codeSigningInfo", "getCodeSigningInfoAsync", "options", "privateKeyPath", "easProjectId", "extra", "eas", "projectId", "<PERSON><PERSON>ey", "getScopeKeyAsync", "slug", "expoUpdatesManifest", "id", "crypto", "randomUUID", "createdAt", "Date", "toISOString", "launchAsset", "key", "contentType", "url", "assets", "metadata", "undefined", "expoClient", "expoGo", "stringifiedManifest", "JSON", "stringify", "manifestPartHeaders", "certificateChainBody", "signature", "signManifestString", "serializeDictionary", "convertToDictionaryItemsRepresentation", "keyid", "keyId", "sig", "alg", "certificateChainForResponse", "join", "encoded", "encodeFormDataAsync", "boundary", "body", "version", "getContentTypeForResponseContentType", "Object", "entries", "for<PERSON>ach", "value", "fields", "name", "partHeaders", "length", "push", "encodeMultipartMixed", "scopeKeyFromCodeSigningInfo", "getAnonymousScopeKeyAsync", "userAnonymousIdentifier", "getAnonymousIdAsync", "ANONYMOUS_USERNAME", "obj", "map", "k", "v"], "mappings": ";;;;;;;;;;;IAuCaA,+BAA+B;eAA/BA;;IAZDC,mBAAmB;eAAnBA;;;;yBA1BY;;;;;;;gEACJ;;;;;;;gEACD;;;;;;;yBAC6B;;;;;;oCAEQ;iCACG;2DACD;8BAEtB;sBACD;6BAK5B;wBACsB;gCAKtB;qBACmB;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAExB,IAAA,AAAKF,6CAAAA;;;;;WAAAA;;AAYL,MAAMD,wCAAwCI,sCAAkB;IAC9DC,iBAAiBC,GAAkB,EAA6B;QACrE,IAAIC,WAAWC,IAAAA,oCAAmB,EAACF;QAEnC,IAAI,CAACC,UAAU;YACbL,MACE,CAAC,yFAAyF,CAAC;YAE7FK,WAAW;QACb;QAEAE,IAAAA,sCAAqB,EAACF;QAEtB,+FAA+F;QAC/F,kGAAkG;QAClG,gGAAgG;QAChG,0BAA0B;QAC1B,MAAMG,SAASC,IAAAA,kBAAO,EAACL;QACvB,MAAMM,eAAeF,OAAOG,KAAK,CAAC;YAChC;YACA;YACA;YACA;YACA;SACD;QAED,IAAIC;QACJ,OAAQF;YACN,KAAK;gBACHE;gBACA;YACF,KAAK;gBACHA;gBACA;YACF,KAAK;gBACHA;gBACA;YACF;gBACEA;gBACA;QACJ;QAEA,MAAMC,kBAAkBT,IAAIU,OAAO,CAAC,wBAAwB;QAE5D,OAAO;YACLF;YACAP;YACAQ,iBAAiBA,kBAAkBE,OAAOF,mBAAmB;YAC7DG,UAAUC,IAAAA,cAAS,EAACb,IAAIU,OAAO,CAAC,OAAO;YACvCI,UAAUd,IAAIU,OAAO,CAAC,oBAAoB;QAC5C;IACF;IAEQK,4BAA2C;QACjD,MAAML,UAAU,IAAIM;QACpB,+DAA+D;QAC/DN,QAAQO,GAAG,CAAC,yBAAyB;QACrCP,QAAQO,GAAG,CAAC,oBAAoB;QAChCP,QAAQO,GAAG,CAAC,iBAAiB;QAC7B,OAAOP;IACT;IAEA,MAAaQ,0BAA0BC,cAAyC,EAI7E;YA8BoBC,gBAAAA;QA7BrB,MAAM,EAAEA,GAAG,EAAEC,OAAO,EAAEC,YAAY,EAAEC,SAAS,EAAE,GAC7C,MAAM,IAAI,CAACC,4BAA4B,CAACL;QAE1C,MAAMM,iBACJ,AAAC,MAAMC,IAAAA,oFAAyC,EAAC;YAC/CC,aAAa,IAAI,CAACA,WAAW;YAC7B1B,UAAUkB,eAAelB,QAAQ;QACnC,MACA,gGAAgG;QAChG,oGAAoG;QACpG,iCAAiC;QAChC,MAAM2B,wBAAO,CAACC,sBAAsB,CACnC,IAAI,CAACF,WAAW,EAChB;YAAE,GAAGP,GAAG;YAAEK,gBAAgBL,IAAIK,cAAc,IAAI;gBAAEK,QAAQ;YAAa;QAAE,GACzEX,eAAelB,QAAQ;QAE3B,IAAI,CAACwB,gBAAgB;YACnB,MAAM,IAAIM,oBAAY,CACpB,uBACA,CAAC,kDAAkD,EAAEZ,eAAelB,QAAQ,CAAC,CAAC,CAAC;QAEnF;QAEA,MAAM+B,kBAAkB,MAAMC,IAAAA,oCAAuB,EACnDb,KACAD,eAAeV,eAAe,EAC9B,IAAI,CAACyB,OAAO,CAACC,cAAc;QAG7B,MAAMC,gBAAehB,aAAAA,IAAIiB,KAAK,sBAATjB,iBAAAA,WAAWkB,GAAG,qBAAdlB,eAAgBmB,SAAS;QAC9C,MAAMC,WAAW,MAAM9C,gCAAgC+C,gBAAgB,CAAC;YACtEC,MAAMtB,IAAIsB,IAAI;YACdV;QACF;QAEA,MAAMW,sBAA2C;YAC/CC,IAAIC,iBAAM,CAACC,UAAU;YACrBC,WAAW,IAAIC,OAAOC,WAAW;YACjCxB;YACAyB,aAAa;gBACXC,KAAK;gBACLC,aAAa;gBACbC,KAAK9B;YACP;YACA+B,QAAQ,EAAE;YACVC,UAAU,CAAC;YACXlB,OAAO;gBACLC,KAAK;oBACHC,WAAWH,gBAAgBoB;gBAC7B;gBACAC,YAAY;oBACV,GAAGrC,GAAG;oBACNC;gBACF;gBACAqC,QAAQpC;gBACRkB;YACF;QACF;QAEA,MAAMmB,sBAAsBC,KAAKC,SAAS,CAAClB;QAE3C,IAAImB,sBAA2D;QAC/D,IAAIC,uBAAsC;QAC1C,IAAI/B,iBAAiB;YACnB,MAAMgC,YAAYC,IAAAA,+BAAkB,EAACN,qBAAqB3B;YAC1D8B,sBAAsB;gBACpB,kBAAkBI,IAAAA,wCAAmB,EACnCC,uCAAuC;oBACrCC,OAAOpC,gBAAgBqC,KAAK;oBAC5BC,KAAKN;oBACLO,KAAK;gBACP;YAEJ;YACAR,uBAAuB/B,gBAAgBwC,2BAA2B,CAACC,IAAI,CAAC;QAC1E;QAEA,MAAM/D,UAAU,IAAI,CAACK,yBAAyB;QAE9C,OAAQI,eAAeX,mBAAmB;YACxC;gBAA0C;oBACxC,MAAMkE,UAAU,MAAM,IAAI,CAACC,mBAAmB,CAAC;wBAC7ChB;wBACAG;wBACAC;oBACF;oBACArD,QAAQO,GAAG,CAAC,gBAAgB,CAAC,0BAA0B,EAAEyD,QAAQE,QAAQ,EAAE;oBAC3E,OAAO;wBACLC,MAAMH,QAAQG,IAAI;wBAClBC,SAASrD;wBACTf;oBACF;gBACF;YACA;YACA;YACA;gBAAqC;oBACnCA,QAAQO,GAAG,CACT,gBACAvB,gCAAgCqF,oCAAoC,CAClE5D,eAAeX,mBAAmB;oBAGtC,IAAIsD,qBAAqB;wBACvBkB,OAAOC,OAAO,CAACnB,qBAAqBoB,OAAO,CAAC,CAAC,CAAC/B,KAAKgC,MAAM;4BACvDzE,QAAQO,GAAG,CAACkC,KAAKgC;wBACnB;oBACF;oBAEA,OAAO;wBACLN,MAAMlB;wBACNmB,SAASrD;wBACTf;oBACF;gBACF;QACF;IACF;IAEA,OAAeqE,qCACbvE,mBAAwC,EAChC;QACR,OAAQA;YACN;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQmE,oBAAoB,EAC1BhB,mBAAmB,EACnBG,mBAAmB,EACnBC,oBAAoB,EAKrB,EAA4B;QAC3B,MAAMqB,SAA0B;YAC9B;gBACEC,MAAM;gBACNF,OAAOxB;gBACPP,aAAa;gBACbkC,aAAaxB;YACf;SACD;QACD,IAAIC,wBAAwBA,qBAAqBwB,MAAM,GAAG,GAAG;YAC3DH,OAAOI,IAAI,CAAC;gBACVH,MAAM;gBACNF,OAAOpB;gBACPX,aAAa;YACf;QACF;QACA,OAAOqC,IAAAA,oCAAoB,EAACL;IAC9B;IAEA,aAAqB3C,iBAAiB,EACpCC,IAAI,EACJV,eAAe,EAIhB,EAAmB;QAClB,MAAM0D,8BAA8B1D,mCAAAA,gBAAiBQ,QAAQ;QAC7D,IAAIkD,6BAA6B;YAC/B,OAAOA;QACT;QAEA,YAAY;QACZ,qBAAqB;QACrB,kEAAkE;QAClE,iDAAiD;QACjD,KAAK;QACL,OAAO,MAAMC,0BAA0BjD;IACzC;AACF;AAEA,eAAeiD,0BAA0BjD,IAAY;IACnD,MAAMkD,0BAA0B,MAAMC,IAAAA,iCAAmB;IACzD,OAAO,CAAC,CAAC,EAAEC,wBAAkB,CAAC,CAAC,EAAEpD,KAAK,CAAC,EAAEkD,yBAAyB;AACpE;AAEA,SAASzB,uCAAuC4B,GAA8B;IAC5E,OAAO,IAAI/E,IACTgE,OAAOC,OAAO,CAACc,KAAKC,GAAG,CAAC,CAAC,CAACC,GAAGC,EAAE;QAC7B,OAAO;YAACD;YAAG;gBAACC;gBAAG,IAAIlF;aAAM;SAAC;IAC5B;AAEJ"}