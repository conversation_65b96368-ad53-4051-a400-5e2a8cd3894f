/**
 * Быстрый тест подключения к базе данных
 */

require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
  connectionTimeoutMillis: 5000,
});

async function quickTest() {
  console.log('🔍 Быстрый тест подключения к БД...');
  
  try {
    const client = await pool.connect();
    console.log('✅ Подключение к БД успешно');
    
    // Проверяем новые таблицы
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%contract%' OR table_name LIKE '%transaction%'
      ORDER BY table_name
    `);
    
    console.log(`📋 Найдено таблиц: ${result.rows.length}`);
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    client.release();
    await pool.end();
    
    console.log('✅ Тест завершен успешно');
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
    process.exit(1);
  }
}

quickTest();
