#!/usr/bin/env node

/**
 * Скрипт для быстрой настройки смарт-контрактов
 * Автоматизирует процесс компиляции, настройки и проверки
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Цвета для консоли
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function checkEnvironment() {
  logStep('1', 'Проверка переменных окружения');
  
  const requiredVars = [
    'DB_USER', 'DB_HOST', 'DB_NAME', 'DB_PASSWORD', 'DB_PORT'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    logError(`Отсутствуют обязательные переменные окружения: ${missingVars.join(', ')}`);
    logWarning('Скопируйте .env.example в .env и заполните необходимые значения');
    return false;
  }
  
  logSuccess('Основные переменные окружения настроены');
  
  // Проверка TON настроек
  const tonVars = ['TON_API_KEY', 'PLATFORM_ADDRESS', 'PLATFORM_MNEMONIC'];
  const missingTonVars = tonVars.filter(varName => !process.env[varName] || process.env[varName] === 'your_api_key_here');
  
  if (missingTonVars.length > 0) {
    logWarning(`Не настроены TON переменные: ${missingTonVars.join(', ')}`);
    logWarning('Смарт-контракты будут работать в ограниченном режиме');
  } else {
    logSuccess('TON переменные настроены');
  }
  
  return true;
}

async function checkDatabase() {
  logStep('2', 'Проверка подключения к базе данных');
  
  const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
    ssl: false,
  });
  
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    logSuccess('Подключение к базе данных успешно');
    
    // Проверка существования таблиц
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('device_contracts', 'streamer_earnings', 'control_transactions')
    `;
    
    const result = await pool.query(tablesQuery);
    const existingTables = result.rows.map(row => row.table_name);
    
    const requiredTables = ['device_contracts', 'streamer_earnings', 'control_transactions'];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      logWarning(`Отсутствуют таблицы: ${missingTables.join(', ')}`);
      logWarning('Выполните миграции базы данных');
    } else {
      logSuccess('Все необходимые таблицы существуют');
    }
    
    await pool.end();
    return true;
  } catch (error) {
    logError(`Ошибка подключения к базе данных: ${error.message}`);
    await pool.end();
    return false;
  }
}

async function checkDependencies() {
  logStep('3', 'Проверка зависимостей');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    logError('package.json не найден');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const requiredDeps = [
    '@ton/ton',
    '@ton/core', 
    '@ton/crypto',
    '@ton/blueprint'
  ];
  
  const missingDeps = requiredDeps.filter(dep => !dependencies[dep]);
  
  if (missingDeps.length > 0) {
    logError(`Отсутствуют зависимости: ${missingDeps.join(', ')}`);
    log('Установите их командой:', 'yellow');
    log(`npm install ${missingDeps.join(' ')}`, 'cyan');
    return false;
  }
  
  logSuccess('Все необходимые зависимости установлены');
  return true;
}

async function compileContracts() {
  logStep('4', 'Компиляция смарт-контрактов');
  
  const contractPath = path.join(__dirname, '..', 'contracts', 'device_control.fc');
  const compilePath = path.join(__dirname, '..', 'contracts', 'compile.js');
  
  if (!fs.existsSync(contractPath)) {
    logError('Файл смарт-контракта не найден: contracts/device_control.fc');
    return false;
  }
  
  if (!fs.existsSync(compilePath)) {
    logError('Скрипт компиляции не найден: contracts/compile.js');
    return false;
  }
  
  try {
    const { compileContract } = require(compilePath);
    await compileContract();
    logSuccess('Смарт-контракт успешно скомпилирован');
    return true;
  } catch (error) {
    logError(`Ошибка компиляции: ${error.message}`);
    return false;
  }
}

async function checkServices() {
  logStep('5', 'Проверка сервисов');
  
  const servicesPath = path.join(__dirname, '..', 'services');
  const requiredServices = [
    'TonService.js',
    'ContractDeployService.js',
    'TransactionMonitorService.js'
  ];
  
  let allServicesExist = true;
  
  for (const service of requiredServices) {
    const servicePath = path.join(servicesPath, service);
    if (!fs.existsSync(servicePath)) {
      logError(`Сервис не найден: ${service}`);
      allServicesExist = false;
    }
  }
  
  if (allServicesExist) {
    logSuccess('Все сервисы найдены');
    
    // Проверка инициализации сервисов
    try {
      const TonService = require(path.join(servicesPath, 'TonService.js'));
      const ContractDeployService = require(path.join(servicesPath, 'ContractDeployService.js'));
      
      logSuccess('Сервисы успешно загружены');
      return true;
    } catch (error) {
      logError(`Ошибка загрузки сервисов: ${error.message}`);
      return false;
    }
  }
  
  return false;
}

async function generateSummary() {
  logStep('6', 'Генерация отчета');
  
  log('\n' + '='.repeat(60), 'cyan');
  log('📋 ОТЧЕТ О НАСТРОЙКЕ СМАРТ-КОНТРАКТОВ', 'bright');
  log('='.repeat(60), 'cyan');
  
  log('\n🔧 Конфигурация:', 'yellow');
  log(`   Сеть: ${process.env.TON_NETWORK || 'testnet'}`, 'reset');
  log(`   Комиссия платформы: ${(process.env.PLATFORM_FEE_PERCENT || 500) / 100}%`, 'reset');
  log(`   Адрес платформы: ${process.env.PLATFORM_ADDRESS || 'не установлен'}`, 'reset');
  
  log('\n📁 Файлы:', 'yellow');
  log('   ✅ Смарт-контракт: contracts/device_control.fc', 'green');
  log('   ✅ Конфигурация: config/contracts.js', 'green');
  log('   ✅ Документация: docs/SMART_CONTRACTS_INTEGRATION.md', 'green');
  
  log('\n🌐 API Endpoints:', 'yellow');
  log('   POST /api/contracts/create-device-contract', 'reset');
  log('   POST /api/contracts/buy-time', 'reset');
  log('   POST /api/contracts/withdraw-earnings', 'reset');
  log('   GET  /admin-panel', 'reset');
  
  log('\n🚀 Следующие шаги:', 'yellow');
  log('   1. Настройте переменные окружения в .env', 'reset');
  log('   2. Выполните миграции базы данных', 'reset');
  log('   3. Запустите сервер: npm start', 'reset');
  log('   4. Откройте админ-панель: http://localhost:4000/admin-panel', 'reset');
  
  log('\n' + '='.repeat(60), 'cyan');
}

async function main() {
  log('🚀 НАСТРОЙКА СМАРТ-КОНТРАКТОВ ДЛЯ УПРАВЛЕНИЯ УСТРОЙСТВАМИ', 'bright');
  log('='.repeat(60), 'cyan');
  
  const checks = [
    checkEnvironment,
    checkDatabase,
    checkDependencies,
    compileContracts,
    checkServices
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = await check();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      logError(`Ошибка при выполнении проверки: ${error.message}`);
      allPassed = false;
    }
  }
  
  await generateSummary();
  
  if (allPassed) {
    log('\n🎉 Настройка завершена успешно!', 'green');
    log('Система готова к работе со смарт-контрактами', 'green');
  } else {
    log('\n⚠️  Настройка завершена с предупреждениями', 'yellow');
    log('Исправьте указанные проблемы для полной функциональности', 'yellow');
  }
}

// Запуск скрипта
if (require.main === module) {
  main().catch(error => {
    logError(`Критическая ошибка: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkEnvironment,
  checkDatabase,
  checkDependencies,
  compileContracts,
  checkServices
};
