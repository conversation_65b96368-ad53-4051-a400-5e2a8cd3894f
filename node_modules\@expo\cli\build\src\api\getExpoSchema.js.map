{"version": 3, "sources": ["../../../src/api/getExpoSchema.ts"], "sourcesContent": ["import { J<PERSON>NObject } from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { createCachedFetch, getResponseDataOrThrow } from './rest/client';\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { jsonSchemaDeref } from '../utils/jsonSchemaDeref';\n\nexport type Schema = any;\n\nexport type AssetSchema = {\n  fieldPath: string;\n};\n\nconst schemaJson: { [sdkVersion: string]: Schema } = {};\n\nexport async function _getSchemaAsync(sdkVersion: string): Promise<Schema> {\n  const json = await getSchemaJSONAsync(sdkVersion);\n  // NOTE(@kitten): This is a replacement for the `json-schema-deref-sync` package.\n  // We re-implemented it locally to remove it, since it comes with heavy dependencies\n  // and a large install time impact.\n  // The tests have been ported to match the behaviour closely, but we removed\n  // local file ref support. For our purposes the behaviour should be identical.\n  return jsonSchemaDeref(json.schema);\n}\n\n/**\n * Array of schema nodes that refer to assets along with their field path (eg. 'notification.icon')\n *\n * @param sdkVersion\n */\nexport async function getAssetSchemasAsync(sdkVersion: string = 'UNVERSIONED'): Promise<string[]> {\n  // If no SDK version is available then fall back to unversioned\n  const schema = await _getSchemaAsync(sdkVersion);\n  const assetSchemas: string[] = [];\n  const visit = (node: Schema, fieldPath: string) => {\n    if (node.meta && node.meta.asset) {\n      assetSchemas.push(fieldPath);\n    }\n    const properties = node.properties;\n    if (properties) {\n      Object.keys(properties).forEach((property) =>\n        visit(properties[property], `${fieldPath}${fieldPath.length > 0 ? '.' : ''}${property}`)\n      );\n    }\n  };\n  visit(schema, '');\n\n  return assetSchemas;\n}\n\nasync function getSchemaJSONAsync(sdkVersion: string): Promise<{ schema: Schema }> {\n  if (env.EXPO_UNIVERSE_DIR) {\n    return JSON.parse(\n      fs\n        .readFileSync(\n          path.join(\n            env.EXPO_UNIVERSE_DIR,\n            'server',\n            'www',\n            'xdl-schemas',\n            'UNVERSIONED-schema.json'\n          )\n        )\n        .toString()\n    );\n  }\n\n  if (!schemaJson[sdkVersion]) {\n    try {\n      schemaJson[sdkVersion] = await getConfigurationSchemaAsync(sdkVersion);\n    } catch (e: any) {\n      if (e.code === 'INVALID_JSON') {\n        throw new CommandError('INVALID_JSON', `Couldn't read schema from server`);\n      }\n\n      throw e;\n    }\n  }\n\n  return schemaJson[sdkVersion];\n}\n\nasync function getConfigurationSchemaAsync(sdkVersion: string): Promise<JSONObject> {\n  // Reconstruct the cached fetch since caching could be disabled.\n  const fetchAsync = createCachedFetch({\n    cacheDirectory: 'schema-cache',\n    // We'll use a 1 week cache for versions so older versions get flushed out eventually.\n    ttl: 1000 * 60 * 60 * 24 * 7,\n  });\n  const response = await fetchAsync(`project/configuration/schema/${sdkVersion}`);\n  const json = await response.json();\n\n  return getResponseDataOrThrow<JSONObject>(json);\n}\n"], "names": ["_getSchemaAsync", "getAssetSchemasAsync", "schema<PERSON>son", "sdkVersion", "json", "getSchemaJSONAsync", "jsonSchemaDeref", "schema", "assetSchemas", "visit", "node", "fieldPath", "meta", "asset", "push", "properties", "Object", "keys", "for<PERSON>ach", "property", "length", "env", "EXPO_UNIVERSE_DIR", "JSON", "parse", "fs", "readFileSync", "path", "join", "toString", "getConfigurationSchemaAsync", "e", "code", "CommandError", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "response", "getResponseDataOrThrow"], "mappings": ";;;;;;;;;;;IAiBsBA,eAAe;eAAfA;;IAeAC,oBAAoB;eAApBA;;;;gEA/BP;;;;;;;gEACE;;;;;;wBAEyC;qBACtC;wBACS;iCACG;;;;;;AAQhC,MAAMC,aAA+C,CAAC;AAE/C,eAAeF,gBAAgBG,UAAkB;IACtD,MAAMC,OAAO,MAAMC,mBAAmBF;IACtC,iFAAiF;IACjF,oFAAoF;IACpF,mCAAmC;IACnC,4EAA4E;IAC5E,8EAA8E;IAC9E,OAAOG,IAAAA,gCAAe,EAACF,KAAKG,MAAM;AACpC;AAOO,eAAeN,qBAAqBE,aAAqB,aAAa;IAC3E,+DAA+D;IAC/D,MAAMI,SAAS,MAAMP,gBAAgBG;IACrC,MAAMK,eAAyB,EAAE;IACjC,MAAMC,QAAQ,CAACC,MAAcC;QAC3B,IAAID,KAAKE,IAAI,IAAIF,KAAKE,IAAI,CAACC,KAAK,EAAE;YAChCL,aAAaM,IAAI,CAACH;QACpB;QACA,MAAMI,aAAaL,KAAKK,UAAU;QAClC,IAAIA,YAAY;YACdC,OAAOC,IAAI,CAACF,YAAYG,OAAO,CAAC,CAACC,WAC/BV,MAAMM,UAAU,CAACI,SAAS,EAAE,GAAGR,YAAYA,UAAUS,MAAM,GAAG,IAAI,MAAM,KAAKD,UAAU;QAE3F;IACF;IACAV,MAAMF,QAAQ;IAEd,OAAOC;AACT;AAEA,eAAeH,mBAAmBF,UAAkB;IAClD,IAAIkB,QAAG,CAACC,iBAAiB,EAAE;QACzB,OAAOC,KAAKC,KAAK,CACfC,aAAE,CACCC,YAAY,CACXC,eAAI,CAACC,IAAI,CACPP,QAAG,CAACC,iBAAiB,EACrB,UACA,OACA,eACA,4BAGHO,QAAQ;IAEf;IAEA,IAAI,CAAC3B,UAAU,CAACC,WAAW,EAAE;QAC3B,IAAI;YACFD,UAAU,CAACC,WAAW,GAAG,MAAM2B,4BAA4B3B;QAC7D,EAAE,OAAO4B,GAAQ;YACf,IAAIA,EAAEC,IAAI,KAAK,gBAAgB;gBAC7B,MAAM,IAAIC,oBAAY,CAAC,gBAAgB,CAAC,gCAAgC,CAAC;YAC3E;YAEA,MAAMF;QACR;IACF;IAEA,OAAO7B,UAAU,CAACC,WAAW;AAC/B;AAEA,eAAe2B,4BAA4B3B,UAAkB;IAC3D,gEAAgE;IAChE,MAAM+B,aAAaC,IAAAA,yBAAiB,EAAC;QACnCC,gBAAgB;QAChB,sFAAsF;QACtFC,KAAK,OAAO,KAAK,KAAK,KAAK;IAC7B;IACA,MAAMC,WAAW,MAAMJ,WAAW,CAAC,6BAA6B,EAAE/B,YAAY;IAC9E,MAAMC,OAAO,MAAMkC,SAASlC,IAAI;IAEhC,OAAOmC,IAAAA,8BAAsB,EAAanC;AAC5C"}