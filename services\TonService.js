/**
 * Сервис для работы с TON блокчейном
 * Обеспечивает взаимодействие со смарт-контрактами устройств
 */

const { TonClient, WalletContractV4, internal, fromNano, toNano } = require('@ton/ton');
const { mnemonicToWalletKey } = require('@ton/crypto');
const { Address, Cell, beginCell } = require('@ton/core');

class TonService {
  constructor(config) {
    this.config = config;
    this.client = new TonClient({
      endpoint: config.endpoint || 'https://toncenter.com/api/v2/jsonRPC',
      apiKey: config.apiKey
    });
    this.platformWallet = null;
    this.init();
  }

  async init() {
    try {
      // Инициализация кошелька платформы для деплоя контрактов
      if (this.config.platformMnemonic) {
        const keyPair = await mnemonicToWalletKey(this.config.platformMnemonic.split(' '));
        this.platformWallet = WalletContractV4.create({
          publicKey: keyPair.publicKey,
          workchain: 0
        });
      }
      console.log('✅ TonService инициализирован');
    } catch (error) {
      console.error('❌ Ошибка инициализации TonService:', error);
    }
  }

  /**
   * Получение информации о транзакции
   */
  async getTransaction(hash) {
    try {
      const response = await this.client.getTransactions(Address.parse('EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c'), {
        limit: 1,
        hash: hash
      });
      return response[0] || null;
    } catch (error) {
      console.error('Ошибка получения транзакции:', error);
      throw error;
    }
  }

  /**
   * Получение баланса адреса
   */
  async getBalance(address) {
    try {
      const balance = await this.client.getBalance(Address.parse(address));
      return fromNano(balance);
    } catch (error) {
      console.error('Ошибка получения баланса:', error);
      throw error;
    }
  }

  /**
   * Деплой смарт-контракта устройства
   */
  async deployDeviceContract(deviceId, ownerAddress, pricePerMinute, platformFeePercent = 500) {
    try {
      if (!this.platformWallet) {
        throw new Error('Platform wallet not initialized');
      }

      // Создание кода контракта (здесь должен быть скомпилированный код)
      const contractCode = Cell.fromBase64(this.config.deviceContractCode);

      // Создание начального состояния контракта
      const initialData = beginCell()
        .storeAddress(Address.parse(ownerAddress))           // owner_address
        .storeUint(this.hashDeviceId(deviceId), 256)        // device_id
        .storeCoins(toNano(pricePerMinute))                 // price_per_minute
        .storeAddress(null)                                 // current_controller (empty)
        .storeUint(0, 32)                                   // control_end_time
        .storeUint(platformFeePercent, 16)                  // platform_fee_percent
        .storeAddress(Address.parse(this.config.platformAddress)) // platform_address
        .storeCoins(0)                                      // total_earned
        .storeUint(1, 1)                                    // is_active
        .endCell();

      // Вычисление адреса контракта
      const contractAddress = this.calculateContractAddress(contractCode, initialData);

      // Создание сообщения для деплоя
      const deployMessage = internal({
        to: contractAddress,
        value: toNano('0.1'), // Начальный баланс контракта
        init: {
          code: contractCode,
          data: initialData
        },
        body: beginCell().endCell()
      });

      return {
        contractAddress: contractAddress.toString(),
        deployMessage,
        estimatedFee: '0.1'
      };
    } catch (error) {
      console.error('Ошибка деплоя контракта:', error);
      throw error;
    }
  }

  /**
   * Покупка времени управления устройством
   */
  async buyControlTime(contractAddress, durationMinutes, buyerAddress) {
    try {
      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      // Получение текущей цены
      const deviceInfo = await contract.getDeviceInfo();
      const pricePerMinute = deviceInfo[2];
      const totalCost = pricePerMinute * BigInt(durationMinutes);

      // Создание сообщения для покупки времени
      const buyMessage = beginCell()
        .storeUint(0x1, 32)                    // op::buy_control_time
        .storeUint(Date.now(), 64)             // query_id
        .storeUint(durationMinutes, 16)        // duration_minutes
        .endCell();

      return {
        to: contractAddress,
        value: totalCost + toNano('0.05'), // +0.05 TON на газ
        body: buyMessage,
        estimatedCost: fromNano(totalCost)
      };
    } catch (error) {
      console.error('Ошибка создания транзакции покупки:', error);
      throw error;
    }
  }

  /**
   * Получение статуса управления устройством
   */
  async getControlStatus(contractAddress) {
    try {
      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const status = await contract.getControlStatus();

      return {
        isControlled: status[0],
        currentController: status[1]?.toString() || null,
        controlEndTime: Number(status[2]),
        timeRemaining: Number(status[3])
      };
    } catch (error) {
      console.error('Ошибка получения статуса управления:', error);
      throw error;
    }
  }

  /**
   * Установка новой цены за управление
   */
  async setDevicePrice(contractAddress, newPrice, ownerAddress) {
    try {
      const setPriceMessage = beginCell()
        .storeUint(0x3, 32)                    // op::set_price
        .storeUint(Date.now(), 64)             // query_id
        .storeCoins(toNano(newPrice))          // new_price
        .endCell();

      return {
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: setPriceMessage
      };
    } catch (error) {
      console.error('Ошибка создания транзакции установки цены:', error);
      throw error;
    }
  }

  /**
   * Вывод заработанных средств
   */
  async withdrawEarnings(contractAddress, ownerAddress) {
    try {
      const withdrawMessage = beginCell()
        .storeUint(0x4, 32)                    // op::withdraw_earnings
        .storeUint(Date.now(), 64)             // query_id
        .endCell();

      return {
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: withdrawMessage
      };
    } catch (error) {
      console.error('Ошибка создания транзакции вывода:', error);
      throw error;
    }
  }

  /**
   * Экстренная остановка контракта
   */
  async emergencyStop(contractAddress, ownerAddress) {
    try {
      const stopMessage = beginCell()
        .storeUint(0x5, 32)                    // op::emergency_stop
        .storeUint(Date.now(), 64)             // query_id
        .endCell();

      return {
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: stopMessage
      };
    } catch (error) {
      console.error('Ошибка создания транзакции остановки:', error);
      throw error;
    }
  }

  /**
   * Мониторинг транзакций контракта
   */
  async getContractTransactions(contractAddress, limit = 10) {
    try {
      const transactions = await this.client.getTransactions(
        Address.parse(contractAddress),
        { limit }
      );

      return transactions.map(tx => ({
        hash: tx.hash().toString('hex'),
        timestamp: tx.now,
        value: fromNano(tx.inMessage?.value || 0),
        from: tx.inMessage?.source?.toString() || null,
        success: tx.description.type === 'generic' && tx.description.computePhase.type === 'vm' && tx.description.computePhase.success
      }));
    } catch (error) {
      console.error('Ошибка получения транзакций контракта:', error);
      throw error;
    }
  }

  /**
   * Валидация транзакции
   */
  async validateTransaction(hash, expectedAmount, expectedRecipient) {
    try {
      const tx = await this.getTransaction(hash);
      if (!tx) return false;

      const actualAmount = fromNano(tx.inMessage?.value || 0);
      const actualRecipient = tx.inMessage?.destination?.toString();

      return (
        parseFloat(actualAmount) >= parseFloat(expectedAmount) &&
        actualRecipient === expectedRecipient
      );
    } catch (error) {
      console.error('Ошибка валидации транзакции:', error);
      return false;
    }
  }

  /**
   * Вспомогательные методы
   */
  hashDeviceId(deviceId) {
    // Простое хеширование device_id для использования в контракте
    const crypto = require('crypto');
    return BigInt('0x' + crypto.createHash('sha256').update(deviceId).digest('hex').slice(0, 64));
  }

  calculateContractAddress(code, data) {
    // Вычисление адреса контракта на основе кода и данных
    const stateInit = beginCell()
      .storeBit(0) // split_depth
      .storeBit(0) // special
      .storeBit(1) // code
      .storeRef(code)
      .storeBit(1) // data
      .storeRef(data)
      .storeBit(0) // library
      .endCell();

    return new Address(0, stateInit.hash());
  }

  /**
   * Получение информации о контракте
   */
  async getContractInfo(contractAddress) {
    try {
      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const [deviceInfo, controlStatus, contractStats] = await Promise.all([
        contract.getDeviceInfo(),
        contract.getControlStatus(),
        contract.getContractStats()
      ]);

      return {
        owner: deviceInfo[0]?.toString(),
        deviceId: deviceInfo[1].toString(),
        pricePerMinute: fromNano(deviceInfo[2]),
        isControlled: controlStatus[0],
        currentController: controlStatus[1]?.toString() || null,
        controlEndTime: Number(controlStatus[2]),
        timeRemaining: Number(controlStatus[3]),
        totalEarned: fromNano(contractStats[0]),
        platformFeePercent: Number(contractStats[1]) / 100,
        isActive: Boolean(contractStats[2])
      };
    } catch (error) {
      console.error('Ошибка получения информации о контракте:', error);
      throw error;
    }
  }

  /**
   * Вывод комиссии платформы
   */
  async withdrawPlatformFee(contractAddress, adminWallet) {
    try {
      const withdrawMessage = beginCell()
        .storeUint(0x5, 32)                    // op::withdraw_platform_fee
        .storeUint(Date.now(), 64)             // query_id
        .endCell();

      return {
        success: true,
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: withdrawMessage,
        transactionId: `platform_withdraw_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };
    } catch (error) {
      console.error('Ошибка создания транзакции вывода комиссии платформы:', error);
      throw error;
    }
  }

  /**
   * Получение накопленных балансов контракта
   */
  async getAccumulatedBalances(contractAddress) {
    try {
      if (!this.client) {
        throw new Error('TON клиент не инициализирован');
      }

      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const result = await contract.getAccumulatedBalances();

      return {
        success: true,
        accumulatedEarnings: fromNano(result[0]),
        accumulatedPlatformFee: fromNano(result[1])
      };
    } catch (error) {
      console.error('Ошибка получения накопленных балансов:', error);
      return {
        success: false,
        message: error.message,
        accumulatedEarnings: '0',
        accumulatedPlatformFee: '0'
      };
    }
  }

  /**
   * Обновление накопленных балансов в базе данных
   */
  async syncAccumulatedBalances(pool, contractAddress, deviceId) {
    try {
      const balances = await this.getAccumulatedBalances(contractAddress);

      if (balances.success) {
        await pool.query(`
          UPDATE device_contracts
          SET
            accumulated_earnings = $1,
            accumulated_platform_fee = $2,
            last_sync_time = NOW()
          WHERE device_id = $3 AND contract_address = $4
        `, [
          balances.accumulatedEarnings,
          balances.accumulatedPlatformFee,
          deviceId,
          contractAddress
        ]);

        return {
          success: true,
          accumulatedEarnings: balances.accumulatedEarnings,
          accumulatedPlatformFee: balances.accumulatedPlatformFee
        };
      }

      return balances;
    } catch (error) {
      console.error('Ошибка синхронизации накопленных балансов:', error);
      throw error;
    }
  }
}

module.exports = TonService;
