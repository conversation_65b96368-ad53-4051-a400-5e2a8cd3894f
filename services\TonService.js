/**
 * Сервис для работы с TON блокчейном
 * Обеспечивает взаимодействие со смарт-контрактами устройств
 */

const { TonClient, WalletContractV4, internal, fromNano, toNano } = require('@ton/ton');
const { mnemonicToWalletKey } = require('@ton/crypto');
const UniversalContractService = require('./ContractDeployService');
const { Address, Cell, beginCell } = require('@ton/core');

class TonService {
  constructor(config) {
    this.config = config;
    this.client = new TonClient({
      endpoint: config.endpoint || 'https://toncenter.com/api/v2/jsonRPC',
      apiKey: config.apiKey
    });
    this.platformWallet = null;
    this.init();
  }

  async init() {
    try {
      // Инициализация кошелька платформы для деплоя контрактов
      if (this.config.platformMnemonic) {
        const keyPair = await mnemonicToWalletKey(this.config.platformMnemonic.split(' '));
        this.platformWallet = WalletContractV4.create({
          publicKey: keyPair.publicKey,
          workchain: 0
        });
      }

      // Инициализация универсального сервиса контрактов
      this.universalContractService = new UniversalContractService({
        endpoint: this.config.endpoint,
        apiKey: this.config.apiKey,
        workchain: this.config.workchain || 0,
        platformAddress: this.config.platformAddress,
        platformMnemonic: this.config.platformMnemonic,
        platformFeePercent: this.config.platformFeePercent || 500
      });

      await this.universalContractService.initialize();

      console.log('✅ TonService инициализирован');
    } catch (error) {
      console.error('❌ Ошибка инициализации TonService:', error);
    }
  }

  /**
   * Получение информации о транзакции
   */
  async getTransaction(hash) {
    try {
      const response = await this.client.getTransactions(Address.parse('EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c'), {
        limit: 1,
        hash: hash
      });
      return response[0] || null;
    } catch (error) {
      console.error('Ошибка получения транзакции:', error);
      throw error;
    }
  }

  /**
   * Получение баланса адреса
   */
  async getBalance(address) {
    try {
      const balance = await this.client.getBalance(Address.parse(address));
      return fromNano(balance);
    } catch (error) {
      console.error('Ошибка получения баланса:', error);
      throw error;
    }
  }

  /**
   * Деплой смарт-контракта устройства
   */
  async deployDeviceContract(deviceId, ownerAddress, pricePerMinute, platformFeePercent = 500) {
    try {
      if (!this.platformWallet) {
        throw new Error('Platform wallet not initialized');
      }

      // Создание кода контракта (здесь должен быть скомпилированный код)
      const contractCode = Cell.fromBase64(this.config.deviceContractCode);

      // Создание начального состояния контракта
      const initialData = beginCell()
        .storeAddress(Address.parse(ownerAddress))           // owner_address
        .storeUint(this.hashDeviceId(deviceId), 256)        // device_id
        .storeCoins(toNano(pricePerMinute))                 // price_per_minute
        .storeAddress(null)                                 // current_controller (empty)
        .storeUint(0, 32)                                   // control_end_time
        .storeUint(platformFeePercent, 16)                  // platform_fee_percent
        .storeAddress(Address.parse(this.config.platformAddress)) // platform_address
        .storeCoins(0)                                      // total_earned
        .storeUint(1, 1)                                    // is_active
        .endCell();

      // Вычисление адреса контракта
      const contractAddress = this.calculateContractAddress(contractCode, initialData);

      // Создание сообщения для деплоя
      const deployMessage = internal({
        to: contractAddress,
        value: toNano('0.1'), // Начальный баланс контракта
        init: {
          code: contractCode,
          data: initialData
        },
        body: beginCell().endCell()
      });

      return {
        contractAddress: contractAddress.toString(),
        deployMessage,
        estimatedFee: '0.1'
      };
    } catch (error) {
      console.error('Ошибка деплоя контракта:', error);
      throw error;
    }
  }

  /**
   * Покупка времени управления устройством через универсальный контракт (демо-версия)
   */
  async buyControlTime(streamerAddress, deviceId, durationMinutes, buyerAddress, pricePerMinute = 1.0) {
    try {
      console.log(`💰 Создание транзакции покупки времени для устройства ${deviceId}`);

      // Используем переданную цену или значение по умолчанию
      const totalCost = pricePerMinute * durationMinutes;

      // Создаем мемо-фразу
      const memo = `${streamerAddress}:${deviceId}`;

      // Возвращаем данные для транзакции
      return {
        success: true,
        to: this.getUniversalContractAddress() || 'EQD7xex6_byqQ1uCprpSJo5afnLuJT45lH3ll3gPL0sIrxwS',
        value: totalCost.toString(), // Передаем как строку в TON
        estimatedCost: totalCost.toString(),
        memo: memo,
        body: null // В демо-режиме без payload
      };
    } catch (error) {
      console.error('Ошибка создания транзакции покупки времени:', error);
      throw error;
    }
  }

  /**
   * Получение статуса управления устройством
   */
  async getControlStatus(contractAddress) {
    try {
      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const status = await contract.getControlStatus();

      return {
        isControlled: status[0],
        currentController: status[1]?.toString() || null,
        controlEndTime: Number(status[2]),
        timeRemaining: Number(status[3])
      };
    } catch (error) {
      console.error('Ошибка получения статуса управления:', error);
      throw error;
    }
  }

  /**
   * Установка новой цены за управление
   */
  async setDevicePrice(contractAddress, newPrice, ownerAddress) {
    try {
      const setPriceMessage = beginCell()
        .storeUint(0x3, 32)                    // op::set_price
        .storeUint(Date.now(), 64)             // query_id
        .storeCoins(toNano(newPrice))          // new_price
        .endCell();

      return {
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: setPriceMessage
      };
    } catch (error) {
      console.error('Ошибка создания транзакции установки цены:', error);
      throw error;
    }
  }

  /**
   * Вывод заработанных средств (демо-версия)
   */
  async withdrawEarnings(streamerAddress) {
    try {
      console.log(`💸 Создание транзакции вывода средств для стримера ${streamerAddress}`);

      // В демо-режиме создаем заглушку транзакции
      const transactionId = `withdraw_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      return {
        success: true,
        transactionId: transactionId,
        to: this.getUniversalContractAddress() || 'EQD7xex6_byqQ1uCprpSJo5afnLuJT45lH3ll3gPL0sIrxwS',
        value: '0.05', // Газ для операции
        estimatedFee: '0.05',
        body: null // В демо-режиме без payload
      };
    } catch (error) {
      console.error('Ошибка создания сообщения вывода средств:', error);
      throw error;
    }
  }

  /**
   * Экстренная остановка контракта
   */
  async emergencyStop(contractAddress, ownerAddress) {
    try {
      const stopMessage = beginCell()
        .storeUint(0x5, 32)                    // op::emergency_stop
        .storeUint(Date.now(), 64)             // query_id
        .endCell();

      return {
        to: contractAddress,
        value: toNano('0.05'), // Газ для операции
        body: stopMessage
      };
    } catch (error) {
      console.error('Ошибка создания транзакции остановки:', error);
      throw error;
    }
  }

  /**
   * Мониторинг транзакций контракта
   */
  async getContractTransactions(contractAddress, limit = 10) {
    try {
      const transactions = await this.client.getTransactions(
        Address.parse(contractAddress),
        { limit }
      );

      return transactions.map(tx => ({
        hash: tx.hash().toString('hex'),
        timestamp: tx.now,
        value: fromNano(tx.inMessage?.value || 0),
        from: tx.inMessage?.source?.toString() || null,
        success: tx.description.type === 'generic' && tx.description.computePhase.type === 'vm' && tx.description.computePhase.success
      }));
    } catch (error) {
      console.error('Ошибка получения транзакций контракта:', error);
      throw error;
    }
  }

  /**
   * Валидация транзакции
   */
  async validateTransaction(hash, expectedAmount, expectedRecipient) {
    try {
      const tx = await this.getTransaction(hash);
      if (!tx) return false;

      const actualAmount = fromNano(tx.inMessage?.value || 0);
      const actualRecipient = tx.inMessage?.destination?.toString();

      return (
        parseFloat(actualAmount) >= parseFloat(expectedAmount) &&
        actualRecipient === expectedRecipient
      );
    } catch (error) {
      console.error('Ошибка валидации транзакции:', error);
      return false;
    }
  }

  /**
   * Вспомогательные методы
   */
  hashDeviceId(deviceId) {
    // Простое хеширование device_id для использования в контракте
    const crypto = require('crypto');
    return BigInt('0x' + crypto.createHash('sha256').update(deviceId).digest('hex').slice(0, 64));
  }

  calculateContractAddress(code, data) {
    // Вычисление адреса контракта на основе кода и данных
    const stateInit = beginCell()
      .storeBit(0) // split_depth
      .storeBit(0) // special
      .storeBit(1) // code
      .storeRef(code)
      .storeBit(1) // data
      .storeRef(data)
      .storeBit(0) // library
      .endCell();

    return new Address(0, stateInit.hash());
  }

  /**
   * Получение информации о контракте
   */
  async getContractInfo(contractAddress) {
    try {
      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const [deviceInfo, controlStatus, contractStats] = await Promise.all([
        contract.getDeviceInfo(),
        contract.getControlStatus(),
        contract.getContractStats()
      ]);

      return {
        owner: deviceInfo[0]?.toString(),
        deviceId: deviceInfo[1].toString(),
        pricePerMinute: fromNano(deviceInfo[2]),
        isControlled: controlStatus[0],
        currentController: controlStatus[1]?.toString() || null,
        controlEndTime: Number(controlStatus[2]),
        timeRemaining: Number(controlStatus[3]),
        totalEarned: fromNano(contractStats[0]),
        platformFeePercent: Number(contractStats[1]) / 100,
        isActive: Boolean(contractStats[2])
      };
    } catch (error) {
      console.error('Ошибка получения информации о контракте:', error);
      throw error;
    }
  }

  /**
   * Вывод комиссии платформы
   */
  async withdrawPlatformFee() {
    try {
      if (!this.universalContractService) {
        throw new Error('UniversalContractService не инициализирован');
      }

      // Создаем сообщение для вывода комиссии платформы
      const message = this.universalContractService.createWithdrawPlatformFeeMessage();

      return {
        success: true,
        ...message,
        transactionId: `platform_withdraw_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };
    } catch (error) {
      console.error('Ошибка создания транзакции вывода комиссии платформы:', error);
      throw error;
    }
  }

  /**
   * Получение накопленных балансов контракта
   */
  async getAccumulatedBalances(contractAddress) {
    try {
      if (!this.client) {
        throw new Error('TON клиент не инициализирован');
      }

      const contract = this.client.open({
        address: Address.parse(contractAddress)
      });

      const result = await contract.getAccumulatedBalances();

      return {
        success: true,
        accumulatedEarnings: fromNano(result[0]),
        accumulatedPlatformFee: fromNano(result[1])
      };
    } catch (error) {
      console.error('Ошибка получения накопленных балансов:', error);
      return {
        success: false,
        message: error.message,
        accumulatedEarnings: '0',
        accumulatedPlatformFee: '0'
      };
    }
  }

  /**
   * Обновление накопленных балансов в базе данных
   */
  async syncAccumulatedBalances(pool, contractAddress, deviceId) {
    try {
      const balances = await this.getAccumulatedBalances(contractAddress);

      if (balances.success) {
        await pool.query(`
          UPDATE device_contracts
          SET
            accumulated_earnings = $1,
            accumulated_platform_fee = $2,
            last_sync_time = NOW()
          WHERE device_id = $3 AND contract_address = $4
        `, [
          balances.accumulatedEarnings,
          balances.accumulatedPlatformFee,
          deviceId,
          contractAddress
        ]);

        return {
          success: true,
          accumulatedEarnings: balances.accumulatedEarnings,
          accumulatedPlatformFee: balances.accumulatedPlatformFee
        };
      }

      return balances;
    } catch (error) {
      console.error('Ошибка синхронизации накопленных балансов:', error);
      throw error;
    }
  }

  /**
   * Деплой универсального контракта (выполняется один раз)
   */
  async deployUniversalContract() {
    try {
      if (!this.universalContractService) {
        throw new Error('UniversalContractService не инициализирован');
      }

      return await this.universalContractService.deployUniversalContract();
    } catch (error) {
      console.error('Ошибка деплоя универсального контракта:', error);
      throw error;
    }
  }

  /**
   * Получение адреса универсального контракта
   */
  getUniversalContractAddress() {
    if (!this.universalContractService) {
      return null;
    }
    return this.universalContractService.getContractAddress();
  }

  /**
   * Деплой контракта в блокчейн
   */
  async deployContract(contractData) {
    try {
      if (!this.contractDeployService) {
        throw new Error('ContractDeployService не инициализирован');
      }

      return await this.contractDeployService.deployContract(contractData);
    } catch (error) {
      console.error('Ошибка деплоя контракта:', error);
      throw error;
    }
  }

  /**
   * Проверка существования контракта
   */
  async checkContractExists(contractAddress) {
    try {
      if (!this.contractDeployService) {
        throw new Error('ContractDeployService не инициализирован');
      }

      return await this.contractDeployService.checkContractExists(contractAddress);
    } catch (error) {
      console.error('Ошибка проверки контракта:', error);
      return false;
    }
  }

  /**
   * Получение баланса стримера (демо-версия)
   */
  async getStreamerBalance(streamerAddress) {
    try {
      console.log(`💰 Получение баланса стримера ${streamerAddress}`);

      // В демо-режиме возвращаем случайный баланс для демонстрации
      const demoBalance = (Math.random() * 10).toFixed(2);
      console.log(`💰 Демо-баланс стримера: ${demoBalance} TON`);

      return demoBalance;
    } catch (error) {
      console.error('Ошибка получения баланса стримера:', error);
      return '0';
    }
  }

  /**
   * Получение статистики платформы
   */
  async getPlatformStats() {
    try {
      if (!this.universalContractService) {
        throw new Error('UniversalContractService не инициализирован');
      }

      return await this.universalContractService.getPlatformStats();
    } catch (error) {
      console.error('Ошибка получения статистики платформы:', error);
      return {
        feePercent: 0,
        totalFees: '0',
        totalVolume: '0'
      };
    }
  }

  /**
   * Получение активной сессии устройства (демо-версия)
   */
  async getActiveSession(streamerAddress, deviceId) {
    try {
      console.log(`🔍 Проверка активной сессии для устройства ${deviceId}`);

      // В демо-режиме всегда возвращаем, что устройство свободно
      return {
        endTime: 0,
        controller: null,
        isActive: false
      };
    } catch (error) {
      console.error('Ошибка получения активной сессии:', error);
      return {
        endTime: 0,
        controller: null,
        isActive: false
      };
    }
  }

  /**
   * Получение цены устройства
   */
  async getDevicePrice(streamerAddress, deviceId) {
    try {
      if (!this.universalContractService) {
        throw new Error('UniversalContractService не инициализирован');
      }

      return await this.universalContractService.getDevicePrice(streamerAddress, deviceId);
    } catch (error) {
      console.error('Ошибка получения цены устройства:', error);
      return '1.0';
    }
  }

  /**
   * Проверка существования универсального контракта
   */
  async checkUniversalContractExists() {
    try {
      if (!this.universalContractService) {
        throw new Error('UniversalContractService не инициализирован');
      }

      return await this.universalContractService.checkContractExists();
    } catch (error) {
      console.error('Ошибка проверки универсального контракта:', error);
      return false;
    }
  }

  /**
   * Создание мемо для устройства
   */
  createDeviceMemo(streamerAddress, deviceId) {
    if (!this.universalContractService) {
      return `${streamerAddress}:${deviceId}`;
    }
    return this.universalContractService.createDeviceMemo(streamerAddress, deviceId);
  }
}

module.exports = TonService;
