{"version": 3, "sources": ["../../../src/utils/open.ts"], "sourcesContent": ["import betterOpenBrowserAsync from 'better-opn';\n\n/**\n * Due to a bug in `open`, which is used as fallback on Windows, we need to ensure `process.env.SYSTEMROOT` is set.\n * This environment variable is set by Windows on `SystemRoot`, causing `open` to execute a command with an \"unknown\" drive letter.\n *\n * @see https://github.com/sindresorhus/open/issues/205\n */\nexport async function openBrowserAsync(\n  target: string,\n  options?: any\n): Promise<import('child_process').ChildProcess | false> {\n  if (process.platform !== 'win32') {\n    return await betterOpenBrowserAsync(target, options);\n  }\n\n  const oldSystemRoot = process.env.SYSTEMROOT;\n  try {\n    process.env.SYSTEMROOT = process.env.SYSTEMROOT ?? process.env.SystemRoot;\n    return await betterOpenBrowserAsync(target, options);\n  } finally {\n    process.env.SYSTEMROOT = oldSystemRoot;\n  }\n}\n"], "names": ["openBrowserAsync", "target", "options", "process", "platform", "betterOpenBrowserAsync", "oldSystemRoot", "env", "SYSTEMROOT", "SystemRoot"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;gEARa;;;;;;;;;;;AAQ5B,eAAeA,iBACpBC,MAAc,EACdC,OAAa;IAEb,IAAIC,QAAQC,QAAQ,KAAK,SAAS;QAChC,OAAO,MAAMC,IAAAA,oBAAsB,EAACJ,QAAQC;IAC9C;IAEA,MAAMI,gBAAgBH,QAAQI,GAAG,CAACC,UAAU;IAC5C,IAAI;QACFL,QAAQI,GAAG,CAACC,UAAU,GAAGL,QAAQI,GAAG,CAACC,UAAU,IAAIL,QAAQI,GAAG,CAACE,UAAU;QACzE,OAAO,MAAMJ,IAAAA,oBAAsB,EAACJ,QAAQC;IAC9C,SAAU;QACRC,QAAQI,GAAG,CAACC,UAAU,GAAGF;IAC3B;AACF"}