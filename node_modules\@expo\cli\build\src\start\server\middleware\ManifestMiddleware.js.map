{"version": 3, "sources": ["../../../../../src/start/server/middleware/ManifestMiddleware.ts"], "sourcesContent": ["import {\n  ExpoConfig,\n  ExpoGoConfig,\n  getConfig,\n  PackageJSONConfig,\n  ProjectConfig,\n} from '@expo/config';\nimport { resolveEntryPoint, getMetroServerRoot } from '@expo/config/paths';\nimport path from 'path';\nimport { resolve } from 'url';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  shouldEnableAsyncImports,\n  createBundleUrlPath,\n  getBaseUrlFromExpoConfig,\n  getAsyncRoutesFromExpoConfig,\n  createBundleUrlPathFromExpoConfig,\n  convertPathToModuleSpecifier,\n} from './metroOptions';\nimport { resolveGoogleServicesFile, resolveManifestAssets } from './resolveAssets';\nimport { parsePlatformHeader, RuntimePlatform } from './resolvePlatform';\nimport { ServerHeaders, ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport { isEnableHermesManaged } from '../../../export/exportHermes';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { stripExtension } from '../../../utils/url';\nimport * as ProjectDevices from '../../project/devices';\nimport { UrlCreator } from '../UrlCreator';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\nimport { getPlatformBundlers, PlatformBundlers } from '../platformBundlers';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../webTemplate';\n\nconst debug = require('debug')('expo:start:server:middleware:manifest') as typeof console.log;\n\nconst supportedPlatforms = ['ios', 'android', 'web', 'none'];\n\nexport function getEntryWithServerRoot(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n) {\n  if (!supportedPlatforms.includes(props.platform)) {\n    throw new CommandError(\n      `Failed to resolve the project's entry file: The platform \"${props.platform}\" is not supported.`\n    );\n  }\n  return convertPathToModuleSpecifier(\n    path.relative(getMetroServerRoot(projectRoot), resolveEntryPoint(projectRoot, props))\n  );\n}\n\n/** Get the main entry module ID (file) relative to the project root. */\nexport function resolveMainModuleName(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n): string {\n  const entryPoint = getEntryWithServerRoot(projectRoot, props);\n\n  debug(`Resolved entry point: ${entryPoint} (project root: ${projectRoot})`);\n\n  return convertPathToModuleSpecifier(stripExtension(entryPoint, 'js'));\n}\n\n/** Info about the computer hosting the dev server. */\nexport interface HostInfo {\n  host: string;\n  server: 'expo';\n  serverVersion: string;\n  serverDriver: string | null;\n  serverOS: NodeJS.Platform;\n  serverOSVersion: string;\n}\n\n/** Parsed values from the supported request headers. */\nexport interface ManifestRequestInfo {\n  /** Platform to serve. */\n  platform: RuntimePlatform;\n  /** Requested host name. */\n  hostname?: string | null;\n  /** The protocol used to request the manifest */\n  protocol?: 'http' | 'https';\n}\n\n/** Project related info. */\nexport type ResponseProjectSettings = {\n  expoGoConfig: ExpoGoConfig;\n  hostUri: string;\n  bundleUrl: string;\n  exp: ExpoConfig;\n};\n\nexport const DEVELOPER_TOOL = 'expo-cli';\n\nexport type ManifestMiddlewareOptions = {\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n  constructUrl: UrlCreator['constructUrl'];\n  isNativeWebpack?: boolean;\n  privateKeyPath?: string;\n};\n\n/** Base middleware creator for serving the Expo manifest (like the index.html but for native runtimes). */\nexport abstract class ManifestMiddleware<\n  TManifestRequestInfo extends ManifestRequestInfo,\n> extends ExpoMiddleware {\n  private initialProjectConfig: ProjectConfig;\n  private platformBundlers: PlatformBundlers;\n\n  constructor(\n    protected projectRoot: string,\n    protected options: ManifestMiddlewareOptions\n  ) {\n    super(\n      projectRoot,\n      /**\n       * Only support `/`, `/manifest`, `/index.exp` for the manifest middleware.\n       */\n      ['/', '/manifest', '/index.exp']\n    );\n    this.initialProjectConfig = getConfig(projectRoot);\n    this.platformBundlers = getPlatformBundlers(projectRoot, this.initialProjectConfig.exp);\n  }\n\n  /** Exposed for testing. */\n  public async _resolveProjectSettingsAsync({\n    platform,\n    hostname,\n    protocol,\n  }: Pick<\n    TManifestRequestInfo,\n    'hostname' | 'platform' | 'protocol'\n  >): Promise<ResponseProjectSettings> {\n    // Read the config\n    const projectConfig = getConfig(this.projectRoot);\n\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: projectConfig.pkg,\n      platform,\n    });\n\n    const isHermesEnabled = isEnableHermesManaged(projectConfig.exp, platform);\n\n    // Create the manifest and set fields within it\n    const expoGoConfig = this.getExpoGoConfig({\n      mainModuleName,\n      hostname,\n    });\n\n    const hostUri = this.options.constructUrl({ scheme: '', hostname });\n\n    const bundleUrl = this._getBundleUrl({\n      platform,\n      mainModuleName,\n      hostname,\n      engine: isHermesEnabled ? 'hermes' : undefined,\n      baseUrl: getBaseUrlFromExpoConfig(projectConfig.exp),\n      asyncRoutes: getAsyncRoutesFromExpoConfig(\n        projectConfig.exp,\n        this.options.mode ?? 'development',\n        platform\n      ),\n      routerRoot: getRouterDirectoryModuleIdWithManifest(this.projectRoot, projectConfig.exp),\n      protocol,\n      reactCompiler: !!projectConfig.exp.experiments?.reactCompiler,\n    });\n\n    // Resolve all assets and set them on the manifest as URLs\n    await this.mutateManifestWithAssetsAsync(projectConfig.exp, bundleUrl);\n\n    return {\n      expoGoConfig,\n      hostUri,\n      bundleUrl,\n      exp: projectConfig.exp,\n    };\n  }\n\n  /** Get the main entry module ID (file) relative to the project root. */\n  private resolveMainModuleName(props: { pkg: PackageJSONConfig; platform: string }): string {\n    let entryPoint = getEntryWithServerRoot(this.projectRoot, props);\n\n    debug(`Resolved entry point: ${entryPoint} (project root: ${this.projectRoot})`);\n\n    // NOTE(Bacon): Webpack is currently hardcoded to index.bundle on native\n    // in the future (TODO) we should move this logic into a Webpack plugin and use\n    // a generated file name like we do on web.\n    // const server = getDefaultDevServer();\n    // // TODO: Move this into BundlerDevServer and read this info from self.\n    // const isNativeWebpack = server instanceof WebpackBundlerDevServer && server.isTargetingNative();\n    if (this.options.isNativeWebpack) {\n      entryPoint = 'index.js';\n    }\n\n    return stripExtension(entryPoint, 'js');\n  }\n\n  /** Parse request headers into options. */\n  public abstract getParsedHeaders(req: ServerRequest): TManifestRequestInfo;\n\n  /** Store device IDs that were sent in the request headers. */\n  private async saveDevicesAsync(req: ServerRequest) {\n    const deviceIds = req.headers?.['expo-dev-client-id'];\n    if (deviceIds) {\n      await ProjectDevices.saveDevicesAsync(this.projectRoot, deviceIds).catch((e) =>\n        Log.exception(e)\n      );\n    }\n  }\n\n  /** Create the bundle URL (points to the single JS entry file). Exposed for testing. */\n  public _getBundleUrl({\n    platform,\n    mainModuleName,\n    hostname,\n    engine,\n    baseUrl,\n    isExporting,\n    asyncRoutes,\n    routerRoot,\n    protocol,\n    reactCompiler,\n  }: {\n    platform: string;\n    hostname?: string | null;\n    mainModuleName: string;\n    engine?: 'hermes';\n    baseUrl?: string;\n    asyncRoutes: boolean;\n    isExporting?: boolean;\n    routerRoot: string;\n    protocol?: 'http' | 'https';\n    reactCompiler: boolean;\n  }): string {\n    const path = createBundleUrlPath({\n      mode: this.options.mode ?? 'development',\n      minify: this.options.minify,\n      platform,\n      mainModuleName,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      engine,\n      bytecode: engine === 'hermes',\n      baseUrl,\n      isExporting: !!isExporting,\n      asyncRoutes,\n      routerRoot,\n      reactCompiler,\n    });\n\n    return (\n      this.options.constructUrl({\n        scheme: protocol ?? 'http',\n        // hostType: this.options.location.hostType,\n        hostname,\n      }) + path\n    );\n  }\n\n  /** Get the manifest response to return to the runtime. This file contains info regarding where the assets can be loaded from. Exposed for testing. */\n  public abstract _getManifestResponseAsync(options: TManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }>;\n\n  private getExpoGoConfig({\n    mainModuleName,\n    hostname,\n  }: {\n    mainModuleName: string;\n    hostname?: string | null;\n  }): ExpoGoConfig {\n    return {\n      // localhost:8081\n      debuggerHost: this.options.constructUrl({ scheme: '', hostname }),\n      // Required for Expo Go to function.\n      developer: {\n        tool: DEVELOPER_TOOL,\n        projectRoot: this.projectRoot,\n      },\n      packagerOpts: {\n        // Required for dev client.\n        dev: this.options.mode !== 'production',\n      },\n      // Indicates the name of the main bundle.\n      mainModuleName,\n      // Add this string to make Flipper register React Native / Metro as \"running\".\n      // Can be tested by running:\n      // `METRO_SERVER_PORT=8081 open -a flipper.app`\n      // Where 8081 is the port where the Expo project is being hosted.\n      __flipperHack: 'React Native packager is running',\n    };\n  }\n\n  /** Resolve all assets and set them on the manifest as URLs */\n  private async mutateManifestWithAssetsAsync(manifest: ExpoConfig, bundleUrl: string) {\n    await resolveManifestAssets(this.projectRoot, {\n      manifest,\n      resolver: async (path) => {\n        if (this.options.isNativeWebpack) {\n          // When using our custom dev server, just do assets normally\n          // without the `assets/` subpath redirect.\n          return resolve(bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0], path);\n        }\n        return bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0] + 'assets/' + path;\n      },\n    });\n    // The server normally inserts this but if we're offline we'll do it here\n    await resolveGoogleServicesFile(this.projectRoot, manifest);\n  }\n\n  public getWebBundleUrl() {\n    const platform = 'web';\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: this.initialProjectConfig.pkg,\n      platform,\n    });\n\n    return createBundleUrlPathFromExpoConfig(this.projectRoot, this.initialProjectConfig.exp, {\n      platform,\n      mainModuleName,\n      minify: this.options.minify,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      mode: this.options.mode ?? 'development',\n      // Hermes doesn't support more modern JS features than most, if not all, modern browser.\n      engine: 'hermes',\n      isExporting: false,\n      bytecode: false,\n    });\n  }\n\n  /**\n   * Web platforms should create an index.html response using the same script resolution as native.\n   *\n   * Instead of adding a `bundleUrl` to a `manifest.json` (native) we'll add a `<script src=\"\">`\n   * to an `index.html`, this enables the web platform to load JavaScript from the server.\n   */\n  private async handleWebRequestAsync(req: ServerRequest, res: ServerResponse) {\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(await this.getSingleHtmlTemplateAsync());\n  }\n\n  getSingleHtmlTemplateAsync() {\n    // Read from headers\n    const bundleUrl = this.getWebBundleUrl();\n\n    return createTemplateHtmlFromExpoConfigAsync(this.projectRoot, {\n      exp: this.initialProjectConfig.exp,\n      scripts: [bundleUrl],\n    });\n  }\n\n  /** Exposed for testing. */\n  async checkBrowserRequestAsync(req: ServerRequest, res: ServerResponse, next: ServerNext) {\n    if (\n      this.platformBundlers.web === 'metro' &&\n      this.initialProjectConfig.exp.platforms?.includes('web')\n    ) {\n      // NOTE(EvanBacon): This effectively disables the safety check we do on custom runtimes to ensure\n      // the `expo-platform` header is included. When `web.bundler=web`, if the user has non-standard Expo\n      // code loading then they'll get a web bundle without a clear assertion of platform support.\n      const platform = parsePlatformHeader(req);\n      // On web, serve the public folder\n      if (!platform || platform === 'web') {\n        if (['static', 'server'].includes(this.initialProjectConfig.exp.web?.output ?? '')) {\n          // Skip the spa-styled index.html when static generation is enabled.\n          next();\n          return true;\n        } else {\n          await this.handleWebRequestAsync(req, res);\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    // First check for standard JavaScript runtimes (aka legacy browsers like Chrome).\n    if (await this.checkBrowserRequestAsync(req, res, next)) {\n      return;\n    }\n\n    // Save device IDs for dev client.\n    await this.saveDevicesAsync(req);\n\n    // Read from headers\n    const options = this.getParsedHeaders(req);\n    const { body, headers } = await this._getManifestResponseAsync(options);\n    for (const [headerName, headerValue] of headers) {\n      res.setHeader(headerName, headerValue);\n    }\n    res.end(body);\n  }\n}\n"], "names": ["DEVELOPER_TOOL", "ManifestMiddleware", "getEntryWithServerRoot", "resolveMainModuleName", "debug", "require", "supportedPlatforms", "projectRoot", "props", "includes", "platform", "CommandError", "convertPathToModuleSpecifier", "path", "relative", "getMetroServerRoot", "resolveEntryPoint", "entryPoint", "stripExtension", "ExpoMiddleware", "constructor", "options", "initialProjectConfig", "getConfig", "platformBundlers", "getPlatformBundlers", "exp", "_resolveProjectSettingsAsync", "hostname", "protocol", "projectConfig", "mainModuleName", "pkg", "isHermesEnabled", "isEnableHermesManaged", "expoGoConfig", "getExpoGoConfig", "hostUri", "constructUrl", "scheme", "bundleUrl", "_getBundleUrl", "engine", "undefined", "baseUrl", "getBaseUrlFromExpoConfig", "asyncRoutes", "getAsyncRoutesFromExpoConfig", "mode", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "reactCompiler", "experiments", "mutateManifestWithAssetsAsync", "isNativeWebpack", "saveDevicesAsync", "req", "deviceIds", "headers", "ProjectDevices", "catch", "e", "Log", "exception", "isExporting", "createBundleUrlPath", "minify", "lazy", "shouldEnableAsyncImports", "bytecode", "debuggerHost", "developer", "tool", "packagerOpts", "dev", "__flipperHack", "manifest", "resolveManifestAssets", "resolver", "resolve", "match", "resolveGoogleServicesFile", "getWebBundleUrl", "createBundleUrlPathFromExpoConfig", "handleWebRequestAsync", "res", "<PERSON><PERSON><PERSON><PERSON>", "end", "getSingleHtmlTemplateAsync", "createTemplateHtmlFromExpoConfigAsync", "scripts", "checkBrowserRequestAsync", "next", "web", "platforms", "parsePlatformHeader", "output", "handleRequestAsync", "getParsedHeaders", "body", "_getManifestResponseAsync", "headerName", "headerValue"], "mappings": ";;;;;;;;;;;IA2FaA,cAAc;eAAdA;;IAaSC,kBAAkB;eAAlBA;;IAnENC,sBAAsB;eAAtBA;;IAeAC,qBAAqB;eAArBA;;;;yBA9CT;;;;;;;yBAC+C;;;;;;;gEACrC;;;;;;;yBACO;;;;;;gCAEO;8BAQxB;+BAC0D;iCACZ;8BAEf;6DACjB;wBACQ;sBACE;iEACC;wBAEuB;kCACD;6BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtD,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,qBAAqB;IAAC;IAAO;IAAW;IAAO;CAAO;AAErD,SAASJ,uBACdK,WAAmB,EACnBC,KAAoD;IAEpD,IAAI,CAACF,mBAAmBG,QAAQ,CAACD,MAAME,QAAQ,GAAG;QAChD,MAAM,IAAIC,oBAAY,CACpB,CAAC,0DAA0D,EAAEH,MAAME,QAAQ,CAAC,mBAAmB,CAAC;IAEpG;IACA,OAAOE,IAAAA,0CAA4B,EACjCC,eAAI,CAACC,QAAQ,CAACC,IAAAA,2BAAkB,EAACR,cAAcS,IAAAA,0BAAiB,EAACT,aAAaC;AAElF;AAGO,SAASL,sBACdI,WAAmB,EACnBC,KAAoD;IAEpD,MAAMS,aAAaf,uBAAuBK,aAAaC;IAEvDJ,MAAM,CAAC,sBAAsB,EAAEa,WAAW,gBAAgB,EAAEV,YAAY,CAAC,CAAC;IAE1E,OAAOK,IAAAA,0CAA4B,EAACM,IAAAA,oBAAc,EAACD,YAAY;AACjE;AA8BO,MAAMjB,iBAAiB;AAavB,MAAeC,2BAEZkB,8BAAc;IAItBC,YACE,AAAUb,WAAmB,EAC7B,AAAUc,OAAkC,CAC5C;QACA,KAAK,CACHd,aACA;;OAEC,GACD;YAAC;YAAK;YAAa;SAAa,QARxBA,cAAAA,kBACAc,UAAAA;QASV,IAAI,CAACC,oBAAoB,GAAGC,IAAAA,mBAAS,EAAChB;QACtC,IAAI,CAACiB,gBAAgB,GAAGC,IAAAA,qCAAmB,EAAClB,aAAa,IAAI,CAACe,oBAAoB,CAACI,GAAG;IACxF;IAEA,yBAAyB,GACzB,MAAaC,6BAA6B,EACxCjB,QAAQ,EACRkB,QAAQ,EACRC,QAAQ,EAIT,EAAoC;YAiChBC;QAhCnB,kBAAkB;QAClB,MAAMA,gBAAgBP,IAAAA,mBAAS,EAAC,IAAI,CAAChB,WAAW;QAEhD,oBAAoB;QACpB,MAAMwB,iBAAiB,IAAI,CAAC5B,qBAAqB,CAAC;YAChD6B,KAAKF,cAAcE,GAAG;YACtBtB;QACF;QAEA,MAAMuB,kBAAkBC,IAAAA,mCAAqB,EAACJ,cAAcJ,GAAG,EAAEhB;QAEjE,+CAA+C;QAC/C,MAAMyB,eAAe,IAAI,CAACC,eAAe,CAAC;YACxCL;YACAH;QACF;QAEA,MAAMS,UAAU,IAAI,CAAChB,OAAO,CAACiB,YAAY,CAAC;YAAEC,QAAQ;YAAIX;QAAS;QAEjE,MAAMY,YAAY,IAAI,CAACC,aAAa,CAAC;YACnC/B;YACAqB;YACAH;YACAc,QAAQT,kBAAkB,WAAWU;YACrCC,SAASC,IAAAA,sCAAwB,EAACf,cAAcJ,GAAG;YACnDoB,aAAaC,IAAAA,0CAA4B,EACvCjB,cAAcJ,GAAG,EACjB,IAAI,CAACL,OAAO,CAAC2B,IAAI,IAAI,eACrBtC;YAEFuC,YAAYC,IAAAA,8CAAsC,EAAC,IAAI,CAAC3C,WAAW,EAAEuB,cAAcJ,GAAG;YACtFG;YACAsB,eAAe,CAAC,GAACrB,iCAAAA,cAAcJ,GAAG,CAAC0B,WAAW,qBAA7BtB,+BAA+BqB,aAAa;QAC/D;QAEA,0DAA0D;QAC1D,MAAM,IAAI,CAACE,6BAA6B,CAACvB,cAAcJ,GAAG,EAAEc;QAE5D,OAAO;YACLL;YACAE;YACAG;YACAd,KAAKI,cAAcJ,GAAG;QACxB;IACF;IAEA,sEAAsE,GACtE,AAAQvB,sBAAsBK,KAAmD,EAAU;QACzF,IAAIS,aAAaf,uBAAuB,IAAI,CAACK,WAAW,EAAEC;QAE1DJ,MAAM,CAAC,sBAAsB,EAAEa,WAAW,gBAAgB,EAAE,IAAI,CAACV,WAAW,CAAC,CAAC,CAAC;QAE/E,wEAAwE;QACxE,+EAA+E;QAC/E,2CAA2C;QAC3C,wCAAwC;QACxC,yEAAyE;QACzE,mGAAmG;QACnG,IAAI,IAAI,CAACc,OAAO,CAACiC,eAAe,EAAE;YAChCrC,aAAa;QACf;QAEA,OAAOC,IAAAA,oBAAc,EAACD,YAAY;IACpC;IAKA,4DAA4D,GAC5D,MAAcsC,iBAAiBC,GAAkB,EAAE;YAC/BA;QAAlB,MAAMC,aAAYD,eAAAA,IAAIE,OAAO,qBAAXF,YAAa,CAAC,qBAAqB;QACrD,IAAIC,WAAW;YACb,MAAME,SAAeJ,gBAAgB,CAAC,IAAI,CAAChD,WAAW,EAAEkD,WAAWG,KAAK,CAAC,CAACC,IACxEC,KAAIC,SAAS,CAACF;QAElB;IACF;IAEA,qFAAqF,GACrF,AAAOpB,cAAc,EACnB/B,QAAQ,EACRqB,cAAc,EACdH,QAAQ,EACRc,MAAM,EACNE,OAAO,EACPoB,WAAW,EACXlB,WAAW,EACXG,UAAU,EACVpB,QAAQ,EACRsB,aAAa,EAYd,EAAU;QACT,MAAMtC,OAAOoD,IAAAA,iCAAmB,EAAC;YAC/BjB,MAAM,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,IAAI;YAC3BkB,QAAQ,IAAI,CAAC7C,OAAO,CAAC6C,MAAM;YAC3BxD;YACAqB;YACAoC,MAAMC,IAAAA,sCAAwB,EAAC,IAAI,CAAC7D,WAAW;YAC/CmC;YACA2B,UAAU3B,WAAW;YACrBE;YACAoB,aAAa,CAAC,CAACA;YACflB;YACAG;YACAE;QACF;QAEA,OACE,IAAI,CAAC9B,OAAO,CAACiB,YAAY,CAAC;YACxBC,QAAQV,YAAY;YACpB,4CAA4C;YAC5CD;QACF,KAAKf;IAET;IASQuB,gBAAgB,EACtBL,cAAc,EACdH,QAAQ,EAIT,EAAgB;QACf,OAAO;YACL,iBAAiB;YACjB0C,cAAc,IAAI,CAACjD,OAAO,CAACiB,YAAY,CAAC;gBAAEC,QAAQ;gBAAIX;YAAS;YAC/D,oCAAoC;YACpC2C,WAAW;gBACTC,MAAMxE;gBACNO,aAAa,IAAI,CAACA,WAAW;YAC/B;YACAkE,cAAc;gBACZ,2BAA2B;gBAC3BC,KAAK,IAAI,CAACrD,OAAO,CAAC2B,IAAI,KAAK;YAC7B;YACA,yCAAyC;YACzCjB;YACA,8EAA8E;YAC9E,4BAA4B;YAC5B,+CAA+C;YAC/C,iEAAiE;YACjE4C,eAAe;QACjB;IACF;IAEA,4DAA4D,GAC5D,MAActB,8BAA8BuB,QAAoB,EAAEpC,SAAiB,EAAE;QACnF,MAAMqC,IAAAA,oCAAqB,EAAC,IAAI,CAACtE,WAAW,EAAE;YAC5CqE;YACAE,UAAU,OAAOjE;gBACf,IAAI,IAAI,CAACQ,OAAO,CAACiC,eAAe,EAAE;oBAChC,4DAA4D;oBAC5D,0CAA0C;oBAC1C,OAAOyB,IAAAA,cAAO,EAACvC,UAAWwC,KAAK,CAAC,oBAAqB,CAAC,EAAE,EAAEnE;gBAC5D;gBACA,OAAO2B,UAAWwC,KAAK,CAAC,oBAAqB,CAAC,EAAE,GAAG,YAAYnE;YACjE;QACF;QACA,yEAAyE;QACzE,MAAMoE,IAAAA,wCAAyB,EAAC,IAAI,CAAC1E,WAAW,EAAEqE;IACpD;IAEOM,kBAAkB;QACvB,MAAMxE,WAAW;QACjB,oBAAoB;QACpB,MAAMqB,iBAAiB,IAAI,CAAC5B,qBAAqB,CAAC;YAChD6B,KAAK,IAAI,CAACV,oBAAoB,CAACU,GAAG;YAClCtB;QACF;QAEA,OAAOyE,IAAAA,+CAAiC,EAAC,IAAI,CAAC5E,WAAW,EAAE,IAAI,CAACe,oBAAoB,CAACI,GAAG,EAAE;YACxFhB;YACAqB;YACAmC,QAAQ,IAAI,CAAC7C,OAAO,CAAC6C,MAAM;YAC3BC,MAAMC,IAAAA,sCAAwB,EAAC,IAAI,CAAC7D,WAAW;YAC/CyC,MAAM,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,IAAI;YAC3B,wFAAwF;YACxFN,QAAQ;YACRsB,aAAa;YACbK,UAAU;QACZ;IACF;IAEA;;;;;GAKC,GACD,MAAce,sBAAsB5B,GAAkB,EAAE6B,GAAmB,EAAE;QAC3EA,IAAIC,SAAS,CAAC,gBAAgB;QAE9BD,IAAIE,GAAG,CAAC,MAAM,IAAI,CAACC,0BAA0B;IAC/C;IAEAA,6BAA6B;QAC3B,oBAAoB;QACpB,MAAMhD,YAAY,IAAI,CAAC0C,eAAe;QAEtC,OAAOO,IAAAA,kDAAqC,EAAC,IAAI,CAAClF,WAAW,EAAE;YAC7DmB,KAAK,IAAI,CAACJ,oBAAoB,CAACI,GAAG;YAClCgE,SAAS;gBAAClD;aAAU;QACtB;IACF;IAEA,yBAAyB,GACzB,MAAMmD,yBAAyBnC,GAAkB,EAAE6B,GAAmB,EAAEO,IAAgB,EAAE;YAGtF;QAFF,IACE,IAAI,CAACpE,gBAAgB,CAACqE,GAAG,KAAK,aAC9B,2CAAA,IAAI,CAACvE,oBAAoB,CAACI,GAAG,CAACoE,SAAS,qBAAvC,yCAAyCrF,QAAQ,CAAC,SAClD;YACA,iGAAiG;YACjG,oGAAoG;YACpG,4FAA4F;YAC5F,MAAMC,WAAWqF,IAAAA,oCAAmB,EAACvC;YACrC,kCAAkC;YAClC,IAAI,CAAC9C,YAAYA,aAAa,OAAO;oBACD;gBAAlC,IAAI;oBAAC;oBAAU;iBAAS,CAACD,QAAQ,CAAC,EAAA,qCAAA,IAAI,CAACa,oBAAoB,CAACI,GAAG,CAACmE,GAAG,qBAAjC,mCAAmCG,MAAM,KAAI,KAAK;oBAClF,oEAAoE;oBACpEJ;oBACA,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,CAACR,qBAAqB,CAAC5B,KAAK6B;oBACtC,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAMY,mBACJzC,GAAkB,EAClB6B,GAAmB,EACnBO,IAAgB,EACD;QACf,kFAAkF;QAClF,IAAI,MAAM,IAAI,CAACD,wBAAwB,CAACnC,KAAK6B,KAAKO,OAAO;YACvD;QACF;QAEA,kCAAkC;QAClC,MAAM,IAAI,CAACrC,gBAAgB,CAACC;QAE5B,oBAAoB;QACpB,MAAMnC,UAAU,IAAI,CAAC6E,gBAAgB,CAAC1C;QACtC,MAAM,EAAE2C,IAAI,EAAEzC,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC0C,yBAAyB,CAAC/E;QAC/D,KAAK,MAAM,CAACgF,YAAYC,YAAY,IAAI5C,QAAS;YAC/C2B,IAAIC,SAAS,CAACe,YAAYC;QAC5B;QACAjB,IAAIE,GAAG,CAACY;IACV;AACF"}