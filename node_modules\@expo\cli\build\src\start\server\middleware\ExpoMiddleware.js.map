{"version": 3, "sources": ["../../../../../src/start/server/middleware/ExpoMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\n/** Base middleware creator for Expo dev servers. */\nexport abstract class ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected supportedPaths: string[]\n  ) {}\n\n  /**\n   * Returns true when the middleware should handle the incoming server request.\n   * Exposed for testing.\n   */\n  public shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url) {\n      return false;\n    }\n    const parsed = parse(req.url);\n    // Strip the query params\n    if (!parsed.pathname) {\n      return false;\n    }\n\n    return this.supportedPaths.includes(parsed.pathname);\n  }\n\n  abstract handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void>;\n\n  /** Create a server middleware handler. */\n  public getHandler() {\n    const internalMiddleware = async (\n      req: ServerRequest,\n      res: ServerResponse,\n      next: ServerNext\n    ) => {\n      try {\n        return await this.handleRequestAsync(req, res, next);\n      } catch (error: any) {\n        Log.exception(error);\n        // 5xx = Server Error HTTP code\n        res.statusCode = 500;\n        if (typeof error === 'object' && error !== null) {\n          res.end(\n            JSON.stringify({\n              error: error.toString(),\n            })\n          );\n        } else {\n          res.end(`Unexpected error: ${error}`);\n        }\n      }\n    };\n    const middleware = async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n      if (!this.shouldHandleRequest(req)) {\n        return next();\n      }\n      return internalMiddleware(req, res, next);\n    };\n\n    middleware.internal = internalMiddleware;\n\n    return middleware;\n  }\n}\n\nexport function disableResponseCache(res: ServerResponse): ServerResponse {\n  res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');\n  res.setHeader('Expires', '-1');\n  res.setHeader('Pragma', 'no-cache');\n  return res;\n}\n"], "names": ["ExpoMiddleware", "disableResponseCache", "constructor", "projectRoot", "supportedPaths", "shouldHandleRequest", "req", "url", "parsed", "parse", "pathname", "includes", "<PERSON><PERSON><PERSON><PERSON>", "internalMiddleware", "res", "next", "handleRequestAsync", "error", "Log", "exception", "statusCode", "end", "JSON", "stringify", "toString", "middleware", "internal", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;IAMsBA,cAAc;eAAdA;;IAkENC,oBAAoB;eAApBA;;;;yBAxEM;;;;;;6DAGD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGd,MAAeD;IACpBE,YACE,AAAUC,WAAmB,EAC7B,AAAUC,cAAwB,CAClC;aAFUD,cAAAA;aACAC,iBAAAA;IACT;IAEH;;;GAGC,GACD,AAAOC,oBAAoBC,GAAkB,EAAW;QACtD,IAAI,CAACA,IAAIC,GAAG,EAAE;YACZ,OAAO;QACT;QACA,MAAMC,SAASC,IAAAA,YAAK,EAACH,IAAIC,GAAG;QAC5B,yBAAyB;QACzB,IAAI,CAACC,OAAOE,QAAQ,EAAE;YACpB,OAAO;QACT;QAEA,OAAO,IAAI,CAACN,cAAc,CAACO,QAAQ,CAACH,OAAOE,QAAQ;IACrD;IAQA,wCAAwC,GACxC,AAAOE,aAAa;QAClB,MAAMC,qBAAqB,OACzBP,KACAQ,KACAC;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACC,kBAAkB,CAACV,KAAKQ,KAAKC;YACjD,EAAE,OAAOE,OAAY;gBACnBC,KAAIC,SAAS,CAACF;gBACd,+BAA+B;gBAC/BH,IAAIM,UAAU,GAAG;gBACjB,IAAI,OAAOH,UAAU,YAAYA,UAAU,MAAM;oBAC/CH,IAAIO,GAAG,CACLC,KAAKC,SAAS,CAAC;wBACbN,OAAOA,MAAMO,QAAQ;oBACvB;gBAEJ,OAAO;oBACLV,IAAIO,GAAG,CAAC,CAAC,kBAAkB,EAAEJ,OAAO;gBACtC;YACF;QACF;QACA,MAAMQ,aAAa,OAAOnB,KAAoBQ,KAAqBC;YACjE,IAAI,CAAC,IAAI,CAACV,mBAAmB,CAACC,MAAM;gBAClC,OAAOS;YACT;YACA,OAAOF,mBAAmBP,KAAKQ,KAAKC;QACtC;QAEAU,WAAWC,QAAQ,GAAGb;QAEtB,OAAOY;IACT;AACF;AAEO,SAASxB,qBAAqBa,GAAmB;IACtDA,IAAIa,SAAS,CAAC,iBAAiB;IAC/Bb,IAAIa,SAAS,CAAC,WAAW;IACzBb,IAAIa,SAAS,CAAC,UAAU;IACxB,OAAOb;AACT"}